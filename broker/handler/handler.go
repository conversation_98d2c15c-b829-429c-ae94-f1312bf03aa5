package handler

import (
	"context"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/broker"
	"go-micro.dev/v4/errors"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/metadata"
)

type Broker struct{}

func (h *Broker) Publish(ctx context.Context, req *pb.PublishRequest, rsp *pb.Empty) error {
	// validate the request
	if req.Message == nil {
		return errors.BadRequest("broker.Broker.Publish", "Missing message")
	}

	// ensure the header is not nil
	if req.Message.Header == nil {
		req.Message.Header = map[string]string{}
	}

	// set any headers which aren't already set
	if md, ok := metadata.FromContext(ctx); ok {
		for k, v := range md {
			if _, ok := req.Message.Header[k]; !ok {
				req.Message.Header[k] = v
			}
		}
	}

	logger.Debugf("Publishing message to %s topic", req.Topic)
	err := broker.DefaultBroker.Publish(req.Topic, &broker.Message{
		Header: req.Message.Header,
		Body:   req.Message.Body,
	})
	logger.Debugf("Published message to %s topic", req.Topic)
	if err != nil {
		return errors.InternalServerError("broker.Broker.Publish", err.Error())
	}
	return nil
}

func (h *Broker) Subscribe(ctx context.Context, req *pb.SubscribeRequest, stream pb.Broker_SubscribeStream) error {
	if len(req.Topic) == 0 {
		return errors.BadRequest("broker.Broker.Subscribe", "Missing topic")
	}
	errChan := make(chan error, 1)

	// message Broker to stream back messages from broker
	handle := func(event broker.Event) error {
		if err := stream.Send(&pb.Message{
			Header: event.Message().Header,
			Body:   event.Message().Body,
		}); err != nil {
			select {
			case errChan <- err:
				return err
			default:
				return err
			}
		}
		return nil
	}

	logger.Debugf("Subscribing to %s topic", req.Topic)
	var opts []broker.SubscribeOption
	if len(req.Queue) > 0 {
		opts = append(opts, broker.Queue(req.Queue))
	}
	sub, err := broker.DefaultBroker.Subscribe(req.Topic, handle, opts...)
	if err != nil {
		return errors.InternalServerError("broker.Broker.Subscribe", err.Error())
	}
	defer func() {
		logger.Debugf("Unsubscribing from topic %s", req.Topic)
		sub.Unsubscribe()
	}()

	select {
	case <-ctx.Done():
		logger.Debugf("Context done for subscription to topic %s", req.Topic)
		return nil
	case err := <-errChan:
		logger.Debugf("Subscription error for topic %s: %v", req.Topic, err)
		return err
	}
}
