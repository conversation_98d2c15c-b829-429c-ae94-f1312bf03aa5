stages:
  - unit_test
  - developer_test
  - integration_test
  - release

before_script:
  - export PATH=/usr/local/go/bin:/usr/local/gopath/bin:/usr/local/bin:$PATH
  - docker login -u "${HARBOR_FOBASE}" -p"${HARBOR_FOBASE_P}" "${HARBOR_LOGIN_ADDR}"
  - echo "machine git.gobies.org login gitlab-ci-token password ${CI_JOB_TOKEN}" > ~/.netrc

after_script:
  - rm -rf ~/.netrc

unit_test:
    stage: unit_test
    script:
      - go test ./cmd/... -gcflags=all=-l --cover --count=1 -coverprofile=coverage.out
      - go tool cover --func=coverage.out
      - go tool cover --html=coverage.out  -o detail.html
    coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
    artifacts:
      paths:
        - detail.html
    tags:
      - foscan_checkurl
developer_test:
  stage: developer_test
  script:
    - goreleaser --skip-validate --clean -f .goreleaser_test.yaml
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose down"
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose pull"
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose up -d"
    - echo "DEVELOPER ENVIRONMENT DEPLOY DONE"
  tags:
    - foscan_checkurl

release:
  stage: release
  tags:
    - foscan_checkurl
  only:
    refs:
      - tags
  script:
    - goreleaser release --clean


