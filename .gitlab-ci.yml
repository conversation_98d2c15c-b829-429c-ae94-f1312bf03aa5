stages:
  - unit_test
  - developer_test
  - integration_test
  - release

before_script:
  - echo "${PWD}"
  - echo "machine git.gobies.org login gitlab-ci-token password ${CI_JOB_TOKEN}" > ~/.netrc
  - git submodule deinit -f -- ./probe
  - rm -rf .git/modules/probe
  - git rm -f ./probe
  - git submodule add --name probe -- https://git.gobies.org/fofapro_bg/rawgrab_probe_all.git probe
  - git submodule sync --recursive
  - git submodule update --init --recursive
  - export PATH=/usr/local/go/bin:/usr/local/gopath/bin:/usr/local/bin:$PATH
  - go mod tidy
  - docker login -u "${HARBOR_FOBASE}" -p"${HARBOR_FOBASE_P}" "${HARBOR_LOGIN_ADDR}"

after_script:
  - rm -rf ~/.netrc

unit_test:
    stage: unit_test
    script:
      - go test -gcflags=all=-l $(go list ./... | grep -v /probe/) --cover --count=1 -coverprofile=coverage.out
      - go tool cover --func=coverage.out
      - go tool cover --html=coverage.out  -o detail.html
    coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
    artifacts:
      paths:
        - detail.html
    allow_failure: true
    tags:
      - gitlab-runner-shared

developer_test:
  stage: developer_test
  script:
    - mkdir -p /home/<USER>/builds/foradar/docker-all-in-one/foscan/rawgrab/
    - mkdir -p /home/<USER>/builds/foeye/foscan/releases/rawgrab/
    - mkdir -p /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/rawgrab/
    - goreleaser --skip-validate --clean -f .goreleaser_test.yaml
#    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose down"
#    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose pull"
#    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose up -d"
#    - echo "DEVELOPER ENVIRONMENT DEPLOY DONE"
  tags:
    - gitlab-runner-shared

release:
  stage: release
  tags:
    - gitlab-runner-shared
  only:
    refs:
      - tags
  script:
    - goreleaser release --clean --skip-validate


