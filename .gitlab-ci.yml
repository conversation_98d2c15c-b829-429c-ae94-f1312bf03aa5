# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages

stages:          # List of stages for jobs, and their order of execution
  - masscan
  - nmap

before_script:
  - export PATH=/usr/local/node/bin:/usr/local/bin:$PATH

apifox-masscan-test:       # This job runs in the build stage, which runs first.
  stage: masscan
  script:
    - echo "run apifox test"
    - apifox run masscan.apifox-cli.json -r cli,html
  tags: 
    - foscan_integration_test

apifox-nmap-test:       # This job runs in the build stage, which runs first.
  stage: nmap
  script:
    - echo "run apifox test"
    - apifox run nmap.apifox-cli.json -r cli,html
  tags: 
    - foscan_integration_test