stages:
  - unit_test
  - developer_test
  - integration_test
  - release

variables:
  # Disable shallow cloning so that goreleaser can diff between tags to
  # generate a changelog.
  GIT_DEPTH: ''
  # 钉钉markdown换行符 必须\n且前后跟两个空格(shell 转义)
  V_BR: "\ \ \\n\ \ "

before_script:
  - export PATH=/usr/local/go/bin:/usr/local/gopath/bin:/usr/local/bin:$PATH
  - docker login -u "${HARBOR_FOBASE}" -p"${HARBOR_FOBASE_P}" "${HARBOR_LOGIN_ADDR}"
  - echo "machine git.gobies.org login gitlab-ci-token password ${CI_JOB_TOKEN}" > ~/.netrc

after_script:
  - rm -rf ~/.netrc
  - |
    if [ "$CI_JOB_STATUS" == "failed" ]; then
       V_TEXT="**CI任务<font color=\\\"#FF3333\\\">执行失败</font>通知**${V_BR}\
          **任务ID**: **${CI_JOB_ID}**${V_BR}\
          **任务名**: **${CI_JOB_NAME}**${V_BR}\
          **项目**: **${CI_PROJECT_PATH}**${V_BR}\
          **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
          **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
          "
    
       curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI执行失败通知\",\"text\":\"${V_TEXT}\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
    fi

unit_test:
  stage: unit_test
  script:
    - go test ./... -gcflags=all=-l --cover --count=1 -coverprofile=coverage.out
    - go tool cover --func=coverage.out
    - go tool cover --html=coverage.out  -o detail.html
    - |
      if [ -f "coverage.out" ]; then
          coverage=$(go tool cover -func=coverage.out | awk '/^total:/ {print substr($3, 1, length($3)-1)}')
          if (( $(echo "$coverage < $COVERAGE_LIMIT" | bc -l) )); then
              V_TEXT="**<font color=\\\"#FF3333\\\">单元测试覆盖<$COVERAGE_LIMIT%通知</font>**${V_BR}\
                **任务ID**: **${CI_JOB_ID}**${V_BR}\
                **任务名**: **${CI_JOB_NAME}**${V_BR}\
                **项目**: **${CI_PROJECT_PATH}**${V_BR}\
                **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
                **覆盖度**: **<font color=\\\"#FF3333\\\">$coverage%</font>**${V_BR}\
                **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
                "
      
              curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI 单元测试覆盖度不达标通知\",\"text\":\"$V_TEXT\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
          fi
        else
          echo "No coverage report found."
      fi
  coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
  artifacts:
    paths:
      - detail.html
  tags:
    - foscan_dispatcher

developer_test:
  stage: developer_test
  script:
    - goreleaser --skip-validate --clean -f .goreleaser_test.yaml
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose down"
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose pull"
    - ssh -o StrictHostKeyChecking=no $DEVELOPER_HOST "docker-compose up -d"
    - echo "DEVELOPER ENVIRONMENT DEPLOY DONE"
  tags:
    - foscan_dispatcher

release:
  stage: release
  tags:
    - foscan_dispatcher
  only:
    refs:
      - tags
  script:
    - goreleaser release --clean
