{"apifoxCli": "1.2.21", "item": [{"item": [{"id": "6cb47642-cc3e-4646-8258-e69a61179e10", "type": "group", "metaInfo": {"id": "6cb47642-cc3e-4646-8258-e69a61179e10", "type": "group", "scopeType": "start", "scopeEndId": "ae804833-549c-4ef9-80dc-0447075371cf", "name": "root", "onError": "end"}}, {"id": "a43f8610-e4ca-41be-b89c-e18487740b18", "name": "添加任务 nmap(1个tcp 存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:80?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个tcp 存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(true);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074905, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个tcp 存活端口", "id": "2f3d2ced-156d-4868-bbd0-3db9d0117b23", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "ddd1cff7-c615-406e-bad7-19b510939aab", "name": "添加任务 nmap(1个tcp不存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\":\"300\",\n    \"blacklist\":\"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\":false,\n    \"crawler_specific_url\":\"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\":\"\",\n    \"deep_get_mac\":false,\n    \"deep_get_os\":false,\n    \"gateway_mac\":\"false\",\n    \"grab_concurrent\":10,\n    \"hostinfos\":[],\n    \"ip_list\":\"**********36\",\n    \"ip_list_filename\":\"\",\n    \"is_ipv6\":false,\n    \"max_asset_num\":10000, \n    \"ping_scan\":false,\n    \"ports\":\"7,11,13,17,19,21,22,23,25,26,37,43,49,53,67,70,79,81,82,83,84,88,102,104,110,111,113,119,135,138,139,143,175,179,199,264,389,444,445,465,502,503,512,515,523,548,554,564,587,631,636,646,771,789,873,880,902,992,993,995,1025,1026,1027,1080,1099,1177,1194,1200,1201,1234,1241,1260,1311,1344,1433,1471,1505,1521,1723,1863,1883,1911,1962,1991,2000,2001,2049,2080,2082,2083,2086,2087,2121,2181,2222,2323,2332,2375,2376,2379,2401,2404,2424,2455,2480,2501,2628,3000,3128,3260,3288,3299,3306,3307,3310,3333,3388,3389,3390,3460,3541,3542,3689,3690,3749,3780,4000,4022,4040,4063,4064,4369,4443,4444,4567,4712,4730,4786,4840,4848,4880,4911,4949,5000,5001,5006,5007,5009,5050,5084,5222,5269,5357,5400,5432,5555,5560,5577,5672,5678,5900,5901,5938,5984,5985,5986,6001,6068,6379,6488,6664,6665,6666,6667,6668,6669,7000,7001,7071,7077,7288,7474,7547,7548,7634,7777,7779,7911,8000,8001,8008,8009,8010,8020,8025,8030,8040,8060,8069,8080,8081,8086,8087,8089,8090,8098,8099,8112,8125,8126,8139,8161,8200,8291,8333,8334,8377,8378,8443,8500,8545,8554,8649,8686,8800,8834,8880,8888,8889,8983,9000,9001,9003,9010,9042,9051,9080,9090,9092,9093,9100,9151,9191,9200,9333,9418,9443,9527,9530,9595,9653,9700,9711,9944,9981,9999,10000,10001,10162,10243,10333,11001,11211,11300,11310,12345,13579,14000,14147,14265,16010,16030,16992,16993,18001,18081,18245,20000,20547,20880,22105,22222,23023,23424,25000,25105,25565,27015,27017,28017,32400,33338,37777,41795,45554,49151,49152,49153,49154,49155,50000,50050,50070,50100,51106,55553,60010,60030,61613,61616,62078,89,80,6000,443,59110,T:3308:exec,T:3308:iec61850,T:3308:synchrophasor,T:3311:https,T:3311:git,T:3311:ssh,T:3311:ftp,T:3311:mysql,T:3312:https,T:3312:git,T:3312:ssh,T:3312:ftp,T:3312:mysql,T:3313:https,T:3313:git,T:3313:ssh,T:3313:ftp,T:3313:mysql,T:498:exec,T:498:9p,T:59855:synchrophasor,T:59855:android-debug-bridge,T:59855:iec61850,T:45198:hbase_region_server(http),T:45198:hbase_master(http),T:3309:https,T:3309:git,T:3309:ssh,T:3309:ftp,T:3309:mysql,T:8082:http,T:8082:https,T:1245:cassandra,T:1245:iec61850,T:6589:cassandra,T:6589:iec61850,T:5587:cassandra,T:5587:iec61850,U:53,U:67,U:69,U:113,U:123,U:137,U:161,U:162,U:391,U:500,U:520,U:623,U:626,U:705,U:1027,U:1194,U:1434,U:1604,U:1645,U:1701,U:1812,U:1900,U:1967,U:1993,U:2094,U:2123,U:2152,U:2424,U:2425,U:2427,U:3283,U:3333,U:3671,U:3702,U:3784,U:4070,U:4500,U:4800,U:5006,U:5050,U:5060,U:5094,U:5351,U:5353,U:5554,U:5632,U:5683,U:6881,U:6969,U:8888,U:9600,U:10001,U:17185,U:20000,U:28784,U:30310,U:30311,U:30312,U:30313,U:30718,U:32768,U:34962,U:34964,U:44818,U:47808,U:48899,U:80,U:443,U:59110\",\n    \n    \"protocol_update_cycle\":0,\n    \"repeat_times\":0,\n    \"resume_filename\":\"\",\n    \"send_eth\":\"\",\n    \"task_id\": \"{% mock 'string' , '0123456789' , 5 , 10 %}\",\n    \"task_type\":\"common\",\n    \"treck_scan\":false,\n    \"unknown_protocol_in_db\":false\n}\n", "generateMode": "request", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:81?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个tcp不存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(false);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074906, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个tcp不存活端口", "id": "b09088e0-2522-4ee6-89cd-2b2dc20cd228", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "8c53502d-7683-479a-ac7c-32a8e27dd286", "name": "添加任务 nmap(多个tcp存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\":\"100\",\n    \"blacklist\":\"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\":false,\n    \"crawler_specific_url\":\"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\":\"\",\n    \"deep_get_mac\":false,\n    \"deep_get_os\":false,\n    \"gateway_mac\":\"false\",\n    \"grab_concurrent\":10,\n    \"hostinfos\":[],\n    \"ip_list\":\"************\",\n    \"ip_list_filename\":\"\",\n    \"is_ipv6\":false,\n    \"max_asset_num\":10000, \n    \"ping_scan\":false,\n    \"ports\":\"22,80,443\",\n    \n    \"protocol_update_cycle\":0,\n    \"repeat_times\":0,\n    \"resume_filename\":\"\",\n    \"send_eth\":\"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\":\"common\",\n    \"treck_scan\":false,\n    \"unknown_protocol_in_db\":false\n}\n", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "const queryRequest = {", "  url: elastic + \"/fofaee_service/service/_search\",", "  method: \"POST\",", "  header: {", "    \"Content-Type\": \"application/json\"", "  },", "  body: {", "    mode: 'raw',", "    raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"ip\": \"************\" } }, { \"terms\": { \"port\": [22, 80, 443] } }] } } }),", "  }", "}", "", "let timer = setInterval(() => {", "  query();", "}, 5000);", "", "function clearTimer() {", "  clearInterval(timer)", "  timer = null", "}", "", "function assert() {", "  pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json())", "    pm.test(\"多个tcp存活端口\", function () {", "      pm.response.to.not.be.error;", "      pm.expect(response.json().hits.total).to.be.above(2);", "    });", "  });", "}", "", "function query() {", "  pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "    console.log(response.json())", "    if (response.json().data.state == \"5\") {", "      clearTimer()", "      assert()", "    }", "", "    if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "      clearTimer()", "    }", "", "    console.log(\"等待任务结束：\", response.json())", "  });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074907, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个tcp存活端口", "id": "785ea3b9-0394-4e3b-be6d-b671e5bfb549", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "84921574-873c-4df9-9678-efcc2b6fd63d", "name": "添加任务 nmap(多个tcp端口全部不存活)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"21,81,444\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "  url: elastic + \"/fofaee_service/service/_search\",", "  method: \"POST\",", "  header: {", "    \"Content-Type\": \"application/json\"", "  },", "  body: {", "    mode: 'raw',", "    raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"ip\": \"************\" } }, { \"terms\": { \"port\": [21, 81, 444] } }] } } }),", "  }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "  query();", "}, 5000);", "", "function clearTimer() {", "  clearInterval(timer)", "  timer = null", "}", "", "function assert() {", "  pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json())", "    pm.test(\"多个tcp端口全部不存活\", function () {", "      pm.response.to.not.be.error;", "      pm.expect(response.json().hits.total).to.be.equal(0);", "    });", "  });", "}", "", "function query() {", "  pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "    console.log(response.json())", "    if (response.json().data.state == \"5\") {", "      clearTimer()", "      assert()", "    }", "", "    if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "      clearTimer()", "    }", "", "    console.log(\"等待任务结束：\", response.json())", "  });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074908, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个tcp端口全部不存活", "id": "32fbf314-7aa0-4cb2-8bc8-71899b563807", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "78f36ccb-8cd3-4304-88c4-af266f21a600", "name": "添加任务 nmap(1个udp 存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:111\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/**********19:111?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个udp存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(true);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074909, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个udp 存活端口", "id": "7804c8a5-72fc-4e9d-a9d3-5329dfb83f23", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "1ee6064c-9c28-4fd3-9ad8-59a3d335476e", "name": "添加任务 nmap(1个udp 不存活端口 )", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:112\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/**********19:112?pretty\", function (err, response) {", "    console.log(response.json())", "    pm.test(\"1个udp 不存活端口\", function () {", "        pm.response.to.not.be.error;", "        pm.expect(response.json().found).to.equal(false);", "    });", "});", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074910, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个udp 不存活端口 ", "id": "6a3837c2-6203-4310-8acf-0885f0c701cf", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "7b644d46-04fc-4633-938e-109a8b22b04e", "name": "添加任务 nmap(多个udp存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:111,U:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"base_protocol\": \"udp\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"多个udp存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074911, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个udp存活端口", "id": "b4eed903-7e7e-4485-896d-7b246018f0b5", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "6303ffda-e85d-4300-97f7-6b8b6722a7fa", "name": "添加任务 nmap(多个udp端口全部不存活)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:5060,U:1194,U:80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"base_protocol\": \"udp\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"多个udp端口全部不存活\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074912, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个udp端口全部不存活", "id": "2f5332c9-67a7-495b-8dcc-ced84938f809", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "f45f397a-e12c-45da-b94c-87ad0850419a", "name": "添加任务 nmap(tcp、udp端口混扫，至少各存活一个)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,443,22,U:111,U:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"aggs\": { \"port_terms\": { \"terms\": { \"field\": \"port\", \"size\": 6 } } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"tcp、udp端口混扫，至少各存活一个\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().aggregations.port_terms.buckets.length).to.be.above(3);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074963, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "tcp、udp端口混扫，至少各存活一个", "id": "d850e144-1f67-476a-8c26-2c860b47e0d4", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "8db5490d-5167-4fb3-aa08-b4d8665fe784", "name": "添加任务 nmap(错误端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"http,22,S:111,U:mysql:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"错误端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074964, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "错误端口", "id": "fa29cb3e-aff4-49d8-9696-24da4c1d1e7e", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "6b624738-323b-4f23-9fd9-5a8a320a88e3", "name": "添加任务 nmap(发包速率小于0)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"-100000000\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 313640055, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074965, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "发包速率小于0", "id": "e81a3c3a-db0a-4592-bca7-9e8762d2699f", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "842ac52c-9069-4d4f-a386-305d52919fcc", "name": "添加任务 nmap(发包速率超出整数范围)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"9999999999999999999999999999999\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********19,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 313640055, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074966, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "发包速率超出整数范围", "id": "7dc4acf7-7f61-4dfb-b443-2cef0a156395", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "40d61147-9571-4163-8fb1-70d217f951b7", "name": "添加任务 nmap(扫描目标全是黑名单ip)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074967, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "扫描目标全是黑名单ip", "id": "f6003ae6-4ab1-4e2f-ba65-3da02952107d", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "3aa5b3ec-47b3-4ef9-afea-32de4d09b051", "name": "添加任务 nmap(扫描目标不全是黑名单ip)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标不全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074968, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "扫描目标不全是黑名单ip", "id": "19e21810-795a-4558-a0c5-6ccb54703dfc", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "b949e790-b46e-40bd-b002-1bd404905b18", "name": "添加任务 nmap(黑名单ip不是有效的ipv4)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"111***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 313640055, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074969, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "黑名单ip不是有效的ipv4", "id": "dce705b7-03b5-4209-9138-d19146dab775", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "c957810f-b696-4dd1-9c5e-183a493ee655", "name": "添加任务 nmap(ipv4扫描黑名单ip是ipv6)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"2001:4b98:e01::38,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"ipv4扫描黑名单ip是ipv6\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074970, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "ipv4扫描黑名单ip是ipv6", "id": "c499d804-0b8c-46f8-84b5-8fba7dfd831a", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "8facc5d8-e03e-4f8e-8c85-b08eeba7be24", "name": "添加任务 nmap(黑名单ip不是有效的ipv6)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"2001aaaa:4b98:e01::38,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": true,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 313640055, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074971, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "黑名单ip不是有效的ipv6", "id": "1c190b4b-86c5-4a68-ad3b-339deba888b4", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "f5784dae-07a6-42e1-be01-f6ffeab7da4f", "name": "添加任务 nmap(ipv6扫描，目标是ipv4)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": true,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074972, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "ipv6扫描，目标是ipv4", "id": "b395fb04-6ab5-46b6-871b-79105bacf247", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "0551ae53-8a94-47cb-a228-032b655e9ffb", "name": "添加任务 nmap(全端口扫描)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"0-65535,U:53,67,69,80,88,113,123,137,138,161,162,391,427,443,500,520,623,626,705,853,1027,1194,1434,1604,1645,1701,1812,1900,1967,1993,2083,2094,2123,2152,2424,2425,2427,3283,3333,3391,3478,3671,3702,3784,4050,4070,4500,4800,5000,5001,5002,5004,5005,5006,5007,5008,5050,5060,5061,5093,5094,5095,5351,5353,5554,5632,5673,5683,6002,6003,6006,6060,6881,6969,7000,7001,7003,7005,8002,8888,9000,9100,9600,10001,17185,20000,28784,30310,30311,30312,30313,30718,32768,34962,34963,34964,44818,47808,48899,59110\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标不全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074973, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全端口扫描", "id": "a677a06a-de88-40e1-9da2-a76affcbd6bf", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "ce46cd18-d0d8-45c1-ad69-29f476c2d693", "name": "添加任务 nmap(全端口扫描端口不正常 -1-65537)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"-1-65537,U:53,67,69,80,88,113,123,137,138,161,162,391,427,443,500,520,623,626,705,853,1027,1194,1434,1604,1645,1701,1812,1900,1967,1993,2083,2094,2123,2152,2424,2425,2427,3283,3333,3391,3478,3671,3702,3784,4050,4070,4500,4800,5000,5001,5002,5004,5005,5006,5007,5008,5050,5060,5061,5093,5094,5095,5351,5353,5554,5632,5673,5683,6002,6003,6006,6060,6881,6969,7000,7001,7003,7005,8002,8888,9000,9100,9600,10001,17185,20000,28784,30310,30311,30312,30313,30718,32768,34962,34963,34964,44818,47808,48899,59110\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"全端口扫描，输入端口不正常 -1-65537\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074974, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全端口扫描端口不正常 -1-65537", "id": "a40b87dc-243d-4b57-b74d-fe30a8d5c51f", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "66dac135-2df0-494a-a45a-fe1fc17fe62e", "name": "添加任务 nmap(全协议识别，开关关闭，扫描非默认端口协议)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\" } }] } } }),", "    }", "}", "", "", "// 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"全协议识别，开关关闭，扫描非默认端口协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074975, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全协议识别，开关关闭，扫描非默认端口协议", "id": "84d74f37-e8c0-46da-bd36-0d1642b78183", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "e08dedbe-bdc6-4833-bf03-ccaeb2c6f148", "name": "添加任务 nmap(全协议识别，开关打开，扫描非默认端口协议2179 etcd)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"terms\": { \"protocol\": [\"etcd\", \"http\"] } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"全协议识别，开关关闭，扫描非默认端口协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074976, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全协议识别，开关打开，扫描非默认端口协议2179 etcd", "id": "a2da8e05-5710-4d1b-94db-c0a217dcb5a2", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "2676d840-26fe-45e9-80ad-8674a8fe552a", "name": "添加任务 nmap(指定端口扫描2179 etcd)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"T:2179:etcd\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\", } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"指定端口扫描2179 etcd\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074977, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "指定端口扫描2179 etcd", "id": "5b0b0461-8870-4ad5-ba2d-1da7c876bd15", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "10c4ed5b-8436-4bd4-becd-edc03e2c0735", "name": "添加任务 nmap(指定端口扫描2179 etcd  不指定协议)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.delay", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        setTimeout(()=>{}, 8000);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"指定端口扫描2179 etcd  不指定协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110074978, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "指定端口扫描2179 etcd  不指定协议", "id": "6ae9ea79-6a73-4c5b-92b3-5709d3151ce1", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "0f0655e1-0373-4091-a051-2785212a11c4", "name": "添加任务 nmap(域名扫描，不指定ip域名关系)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"*************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_subdomain/subdomain/*************\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"域名扫描，不指定ip域名关系\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.be.equal(true);", "            pm.expect(response.json()._source.status_code).to.be.equal(403);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110075029, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "域名扫描，不指定ip域名关系", "id": "3f86b255-fc08-4ff4-9940-a65ba548c57f", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "acbaf024-56f9-4fcf-8c7a-6705e8ca2d3a", "name": "添加任务 nmap(域名扫描，指定ip域名关系)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_domain_relations\": {\n        \"*************\": [\n            \"bbs.1cool.vip\"\n        ]\n    },\n    \"ip_list\": \"*************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"common\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_subdomain/subdomain/bbs.1cool.vip\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"域名扫描，指定ip域名关系\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.be.equal(true);", "            pm.expect(response.json()._source.status_code).to.be.equal(200);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 313640054, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 3260764, "ordering": 1, "createdAt": "2023-09-15T06:52:30.000Z", "updatedAt": "2023-09-15T06:52:30.000Z", "deletedAt": null, "apiDetailId": 110986947, "responseExamples": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 110986947, "httpApiCaseId": 110075030, "httpApiName": "添加任务 nmap", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "域名扫描，指定ip域名关系", "id": "c8dece89-ff4c-4a59-9e82-7e5e591e3770", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "ae804833-549c-4ef9-80dc-0447075371cf", "type": "group", "metaInfo": {"id": "ae804833-549c-4ef9-80dc-0447075371cf", "type": "group", "scopeType": "end", "scopeStartId": "6cb47642-cc3e-4646-8258-e69a61179e10"}}], "name": "nmap"}], "info": {"name": "nmap"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 14091549, "name": "测试环境", "baseUrl": "http://************:61234", "baseUrls": {"default": "http://************:61234"}, "variable": {"id": "2b8309c3-727a-40d6-942f-2a4b335700c0", "name": "测试环境", "values": [{"type": "any", "value": "", "key": "elastic", "isBindInitial": true, "initialValue": "http://************:9200"}, {"type": "any", "value": "", "key": "task_state", "isBindInitial": true, "initialValue": "http://************:6789"}]}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "eeefe1b2-1b7b-498b-962c-db0e8cb373c6", "values": []}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": true}