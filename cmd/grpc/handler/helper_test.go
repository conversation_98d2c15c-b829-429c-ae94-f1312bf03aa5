package handler

import "testing"

func TestHostWrap(t *testing.T) {
	type args struct {
		host string
		port uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				host: "a.com",
				port: 80,
			},
			want: "a.com",
		},
		{
			args: args{
				host: "a.com",
				port: 443,
			},
			want: "a.com",
		},
		{
			args: args{
				host: "a.com",
				port: 8000,
			},
			want: "a.com:8000",
		},
		{
			args: args{
				host: "https://a.com",
				port: 443,
			},
			want: "a.com",
		},
		{
			args: args{
				host: "http://a.com",
				port: 80,
			},
			want: "a.com",
		},
		{
			args: args{
				host: "https://www.abchina.com:9091/cn/branch/hn/",
				port: 9091,
			},
			want: "www.abchina.com:9091",
		},
		{
			args: args{
				host: "https://************:8000/cn/branch/hn/",
				port: 8000,
			},
			want: "************:8000",
		},
		{
			args: args{
				host: "www.abchina.com",
				port: 9901,
			},
			want: "www.abchina.com:9901",
		},
		{
			args: args{
				host: "https://[2409:8c28:202:b::238]:8080/",
				port: 8080,
			},
			want: "[2409:8c28:202:b::238]:8080",
		},
		{
			args: args{
				host: "https://www.trayectos.zglobalhost.com:2083/",
				port: 2083,
			},
			want: "www.trayectos.zglobalhost.com:2083",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := HostWrap(tt.args.host, tt.args.port); got != tt.want {
				t.Errorf("HostWrap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestURLTargetWrap(t *testing.T) {
	type args struct {
		ip       string
		protocol string
		port     uint32
		isIPv6   bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ipv4 http 80",
			args: args{
				ip:       "***********",
				protocol: "http",
				port:     80,
				isIPv6:   false,
			},
			want: "http://***********",
		},
		{
			name: "ipv4 http not 80",
			args: args{
				ip:       "***********",
				protocol: "http",
				port:     8000,
				isIPv6:   false,
			},
			want: "http://***********:8000",
		},
		{
			name: "ipv4 https 443",
			args: args{
				ip:       "***********",
				protocol: "https",
				port:     443,
				isIPv6:   false,
			},
			want: "https://***********",
		},
		{
			name: "ipv4 https not 443",
			args: args{
				ip:       "***********",
				protocol: "https",
				port:     8000,
				isIPv6:   false,
			},
			want: "https://***********:8000",
		},
		{
			name: "ipv6 https 443",
			args: args{
				ip:       "***********",
				protocol: "https",
				port:     443,
				isIPv6:   true,
			},
			want: "https://[***********]",
		},
		{
			name: "ipv6 https not 443",
			args: args{
				ip:       "***********",
				protocol: "https",
				port:     8000,
				isIPv6:   true,
			},
			want: "https://[***********]:8000",
		},
		{
			name: "ipv6 http not 80",
			args: args{
				ip:       "***********",
				protocol: "http",
				port:     8000,
				isIPv6:   true,
			},
			want: "http://[***********]:8000",
		},
		{
			name: "ipv6 http 80",
			args: args{
				ip:       "***********",
				protocol: "http",
				port:     80,
				isIPv6:   true,
			},
			want: "http://[***********]",
		},
		{
			name: "contain schema",
			args: args{
				ip:       "https://[***********]:8000",
				protocol: "https",
				port:     8000,
				isIPv6:   true,
			},
			want: "https://[***********]:8000",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := URLTargetWrap(tt.args.ip, tt.args.protocol, tt.args.port, tt.args.isIPv6); got != tt.want {
				t.Errorf("URLTargetWrap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseDomain(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		want1   string
		wantErr bool
	}{
		{
			name: "fofa.info",
			args: args{
				s: "fofa.info",
			},
			want:    "fofa.info",
			want1:   "",
			wantErr: false,
		},
		{
			name: "download.fofa.info",
			args: args{
				s: "download.fofa.info",
			},
			want:    "fofa.info",
			want1:   "download",
			wantErr: false,
		},
		{
			name: "https://www.abchina.com/cn/branch/hn/",
			args: args{
				s: "https://www.abchina.com/cn/branch/hn/",
			},
			want:    "abchina.com",
			want1:   "www",
			wantErr: false,
		},
		{
			name: "mms0.baidu.com.a.bdydns.com",
			args: args{
				s: "mms0.baidu.com.a.bdydns.com",
			},
			want:    "bdydns.com",
			want1:   "mms0.baidu.com.a",
			wantErr: false,
		},
		{
			name: "a.cc.roxin999.com",
			args: args{
				s: "a.cc.roxin999.com",
			},
			want:    "roxin999.com",
			want1:   "a.cc",
			wantErr: false,
		},
		{
			name: "cool.商店",
			args: args{
				s: "cool.商店",
			},
			want:    "cool.商店",
			want1:   "",
			wantErr: false,
		},
		{
			name: "cool.商标",
			args: args{
				s: "cool.商标",
			},
			want:    "cool.商标",
			want1:   "",
			wantErr: false,
		},
		{
			name: "t葫芦.com",
			args: args{
				s: "葫芦.t葫芦.com",
			},
			want:    "t葫芦.com",
			want1:   "葫芦",
			wantErr: false,
		},
		{
			name: "error",
			args: args{
				s: "http:123",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "error",
			args: args{
				s: ".baidu.com",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "error",
			args: args{
				s: "-baidu.com",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "error",
			args: args{
				s: "bai!du.com",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "temp.sync-class.com",
			args: args{
				s: "temp.sync-class.com",
			},
			want:    "sync-class.com",
			want1:   "temp",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := ParseDomain(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseDomain() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ParseDomain() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("ParseDomain() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
