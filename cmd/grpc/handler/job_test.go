package handler

import (
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"reflect"
	"testing"
)

type JobSuit struct {
	suite.Suite
	job job
}

func TestJobSuit(t *testing.T) {
	s := &JobSuit{}
	s.job = job{}
	suite.Run(t, s)
}

func TestNewJob(t *testing.T) {
	type args struct {
		msg *rpcx.CheckURLEvent
	}
	tests := []struct {
		name string
		args args
		want Job
	}{
		{
			args: args{
				msg: &rpcx.CheckURLEvent{},
			},
			want: &job{
				msg: &rpcx.CheckURLEvent{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewJob(tt.args.msg); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("NewJob() = %v, want %v", got, tt.want)
			}
		})
	}
}

func (s *JobSuit) Test_job_Run() {
	Convey("Test_job_Run", s.T(), func() {
		s.job.msg = &rpcx.CheckURLEvent{
			Ip:           "***********",
			Protocol:     "http",
			BaseProtocol: "tcp",
			Port:         80,
			JobId:        "",
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "",
			},
		}
		Convey("ipv4 http pass", func() {
			e, err := s.job.Run()
			So(err, ShouldBeNil)
			So(e.Url, ShouldEqual, "http://***********")
		})

		Convey("ipv6 https pass", func() {
			s.job.msg.TaskInfo.IsIpv6 = true
			s.job.msg.Ip = "2001:DB8::8:800:200C:417A"
			s.job.msg.Protocol = "https"
			s.job.msg.Port = 445
			e, err := s.job.Run()
			So(err, ShouldBeNil)
			So(e.Url, ShouldEqual, "https://[2001:DB8::8:800:200C:417A]:445")
		})

		Convey("https 443 pass", func() {
			s.job.msg.Protocol = "https"
			s.job.msg.Port = 443
			e, err := s.job.Run()
			So(err, ShouldBeNil)
			So(e.Url, ShouldEqual, "https://***********")
		})
	})
}
