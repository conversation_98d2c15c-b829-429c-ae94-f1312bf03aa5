package handler

import (
	"errors"
	"fmt"
	"github.com/imroc/domain"
	"go-micro.dev/v4/logger"
	"net/url"
	"strings"
)

// HostWrap  come from the check url ip_domain_relations
// it only api input param host info or ip_domain_relations.
// but when api input param ip_domain_relations have value which of url address is effective.
func HostWrap(host string, port uint32) string {
	if strings.Contains(host, "http://") ||
		strings.Contains(host, "https://") {
		parsedURL, err := url.Parse(host)
		if err != nil {
			logger.Warn("URL Parse failed：", host)
		} else {
			return parsedURL.Host
		}
	}

	host = strings.TrimPrefix(host, "http://")
	host = strings.TrimPrefix(host, "https://")

	if port == 80 || port == 443 {
		return host
	}

	return fmt.Sprintf("%s:%d", host, port)
}

func URLTargetWrap(ip, protocol string, port uint32, isIPv6 bool) string {
	if strings.Contains(ip, "http://") ||
		strings.Contains(ip, "https://") {
		return ip
	}

	if isIPv6 {
		if protocol == "http" && port == 80 {
			return fmt.Sprintf("http://[%s]", ip)
		}

		if protocol == "http" && port != 80 {
			return fmt.Sprintf("http://[%s]:%d", ip, port)
		}

		if protocol == "https" && port == 443 {
			return fmt.Sprintf("https://[%s]", ip)
		}

		if protocol == "https" && port != 443 {
			return fmt.Sprintf("https://[%s]:%d", ip, port)
		}

		return ""
	}

	if protocol == "http" && port == 80 {
		return fmt.Sprintf("http://%s", ip)
	}

	if protocol == "http" && port != 80 {
		return fmt.Sprintf("http://%s:%d", ip, port)
	}

	if protocol == "https" && port == 443 {
		return fmt.Sprintf("https://%s", ip)
	}

	if protocol == "https" && port != 443 {
		return fmt.Sprintf("https://%s:%d", ip, port)
	}

	return ""
}

func ParseDomain(s string) (string, string, error) {
	u, err := domain.Parse(s)

	if err != nil {
		return "", "", err
	}

	d := u.Domain
	if u.Domain != "" && u.PublicSuffix != "" {
		d = u.Domain + "." + u.PublicSuffix
	}

	if !isValidDomain(d) {
		return "", "", errors.New(fmt.Sprintf("不是有效的跟域：%s", d))
	}

	return d, u.Subdomain, nil
}

func isValidDomain(domain string) bool {
	// 域名不能为空
	if domain == "" {
		return false
	}
	// 第一个字符不能是特殊符号
	if strings.ContainsAny(string(domain[0]), `!@#$%^&*()+-=[]\{}|;:'",<>/?`) {
		return false
	}

	// 域名不能以点号开头或结尾
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	// 分割域名为标签
	labels := strings.Split(domain, ".")

	// 至少需要一个标签和一个顶级域名
	if len(labels) < 2 {
		return false
	}

	// 检查每个标签的有效性，不包含连字符（减号）
	for _, label := range labels {
		// 标签不能为空
		if label == "" {
			return false
		}

		// 标签不能包含特殊字符
		if strings.ContainsAny(label, `!@#$%^&*()+=[]\{}|;:'",<>/?`) {
			return false
		}
	}

	return true
}
