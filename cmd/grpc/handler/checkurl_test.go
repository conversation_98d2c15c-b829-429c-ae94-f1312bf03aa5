package handler

import (
	"context"
	"errors"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"reflect"
	"sync"
	"testing"
)

type CheckURLSuit struct {
	suite.Suite
	checkURL CheckURL
}

func TestCheckURLSuit(t *testing.T) {
	s := &CheckURLSuit{}
	s.checkURL = CheckURL{
		srv:        micro.NewService(),
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
	suite.Run(t, s)
}

func TestCheckURL_Decrement(t *testing.T) {
	type fields struct {
		ipDomainRelations map[string]map[string][]string
		jobs              map[string]int64
		remainJobs        map[string]int64
		lock              sync.Mutex
		srv               micro.Service
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
			fields: fields{
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CheckURL{
				ipDomainRelations: tt.fields.ipDomainRelations,
				jobs:              tt.fields.jobs,
				remainJobs:        tt.fields.remainJobs,
				lock:              tt.fields.lock,
				srv:               tt.fields.srv,
			}
			c.Decrement(tt.args.taskId)
		})
	}
}

func TestCheckURL_FinishedNotify(t *testing.T) {
	type fields struct {
		ipDomainRelations map[string]map[string][]string
		jobs              map[string]int64
		remainJobs        map[string]int64
		lock              sync.Mutex
		srv               micro.Service
	}
	type args struct {
		ctx context.Context
		req *pb.FinishedNotifyRequest
		rsp *pb.FinishedNotifyResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
			args: args{
				ctx: context.Background(),
				req: &pb.FinishedNotifyRequest{},
				rsp: &pb.FinishedNotifyResponse{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CheckURL{
				ipDomainRelations: tt.fields.ipDomainRelations,
				jobs:              tt.fields.jobs,
				remainJobs:        tt.fields.remainJobs,
				lock:              tt.fields.lock,
				srv:               tt.fields.srv,
			}
			if err := c.FinishedNotify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("FinishedNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func (s *CheckURLSuit) TestCheckURL_HandleEvent() {
	Convey("TestCheckURL_HandleEvent", s.T(), func() {
		Convey("param error", func() {
			r := s.checkURL.HandleEvent(context.Background(), &pb.CheckURLEvent{
				TaskInfo: &pb.TaskInfo{},
			})
			So(r, ShouldResemble, errors.New("参数错误"))
		})

		Convey("j.Run error", func() {
			j := NewJob(&pb.CheckURLEvent{})
			defer ApplyFuncReturn(NewJob, j).Reset()
			defer ApplyMethodReturn(j, "Run", nil, errors.New("")).Reset()
			r := s.checkURL.HandleEvent(context.Background(), &pb.CheckURLEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId: "1",
				},
				Ip:           "1",
				Protocol:     "1",
				BaseProtocol: "1",
				Port:         1,
			})
			So(r, ShouldBeError)
		})

		Convey("ipDomainRelations empty", func() {
			m := make(map[string]map[string][]string)
			mm := make(map[string][]string)
			mm["***********"] = []string{
				"a.com",
			}
			m["task_id"] = mm
			j := NewJob(&pb.CheckURLEvent{})
			defer ApplyFuncReturn(NewJob, j).Reset()
			defer ApplyGlobalVar(&s.checkURL.ipDomainRelations, m).Reset()
			defer ApplyMethodReturn(j, "Run", &pb.CrawlerEvent{}, nil).Reset()
			defer ApplyMethodReturn(s.checkURL.srv.Client(), "Publish", nil).Reset()
			r := s.checkURL.HandleEvent(context.Background(), &pb.CheckURLEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId: "task_id",
				},
				JobId:        "1",
				Ip:           "***********",
				Protocol:     "1",
				BaseProtocol: "1",
				Port:         1,
			})
			So(r, ShouldBeNil)
		})

		Convey("has ipDomainRelations", func() {
			m := make(map[string]map[string][]string)
			mm := make(map[string][]string)
			mm["***********"] = []string{
				"a.com",
			}
			m["task_id"] = mm
			j := NewJob(&pb.CheckURLEvent{})
			defer ApplyFuncReturn(NewJob, j).Reset()
			defer ApplyGlobalVar(&s.checkURL.ipDomainRelations, m).Reset()
			defer ApplyMethodReturn(j, "Run", &pb.CrawlerEvent{}, nil).Reset()
			defer ApplyMethodReturn(s.checkURL.srv.Client(), "Publish", nil).Reset()
			r := s.checkURL.HandleEvent(context.Background(), &pb.CheckURLEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId: "task_id",
				},
				JobId:        "1",
				Ip:           "***********",
				Protocol:     "1",
				BaseProtocol: "1",
				Port:         1,
			})
			So(r, ShouldBeNil)
		})
	})
}

func TestCheckURL_Increment(t *testing.T) {
	type fields struct {
		ipDomainRelations map[string]map[string][]string
		jobs              map[string]int64
		remainJobs        map[string]int64
		lock              sync.Mutex
		srv               micro.Service
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
			fields: fields{
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CheckURL{
				ipDomainRelations: tt.fields.ipDomainRelations,
				jobs:              tt.fields.jobs,
				remainJobs:        tt.fields.remainJobs,
				lock:              tt.fields.lock,
				srv:               tt.fields.srv,
			}
			c.Increment(tt.args.taskId)
		})
	}
}

func TestCheckURL_SaveIPDomainRelation(t *testing.T) {
	type fields struct {
		ipDomainRelations map[string]map[string][]string
		jobs              map[string]int64
		remainJobs        map[string]int64
		lock              sync.Mutex
		srv               micro.Service
	}
	type args struct {
		ctx context.Context
		req *pb.SaveIPDomainRelationRequest
		rsp *pb.SaveIPDomainRelationResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &pb.SaveIPDomainRelationRequest{},
				rsp: &pb.SaveIPDomainRelationResponse{},
			},
			fields: fields{
				srv:               micro.NewService(),
				jobs:              make(map[string]int64),
				remainJobs:        make(map[string]int64),
				ipDomainRelations: make(map[string]map[string][]string),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CheckURL{
				ipDomainRelations: tt.fields.ipDomainRelations,
				jobs:              tt.fields.jobs,
				remainJobs:        tt.fields.remainJobs,
				lock:              tt.fields.lock,
				srv:               tt.fields.srv,
			}
			if err := c.SaveIPDomainRelation(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("SaveIPDomainRelation() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCheckURL_StateQuery(t *testing.T) {
	type fields struct {
		ipDomainRelations map[string]map[string][]string
		jobs              map[string]int64
		remainJobs        map[string]int64
		lock              sync.Mutex
		srv               micro.Service
	}
	type args struct {
		ctx context.Context
		req *pb.StateQueryRequest
		rsp *pb.StateQueryResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &pb.StateQueryRequest{},
				rsp: &pb.StateQueryResponse{},
			},
			fields: fields{
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CheckURL{
				ipDomainRelations: tt.fields.ipDomainRelations,
				jobs:              tt.fields.jobs,
				remainJobs:        tt.fields.remainJobs,
				lock:              tt.fields.lock,
				srv:               tt.fields.srv,
			}
			if err := c.StateQuery(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("StateQuery() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewCheckURL(t *testing.T) {
	type args struct {
		srv micro.Service
	}
	tests := []struct {
		name string
		args args
		want CheckURL
	}{
		{
			args: args{
				srv: micro.NewService(),
			},
			want: CheckURL{
				srv:               micro.NewService(),
				ipDomainRelations: make(map[string]map[string][]string),
				jobs:              make(map[string]int64),
				remainJobs:        make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCheckURL(tt.args.srv); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCheckURL() = %v, want %v", got, tt.want)
			}
		})
	}
}
