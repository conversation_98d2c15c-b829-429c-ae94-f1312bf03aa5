package handler

import (
	"checkurl/internal/ip"
	"errors"
	"fmt"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
)

type job struct {
	msg *rpcx.CheckURLEvent
}

func (j *job) Run() (*rpcx.CrawlerEvent, error) {
	if !ip.Decimal(j.msg.Ip) {
		return nil, errors.New(fmt.Sprintf("illegal IP: %s", j.msg.Ip))
	}

	return &rpcx.CrawlerEvent{
		TaskInfo:     j.msg.TaskInfo,
		JobId:        j.msg.JobId,
		Url:          URLTargetWrap(j.msg.Ip, j.msg.Protocol, j.msg.Port, j.msg.TaskInfo.IsIpv6),
		Ip:           j.msg.Ip,
		Port:         j.msg.Port,
		Protocol:     j.msg.Protocol,
		BaseProtocol: j.msg.BaseProtocol,
	}, nil
}

type Job interface {
	Run() (*rpcx.CrawlerEvent, error)
}

func NewJob(msg *rpcx.CheckURLEvent) Job {
	return &job{
		msg: msg,
	}
}
