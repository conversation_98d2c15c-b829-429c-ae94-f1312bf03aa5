package handler

import (
	"context"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"sync"
)

type CheckURL struct {
	ipDomainRelations map[string]map[string][]string
	jobs              map[string]int64
	remainJobs        map[string]int64
	lock              sync.Mutex
	srv               micro.Service
}

func NewCheckURL(srv micro.Service) CheckURL {
	return CheckURL{
		srv:               srv,
		ipDomainRelations: make(map[string]map[string][]string),
		jobs:              make(map[string]int64),
		remainJobs:        make(map[string]int64),
	}
}

func (c *CheckURL) Increment(taskId string) {
	c.lock.Lock()
	defer c.lock.Unlock()

	if count, ok := c.jobs[taskId]; ok {
		c.jobs[taskId] = count + 1
	} else {
		c.jobs[taskId] = int64(1)
	}

	if count, ok := c.remainJobs[taskId]; ok {
		c.remainJobs[taskId] = count + 1
	} else {
		c.remainJobs[taskId] = int64(1)
	}

	logger.Infof("the CheckURL jobs total:%d remain:%d", c.jobs[taskId], c.remainJobs[taskId])
}

func (c *CheckURL) Decrement(taskId string) {
	c.lock.Lock()
	defer c.lock.Unlock()

	if count, ok := c.remainJobs[taskId]; ok {
		remain := count - 1
		c.remainJobs[taskId] = remain
		logger.Info("the CheckURL remain jobs:", remain)
	}
}

func (c *CheckURL) HandleEvent(ctx context.Context, event *pb.CheckURLEvent) error {
	logger.Infof("Received CheckURL.HandleEvent request: %v", event)

	if event.TaskInfo.TaskId == "" ||
		event.JobId == "" ||
		event.Ip == "" ||
		event.Protocol == "" ||
		event.BaseProtocol == "" {
		logger.Errorf("参数错误 event: %+v", event)
		return errors.New("参数错误")
	}

	c.Increment(event.TaskInfo.TaskId)

	defer func() {
		c.Decrement(event.TaskInfo.TaskId)
	}()

	j := NewJob(event)
	crawlerEvent, err := j.Run()
	if err != nil {
		logger.Errorf("CheckURL.HandleEvent error: %v event:%v", err, event)
		return err
	}

	err = c.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceCrawler, crawlerEvent))

	if err != nil {
		logger.Errorf("CheckURL.Publish without host error: %v event: %v", err, event)
	} else {
		logger.Infof("CheckURL send crawler without host success task_id: %s IP: %s", event.TaskInfo.TaskId, event.Ip)
	}

	hosts := c.ipDomainRelations[event.TaskInfo.TaskId][event.Ip]

	for _, h := range hosts {
		domain, subdomain, err := ParseDomain(h)
		if err != nil {
			logger.Warn("invalid domain: ", h)
			continue
		}
		crawlerEvent.Host = HostWrap(h, event.Port)
		crawlerEvent.Domain = domain
		crawlerEvent.Subdomain = subdomain
		crawlerEvent.Url = URLTargetWrap(h, event.Protocol, event.Port, event.TaskInfo.IsIpv6)
		err = c.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceCrawler, crawlerEvent))

		if err != nil {
			logger.Errorf("CheckURL.Publish with host error: %v event: %v", err, event)
			continue
		}

		logger.Infof("CheckURL send crawler with host  success task_id: %s IP: %s", event.TaskInfo.TaskId, event.Ip)
	}

	return nil
}

func (c *CheckURL) StateQuery(ctx context.Context, req *pb.StateQueryRequest, rsp *pb.StateQueryResponse) error {
	logger.Infof("Received CheckURL.StateQuery request: %v", req)

	if count, ok := c.jobs[req.TaskId]; ok {
		rsp.Total = count
	}

	if remain, ok := c.remainJobs[req.TaskId]; ok {
		rsp.Remain = remain
	}

	return nil
}

func (c *CheckURL) FinishedNotify(ctx context.Context, req *pb.FinishedNotifyRequest, rsp *pb.FinishedNotifyResponse) error {
	logger.Infof("Received CheckURL.FinishedNotify request: %v", req)

	c.lock.Lock()
	defer c.lock.Unlock()

	delete(c.jobs, req.TaskId)
	delete(c.remainJobs, req.TaskId)
	delete(c.ipDomainRelations, req.TaskId)

	return nil
}

func (c *CheckURL) SaveIPDomainRelation(ctx context.Context, req *pb.SaveIPDomainRelationRequest, rsp *pb.SaveIPDomainRelationResponse) error {
	logger.Infof("Received CheckURL.SaveIPDomainRelation request: %v", req)

	c.lock.Lock()
	defer c.lock.Unlock()

	r := make(map[string][]string)

	for k, v := range req.IpDomainRelations {
		r[k] = v.Domains
	}

	c.ipDomainRelations[req.TaskId] = r

	return nil
}
