package main

import (
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"

	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	_ "google.golang.org/protobuf/types/known/structpb"
	"rawgrab/internal/handler"
	"rawgrab/internal/model"
)

var (
	Version string
	BuildAt string
)

// 1、加载配置文件
// 2、配置服务器ip
// 3、配置证书白名单
// 4、配置其他信息
func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	conf := ReadConfig()

	// Create service
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	if len(conf.NodeID) > 0 {
		_ = srv.Server().Init(server.Id(conf.NodeID))
	}

	srv.Init(
		micro.Name(constant.ServiceRawGrab),
		micro.Version(Version),
	)

	handle := handler.NewRawGrab(conf, srv)

	// Register handler
	if err := pb.RegisterRawGrabHandler(srv.Server(), &handle); err != nil {
		logger.Fatal(err)
	}

	// RegisterSubscriber
	if err := micro.RegisterSubscriber(srv.Server().Options().Name, srv.Server(), handle.HandleEvent, server.SubscriberQueue(srv.Server().Options().Name)); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}
}

func ReadConfig() *model.Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Fatal("can't find the config.yaml file.")
	}

	cc := new(model.Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatalf("read config.yaml failed. %v", err)
	}

	return cc
}
