package main

import (
	"checkurl/cmd/grpc/handler"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	_ "github.com/go-micro/plugins/v4/broker/kafka"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
	_ "google.golang.org/protobuf/types/known/structpb"
)

var (
	Version string
	BuildAt string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	// Create service
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)
	srv.Init(
		micro.Name(constant.ServiceCheckUrl),
		micro.Version(Version),
	)

	c := ReadConfig()

	if len(c.NodeID) > 0 {
		_ = srv.Server().Init(server.Id(c.NodeID))
	}

	checkURL := handler.NewCheckURL(srv)

	// Register handler
	if err := pb.RegisterCheckURLHandler(srv.Server(), &checkURL); err != nil {
		logger.Fatal(err)
	}

	// RegisterSubscriber
	if err := micro.RegisterSubscriber(srv.Server().Options().Name, srv.Server(), checkURL.HandleEvent, server.SubscriberQueue(srv.Server().Options().Name)); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}
}

func ReadConfig() *handler.Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Fatal("can't find the config.yaml file.")
	}

	cc := new(handler.Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config.yaml failed.")
	}

	return cc
}
