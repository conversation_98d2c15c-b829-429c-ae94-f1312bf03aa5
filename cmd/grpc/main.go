package main

import (
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/elastic"
	"git.gobies.org/shared-platform/quickstore/internal/handler"
	conf "git.gobies.org/shared-platform/quickstore/internal/model"
	_ "github.com/go-micro/plugins/v4/broker/kafka"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
	"log"
	"time"
)

var (
	Version string
	BuildAt string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	// 创建一个名为 "Asia/Shanghai" 的固定时区，相对于 UTC 的时差为 +8 小时
	zone := time.FixedZone("Asia/Shanghai", 8*60*60)
	time.Local = zone

	// Create service
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	srv.Init(
		micro.Name(constant.ServiceQuickStore),
		micro.Version(Version),
	)

	c := ReadConfig()

	if len(c.NodeID) > 0 {
		_ = srv.Server().Init(server.Id(c.NodeID))
	}

	client, err := elastic.NewV6(c.Elastic)

	if err != nil {
		log.Fatalln("new elastic client error", err)
	}

	quickStore := handler.NewQuickStore(c, client)

	// Register handler
	if err = rpcx.RegisterQuickStoreHandler(srv.Server(), &quickStore); err != nil {
		logger.Fatal(err)
	}

	if err = micro.RegisterSubscriber(srv.Server().Options().Name, srv.Server(), quickStore.HandleEvent, server.SubscriberQueue(srv.Server().Options().Name)); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err = srv.Run(); err != nil {
		logger.Fatal(err)
	}
}

func ReadConfig() *conf.Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Fatal("can't find the config.yaml file.")
	}

	cc := new(conf.Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config.yaml failed.")
	}

	return cc
}
