package config

import (
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
)

type NmapConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type MasscanConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type WorkerConfig struct {
	BatchSize      int
	SameIpMaxCount int
	SendEth        string
	ReserveInnerIp bool
}

type Config struct {
	Nmap    NmapConfig
	Masscan MasscanConfig
	Worker  WorkerConfig
}

func ReadConfig() *Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Fatal("can't find the config.yaml file.")
	}

	cc := new(Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config.yaml failed.")
	}

	return cc
}
