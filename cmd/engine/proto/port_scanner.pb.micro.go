// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: proto/port_scanner.proto

package rpcx

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/anypb"
	math "math"
)

import (
	context "context"
	api "go-micro.dev/v4/api"
	client "go-micro.dev/v4/client"
	server "go-micro.dev/v4/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for EngineScannerService service

func NewEngineScannerServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for EngineScannerService service

type EngineScannerService interface {
	Start(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error)
	Pause(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error)
	Resume(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error)
	Stop(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error)
	Add(ctx context.Context, in *EngineAttrRequest, opts ...client.CallOption) (*EngineAttrResponse, error)
	Remove(ctx context.Context, in *EngineAttrRequest, opts ...client.CallOption) (*EngineAttrResponse, error)
}

type engineScannerService struct {
	c    client.Client
	name string
}

func NewEngineScannerService(name string, c client.Client) EngineScannerService {
	return &engineScannerService{
		c:    c,
		name: name,
	}
}

func (c *engineScannerService) Start(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Start", in)
	out := new(EngineTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *engineScannerService) Pause(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Pause", in)
	out := new(EngineTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *engineScannerService) Resume(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Resume", in)
	out := new(EngineTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *engineScannerService) Stop(ctx context.Context, in *EngineTaskRequest, opts ...client.CallOption) (*EngineTaskResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Stop", in)
	out := new(EngineTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *engineScannerService) Add(ctx context.Context, in *EngineAttrRequest, opts ...client.CallOption) (*EngineAttrResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Add", in)
	out := new(EngineAttrResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *engineScannerService) Remove(ctx context.Context, in *EngineAttrRequest, opts ...client.CallOption) (*EngineAttrResponse, error) {
	req := c.c.NewRequest(c.name, "EngineScannerService.Remove", in)
	out := new(EngineAttrResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for EngineScannerService service

type EngineScannerServiceHandler interface {
	Start(context.Context, *EngineTaskRequest, *EngineTaskResponse) error
	Pause(context.Context, *EngineTaskRequest, *EngineTaskResponse) error
	Resume(context.Context, *EngineTaskRequest, *EngineTaskResponse) error
	Stop(context.Context, *EngineTaskRequest, *EngineTaskResponse) error
	Add(context.Context, *EngineAttrRequest, *EngineAttrResponse) error
	Remove(context.Context, *EngineAttrRequest, *EngineAttrResponse) error
}

func RegisterEngineScannerServiceHandler(s server.Server, hdlr EngineScannerServiceHandler, opts ...server.HandlerOption) error {
	type engineScannerService interface {
		Start(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error
		Pause(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error
		Resume(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error
		Stop(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error
		Add(ctx context.Context, in *EngineAttrRequest, out *EngineAttrResponse) error
		Remove(ctx context.Context, in *EngineAttrRequest, out *EngineAttrResponse) error
	}
	type EngineScannerService struct {
		engineScannerService
	}
	h := &engineScannerServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&EngineScannerService{h}, opts...))
}

type engineScannerServiceHandler struct {
	EngineScannerServiceHandler
}

func (h *engineScannerServiceHandler) Start(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error {
	return h.EngineScannerServiceHandler.Start(ctx, in, out)
}

func (h *engineScannerServiceHandler) Pause(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error {
	return h.EngineScannerServiceHandler.Pause(ctx, in, out)
}

func (h *engineScannerServiceHandler) Resume(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error {
	return h.EngineScannerServiceHandler.Resume(ctx, in, out)
}

func (h *engineScannerServiceHandler) Stop(ctx context.Context, in *EngineTaskRequest, out *EngineTaskResponse) error {
	return h.EngineScannerServiceHandler.Stop(ctx, in, out)
}

func (h *engineScannerServiceHandler) Add(ctx context.Context, in *EngineAttrRequest, out *EngineAttrResponse) error {
	return h.EngineScannerServiceHandler.Add(ctx, in, out)
}

func (h *engineScannerServiceHandler) Remove(ctx context.Context, in *EngineAttrRequest, out *EngineAttrResponse) error {
	return h.EngineScannerServiceHandler.Remove(ctx, in, out)
}
