syntax = "proto3";

package rpcx;
option go_package = "./proto;rpcx";
import "google/protobuf/any.proto";

message TaskStateNotifyEvent {
	ServiceNode Node = 1;
	string task_id = 2;
	string job_id = 3;
	string state = 4;
	float progress = 5;
}

message ServiceNode {
	string service_id = 1;
	string service_name = 2;
	string service_version = 3;
}

message TaskResultNotifyEvent {
	ServiceNode Node = 1;
	string task_id = 2;
	string job_id = 3;
	string base_protocol = 4;
	string ip = 5;
	uint32 port = 6;
	bool persist = 7;
}

message EngineCommandEvent {
	string command = 1;
	google.protobuf.Any params=2;
}

message EngineAttrRequest {
	string attr_key = 1;
	string attr_value = 2;
}

message EngineAttrResponse {}

// 端口扫描任务的事件
message EngineTaskRequest {
	string task_id = 1;   // 任务ID
	string job_id = 2;    // JobID
	optional JobOption job_option=3;
}

message JobOption {
	// 扫描类型
	uint32  scan_type = 1;

	repeated string ports = 2;

	repeated string host_infos = 3;
	// rate the max bandwidth used to scan
	int32 rate = 4;
	// blacklist ips will not scan.
	string blacklist = 5;
	// use ping to scan the ip
	bool ping_scan = 6;
	// use gateway on mac
	string gateway_mac = 7;

	string send_eth = 8;
	// retry times
	int32 retries = 9;

	// parse the OS info
	bool deep_get_os = 10;
	// parse the mac info
	bool deep_get_mac = 11;
	// indicate is ipv6 ip list.
	bool is_ipv6 = 12;
	// pick the device by deviceName
	string deviceName = 13;

	bool treck_scan = 14;
}

message EngineTaskResponse {}

service EngineScannerService {
	rpc Start(EngineTaskRequest) returns (EngineTaskResponse) {}
	rpc Pause(EngineTaskRequest) returns (EngineTaskResponse) {}
	rpc Resume(EngineTaskRequest) returns (EngineTaskResponse) {}
	rpc Stop(EngineTaskRequest) returns (EngineTaskResponse) {}

	rpc Add(EngineAttrRequest) returns (EngineAttrResponse) {}
	rpc Remove(EngineAttrRequest) returns (EngineAttrResponse) {}
}