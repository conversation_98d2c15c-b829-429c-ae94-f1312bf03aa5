// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.7
// source: proto/port_scanner.proto

package rpcx

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskStateNotifyEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node     *ServiceNode `protobuf:"bytes,1,opt,name=Node,proto3" json:"Node,omitempty"`
	TaskId   string       `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	JobId    string       `protobuf:"bytes,3,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	State    string       `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Progress float32      `protobuf:"fixed32,5,opt,name=progress,proto3" json:"progress,omitempty"`
}

func (x *TaskStateNotifyEvent) Reset() {
	*x = TaskStateNotifyEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStateNotifyEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStateNotifyEvent) ProtoMessage() {}

func (x *TaskStateNotifyEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStateNotifyEvent.ProtoReflect.Descriptor instead.
func (*TaskStateNotifyEvent) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{0}
}

func (x *TaskStateNotifyEvent) GetNode() *ServiceNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *TaskStateNotifyEvent) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStateNotifyEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *TaskStateNotifyEvent) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *TaskStateNotifyEvent) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

type ServiceNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId      string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ServiceName    string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceVersion string `protobuf:"bytes,3,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
}

func (x *ServiceNode) Reset() {
	*x = ServiceNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceNode) ProtoMessage() {}

func (x *ServiceNode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceNode.ProtoReflect.Descriptor instead.
func (*ServiceNode) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceNode) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceNode) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceNode) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

type TaskResultNotifyEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node         *ServiceNode `protobuf:"bytes,1,opt,name=Node,proto3" json:"Node,omitempty"`
	TaskId       string       `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	JobId        string       `protobuf:"bytes,3,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	BaseProtocol string       `protobuf:"bytes,4,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol,omitempty"`
	Ip           string       `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	Port         uint32       `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	Persist      bool         `protobuf:"varint,7,opt,name=persist,proto3" json:"persist,omitempty"`
}

func (x *TaskResultNotifyEvent) Reset() {
	*x = TaskResultNotifyEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskResultNotifyEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskResultNotifyEvent) ProtoMessage() {}

func (x *TaskResultNotifyEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskResultNotifyEvent.ProtoReflect.Descriptor instead.
func (*TaskResultNotifyEvent) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{2}
}

func (x *TaskResultNotifyEvent) GetNode() *ServiceNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *TaskResultNotifyEvent) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskResultNotifyEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *TaskResultNotifyEvent) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

func (x *TaskResultNotifyEvent) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *TaskResultNotifyEvent) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *TaskResultNotifyEvent) GetPersist() bool {
	if x != nil {
		return x.Persist
	}
	return false
}

type EngineCommandEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Command string     `protobuf:"bytes,1,opt,name=command,proto3" json:"command,omitempty"`
	Params  *anypb.Any `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *EngineCommandEvent) Reset() {
	*x = EngineCommandEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCommandEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCommandEvent) ProtoMessage() {}

func (x *EngineCommandEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCommandEvent.ProtoReflect.Descriptor instead.
func (*EngineCommandEvent) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{3}
}

func (x *EngineCommandEvent) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *EngineCommandEvent) GetParams() *anypb.Any {
	if x != nil {
		return x.Params
	}
	return nil
}

type EngineAttrRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttrKey   string `protobuf:"bytes,1,opt,name=attr_key,json=attrKey,proto3" json:"attr_key,omitempty"`
	AttrValue string `protobuf:"bytes,2,opt,name=attr_value,json=attrValue,proto3" json:"attr_value,omitempty"`
}

func (x *EngineAttrRequest) Reset() {
	*x = EngineAttrRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineAttrRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineAttrRequest) ProtoMessage() {}

func (x *EngineAttrRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineAttrRequest.ProtoReflect.Descriptor instead.
func (*EngineAttrRequest) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{4}
}

func (x *EngineAttrRequest) GetAttrKey() string {
	if x != nil {
		return x.AttrKey
	}
	return ""
}

func (x *EngineAttrRequest) GetAttrValue() string {
	if x != nil {
		return x.AttrValue
	}
	return ""
}

type EngineAttrResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EngineAttrResponse) Reset() {
	*x = EngineAttrResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineAttrResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineAttrResponse) ProtoMessage() {}

func (x *EngineAttrResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineAttrResponse.ProtoReflect.Descriptor instead.
func (*EngineAttrResponse) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{5}
}

// 端口扫描任务的事件
type EngineTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    string     `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务ID
	JobId     string     `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`    // JobID
	JobOption *JobOption `protobuf:"bytes,3,opt,name=job_option,json=jobOption,proto3,oneof" json:"job_option,omitempty"`
}

func (x *EngineTaskRequest) Reset() {
	*x = EngineTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineTaskRequest) ProtoMessage() {}

func (x *EngineTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineTaskRequest.ProtoReflect.Descriptor instead.
func (*EngineTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{6}
}

func (x *EngineTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *EngineTaskRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *EngineTaskRequest) GetJobOption() *JobOption {
	if x != nil {
		return x.JobOption
	}
	return nil
}

type JobOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 扫描类型
	ScanType  uint32   `protobuf:"varint,1,opt,name=scan_type,json=scanType,proto3" json:"scan_type,omitempty"`
	Ports     []string `protobuf:"bytes,2,rep,name=ports,proto3" json:"ports,omitempty"`
	HostInfos []string `protobuf:"bytes,3,rep,name=host_infos,json=hostInfos,proto3" json:"host_infos,omitempty"`
	// rate the max bandwidth used to scan
	Rate int32 `protobuf:"varint,4,opt,name=rate,proto3" json:"rate,omitempty"`
	// blacklist ips will not scan.
	Blacklist string `protobuf:"bytes,5,opt,name=blacklist,proto3" json:"blacklist,omitempty"`
	// use ping to scan the ip
	PingScan bool `protobuf:"varint,6,opt,name=ping_scan,json=pingScan,proto3" json:"ping_scan,omitempty"`
	// use gateway on mac
	GatewayMac string `protobuf:"bytes,7,opt,name=gateway_mac,json=gatewayMac,proto3" json:"gateway_mac,omitempty"`
	SendEth    string `protobuf:"bytes,8,opt,name=send_eth,json=sendEth,proto3" json:"send_eth,omitempty"`
	// retry times
	Retries int32 `protobuf:"varint,9,opt,name=retries,proto3" json:"retries,omitempty"`
	// parse the OS info
	DeepGetOs bool `protobuf:"varint,10,opt,name=deep_get_os,json=deepGetOs,proto3" json:"deep_get_os,omitempty"`
	// parse the mac info
	DeepGetMac bool `protobuf:"varint,11,opt,name=deep_get_mac,json=deepGetMac,proto3" json:"deep_get_mac,omitempty"`
	// indicate is ipv6 ip list.
	IsIpv6 bool `protobuf:"varint,12,opt,name=is_ipv6,json=isIpv6,proto3" json:"is_ipv6,omitempty"`
	// pick the device by deviceName
	DeviceName string `protobuf:"bytes,13,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	TreckScan  bool   `protobuf:"varint,14,opt,name=treck_scan,json=treckScan,proto3" json:"treck_scan,omitempty"`
}

func (x *JobOption) Reset() {
	*x = JobOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobOption) ProtoMessage() {}

func (x *JobOption) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobOption.ProtoReflect.Descriptor instead.
func (*JobOption) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{7}
}

func (x *JobOption) GetScanType() uint32 {
	if x != nil {
		return x.ScanType
	}
	return 0
}

func (x *JobOption) GetPorts() []string {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *JobOption) GetHostInfos() []string {
	if x != nil {
		return x.HostInfos
	}
	return nil
}

func (x *JobOption) GetRate() int32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *JobOption) GetBlacklist() string {
	if x != nil {
		return x.Blacklist
	}
	return ""
}

func (x *JobOption) GetPingScan() bool {
	if x != nil {
		return x.PingScan
	}
	return false
}

func (x *JobOption) GetGatewayMac() string {
	if x != nil {
		return x.GatewayMac
	}
	return ""
}

func (x *JobOption) GetSendEth() string {
	if x != nil {
		return x.SendEth
	}
	return ""
}

func (x *JobOption) GetRetries() int32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *JobOption) GetDeepGetOs() bool {
	if x != nil {
		return x.DeepGetOs
	}
	return false
}

func (x *JobOption) GetDeepGetMac() bool {
	if x != nil {
		return x.DeepGetMac
	}
	return false
}

func (x *JobOption) GetIsIpv6() bool {
	if x != nil {
		return x.IsIpv6
	}
	return false
}

func (x *JobOption) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *JobOption) GetTreckScan() bool {
	if x != nil {
		return x.TreckScan
	}
	return false
}

type EngineTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EngineTaskResponse) Reset() {
	*x = EngineTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_port_scanner_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineTaskResponse) ProtoMessage() {}

func (x *EngineTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_port_scanner_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineTaskResponse.ProtoReflect.Descriptor instead.
func (*EngineTaskResponse) Descriptor() ([]byte, []int) {
	return file_proto_port_scanner_proto_rawDescGZIP(), []int{8}
}

var File_proto_port_scanner_proto protoreflect.FileDescriptor

var file_proto_port_scanner_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x70, 0x63, 0x78,
	0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9f, 0x01, 0x0a, 0x14,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x78, 0x0a,
	0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd1, 0x01, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x25, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x22, 0x5c, 0x0a, 0x12, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x2c, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x4d, 0x0a, 0x11, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x74, 0x74, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x74, 0x74, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x87,
	0x01, 0x0a, 0x11, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a,
	0x6f, 0x62, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x4a, 0x6f, 0x62, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09, 0x6a, 0x6f, 0x62,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6a, 0x6f,
	0x62, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9c, 0x03, 0x0a, 0x09, 0x4a, 0x6f, 0x62,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f, 0x73,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x68,
	0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x65, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x64,
	0x45, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a,
	0x0b, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x64, 0x65, 0x65, 0x70, 0x47, 0x65, 0x74, 0x4f, 0x73, 0x12, 0x20, 0x0a,
	0x0c, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x63, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x69, 0x73, 0x49, 0x70, 0x76, 0x36, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x65, 0x63,
	0x6b, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x74, 0x72,
	0x65, 0x63, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x22, 0x14, 0x0a, 0x12, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x89, 0x03,
	0x0a, 0x14, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x05, 0x50, 0x61, 0x75, 0x73, 0x65, 0x12, 0x17, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3d, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x17, 0x2e, 0x72,
	0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x3b, 0x0a, 0x04, 0x53, 0x74, 0x6f, 0x70, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78,
	0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3a,
	0x0a, 0x03, 0x41, 0x64, 0x64, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3d, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0e, 0x5a, 0x0c, 0x2e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x72, 0x70, 0x63, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_port_scanner_proto_rawDescOnce sync.Once
	file_proto_port_scanner_proto_rawDescData = file_proto_port_scanner_proto_rawDesc
)

func file_proto_port_scanner_proto_rawDescGZIP() []byte {
	file_proto_port_scanner_proto_rawDescOnce.Do(func() {
		file_proto_port_scanner_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_port_scanner_proto_rawDescData)
	})
	return file_proto_port_scanner_proto_rawDescData
}

var file_proto_port_scanner_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proto_port_scanner_proto_goTypes = []interface{}{
	(*TaskStateNotifyEvent)(nil),  // 0: rpcx.TaskStateNotifyEvent
	(*ServiceNode)(nil),           // 1: rpcx.ServiceNode
	(*TaskResultNotifyEvent)(nil), // 2: rpcx.TaskResultNotifyEvent
	(*EngineCommandEvent)(nil),    // 3: rpcx.EngineCommandEvent
	(*EngineAttrRequest)(nil),     // 4: rpcx.EngineAttrRequest
	(*EngineAttrResponse)(nil),    // 5: rpcx.EngineAttrResponse
	(*EngineTaskRequest)(nil),     // 6: rpcx.EngineTaskRequest
	(*JobOption)(nil),             // 7: rpcx.JobOption
	(*EngineTaskResponse)(nil),    // 8: rpcx.EngineTaskResponse
	(*anypb.Any)(nil),             // 9: google.protobuf.Any
}
var file_proto_port_scanner_proto_depIdxs = []int32{
	1,  // 0: rpcx.TaskStateNotifyEvent.Node:type_name -> rpcx.ServiceNode
	1,  // 1: rpcx.TaskResultNotifyEvent.Node:type_name -> rpcx.ServiceNode
	9,  // 2: rpcx.EngineCommandEvent.params:type_name -> google.protobuf.Any
	7,  // 3: rpcx.EngineTaskRequest.job_option:type_name -> rpcx.JobOption
	6,  // 4: rpcx.EngineScannerService.Start:input_type -> rpcx.EngineTaskRequest
	6,  // 5: rpcx.EngineScannerService.Pause:input_type -> rpcx.EngineTaskRequest
	6,  // 6: rpcx.EngineScannerService.Resume:input_type -> rpcx.EngineTaskRequest
	6,  // 7: rpcx.EngineScannerService.Stop:input_type -> rpcx.EngineTaskRequest
	4,  // 8: rpcx.EngineScannerService.Add:input_type -> rpcx.EngineAttrRequest
	4,  // 9: rpcx.EngineScannerService.Remove:input_type -> rpcx.EngineAttrRequest
	8,  // 10: rpcx.EngineScannerService.Start:output_type -> rpcx.EngineTaskResponse
	8,  // 11: rpcx.EngineScannerService.Pause:output_type -> rpcx.EngineTaskResponse
	8,  // 12: rpcx.EngineScannerService.Resume:output_type -> rpcx.EngineTaskResponse
	8,  // 13: rpcx.EngineScannerService.Stop:output_type -> rpcx.EngineTaskResponse
	5,  // 14: rpcx.EngineScannerService.Add:output_type -> rpcx.EngineAttrResponse
	5,  // 15: rpcx.EngineScannerService.Remove:output_type -> rpcx.EngineAttrResponse
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_proto_port_scanner_proto_init() }
func file_proto_port_scanner_proto_init() {
	if File_proto_port_scanner_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_port_scanner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskStateNotifyEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskResultNotifyEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCommandEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineAttrRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineAttrResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_port_scanner_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_port_scanner_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_port_scanner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_port_scanner_proto_goTypes,
		DependencyIndexes: file_proto_port_scanner_proto_depIdxs,
		MessageInfos:      file_proto_port_scanner_proto_msgTypes,
	}.Build()
	File_proto_port_scanner_proto = out.File
	file_proto_port_scanner_proto_rawDesc = nil
	file_proto_port_scanner_proto_goTypes = nil
	file_proto_port_scanner_proto_depIdxs = nil
}
