package common

import (
	"errors"
	"fmt"
	"go-micro.dev/v4"
	"go-micro.dev/v4/registry"
)

func CurrentNode(srv micro.Service) (*registry.Node, error) {
	serviceUUID := fmt.Sprintf("%s-%s", srv.Server().Options().Name, srv.Server().Options().Id)
	services, _ := srv.Client().Options().Registry.GetService(srv.Server().Options().Name)
	for _, service := range services {
		if service.Version != srv.Server().Options().Version {
			continue
		}
		for _, node := range service.Nodes {
			if node.Id == serviceUUID {
				return node, nil
			}
		}
	}
	return nil, errors.New("can't find the current node")
}

func GetServiceNode(srv micro.Service, serviceID, serviceName, serviceVersion string) (*registry.Node, error) {
	serviceUUID := fmt.Sprintf("%s-%s", serviceName, serviceID)
	services, _ := srv.Client().Options().Registry.GetService(serviceName)
	for _, service := range services {
		if service.Version != serviceVersion {
			continue
		}
		for _, node := range service.Nodes {
			if node.Id == serviceUUID {
				return node, nil
			}
		}

	}
	return nil, errors.New("can't find the current node")
}
