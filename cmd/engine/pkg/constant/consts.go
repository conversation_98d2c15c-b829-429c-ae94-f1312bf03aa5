package constant

const (
	DefaultVersion = "1.0"

	PrefixService = "net.baimaohui.srv.foscan"
	PrefixTopic   = "net.baimaohui.topic.foscan"

	ServiceDispatcher  = PrefixService + "." + "dispatcher"
	ServicePortScanner = PrefixService + "." + "port_scanner"

	TopicPortScanner      = PrefixTopic + "." + "port_scanner"
	TopicDispatcherState  = PrefixTopic + "." + "dispatcher.state"
	TopicDispatcherResult = PrefixTopic + "." + "dispatcher.result"
)

const (
	StateInitial     = "Initial"
	StateDispatching = "Dispatching"
	StateStarting    = "Starting"
	StateRunning     = "Running"
	StateStopping    = "Stopping"
	StateStopped     = "Stopped"
	StatePausing     = "Pausing"
	StatePaused      = "Paused"
	StateResuming    = "Resuming"
	StateError       = "Error"
	StateFinished    = "Finished"
)

const (
	CommandTaskPause  = "Pause"
	CommandTaskResume = "Resume"
	CommandTaskStart  = "Start"
	CommandTaskStop   = "Stop"
	CommandAttrAdd    = "AttrAdd"
	CommandAttrRemove = "AttrRemove"
)
