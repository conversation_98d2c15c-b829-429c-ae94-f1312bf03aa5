package main

import (
	"context"
	"dispatcher/cmd/engine/pkg/constant"
	pb "dispatcher/cmd/engine/proto"
	"errors"
	"fmt"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"strings"
	"sync"
)

type Engine interface {
	Start() error
}

type TopicEngine struct {
	srv     micro.Service
	mutex   sync.Mutex
	scanner pb.EngineScannerServiceHandler
}

func NewTopicEngine(srv micro.Service, scanner pb.EngineScannerServiceHandler) Engine {
	return &TopicEngine{
		srv:     srv,
		scanner: scanner,
	}
}

func (e *TopicEngine) Start() error {
	// 定义topic
	topic := e.srv.Name() + "." + strings.Replace(e.srv.Server().Options().Id, "-", "", -1)[2:10]
	if err := micro.RegisterSubscriber(topic, e.srv.Server(), e.EngineCommandEventHandler); err != nil {
		logger.Fatal(err)
	}

	return e.srv.Run()
}

func (e *TopicEngine) EngineCommandEventHandler(ctx context.Context, event *pb.EngineCommandEvent) error {
	logger.Infof("received command event. command:%s", event.Command)

	var err error
	switch event.Command {
	case constant.CommandTaskStart:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		err = e.scanner.Start(ctx, task, &pb.EngineTaskResponse{})
	case constant.CommandTaskPause:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行暂停操作
		err = e.scanner.Pause(ctx, task, &pb.EngineTaskResponse{})
	case constant.CommandTaskResume:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行恢复操作
		err = e.scanner.Resume(ctx, task, &pb.EngineTaskResponse{})
	case constant.CommandTaskStop:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行停止操作
		err = e.scanner.Stop(ctx, task, &pb.EngineTaskResponse{})
	case constant.CommandAttrAdd:
		attr := new(pb.EngineAttrRequest)
		if err := event.Params.UnmarshalTo(attr); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		err = e.scanner.Add(ctx, attr, &pb.EngineAttrResponse{})
	case constant.CommandAttrRemove:
		attr := new(pb.EngineAttrRequest)
		if err := event.Params.UnmarshalTo(attr); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		err = e.scanner.Remove(ctx, attr, &pb.EngineAttrResponse{})
	default:
		return errors.New(fmt.Sprintf("invalid command type:%s", event.Command))
	}
	if err != nil {
		logger.Errorf("execute command failed. event:%+v caused by %s", event, err)
	}
	return err
}

type GrpcEngine struct {
	srv     micro.Service
	mutex   sync.Mutex
	scanner pb.EngineScannerServiceHandler
}

func NewGrpcEngine(srv micro.Service, scanner pb.EngineScannerServiceHandler) Engine {
	return &GrpcEngine{
		srv:     srv,
		scanner: scanner,
	}
}

func (e *GrpcEngine) Start() error {
	// 定义topic
	if err := micro.RegisterHandler(e.srv.Server(), e.scanner); err != nil {
		logger.Fatal(err)
	}

	return e.srv.Run()
}
