package main

import (
	"context"
	pb "dispatcher/cmd/engine/proto"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
)

type PortScanner struct {
}

func (p PortScanner) Add(ctx context.Context, request *pb.EngineAttrRequest, response *pb.EngineAttrResponse) error {
	//TODO implement me
	panic("implement me")
}

func (p PortScanner) Remove(ctx context.Context, request *pb.EngineAttrRequest, response *pb.EngineAttrResponse) error {
	//TODO implement me
	panic("implement me")
}

func (p PortScanner) Start(ctx context.Context, request *pb.EngineTaskRequest, response *pb.EngineTaskResponse) error {
	//TODO implement me
	panic("implement me")
}

func (p PortScanner) Pause(ctx context.Context, request *pb.EngineTaskRequest, response *pb.EngineTaskResponse) error {
	//TODO implement me
	panic("implement me")
}

func (p PortScanner) Resume(ctx context.Context, request *pb.EngineTaskRequest, response *pb.EngineTaskResponse) error {
	//TODO implement me
	panic("implement me")
}

func (p PortScanner) Stop(ctx context.Context, request *pb.EngineTaskRequest, response *pb.EngineTaskResponse) error {
	//TODO implement me
	panic("implement me")
}

func main() {
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	srv.Init()

	var engine Engine
	if mode, ok := srv.Server().Options().Metadata["mode"]; ok && mode == "topic" {
		engine = NewTopicEngine(srv, &PortScanner{})
	} else {
		engine = NewGrpcEngine(srv, &PortScanner{})
	}
	engine.Start()
}
