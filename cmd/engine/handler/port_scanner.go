package handler

import (
	"context"
	"dispatcher/cmd/engine/config"
	"dispatcher/cmd/engine/pkg/constant"
	pb "dispatcher/cmd/engine/proto"
	"errors"
	"fmt"
	"sync"

	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
)

type PortScanner struct {
	c     *config.Config
	srv   micro.Service
	tasks map[string]map[string]*Job
	mutex sync.Mutex
}

type Job struct {
	ID       string
	State    string
	Progress float32
	Engine   *ScanTask
}

func NewPortScanner(srv micro.Service, c *config.Config) *PortScanner {
	return &PortScanner{
		srv:   srv,
		c:     c,
		tasks: make(map[string]map[string]*Job),
	}
}

func (p *PortScanner) EngineCommandEventHandler(ctx context.Context, event *pb.EngineCommandEvent) error {
	logger.Infof("received command event. command:%s", event.Command)

	var err error
	switch event.Command {
	case constant.CommandTaskStart:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		err = p.Start(ctx, task)
	case constant.CommandTaskPause:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行暂停操作
		err = p.Pause(ctx, task.TaskId, task.JobId)
	case constant.CommandTaskResume:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行恢复操作
		err = p.Resume(ctx, task.TaskId, task.JobId)
	case constant.CommandTaskStop:
		task := new(pb.EngineTaskRequest)
		if err := event.Params.UnmarshalTo(task); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		// 执行停止操作
		err = p.Stop(ctx, task.TaskId, task.JobId)
	case constant.CommandAttrAdd:
		attr := new(pb.EngineAttrRequest)
		if err := event.Params.UnmarshalTo(attr); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		data := p.srv.Server().Options().Metadata
		data[attr.AttrKey] = attr.AttrValue
	case constant.CommandAttrRemove:
		attr := new(pb.EngineAttrRequest)
		if err := event.Params.UnmarshalTo(attr); err != nil {
			logger.Errorf("unmarshal failed. %+v", event)
			return err
		}
		data := p.srv.Server().Options().Metadata
		delete(data, attr.AttrKey)
	default:
		return errors.New(fmt.Sprintf("invalid command type:%s", event.Command))
	}
	if err != nil {
		logger.Errorf("execute command failed. event:%+v caused by %s", event, err)
	}
	return err
}

func (p *PortScanner) TaskStateNotify(ctx context.Context, event *pb.TaskStateNotifyEvent) error {
	logger.Infof("notify state. task:%s job:%s state:%s progress:%.2f", event.TaskId, event.JobId, event.State, event.Progress)
	err := p.srv.Client().Publish(ctx, client.NewMessage(constant.TopicDispatcherState, event))
	if err != nil {
		logger.Errorf("task state notify failed. event:%+v caused by %s", event, err)
	}
	return err
}

func (p *PortScanner) ScannerResultNotify(ctx context.Context, event *pb.TaskResultNotifyEvent) error {
	err := p.srv.Client().Publish(ctx, client.NewMessage(constant.TopicDispatcherResult, event))
	if err != nil {
		logger.Errorf("task state notify failed. event:%+v caused by %s", event, err)
	}
	return err
}

func (p *PortScanner) getJob(ctx context.Context, taskID, jobID string) *Job {
	if _, exists := p.tasks[taskID]; !exists {
		// 如果任务不存在，可能任务在其他节点上运行，直接返回
		return nil
	}

	if _, exists := p.tasks[taskID][jobID]; !exists {
		// 如果任务不存在，可能任务在其他节点上运行，直接返回
		return nil
	}

	return p.tasks[taskID][jobID]
}

func (p *PortScanner) Pause(ctx context.Context, taskID, jobID string) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，可能是在其他机器上运行，直接返回
		return nil
	}

	// 只有正在运行的调用暂停
	if job.State == constant.StateRunning {
		job.State = constant.StatePausing

		err := job.Engine.Stop()
		if err != nil {
			logger.Errorf("pause task failed. task:% job:%s", taskID, jobID)
			job.State = constant.StateError
			return err
		}

		job.State = constant.StatePaused

		_ = p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
			Node: &pb.ServiceNode{
				ServiceId:      p.srv.Server().Options().Id,
				ServiceName:    p.srv.Server().Options().Name,
				ServiceVersion: p.srv.Server().Options().Version,
			},
			TaskId:   taskID,
			JobId:    jobID,
			State:    constant.StatePaused,
			Progress: job.Progress,
		})
	}
	return nil
}

func (p *PortScanner) Resume(ctx context.Context, taskID, jobID string) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，可能是在其他机器上运行，直接返回
		return nil
	}

	// 只有状态为暂停的才恢复
	if job.State == constant.StatePaused {
		job.State = constant.StateResuming

		err := job.Engine.Resume("")
		if err != nil {
			logger.Errorf("resume task failed. task:% job:%s", taskID, jobID)
			job.State = constant.StateError
			return err
		}

		job.State = constant.StateRunning

		_ = p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
			Node: &pb.ServiceNode{
				ServiceId:      p.srv.Server().Options().Id,
				ServiceName:    p.srv.Server().Options().Name,
				ServiceVersion: p.srv.Server().Options().Version,
			},
			TaskId:   taskID,
			JobId:    jobID,
			State:    constant.StateRunning,
			Progress: job.Progress,
		})
	}

	return nil
}

func (p *PortScanner) Stop(ctx context.Context, taskID, jobID string) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，可能是在其他机器上运行，直接返回
		return nil
	}

	job.State = constant.StateStopping

	err := job.Engine.Stop()
	if err != nil {
		logger.Errorf("stop task failed. task:% job:%s", taskID, jobID)
		job.State = constant.StateError
		return err
	}

	job.State = constant.StateStopped

	_ = p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId:   taskID,
		JobId:    jobID,
		State:    constant.StateStopped,
		Progress: job.Progress,
	})
	// 删除Job信息
	p.clearTaskCache(ctx, taskID, jobID)
	return nil
}

func (p *PortScanner) Start(ctx context.Context, event *pb.EngineTaskRequest) error {
	logger.Infof("received start task event. task:%s job:%s event:%v", event.TaskId, event.JobId, event)

	// 1、判断任务状态
	if jobs, exists := p.tasks[event.TaskId]; !exists {
		p.tasks[event.TaskId] = make(map[string]*Job)
		p.tasks[event.TaskId][event.JobId] = &Job{
			ID:       event.JobId,
			State:    constant.StateInitial,
			Progress: 0,
		}
	} else {
		if _, ok := jobs[event.JobId]; !ok {
			jobs[event.JobId] = &Job{
				ID:       event.JobId,
				State:    constant.StateInitial,
				Progress: 0,
			}
		}
	}

	// 2、获得任务状态
	job := p.tasks[event.TaskId][event.JobId]
	if job.State != constant.StateInitial && job.State != constant.StatePaused {
		return errors.New(fmt.Sprintf("invalid state.task:%s job:%s state:%s", event.TaskId, event.JobId, job.State))
	}

	job.Engine = NewScanTask("", p.c, p, event)
	err := job.Engine.Scan()
	if err != nil {
		logger.Errorf("start scan failed. event:%+v.caused by %s", event, err)
		return err
	}

	// 设置任务状态
	job.State = constant.StateRunning

	// 回调Dispatcher，通知状态
	return p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId: event.TaskId,
		JobId:  event.JobId,
		State:  constant.StateRunning,
	})
}

func (p *PortScanner) OnFinish(ctx context.Context, taskID, jobID string) {
	logger.Infof("scan finished. task:%s job:%s", taskID, jobID)
	_ = p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId:   taskID,
		JobId:    jobID,
		State:    constant.StateFinished,
		Progress: 100,
	})
	// 删除Job信息
	p.clearTaskCache(ctx, taskID, jobID)
}

func (p *PortScanner) OnResult(ctx context.Context, taskID, jobID, ip string, port uint32, baseProtocol string) {
	logger.Infof("on result.task:%s job:%s result:%s %s:%d\n", taskID, jobID, baseProtocol, ip, port)
	_ = p.ScannerResultNotify(ctx, &pb.TaskResultNotifyEvent{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId:       taskID,
		JobId:        jobID,
		BaseProtocol: baseProtocol,
		Ip:           ip,
		Port:         port,
	})
}

func (p *PortScanner) OnProgress(ctx context.Context, taskID, jobID string, progress float64) {
	logger.Infof("on progress.task:%s job:%s progress:%.2f", taskID, jobID, progress)
	job := p.getJob(ctx, taskID, jobID)
	job.Progress = float32(progress)
	_ = p.TaskStateNotify(ctx, &pb.TaskStateNotifyEvent{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId:   taskID,
		JobId:    jobID,
		State:    job.State,
		Progress: job.Progress,
	})
}

func (p *PortScanner) clearTaskCache(ctx context.Context, taskID, jobID string) {
	if len(p.tasks[taskID]) > 1 {
		delete(p.tasks[taskID], jobID)
	} else {
		delete(p.tasks, taskID)
	}
}
