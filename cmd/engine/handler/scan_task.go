package handler

import (
	"dispatcher/cmd/engine/config"
	rpcx "dispatcher/cmd/engine/proto"
	"strings"
)

type ScanTask struct {
	hostName     string
	portScanner  *PortScanner
	lastProgress float64
}

func NewScanTask(hostName string, config *config.Config, portScanner *PortScanner, task *rpcx.EngineTaskRequest) *ScanTask {
	scanTask := &ScanTask{
		hostName:    hostName,
		portScanner: portScanner,
	}
	return scanTask
}

func (t *ScanTask) parseOnePort(port string) string {
	var retPort string

	tmpArr := strings.Split(port, ":")
	if len(tmpArr) == 1 {
		// tcp默认端口
		retPort = tmpArr[0]
	} else if len(tmpArr) == 2 {
		// udp默认端口
		retPort = port
	} else {
		if tmpArr[0] == "U" {
			retPort = tmpArr[0] + ":" + tmpArr[1]
		} else {
			retPort = tmpArr[1]
		}
	}

	return retPort
}

func (t *ScanTask) parsePorts(portAttr []string) string {
	retPorts := ""

	for _, val := range portAttr {
		tmpPort := t.parseOnePort(val)
		if len(tmpPort) > 0 {
			retPorts += tmpPort + ","
		}
	}

	// 如果有TCP端口，加上T:，解决nmap在tcp端口上也扫描了udp的问题
	portsCount := strings.Count(retPorts, ",")
	udpCount := strings.Count(retPorts, "U:")
	if portsCount > udpCount && !strings.HasPrefix(retPorts, "T:") {
		retPorts = "T:" + retPorts
	}

	return retPorts
}

func (t *ScanTask) Scan() error {

	return nil
}

func (t *ScanTask) Resume(location string) error {

	return nil
}

func (t *ScanTask) Stop() error {
	return nil
}

func (t *ScanTask) OnProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {

}

func (t *ScanTask) processOutput(ch <-chan [][4]string) error {
	return nil
}
