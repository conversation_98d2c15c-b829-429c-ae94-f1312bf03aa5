package main

import (
	"baimaohui/portscan_new/cmd/portscanner/config"
	"baimaohui/portscan_new/cmd/portscanner/plugin"
	"baimaohui/portscan_new/internal/util"
	"flag"
	"log"
)

var (
	pluginCmd string
)

func parseFlag() (cfg *config.Config) {
	cfg = config.NewConfig()
	flag.StringVar(&pluginCmd, "plugin", "", "command to start plugin")

	flag.String("c", "config.toml", "plugin config")

	flag.StringVar(&cfg.Masscan.Name, "masscan_name", "masscan", "masscan command name")
	flag.StringVar(&cfg.Masscan.Directory, "masscan_dir", "/usr/bin", "masscan command path")
	flag.IntVar(&cfg.Masscan.SourcePort, "masscan_sp", 58924, "masscan default source port")
	flag.StringVar(&cfg.Masscan.BlackList, "masscan_black", "", "masscan default black lists")

	flag.StringVar(&cfg.Nmap.Name, "nmap_name", "nmap", "nmap command name")
	flag.StringVar(&cfg.Nmap.Directory, "nmap_dir", "/usr/bin", "nmap command path")
	flag.IntVar(&cfg.Nmap.SourcePort, "nmap_sp", 58914, "nmap default source port")
	flag.StringVar(&cfg.Nmap.BlackList, "nmap_black", "", "nmap default black lists")

	flag.IntVar(&cfg.Worker.BatchSize, "worker_bs", 30, "worker batch size")
	flag.IntVar(&cfg.Worker.SameIpMaxCount, "worker_simc", 100, "worker same ip max count")
	flag.StringVar(&cfg.Worker.SendEth, "worker_eth", "", "worker default send eth")
	flag.BoolVar(&cfg.Worker.ReserveInnerIp, "worker_rii", true, "worker reserve inner ip")
	flag.Parse()
	return
}

func main() {

	// 读配置
	conf := parseFlag()

	hostname, err := util.GetHost()
	if err != nil {
		log.Fatal("[FATAL] get hostname failed", err)
	}

	if len(pluginCmd) > 0 {
		err := plugin.RunPlugin(pluginCmd, conf, hostname)
		if err != nil {
			panic(err)
		}
		return
	}

	log.Fatal("[FATAL] failed to run plugin")

}
