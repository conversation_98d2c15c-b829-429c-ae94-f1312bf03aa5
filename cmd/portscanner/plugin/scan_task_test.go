package plugin

import (
	"baimaohui/portscan_new/cmd/portscanner/config"
	"baimaohui/portscan_new/internal/task"
	"errors"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/proto"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
	"time"
)

type TaskCallback struct {
	receivedProgressCount int
	receivedResultCount   int
	receivedDoneCount     int
}

func (c *TaskCallback) OnResult(request *proto.PortScannerCallbackOnResultRequest) error {
	c.receivedResultCount++
	return nil
}

func (c *TaskCallback) OnMacResult(request *proto.PortScannerCallbackOnMacResultRequest) error {
	return nil
}

func (c *TaskCallback) OnProgress(request *proto.PortScannerCallbackOnProgressRequest) error {
	c.receivedProgressCount++
	return nil
}

func (c *TaskCallback) Done() {
	c.receivedDoneCount++
}

type ScanTaskSuite struct {
	suite.Suite
	task *ScanTask
}

func Test_ScanTaskSuite(t *testing.T) {
	s := &ScanTaskSuite{}
	suite.Run(t, s)
}

func (s *ScanTaskSuite) SetupTest() {
	s.task = &ScanTask{
		hostName:    "localhost",
		config:      getConfig(),
		request:     getRequest(),
		engines:     []ScannerEngine{},
		callback:    &TaskCallback{},
		progressMap: map[interface{}]*ProgressInterval{},
	}
}

func getConfig() *config.Config {
	return &config.Config{
		Masscan: &config.MasscanConfig{
			Directory: "/usr/bin",
		},
		Nmap: &config.NmapConfig{
			Directory: "/usr/bin",
		},
		Worker: &config.WorkerConfig{},
	}
}

func getRequest() *proto.PortScannerScanRequest {
	return &proto.PortScannerScanRequest{
		Option: &proto.PortScannerScanOption{},
	}
}

func (s *ScanTaskSuite) TestNewScanTask() {
	Convey("TestNewScanTask", s.T(), func() {
		Convey("normal test", func() {
			t := NewScanTask(0, "localhost", getConfig(), getRequest(), nil)
			So(t.hostName, ShouldEqual, "localhost")
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_OnProgress() {
	Convey("TestScanTask_OnProgress", s.T(), func() {
		Convey("normal test", func() {
			s.task.OnProgress(1, 1, "2", "", "0.5", "", "")
			s.task.OnProgress(1, 1, "3", "", "0.6", "", "")
			callback := s.task.callback.(*TaskCallback)
			So(callback.receivedProgressCount, ShouldEqual, 2)
		})
		Convey("without callback", func() {
			s.task.callback = nil
			s.task.OnProgress(1, 1, "2", "", "0.5", "", "")
			s.task.OnProgress(1, 1, "3", "", "0.6", "", "")
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_OnResult() {
	Convey("TestScanTask_OnResult", s.T(), func() {
		Convey("normal test", func() {
			s.task.OnResult("127.0.0.1", 22, "tcp")
			s.task.OnResult("127.0.0.1", 161, "udp")
			callback := s.task.callback.(*TaskCallback)
			So(callback.receivedResultCount, ShouldEqual, 2)
		})
		Convey("without callback", func() {
			s.task.callback = nil
			s.task.OnResult("127.0.0.1", 22, "tcp")
			s.task.OnResult("127.0.0.1", 161, "udp")
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_Scan() {
	Convey("TestScanTask_Scan", s.T(), func() {
		Convey("normal test1", func() {
			defer gomonkey.ApplyMethodReturn(&BaseScannerEngine{}, "Scan", nil).Reset()
			s.task.initEngines()
			So(s.task.Scan(), ShouldBeNil)
		})
		Convey("normal test2", func() {
			defer gomonkey.ApplyMethodReturn(&BaseScannerEngine{}, "Scan", errors.New("")).Reset()
			s.task.initEngines()
			So(s.task.Scan(), ShouldBeNil)
		})
		Convey("normal test3", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "Run", ch, nil).Reset()
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "HandleResume", ch, nil).Reset()
			go func() {
				ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
				ch <- nil
			}()
			masscanEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscan))
			masscanEngine.currentState = StateFinished

			pingEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscanPing))
			pingEngine.currentState = StateStopped
			s.task.engines = []ScannerEngine{masscanEngine, pingEngine}
			So(s.task.Scan(), ShouldBeNil)
		})
		Convey("normal test4", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "Run", ch, nil).Reset()
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "HandleResume", ch, nil).Reset()
			go func() {
				ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
				ch <- nil
			}()
			masscanEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscan))
			masscanEngine.currentState = StateFinished

			pingEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscanPing))
			pingEngine.currentState = StateStopped

			s.task.current = pingEngine

			s.task.engines = []ScannerEngine{masscanEngine, pingEngine}
			So(s.task.Scan(), ShouldBeNil)
		})
		Convey("normal test5", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "Run", ch, nil).Reset()
			defer gomonkey.ApplyMethodReturn(&task.Masscan{}, "HandleResume", ch, nil).Reset()
			var done = make(chan int)
			go func() {
				for {
					ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
					ch <- nil
					time.Sleep(time.Millisecond * 100)
					select {
					case <-done:
						return
					default:
					}
				}
			}()
			masscanEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscan))
			s.task.current = masscanEngine

			s.task.request.Option.Cursor = &proto.ScanCursor{Cursor: &proto.ScanCursor_Offset{
				Offset: 1,
			}}

			pingEngine := s.task.getScannerEngine(s.task.getMasscanStub(task.ETMasscanPing))
			pingEngine.currentState = StateInitial

			s.task.engines = []ScannerEngine{masscanEngine, pingEngine}
			So(s.task.Scan(), ShouldBeNil)
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_Stop() {
	Convey("TestScanTask_Stop", s.T(), func() {
		Convey("normal test", func() {

		})
	})
}

func (s *ScanTaskSuite) TestScanTask_getMasscanStub() {
	Convey("TestScanTask_getMasscanStub", s.T(), func() {
		Convey("normal test1", func() {
			stub := s.task.getMasscanStub(task.ETMasscan)
			So(stub, ShouldHaveSameTypeAs, &task.Masscan{})
		})
		Convey("normal test2", func() {
			stub := s.task.getMasscanStub(task.ETMasscanPing)
			So(stub, ShouldHaveSameTypeAs, &task.Masscan{})
		})
		Convey("normal test3", func() {
			stub := s.task.getMasscanStub(task.ETTreckScan)
			So(stub, ShouldHaveSameTypeAs, &task.Masscan{})
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_getNmapStub() {
	Convey("TestScanTask_getNmapStub", s.T(), func() {
		Convey("normal test1", func() {
			stub := s.task.getNmapStub(task.ETNmap)
			So(stub, ShouldHaveSameTypeAs, &task.NmapScan{})
		})
		Convey("normal test2", func() {
			stub := s.task.getNmapStub(task.ETNmapPing)
			So(stub, ShouldHaveSameTypeAs, &task.NmapScan{})
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_getScannerEngine() {
	Convey("TestScanTask_getScannerEngine", s.T(), func() {
		Convey("normal test1", func() {
			stub := s.task.getMasscanStub(task.ETMasscan)
			engine := s.task.getScannerEngine(stub)
			So(engine.stub, ShouldEqual, stub)
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_initEngines() {
	Convey("TestScanTask_initEngines", s.T(), func() {
		Convey("normal test1", func() {
			s.task.engines = []ScannerEngine{}

			var taskType uint32
			taskType = uint32(task.ETMasscan)
			s.task.request.TaskType = &taskType

			var pingScan = true
			s.task.request.Option.PingScan = &pingScan

			var trackScan = true
			s.task.request.Option.TreckScan = &trackScan
			s.task.initEngines()
			So(len(s.task.engines), ShouldEqual, 3)
		})
		Convey("normal test2", func() {
			s.task.engines = []ScannerEngine{}

			var taskType uint32
			taskType = uint32(task.ETNmap)
			s.task.request.TaskType = &taskType

			var pingScan = true
			s.task.request.Option.PingScan = &pingScan
			s.task.initEngines()
			So(len(s.task.engines), ShouldEqual, 2)
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_parseOnePort() {
	Convey("TestScanTask_parseOnePort", s.T(), func() {
		Convey("normal test1", func() {
			So(s.task.parseOnePort("T:22:SSH"), ShouldEqual, "22")
		})
		Convey("normal test2", func() {
			So(s.task.parseOnePort("T:22"), ShouldEqual, "T:22")
		})
		Convey("normal test3", func() {
			So(s.task.parseOnePort("22"), ShouldEqual, "22")
		})
		Convey("normal test4", func() {
			So(s.task.parseOnePort("U:22"), ShouldEqual, "U:22")
		})
	})
}

func (s *ScanTaskSuite) TestScanTask_parsePorts() {
	Convey("TestScanTask_parsePorts", s.T(), func() {
		Convey("normal test1", func() {
			So(s.task.parsePorts([]string{"T:22", "U:161:SNMP", "13000"}), ShouldEqual, "T:22,U:161,13000,")
		})
	})
}
