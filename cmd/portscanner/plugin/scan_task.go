package plugin

import (
	"baimaohui/portscan_new/cmd/portscanner/config"
	"baimaohui/portscan_new/internal/task"
	"log"
	"strconv"
	"strings"
	"time"

	pluginPortScanner "git.gobies.org/gobase/plugin/pkg/plugin/share/portscanner"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/proto"
)

type ScanTask struct {
	hostName     string
	taskID       uint64
	config       *config.Config
	request      *proto.PortScannerScanRequest
	callback     pluginPortScanner.Callback
	engines      []ScannerEngine
	current      ScannerEngine
	state        string
	lastProgress float64
	progressMap  map[interface{}]*ProgressInterval
}

type ProgressInterval struct {
	minProgress float64
	maxProgress float64
}

func NewScanTask(taskID uint64, hostName string, config *config.Config, request *proto.PortScannerScanRequest, callback pluginPortScanner.Callback) *ScanTask {
	scanTask := &ScanTask{
		taskID:      taskID,
		hostName:    hostName,
		config:      config,
		request:     request,
		callback:    callback,
		engines:     []ScannerEngine{},
		state:       StateInitial,
		progressMap: map[interface{}]*ProgressInterval{},
	}
	scanTask.initEngines()
	return scanTask
}

func (t *ScanTask) GetScanTask(request *proto.PortScannerScanRequest, callback pluginPortScanner.Callback) *ScanTask {
	t.request = request
	t.callback = callback
	return t
}

func (t *ScanTask) initEngines() {
	if t.request.GetTaskType() == uint32(task.ETMasscan) {
		engine := t.getScannerEngine(t.getMasscanStub(task.ETMasscan))
		t.engines = append(t.engines, engine)

		var syncProgress, pingProgress, treckProgress float64
		if t.request.GetOption().GetPingScan() {
			engine := t.getScannerEngine(t.getMasscanStub(task.ETMasscanPing))
			t.engines = append(t.engines, engine)
			pingProgress = 0.25
		}
		if t.request.GetOption().GetTreckScan() {
			engine := t.getScannerEngine(t.getMasscanStub(task.ETTreckScan))
			t.engines = append(t.engines, engine)
			treckProgress = 0.25
		}

		syncProgress = 1 - pingProgress - treckProgress

		t.progressMap[task.ETMasscan] = &ProgressInterval{
			minProgress: 0,
			maxProgress: syncProgress,
		}

		t.progressMap[task.ETMasscanPing] = &ProgressInterval{
			minProgress: syncProgress,
			maxProgress: syncProgress + pingProgress,
		}

		t.progressMap[task.ETTreckScan] = &ProgressInterval{
			minProgress: 1 - pingProgress - treckProgress,
			maxProgress: 1,
		}

	} else if t.request.GetTaskType() == uint32(task.ETNmap) {
		engine := t.getScannerEngine(t.getNmapStub(task.ETNmap))
		t.engines = append(t.engines, engine)

		var syncProgress, pingProgress float64
		if t.request.GetOption().GetPingScan() {
			engine := t.getScannerEngine(t.getNmapStub(task.ETNmapPing))
			t.engines = append(t.engines, engine)

			pingProgress = 0.25
		}

		syncProgress = 1 - pingProgress
		t.progressMap[task.ETNmap] = &ProgressInterval{
			minProgress: 0,
			maxProgress: syncProgress,
		}

		t.progressMap[task.ETNmapPing] = &ProgressInterval{
			minProgress: syncProgress,
			maxProgress: 1,
		}
	}
}

func (t *ScanTask) getScannerEngine(stub task.PortscanStub) *BaseScannerEngine {
	return NewScannerEngine(
		t,
		t.taskID,
		strconv.Itoa(int(t.request.GetOption().GetRate())),
		strings.Join(t.request.GetHostInfos(), ","),
		t.parsePorts(t.request.GetPorts()),
		t.request.GetOption().GetBlacklist(),
		t.request.GetOption().GetDeviceName(), //SendEth
		int(t.request.GetOption().GetRetries()),
		stub,
	)
}

func (t *ScanTask) getMasscanStub(engineType task.EngineType) task.PortscanStub {
	taskBaseCfg := task.NewScantaskBaseConf(
		engineType,
		t.config.Masscan.Directory,
		t.hostName,
		t.config.Masscan.BlackList,
		t.config.Masscan.SourcePort,
		t.config.Worker.BatchSize,
		t.config.Worker.SameIpMaxCount,
		true,
		t.config.Worker.ReserveInnerIp,
		false)

	taskBaseCfg.SetRouterMac(t.request.GetOption().GetGatewayMac())
	taskBaseCfg.SetScanEngine(engineType)
	stub := task.NewScantask(taskBaseCfg)
	stub.SetScanIPv6(t.request.GetOption().GetIsIpv6())
	stub.SetCallback(nil, t.OnProgress, nil)
	return stub
}

func (t *ScanTask) getNmapStub(engineType task.EngineType) task.PortscanStub {
	taskBaseCfg := task.NewScantaskBaseConf(
		engineType,
		t.config.Nmap.Directory,
		t.hostName,
		t.config.Nmap.BlackList,
		t.config.Nmap.SourcePort,
		t.config.Worker.BatchSize,
		t.config.Worker.SameIpMaxCount,
		true,
		t.config.Worker.ReserveInnerIp,
		false)

	taskBaseCfg.SetRouterMac(t.request.GetOption().GetGatewayMac())
	taskBaseCfg.SetScanEngine(engineType)
	stub := task.NewScantask(taskBaseCfg)
	stub.SetScanIPv6(t.request.GetOption().GetIsIpv6())
	stub.SetCallback(nil, t.OnProgress, nil)
	return stub
}

func (t *ScanTask) parseOnePort(port string) string {
	var retPort string

	tmpArr := strings.Split(port, ":")
	if len(tmpArr) == 1 {
		// tcp默认端口
		retPort = tmpArr[0]
	} else if len(tmpArr) == 2 {
		// udp默认端口
		retPort = port
	} else {
		if tmpArr[0] == "U" {
			retPort = tmpArr[0] + ":" + tmpArr[1]
		} else {
			retPort = tmpArr[1]
		}
	}

	return retPort
}

func (t *ScanTask) parsePorts(portAttr []string) string {
	retPorts := ""

	for _, val := range portAttr {
		tmpPort := t.parseOnePort(val)
		if len(tmpPort) > 0 {
			retPorts += tmpPort + ","
		}
	}

	// 如果有TCP端口，加上T:，解决nmap在tcp端口上也扫描了udp的问题
	portsCount := strings.Count(retPorts, ",")
	udpCount := strings.Count(retPorts, "U:")
	if portsCount > udpCount && !strings.HasPrefix(retPorts, "T:") {
		retPorts = "T:" + retPorts
	}

	return retPorts
}

func (t *ScanTask) Scan() error {
	t.state = StateRunning

	for _, engine := range t.engines {
		err := engine.Scan()
		if err != nil {
			log.Printf("handle error.%+v\n", err)
		}
		time.Sleep(time.Second)
	}
	// 如果是正常结束的，则设置状态为完成
	if t.state == StateRunning {
		t.state = StateFinished
	}

	if t.callback != nil {
		t.callback.Done()
	}

	return nil
}

func (t *ScanTask) Resume(location string) error {
	t.state = StateRunning

	for _, engine := range t.engines {
		err := engine.Resume(location)
		if err != nil {
			log.Printf("handle resume task=%d error.%+v\n", t.taskID, err)
		}
		time.Sleep(time.Second)
	}

	// 如果是正常结束的，则设置状态为完成
	if t.state == StateRunning {
		t.state = StateFinished
	}

	if t.callback != nil {
		t.callback.Done()
	}
	return nil
}

func (t *ScanTask) Stop() (uint64, error) {
	t.state = StateStopped

	var scanOffset uint64
	for _, engine := range t.engines {
		ret, err := engine.Stop()
		if err != nil {
			log.Printf("stop failed.%+v\n", err)
		}
		if ret > 0 {
			scanOffset = ret
		}
	}

	return scanOffset, nil
}

func (t *ScanTask) OnResult(ip string, port uint32, baseProtocol string) {
	if t.callback != nil {
		err := t.callback.OnResult(&proto.PortScannerCallbackOnResultRequest{
			Ip:           ip,
			Port:         port,
			BaseProtocol: baseProtocol,
		})
		if err != nil {
			log.Printf("callback on result failed.%v\n", err)
		}
	}
}

func (t *ScanTask) OnProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {
	log.Printf("engine:%v taskID:%d progress:%s state:%s hostCompleted:%s remainTime:%s errMsg:%s\n", engine, tid, progress, state, hostCompleted, remainTime, errMsg)
	if t.callback == nil {
		return
	}
	p := t.calcProgress(engine, progress)
	if p < t.lastProgress {
		return
	}
	t.lastProgress = p
	err := t.callback.OnProgress(&proto.PortScannerCallbackOnProgressRequest{
		Progress: float32(p),
	})
	if err != nil {
		log.Printf("Notify progress failed.err=%v", err)
	}
}

func (t *ScanTask) calcProgress(engine interface{}, progress string) float64 {
	if p, ok := t.progressMap[engine]; ok {
		progress = strings.TrimSpace(progress)
		if len(progress) <= 0 {
			return 0
		}
		if strings.HasSuffix(progress, "%") {
			progress = progress[:len(progress)-1]
		}
		data, err := strconv.ParseFloat(progress, 64)
		if err != nil {
			return p.minProgress
		}
		return p.minProgress*100 + (p.maxProgress-p.minProgress)*data
	}
	return 0
}
