package plugin

import (
	"baimaohui/portscan_new/internal/task"
	"errors"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type EngineSuite struct {
	suite.Suite
	engine *BaseScannerEngine
}

type EngineResult struct {
	ip       string
	port     uint32
	protocol string
}

type EngineCallback struct {
	result []EngineResult
}

func (c *EngineCallback) OnResult(ip string, port uint32, protocol string) {
	c.result = append(c.result, EngineResult{ip: ip, port: port, protocol: protocol})
}

func Test_EngineSuite(t *testing.T) {
	s := &EngineSuite{}
	suite.Run(t, s)
}

func (s *EngineSuite) SetupTest() {
	s.engine = NewScannerEngine(
		&ScanTask{},
		0,
		"300",
		"127.0.0.1, *************",
		"22,U:161",
		"**********,**********",
		"eth0",
		3,
		nil,
	)
}

func (s *EngineSuite) getStub() task.PortscanStub {
	taskBaseCfg := task.NewScantaskBaseConf(
		task.ETMasscan,
		"/usr/bin",
		"localhost",
		"**********,**********",
		51294,
		1,
		1,
		true,
		true,
		false)

	taskBaseCfg.SetRouterMac("11:22:33:44:55:66")
	taskBaseCfg.SetScanEngine(task.ETMasscan)
	stub := task.NewScantask(taskBaseCfg)
	stub.SetScanIPv6(false)
	stub.SetCallback(nil, nil, nil)
	return stub
}

func (s *EngineSuite) TestBaseScannerEngine_CurrentState() {
	Convey("TestBaseScannerEngine_CurrentState", s.T(), func() {
		Convey("initial state", func() {
			So(s.engine.CurrentState(), ShouldEqual, StateInitial)
		})
	})
}

func (s *EngineSuite) TestNewScannerEngine() {
	Convey("TestNewScannerEngine", s.T(), func() {
		Convey("normal test", func() {
			engine := &BaseScannerEngine{
				rate:         "300",
				hosts:        "127.0.0.1, *************",
				ports:        "22,U:161",
				blackIps:     "**********,**********",
				sendEth:      "eth0",
				retries:      3,
				stub:         nil,
				currentState: StateInitial,
				task:         &ScanTask{},
			}
			So(s.engine, ShouldResemble, engine)
		})
	})
}

func (s *EngineSuite) TestBaseScannerEngine_processOutput() {
	Convey("TestBaseScannerEngine_processOutput", s.T(), func() {
		Convey("normal test", func() {
			var ch = make(chan [][4]string, 5)
			ch <- [][4]string{{"127.0.0.1", "22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
			ch <- nil
			So(s.engine.processOutput(ch), ShouldBeNil)
		})
		Convey("invalid data", func() {
			var ch = make(chan [][4]string, 5)
			ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
			ch <- nil
			So(s.engine.processOutput(ch), ShouldBeNil)
		})
	})
}

func (s *EngineSuite) TestBaseScannerEngine_Scan() {
	Convey("TestBaseScannerEngine_Scan", s.T(), func() {
		stub := s.getStub()
		s.engine.SetStub(stub)
		Convey("normal test", func() {
			var ch = make(chan [][4]string)
			go func() {
				ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
				ch <- nil
			}()
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "Run", ch, nil).Reset()
			Convey("do scan", func() {
				So(s.engine.Scan(), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateFinished)
			})

			Convey("do resume", func() {
				defer gomonkey.ApplyMethodReturn(s.engine.stub, "HandleResume", ch, nil).Reset()
				So(s.engine.Scan(), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateFinished)
			})
		})
	})
}

func (s *EngineSuite) TestBaseScannerEngine_doScan() {
	Convey("TestBaseScannerEngine_doScan", s.T(), func() {
		stub := s.getStub()
		s.engine.SetStub(stub)
		Convey("normal test", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "Run", ch, nil).Reset()
			go func() {
				ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
				ch <- nil
			}()
			Convey("normal finished", func() {
				So(s.engine.doScan(), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateFinished)
			})
			Convey("stop success", func() {
				s.engine.currentState = StateStopping
				So(s.engine.doScan(), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateStopped)
			})

		})
		Convey("scan failed", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "Run", ch, errors.New("scan failed")).Reset()
			So(s.engine.doScan(), ShouldNotBeNil)
			So(s.engine.CurrentState(), ShouldEqual, StateFinished)
		})
	})
}

func (s *EngineSuite) TestBaseScannerEngine_Stop() {
	Convey("TestBaseScannerEngine_Stop", s.T(), func() {
		stub := s.getStub()
		s.engine.SetStub(stub)
		Convey("invalid state", func() {
			Convey("invalid state 1", func() {
				s.engine.currentState = StateStopping
				_, err := s.engine.Stop()
				So(err, ShouldBeNil)
			})
			Convey("invalid state 2", func() {
				s.engine.currentState = StateFinished
				_, err := s.engine.Stop()
				So(err, ShouldBeNil)
			})
			Convey("invalid state 3", func() {
				s.engine.currentState = StateInitial
				_, err := s.engine.Stop()
				So(err, ShouldBeNil)
			})
		})
		Convey("normal state", func() {
			s.engine.currentState = StateRunning
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "Stop", nil).Reset()
			Convey("stop with paused file", func() {
				defer gomonkey.ApplyMethodReturn(s.engine.stub, "ResumeFileExists", true).Reset()
				offset, err := s.engine.Stop()
				So(err, ShouldNotBeNil) //RENAME ERROR
				So(offset, ShouldEqual, 0)
			})
			Convey("stop without paused file", func() {
				defer gomonkey.ApplyMethodReturn(s.engine.stub, "ResumeFileExists", false).Reset()
				offset, err := s.engine.Stop()
				So(err, ShouldBeNil)
				So(offset, ShouldEqual, 0)
			})
		})
	})
}

func (s *EngineSuite) TestBaseScannerEngine_doResume() {
	Convey("TestBaseScannerEngine_doResume", s.T(), func() {
		stub := s.getStub()
		s.engine.SetStub(stub)
		Convey("normal test", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "HandleResume", ch, nil).Reset()
			go func() {
				ch <- [][4]string{{"127.0.0.1", "T:22", "", "tcp"}, {"127.0.0.1", "161", "", "udp"}}
				ch <- nil
			}()
			Convey("normal finished", func() {
				So(s.engine.doResume(""), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateFinished)
			})
			Convey("stop success", func() {
				s.engine.currentState = StateStopping
				So(s.engine.doResume(""), ShouldBeNil)
				So(s.engine.CurrentState(), ShouldEqual, StateStopped)
			})
		})
		Convey("scan failed", func() {
			var ch = make(chan [][4]string)
			defer gomonkey.ApplyMethodReturn(s.engine.stub, "HandleResume", ch, errors.New("scan failed")).Reset()
			So(s.engine.doResume(""), ShouldNotBeNil)
			So(s.engine.CurrentState(), ShouldEqual, StateFinished)
		})
	})
}
