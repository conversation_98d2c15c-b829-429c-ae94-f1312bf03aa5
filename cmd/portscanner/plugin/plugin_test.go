package plugin

import (
	"baimaohui/portscan_new/internal/task"
	"testing"

	"git.gobies.org/gobase/plugin/pkg/plugin/share/proto"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/pkg/errors"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
)

type PluginSuite struct {
	suite.Suite
	portScannerExtension *portScannerExtension
}

func Test_PluginSuite(t *testing.T) {
	s := &PluginSuite{}
	suite.Run(t, s)
}

func (s *PluginSuite) SetupTest() {
	s.portScannerExtension = &portScannerExtension{
		defaultConfig: getConfig(),
		hostname:      "localhost",
		client:        nil,
		scanTasks:     map[uint64]*ScanTask{},
	}
}

func (s *PluginSuite) TearDownTest() {
	s.portScannerExtension = nil
}

func (s *PluginSuite) Test_portScannerExtension_Scan() {
	<PERSON><PERSON>("Test_Scan", s.T(), func() {
		Convey("empty targets", func() {
			request := getRequest()
			So(s.portScannerExtension.Scan(request, nil), ShouldBeError)
		})
		Convey("use masscan engine", func() {
			defer gomonkey.ApplyMethodReturn(&ScanTask{}, "Scan", nil).Reset()
			var t = uint32(task.ETMasscan)
			var taskType = &t
			request := &proto.PortScannerScanRequest{
				HostInfos: []string{"127.0.0.1"},
				TaskType:  taskType,
			}
			So(s.portScannerExtension.Scan(request, nil), ShouldBeNil)
		})
		Convey("use nmap engine", func() {
			defer gomonkey.ApplyMethodReturn(&ScanTask{}, "Scan", nil).Reset()
			var t = uint32(task.ETNmap)
			var taskType = &t
			request := &proto.PortScannerScanRequest{
				HostInfos: []string{"127.0.0.1"},
				TaskType:  taskType,
			}
			So(s.portScannerExtension.Scan(request, nil), ShouldBeNil)
		})
		Convey("test resume", func() {
			var expect = "CALL RESUME"
			defer gomonkey.ApplyMethodReturn(&ScanTask{}, "Scan", errors.New(expect)).Reset()
			var t = uint32(task.ETNmap)
			var taskType = &t
			request := &proto.PortScannerScanRequest{
				HostInfos: []string{"127.0.0.1"},
				TaskType:  taskType,
				Option: &proto.PortScannerScanOption{
					Cursor: &proto.ScanCursor{
						Cursor: &proto.ScanCursor_Offset{
							Offset: 123456789,
						},
					},
				},
			}
			So(s.portScannerExtension.Scan(request, nil), ShouldBeError)
		})
	})
}

func (s *PluginSuite) Test_portScannerExtension_Stop() {
	Convey("Test_Stop", s.T(), func() {
		Convey("engine nil", func() {
			So(s.portScannerExtension.Stop(), ShouldBeNil)
		})
		Convey("normal stop", func() {
			s.portScannerExtension.currentTask = NewScanTask(0, "localhost", getConfig(), getRequest(), nil)
			//defer gomonkey.ApplyMethodReturn(&ScanTask{}, "Stop", uint64(12345678), nil).Reset()
			So(s.portScannerExtension.Stop(), ShouldNotBeNil)
		})
	})
}
