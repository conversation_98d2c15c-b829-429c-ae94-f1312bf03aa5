package plugin

import (
	"baimaohui/portscan_new/internal/task"
	"fmt"
	"log"
	"os"
	"strconv"
)

const DefaultPauseFileTemplate = "/tmp/engine_paused_%d.conf"
const DefaultResumeFileTemplate = "/tmp/engine_resume_%d.conf"

type ScannerEngine interface {
	Scan() error
	Resume(string) error
	Stop() (uint64, error)
	CurrentState() string
	SetState(string)
}

type BaseScannerEngine struct {
	taskID       uint64
	rate         string
	hosts        string
	ports        string
	blackIps     string
	sendEth      string
	retries      int
	stub         task.PortscanStub
	currentState string
	task         *ScanTask
}

func NewScannerEngine(scanTask *ScanTask, taskID uint64, rate, hosts, ports, blackIps, sendEth string, retries int, stub task.PortscanStub) *BaseScannerEngine {
	engine := &BaseScannerEngine{
		taskID:       taskID,
		rate:         rate,
		hosts:        hosts,
		ports:        ports,
		blackIps:     blackIps,
		sendEth:      sendEth,
		retries:      retries,
		stub:         stub,
		currentState: StateInitial,
		task:         scanTask,
	}
	return engine
}

func (e *BaseScannerEngine) SetStub(stub task.PortscanStub) {
	e.stub = stub
}

func (e *BaseScannerEngine) doScan() error {
	log.Printf("do scan\n")
	// 执行扫描请求
	ch, err := e.stub.Run(e.rate, e.hosts, e.ports, e.blackIps, e.sendEth, "", int(e.taskID), e.retries)
	if err != nil {
		e.currentState = StateFinished
		return err
	}
	// 处理请求结果
	err = e.processOutput(ch)
	if err != nil {
		log.Printf("process output error.%+v\n", err)
	}
	if e.currentState == StateStopping {
		// 由于调用了Stop后返回的
		e.currentState = StateStopped
	} else {
		// 正常扫描完成
		e.currentState = StateFinished
	}
	return nil
}

func (e *BaseScannerEngine) doResume(pausedFile string) error {
	log.Printf("do resume\n")
	// 为了让stop生成的文件和--resume的文件不一样，所以新弄了个文件
	resumeFile := fmt.Sprintf(DefaultResumeFileTemplate, e.taskID)
	err := os.Rename(pausedFile, resumeFile)
	if err != nil {
		log.Printf("rename paused file failed. err=%v\n", err)
		//如果更改失败，还是用原来的文件吧
		resumeFile = pausedFile
	}
	ch, err := e.stub.HandleResume(resumeFile, e.blackIps, int(e.taskID))
	if err != nil {
		log.Printf("resume failed. err=%v\n", err)
		e.currentState = StateFinished
		return err
	}
	// 处理请求结果
	err = e.processOutput(ch)
	if err != nil {
		log.Printf("process output error.%+v\n", err)
	}

	// 删除恢复文件
	_ = os.Remove(resumeFile)

	if e.currentState == StateStopping {
		// 由于调用了Stop后返回的
		e.currentState = StateStopped
	} else {
		// 正常扫描完成
		e.currentState = StateFinished
	}
	return nil
}

func (e *BaseScannerEngine) Scan() error {
	if e.task.state == StateStopping || e.task.state == StateStopped {
		log.Printf("task is stopped. exit scan.\n")
		// 如果任务已经停止了，则放弃扫描
		return nil
	}

	if e.currentState != StateInitial {
		return nil
	}
	e.currentState = StateRunning
	return e.doScan()
}

func (e *BaseScannerEngine) Resume(resumeFile string) error {
	if e.task.state == StateStopping || e.task.state == StateStopped {
		log.Printf("task is stopped. exit resume.\n")
		// 如果任务已经停止了，则放弃恢复
		return nil
	}

	if e.currentState == StateInitial || e.currentState == StateCanceled {
		e.currentState = StateRunning
		return e.doScan()
	}

	if e.currentState == StateStopped {
		e.currentState = StateRunning
		_, err := os.Stat(resumeFile)
		if os.IsNotExist(err) {
			// 如果传入的恢复文件不存在，则直接再扫描一遍
			return e.doScan()
		}
		// 执行恢复
		return e.doResume(resumeFile)
	}

	return nil
}

func (e *BaseScannerEngine) Stop() (uint64, error) {
	log.Printf("call engine stop")
	switch e.currentState {
	case StateInitial:
		e.currentState = StateCanceled
		return 0, nil
	case StateRunning:
		return e.doStop()
	default:
		return 0, nil
	}
}

func (e *BaseScannerEngine) doStop() (uint64, error) {
	e.currentState = StateStopping
	err := e.stub.Stop()
	if err != nil {
		log.Printf("call scan stop failed.err=%v\n", err)
		return 0, err
	}

	// 修改暂停文件位置
	if e.stub.ResumeFileExists() {
		pausedFile := e.stub.GetPausedFile()
		templatePath := fmt.Sprintf(DefaultPauseFileTemplate, e.taskID)
		return e.taskID, os.Rename(pausedFile, templatePath)
	}

	return 0, nil
}

func (e *BaseScannerEngine) processOutput(ch <-chan [][4]string) error {
	for hostPorts := range ch {
		if hostPorts == nil {
			break
		}
		// 每个元素内容是：ip/port/taskid/base_protocol
		for _, v := range hostPorts {
			port, err := strconv.Atoi(v[1])
			if err != nil {
				continue
			}
			e.task.OnResult(v[0], uint32(port), v[3])
		}
	}
	return nil
}

func (e *BaseScannerEngine) CurrentState() string {
	return e.currentState
}

func (e *BaseScannerEngine) SetState(state string) {
	e.currentState = state
}
