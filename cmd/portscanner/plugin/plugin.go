package plugin

import (
	"baimaohui/portscan_new/cmd/portscanner/config"
	"fmt"
	"log"
	"os/exec"

	"git.gobies.org/gobase/plugin/pkg/plugin/proxy"
	"git.gobies.org/gobase/plugin/pkg/plugin/share"
	pluginPortScanner "git.gobies.org/gobase/plugin/pkg/plugin/share/portscanner"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/proto"
	"github.com/hashicorp/go-plugin"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/anypb"
)

const (
	StateInitial  = "Initial"
	StateRunning  = "Running"
	StateFinished = "Finished"
	StateStopping = "Stopping"
	StateStopped  = "Stopped"
	StateCanceled = "Canceled"
)

var (
	extension = &portScannerExtension{}
)

type portScannerExtension struct {
	client        *plugin.Client
	defaultConfig *config.Config
	hostname      string
	currentTask   *ScanTask
	scanTasks     map[uint64]*ScanTask
}

func (p *portScannerExtension) Scan(request *proto.PortScannerScanRequest, cb pluginPortScanner.Callback) (err error) {
	log.Println("scan targets", request)
	if len(request.GetHostInfos()) <= 0 {
		return errors.New("empty targets.")
	}
	scanOffset := request.GetOption().GetCursor().GetOffset()
	if scanTask, ok := p.scanTasks[scanOffset]; ok {
		p.currentTask = scanTask
		p.currentTask.request = request
		p.currentTask.callback = cb
		location := fmt.Sprintf(DefaultPauseFileTemplate, scanOffset)
		err = p.currentTask.Resume(location)

		// 如果正常结束，删除缓存
		if p.currentTask.state == StateFinished {
			delete(p.scanTasks, scanOffset)
		}
	} else {
		p.currentTask = NewScanTask(scanOffset, p.hostname, p.defaultConfig, request, cb)
		err = p.currentTask.Scan()
	}
	return
}

func (p *portScannerExtension) Stop() *proto.ScanCursor {
	if p.currentTask == nil {
		log.Println("can't find the current task.")
		return nil
	}
	offset, err := p.currentTask.Stop()
	if err != nil {
		log.Printf("stop err. %+v", err)
		return nil
	}
	// 缓存起来，等待下次恢复处理
	p.scanTasks[offset] = p.currentTask
	return &proto.ScanCursor{Cursor: &proto.ScanCursor_Offset{Offset: offset}}
}

func RunPlugin(cmdPlugin string, config *config.Config, hostname string) error {
	extension.defaultConfig = config
	extension.hostname = hostname
	extension.scanTasks = map[uint64]*ScanTask{}
	return extension.initExtension(cmdPlugin)
}

func (p *portScannerExtension) initExtension(cmdPlugin string) error {

	// We're a host. Start by launching the pluginInstance process.
	client := plugin.NewClient(&plugin.ClientConfig{
		HandshakeConfig:  share.Handshake,
		Plugins:          proxy.ExtensionFactoryMap,
		Cmd:              exec.Command(cmdPlugin),
		AllowedProtocols: []plugin.Protocol{plugin.ProtocolGRPC},
	})
	p.client = client

	// Connect via RPC
	rpcClient, err := client.Client()
	if err != nil {
		fmt.Println("Error:", err.Error())
		return err
	}

	// Request the pluginInstance;
	raw, err := rpcClient.Dispense(proxy.ExtensionFactoryNamePlugin)
	if err != nil {
		fmt.Println("Error:", err.Error())
		return err
	}

	// We should have a Counter store now! This feels like a normal interface
	// implementation but is in fact over an RPC connection.
	pluginInstance := raw.(share.Plugin)
	info, err := pluginInstance.Init()
	if err != nil {
		return err
	}
	switch info.ExtensionType {
	case proto.PluginExtensionType_PluginPortScanner:
		protoInfo := proto.PortScannerPluginDescription{}
		if err := info.Extension.(*anypb.Any).UnmarshalTo(&protoInfo); err != nil {
			return errors.Wrapf(err, "extension (type=%d) can not covert to PluginInfo (%v)", info.ExtensionType, info.Extension)
		}

		//to ready
		raw, err := rpcClient.Dispense(proxy.ExtensionFactoryNamePortScannerPlugin)
		if err != nil {
			return errors.Wrapf(err, "can not Dispense the rpc of udp(%v)", info)
		}
		portScanner := raw.(pluginPortScanner.PortScannerPlugin)
		err = portScanner.Ready(p)
		if err != nil {
			fmt.Println(err.Error())
		}
		return err

	default:
		panic(fmt.Sprintf("unSupport the ExtensionType (%d)", info.ExtensionType))
	}
}
