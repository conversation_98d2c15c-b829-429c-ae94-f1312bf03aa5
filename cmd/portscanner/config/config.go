package config

type NmapConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type MasscanConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type WorkerConfig struct {
	BatchSize      int
	SameIpMaxCount int
	SendEth        string
	ReserveInnerIp bool
}

type Config struct {
	Nmap    *NmapConfig
	Masscan *MasscanConfig
	Worker  *WorkerConfig
}

func NewConfig() *Config {
	return &Config{
		Nmap:    &NmapConfig{},
		Masscan: &MasscanConfig{},
		Worker:  &WorkerConfig{},
	}
}
