Service:
  Id: "" # 注册服务id，空时系统默认生成

Nmap:
  Name: "nmap" # namp进程名称
  Directory: "/usr/bin" # nmap 可执行程序路径
  SourcePort: 58914 # 扫描源端口
  BlackList: "" # 默认黑名单，字符串cidr，多个使用逗号分隔

Masscan:
  Name: "masscan" # masscan进程
  Directory: "/usr/bin" # masscan 可执行程序路径
  SourcePort: 58924  # 扫描源端口
  BlackList: "" # 默认黑名单，字符串cidr，多个使用逗号分隔

Worker:
  BatchSize: 1 # 批次大小，达到一定大小后，往后流转，1表示1个存活端口
  SameIpMaxCount: 100 # 单ip最大扫描存活端口数
  SendEth: "" # 发包网卡名称
  ReserveInnerIp: true # 扫描结果是否保留内网ip，默认true，保留，false不保留
  ResultToDispatcher: false # portscan结果流转对象，默认false流转到rawgrab，true 流转到dispatcher
