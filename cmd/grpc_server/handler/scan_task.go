package handler

import (
	"baimaohui/portscan_new/cmd/grpc_server/config"
	"baimaohui/portscan_new/internal/task"
	"context"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/logger"
	"log"
	"os"
	"strconv"
	"strings"
)

type ScanTask struct {
	hostName      string
	stub          task.PortscanStub
	config        *config.Config
	task          *rpcx.TaskEvent
	portScanner   *PortScanner
	lastProgress  float64
	bindProtocols map[string][]string // {"443":["https","mysql"]}
}

func NewScanTask(hostName string, config *config.Config, portScanner *PortScanner, task *rpcx.TaskEvent) *ScanTask {
	scanTask := &ScanTask{
		task:          task,
		hostName:      hostName,
		config:        config,
		portScanner:   portScanner,
		bindProtocols: make(map[string][]string),
	}
	scanTask.stub = getStub(scanTask)
	return scanTask
}

/*
*
由于有些条件需要特殊处理，比如IpV6目前只能是nmap
*/
func getDefaultTaskType(t *ScanTask) task.EngineType {
	// 如果是IPv6，只使用NMAP
	if *t.task.Options.IsIpv6 {
		return task.ETNmap
	}

	if t.task.Options.GetTreckScan() {
		return task.ETMasscan
	}

	if t.task.TaskType == uint32(task.ETMasscan) {
		return task.ETMasscan
	} else if t.task.TaskType == uint32(task.ETNmap) {
		return task.ETNmap
	}

	return task.ETMasscan
}

func getStub(t *ScanTask) task.PortscanStub {
	dir := getJobDir(t.task.JobId)
	if err := os.Mkdir(dir, os.ModePerm); err != nil {
		log.Printf("mkdir failed.%+v\n", dir)
	}

	var taskType = getDefaultTaskType(t)

	var pingScan = t.task.Options.GetPingScan()

	if taskType == task.ETMasscan {
		if pingScan {
			// 改用新的Masscan与Ping同一命令的
			return getMasscanStub(t, task.ETMasscanPing, dir)
		}
		return getMasscanStub(t, task.ETMasscan, dir)
	} else if taskType == task.ETNmap {
		if pingScan {
			// 改用新的Nmap与Ping同一命令的
			return getNmapStub(t, task.ETNmapPing, dir)
		}
		return getNmapStub(t, task.ETNmap, dir)
	}

	// 默认masscan
	return getMasscanStub(t, task.ETMasscan, dir)
}

func getMasscanStub(t *ScanTask, engineType task.EngineType, dir string) task.PortscanStub {
	taskBaseCfg := task.NewScantaskBaseConf(
		engineType,
		dir,
		t.hostName,
		t.config.Masscan.BlackList,
		t.config.Masscan.SourcePort,
		t.config.Worker.BatchSize,
		t.config.Worker.SameIpMaxCount,
		true,
		t.config.Worker.ReserveInnerIp,
		false)

	taskBaseCfg.SetRouterMac(t.task.Options.GetGatewayMac())
	taskBaseCfg.SetScanEngine(engineType)
	stub := task.NewScantask(taskBaseCfg)
	stub.SetScanIPv6(t.task.Options.GetIsIpv6())
	stub.SetCallback(nil, t.OnProgress, nil)
	return stub
}

func getNmapStub(t *ScanTask, engineType task.EngineType, dir string) task.PortscanStub {
	taskBaseCfg := task.NewScantaskBaseConf(
		engineType,
		dir,
		t.hostName,
		t.config.Nmap.BlackList,
		t.config.Nmap.SourcePort,
		t.config.Worker.BatchSize,
		t.config.Worker.SameIpMaxCount,
		true,
		t.config.Worker.ReserveInnerIp,
		false)

	taskBaseCfg.SetRouterMac(t.task.Options.GetGatewayMac())
	taskBaseCfg.SetScanEngine(engineType)
	stub := task.NewScantask(taskBaseCfg)
	stub.SetScanIPv6(t.task.Options.GetIsIpv6())
	stub.SetCallback(nil, t.OnProgress, nil)
	return stub
}

// 自定义协议必须带前缀 T:123:mysql
func (t *ScanTask) parseOnePort(port string) (string, string) {
	var retPort, bindProtocol string

	tmpArr := strings.Split(port, ":")
	if len(tmpArr) == 1 {
		// tcp默认端口
		retPort = tmpArr[0]
	} else if len(tmpArr) == 2 {
		// udp默认端口
		retPort = port
	} else {
		if tmpArr[0] == "U" {
			retPort = tmpArr[0] + ":" + tmpArr[1]
		} else {
			retPort = tmpArr[1]
		}
		bindProtocol = tmpArr[2]
	}

	return retPort, bindProtocol
}

func (t *ScanTask) parsePorts(portAttr []string) string {
	retPorts := ""

	for _, val := range portAttr {
		tmpPort, bindProtocol := t.parseOnePort(val)
		if len(tmpPort) > 0 {
			retPorts += tmpPort + ","
		}

		if len(bindProtocol) > 0 {
			t.bindProtocols[tmpPort] = append(t.bindProtocols[tmpPort], bindProtocol)
		}
	}

	// 如果有TCP端口，加上T:，解决nmap在tcp端口上也扫描了udp的问题
	portsCount := strings.Count(retPorts, ",")
	udpCount := strings.Count(retPorts, "U:")
	if portsCount > udpCount && !strings.HasPrefix(retPorts, "T:") {
		retPorts = "T:" + retPorts
	}

	return retPorts
}

func (t *ScanTask) Scan() error {
	tid, _ := strconv.Atoi(t.task.JobId)

	blackFile, _ := createBlackListFile(t.task.JobId, strings.Split(*t.task.Options.Blacklist, ","))

	var ch <-chan [][4]string
	var err error
	if len(t.task.GetHostInfos()) > 100 {
		targetFile, _ := createTargetsFile(t.task.JobId, t.task.GetHostInfos())
		ch, err = t.stub.RunFile(
			strconv.Itoa(int(t.task.Options.GetRate())),
			targetFile,
			t.parsePorts(t.task.GetPorts()),
			"",
			t.task.Options.GetSendEth(), //SendEth
			blackFile,
			tid,
			int(t.task.Options.GetRetries()),
		)
	} else {
		ch, err = t.stub.Run(
			strconv.Itoa(int(t.task.Options.GetRate())),
			strings.Join(t.task.GetHostInfos(), ","),
			t.parsePorts(t.task.GetPorts()),
			"",
			t.task.Options.GetSendEth(), //SendEth
			blackFile,
			tid,
			int(t.task.Options.GetRetries()),
		)
	}

	if err != nil {
		log.Printf("run error.%+v\n", err)
		return err
	}
	go func() {
		// 处理请求结果
		err = t.processOutput(ch)
		if err != nil {
			log.Printf("process output error.%+v\n", err)
		}
		// 如果是正常完成，则回调已完成
		if t.stub.IsFinished() {
			t.portScanner.OnFinish(context.Background(), t.task.TaskId, t.task.JobId)
		}
	}()
	return nil
}

func (t *ScanTask) Resume(location string, rate *int32) error {
	tid, _ := strconv.Atoi(t.task.JobId)
	if location == "" {
		location = t.stub.GetPausedFile()
	}

	if rate != nil {
		logger.Infof("location:%s", location)
		// 如果修改扫描参数
		err := changeRate(location, t.task.TaskType, rate)
		if err != nil {
			logger.Warnf("change rate failed. task_id=%s job_id=%s", t.task.TaskId, t.task.JobId)
			return err
		}
	}

	ch, err := t.stub.HandleResume(location, t.task.Options.GetBlacklist(), tid)
	if err != nil {
		log.Printf("resume error.%+v\n", err)
		return err
	}
	go func() {
		// 处理请求结果
		err = t.processOutput(ch)
		if err != nil {
			log.Printf("process output error.%+v\n", err)
		}
		// 如果是正常完成，则回调已完成
		if t.stub.IsFinished() {
			t.portScanner.OnFinish(context.Background(), t.task.TaskId, t.task.JobId)
		}
	}()
	return nil
}

func (t *ScanTask) Stop() error {
	return t.stub.Stop()
}

func (t *ScanTask) OnProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {
	log.Printf("engine: %v task:%d state:%s %s %s %s %s", engine, tid, state, progress, hostCompleted, remainTime, errMsg)
	if state == "6" {
		// 只处理正在运行的进度
		t.portScanner.OnError(context.Background(), t.task.TaskId, t.task.JobId, errMsg)
		return
	}
	if strings.HasSuffix(progress, "%") {
		progress = progress[:len(progress)-1]
	}
	p, _ := strconv.ParseFloat(progress, 64)
	if p < t.lastProgress {
		return
	}
	t.lastProgress = p
	t.portScanner.OnProgress(context.Background(), t.task.TaskId, t.task.JobId, remainTime, p)
}

func (t *ScanTask) processOutput(ch <-chan [][4]string) error {
	for hostPorts := range ch {
		if hostPorts == nil {
			break
		}
		// 每个元素内容是：ip/port/taskid/base_protocol
		for _, v := range hostPorts {
			port, err := strconv.Atoi(v[1])
			if err != nil {
				continue
			}

			if protocols, ok := t.bindProtocols[v[1]]; ok {
				for _, p := range protocols {
					t.portScanner.OnResult(context.Background(), t.task.TaskId, t.task.JobId, v[0], uint32(port), v[3], p)
				}
			} else {
				t.portScanner.OnResult(context.Background(), t.task.TaskId, t.task.JobId, v[0], uint32(port), v[3], "")
			}

		}
	}
	return nil
}
