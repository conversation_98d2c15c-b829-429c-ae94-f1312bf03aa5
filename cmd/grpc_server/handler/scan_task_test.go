package handler

import (
	"baimaohui/portscan_new/cmd/grpc_server/config"
	"baimaohui/portscan_new/internal/task"
	"context"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/agiledragon/gomonkey/v2"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"testing"
)

type ScanTaskSuite struct {
	suite.Suite
	scanTask *ScanTask
	Context  context.Context
}

func Test_ScanTaskSuite(t *testing.T) {
	s := &ScanTaskSuite{}
	suite.Run(t, s)
}

func (s *ScanTaskSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	s.Context = context.Background()
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	cc := config.ReadConfig()
	portScanner := NewPortScanner(srv, cc, "")
	var isIPv6 bool
	var blackList string
	s.scanTask = NewScanTask("", cc, portScanner, &pb.TaskEvent{
		Command:    constant.CommandStart,
		TaskId:     "taskID",
		JobId:      "jobID",
		TaskType:   constant.TaskTypeQuick,
		Ports:      []string{"22", "80", "443"},
		HostInfos:  []string{"127.0.0.1"},
		Options:    &pb.TaskOption{IsIpv6: &isIPv6, Blacklist: &blackList},
		Parameters: &pb.TransparentParameters{},
	})
}

func (s *ScanTaskSuite) Test_getStub() {
	Convey("Test_getStub", s.T(), func() {
		Convey("default masscan", func() {
			stab := getStub(s.scanTask)
			_, ok := stab.(*task.Masscan)
			So(ok, ShouldBeTrue)
		})
		Convey("masscan with ping", func() {
			var isIPv6 = true
			s.scanTask.task.Options.PingScan = &isIPv6
			stab := getStub(s.scanTask)
			_, ok := stab.(*task.Masscan)
			So(ok, ShouldBeTrue)
		})
		Convey("nmap with ping", func() {
			var pingScan = true
			s.scanTask.task.TaskType = uint32(task.ETNmap)
			s.scanTask.task.Options.PingScan = &pingScan
			stab := getStub(s.scanTask)
			_, ok := stab.(*task.NmapScan)
			So(ok, ShouldBeTrue)
		})
	})
}

func (s *ScanTaskSuite) Test_parsePorts() {
	Convey("Test_parsePorts", s.T(), func() {
		Convey("parse port 1", func() {
			ret := s.scanTask.parsePorts([]string{"T:22", "T:34", "U:161", "U:5353:MDNS"})
			So(ret, ShouldEqual, "T:22,T:34,U:161,U:5353,")
		})

		Convey("parse port 2", func() {
			ret := s.scanTask.parsePorts([]string{"22", "T:443:HTTPS", "U:161", "U:5353:MDNS"})
			So(ret, ShouldEqual, "T:22,443,U:161,U:5353,")
		})

		Convey("parse port 3", func() {
			ret := s.scanTask.parsePorts([]string{"U:123", "U:500"})
			So(ret, ShouldEqual, "U:123,U:500,")
		})

		Convey("parse port 4", func() {
			ret, bindProtocol := s.scanTask.parseOnePort("U:123")
			So(ret, ShouldEqual, "U:123")
			So(bindProtocol, ShouldEqual, "")
		})

		Convey("parse port 5", func() {
			ret, bindProtocol := s.scanTask.parseOnePort("U:123:ssh")
			So(ret, ShouldEqual, "U:123")
			So(bindProtocol, ShouldEqual, "ssh")
		})

		Convey("parse port 6", func() {
			ret, bindProtocol := s.scanTask.parseOnePort("T:123:mysql")
			So(ret, ShouldEqual, "123")
			So(bindProtocol, ShouldEqual, "mysql")
		})
	})
}

func (s *ScanTaskSuite) Test_Scan() {
	Convey("Test_Scan", s.T(), func() {
		Convey("scan failed", func() {
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "Run", ch, errors.New("scan failed")).Reset()
			err := s.scanTask.Scan()
			So(err, ShouldNotBeNil)
		})

		Convey("normal scan", func() {
			var targets []string
			for i := 0; i < 101; i++ {
				target := fmt.Sprintf("192.168.100.%d", i+1)
				targets = append(targets, target)
			}
			s.scanTask.task.HostInfos = targets
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "RunFile", ch, nil).Reset()
			err := s.scanTask.Scan()
			//ch <- [][4]string{{"127.0.0.1", "22", "0", "tcp"}}
			//ch <- nil
			So(err, ShouldBeNil)
		})
	})
}

func (s *ScanTaskSuite) Test_Resume() {
	Convey("Test_Resume", s.T(), func() {
		Convey("resume failed", func() {
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "HandleResume", ch, errors.New("resume failed")).Reset()
			err := s.scanTask.Resume("", nil)
			So(err, ShouldNotBeNil)
		})

		Convey("normal resume", func() {
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "HandleResume", ch, nil).Reset()
			err := s.scanTask.Resume("", nil)
			//ch <- [][4]string{{"127.0.0.1", "22", "0", "tcp"}}
			//ch <- nil
			So(err, ShouldBeNil)
		})

		Convey("normal resume with rate", func() {
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "HandleResume", ch, nil).Reset()
			var rate int32 = 30
			err := s.scanTask.Resume("", &rate)
			So(err, ShouldNotBeNil)
		})

		Convey("normal resume with rate success", func() {
			var ch chan [][4]string
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "HandleResume", ch, nil).Reset()
			defer gomonkey.ApplyFuncReturn(changeRate, nil).Reset()
			var rate int32 = 30
			err := s.scanTask.Resume("", &rate)
			So(err, ShouldBeNil)
		})
	})
}

func (s *ScanTaskSuite) Test_Stop() {
	Convey("Test_Stop", s.T(), func() {
		Convey("stop failed", func() {
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "Stop", errors.New("stop failed")).Reset()
			err := s.scanTask.Stop()
			So(err, ShouldNotBeNil)
		})

		Convey("normal stop", func() {
			defer gomonkey.ApplyMethodReturn(s.scanTask.stub, "Stop", nil).Reset()
			err := s.scanTask.Stop()
			So(err, ShouldBeNil)
		})
	})
}

func (s *ScanTaskSuite) Test_OnProgress() {
	Convey("Test_OnProgress", s.T(), func() {
		Convey("no progress 1", func() {
			defer gomonkey.ApplyMethodReturn(s.scanTask.portScanner, "OnError").Reset()
			s.scanTask.OnProgress(constant.TaskTypeQuick, 0, "6", "", "50.0", "00:00:00", "")
		})

		Convey("no progress 2", func() {
			defer gomonkey.ApplyMethodReturn(s.scanTask.portScanner, "OnProgress").Reset()
			s.scanTask.OnProgress(constant.TaskTypeQuick, 0, "2", "", "50.0%", "00:00:00", "")
		})
	})
}
