package handler

import (
	"baimaohui/portscan_new/cmd/grpc_server/config"
	"context"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"strings"
	"sync"
)

type PortScanner struct {
	hostName string
	c        *config.Config
	srv      micro.Service
	tasks    map[string]map[string]*Job
	mutex    sync.Mutex
}

type Job struct {
	ID         string
	State      string
	Progress   float32
	WorkingOn  string
	TaskType   int32
	Parameters *pb.TransparentParameters // 透传参数
	Engine     *ScanTask
}

func NewPortScanner(srv micro.Service, c *config.Config, hostName string) *PortScanner {
	return &PortScanner{
		hostName: hostName,
		srv:      srv,
		c:        c,
		tasks:    make(map[string]map[string]*Job),
	}
}

func (p *PortScanner) TaskEventHandler(ctx context.Context, event *pb.TaskEvent) error {
	logger.Infof("received start task event. task:%s job:%s event:%v", event.TaskId, event.JobId, event)
	var err error
	switch event.Command {
	case constant.CommandStart:
		err = p.Start(ctx, event)
	case constant.CommandPause:
		// 执行暂停操作
		err = p.Pause(ctx, event.TaskId, event.JobId)
	case constant.CommandResume:
		// 执行恢复操作
		err = p.Resume(ctx, event.TaskId, event.JobId, event.Options.Rate)
	case constant.CommandStop:
		// 执行停止操作
		err = p.Stop(ctx, event.TaskId, event.JobId)
	default:
		return errors.New(fmt.Sprintf("invalid command type:%s", event.Command))
	}

	if err != nil {
		logger.Errorf("execute command failed. event:%+v caused by %s", event, err)
	}
	return err

}

func (p *PortScanner) TaskStateNotify(ctx context.Context, taskID, jobID, state, remainTime, message string, progress float32) error {
	logger.Infof("notify state. task:%s job:%s state:%s message:%s remainTime:%s progress:%.2f", taskID, jobID, state, message, remainTime, progress)
	var workingOn string
	if job := p.getJob(ctx, taskID, jobID); job != nil {
		workingOn = job.WorkingOn
	}

	err := p.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceDispatcher, &pb.TaskNotify{
		Node: &pb.ServiceNode{
			ServiceId:      p.srv.Server().Options().Id,
			ServiceName:    p.srv.Server().Options().Name,
			ServiceVersion: p.srv.Server().Options().Version,
		},
		TaskId: taskID,
		JobId:  jobID,
		Data: &pb.TaskNotify_State{
			State: &pb.StateNotify{
				State:      state,
				Progress:   progress,
				WorkingOn:  workingOn,
				RemainTime: remainTime,
				Message:    message,
			},
		},
	}))
	if err != nil {
		logger.Errorf("task state notify failed. task:%s job:%s state:%s progress:%.2f.caused by %s", taskID, jobID, state, progress, err)
	}
	return err
}

func (p *PortScanner) TaskResultNotify(ctx context.Context, taskID, jobID, ip string, port uint32, baseProtocol, bindProtocol string) error {
	if job := p.getJob(ctx, taskID, jobID); job != nil {
		job.WorkingOn = ip

		return p.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceDispatcher, &pb.TaskNotify{
			TaskId: taskID,
			JobId:  jobID,
			Data: &pb.TaskNotify_Result{
				Result: &pb.ResultNotify{
					Ip:           ip,
					Port:         port,
					BaseProtocol: baseProtocol,
					BindProtocol: bindProtocol,
				},
			},
		}))
	}
	return nil
}

func (p *PortScanner) ScannerResultNotify(ctx context.Context, taskID, jobID, ip string, port uint32, baseProtocol, bindProtocol string) error {
	if job := p.getJob(ctx, taskID, jobID); job != nil {
		job.WorkingOn = ip

		if strings.EqualFold(baseProtocol, "icmp") || job.TaskType == constant.TaskTypePing {
			return p.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceQuickStore, &pb.QuickStoreEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId:              taskID,
					IsIpv6:              job.Parameters.IsIpv6,
					TaskType:            job.TaskType,
					UnknownProtocolInDb: job.Parameters.UnknownProtocolIndb,
					IsCrawlerAllUrl:     job.Parameters.CrawlerAllUrl,
					CrawlerSpecificUrl:  job.Parameters.CrawlerSpecificUrl,
					CrawlerUrlBlackKey:  job.Parameters.CrawlerUrlBlackKey,
				},
				JobId:  jobID,
				Origin: constant.ServicePortScanner,
				Data: &pb.QuickStoreEvent_Normal{
					Normal: &pb.Normal{
						Ip:           ip,
						Port:         port,
						BaseProtocol: baseProtocol,
					},
				},
			}))
		} else {
			return p.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceRawGrab, &pb.RawGrabEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId:              taskID,
					IsIpv6:              job.Parameters.IsIpv6,
					TaskType:            job.TaskType,
					UnknownProtocolInDb: job.Parameters.UnknownProtocolIndb,
					IsCrawlerAllUrl:     job.Parameters.CrawlerAllUrl,
					CrawlerSpecificUrl:  job.Parameters.CrawlerSpecificUrl,
					CrawlerUrlBlackKey:  job.Parameters.CrawlerUrlBlackKey,
				},
				JobId:        jobID,
				Origin:       constant.ServicePortScanner,
				BaseProtocol: baseProtocol,
				Ip:           ip,
				Port:         port,
				BindProtocol: bindProtocol,
			}))
		}
	}
	logger.Warnf("can't get job. task:%s job:%s result:%s %s:%d.", taskID, jobID, baseProtocol, ip, port)
	return nil
}

func (p *PortScanner) getJob(ctx context.Context, taskID, jobID string) *Job {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if _, exists := p.tasks[taskID]; !exists {
		// 如果任务不存在，可能任务在其他节点上运行，直接返回
		return nil
	}

	if _, exists := p.tasks[taskID][jobID]; !exists {
		// 如果任务不存在，可能任务在其他节点上运行，直接返回
		return nil
	}

	return p.tasks[taskID][jobID]
}

func (p *PortScanner) Pause(ctx context.Context, taskID, jobID string) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，可能是在其他机器上运行，直接返回
		_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateFinished, "", "", 50)
		return nil
	}

	// 只有正在运行的调用暂停
	if job.State == constant.StateRunning {
		job.State = constant.StatePausing

		err := job.Engine.Stop()
		if err != nil {
			logger.Errorf("pause task failed. task:%s job:%s", taskID, jobID)
			job.State = constant.StateError
			_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateError, "", err.Error(), 0)
			return err
		}

		job.State = constant.StatePaused
		_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StatePaused, "", "", job.Progress)
	}
	return nil
}

func (p *PortScanner) Resume(ctx context.Context, taskID, jobID string, rate *int32) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，直接返回
		_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateFinished, "", "", 100)
		return nil
	}

	// 只有状态为暂停的才恢复
	if job.State == constant.StatePaused {
		job.State = constant.StateResuming
		err := job.Engine.Resume("", rate)
		if err != nil {
			logger.Errorf("resume task failed. task:%s job:%s", taskID, jobID)

			// 如果恢复出错了，则直接完成状态，防止进入错误状态
			p.OnFinish(ctx, taskID, jobID)
			return nil
		}

		job.State = constant.StateRunning

		_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateRunning, "", "", job.Progress)
	}

	return nil
}

func (p *PortScanner) Stop(ctx context.Context, taskID, jobID string) error {
	// 查找缓存，找到相关任务信息
	job := p.getJob(ctx, taskID, jobID)
	if job == nil {
		// 没有找到，直接返回
		_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateStopped, "", "", 0)
		return nil
	}

	job.State = constant.StateStopping

	err := job.Engine.Stop()
	if err != nil {
		logger.Errorf("stop task failed. task:%s job:%s", taskID, jobID)
	}

	job.State = constant.StateStopped

	_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateStopped, "", "", job.Progress)

	// 删除Job信息
	p.clearTaskCache(ctx, taskID, jobID)
	return nil
}

func (p *PortScanner) Start(ctx context.Context, event *pb.TaskEvent) error {
	p.mutex.Lock()
	// 1、判断任务状态
	if jobs, exists := p.tasks[event.TaskId]; !exists {
		p.tasks[event.TaskId] = make(map[string]*Job)
		p.tasks[event.TaskId][event.JobId] = &Job{
			ID:         event.JobId,
			State:      constant.StateInitial,
			Progress:   0,
			TaskType:   int32(event.TaskType),
			Parameters: event.Parameters,
		}
	} else {
		if _, ok := jobs[event.JobId]; !ok {
			jobs[event.JobId] = &Job{
				ID:         event.JobId,
				State:      constant.StateInitial,
				Progress:   0,
				TaskType:   int32(event.TaskType),
				Parameters: event.Parameters,
			}
		}
	}
	p.mutex.Unlock()

	// 2、获得任务状态
	job := p.getJob(ctx, event.TaskId, event.JobId)

	if job.State != constant.StateInitial && job.State != constant.StatePaused {
		return errors.New(fmt.Sprintf("invalid state.task:%s job:%s state:%s", event.TaskId, event.JobId, job.State))
	}

	job.Engine = NewScanTask(p.hostName, p.c, p, event)
	err := job.Engine.Scan()
	if err != nil {
		logger.Errorf("start scan failed. event:%+v.caused by %s", event, err)
		// TODO
		return err
	}

	// 设置任务状态
	job.State = constant.StateRunning

	// 回调Dispatcher，通知状态
	return p.TaskStateNotify(ctx, event.TaskId, event.JobId, constant.StateRunning, "", "", job.Progress)
}

func (p *PortScanner) OnFinish(ctx context.Context, taskID, jobID string) {
	logger.Infof("scan finished. task:%s job:%s", taskID, jobID)

	_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateFinished, "00:00:00", "", 100)
	// 删除Job信息
	p.clearTaskCache(ctx, taskID, jobID)
}

func (p *PortScanner) OnResult(ctx context.Context, taskID, jobID, ip string, port uint32, baseProtocol, bindProtocol string) {
	logger.Infof("on result.task:%s job:%s result:%s %s:%d %s", taskID, jobID, baseProtocol, ip, port, bindProtocol)

	var err error
	if p.c.Worker.ResultToDispatcher {
		err = p.TaskResultNotify(ctx, taskID, jobID, ip, port, baseProtocol, bindProtocol)
	} else {
		err = p.ScannerResultNotify(ctx, taskID, jobID, ip, port, baseProtocol, bindProtocol)
	}
	if err != nil {
		logger.Errorf("task result notify failed. task:%s job:%s result:%s %s:%d. caused by %s", taskID, jobID, baseProtocol, ip, port, err)
	}
}

func (p *PortScanner) OnProgress(ctx context.Context, taskID, jobID, remainTime string, progress float64) {
	logger.Infof("on progress.task:%s job:%s remainTime:%s progress:%.2f", taskID, jobID, remainTime, progress)
	job := p.getJob(ctx, taskID, jobID)
	job.Progress = float32(progress)

	_ = p.TaskStateNotify(ctx, taskID, jobID, job.State, remainTime, "", job.Progress)
}

func (p *PortScanner) OnError(ctx context.Context, taskID, jobID, message string) {
	logger.Infof("on OnError.task:%s job:%s message: %s", taskID, jobID, message)
	job := p.getJob(ctx, taskID, jobID)
	job.State = constant.StateError

	_ = p.TaskStateNotify(ctx, taskID, jobID, constant.StateError, "", "", 0)
	// 删除Job信息
	p.clearTaskCache(ctx, taskID, jobID)
}

func (p *PortScanner) clearTaskCache(ctx context.Context, taskID, jobID string) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(p.tasks[taskID]) > 1 {
		delete(p.tasks[taskID], jobID)
	} else {
		delete(p.tasks, taskID)
	}

	removeJobDir(jobID)
}
