package handler

import (
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"go-micro.dev/v4/logger"
	"os"
	"os/exec"
	"strings"
)

const FileDirTemplate = "/tmp/%s/"
const FilePathTemplate = "/tmp/%s/%s"

func getJobDir(jobID string) string {
	return fmt.Sprintf(FileDirTemplate, jobID)
}

func removeJobDir(jobID string) {
	filePath := fmt.Sprintf(FileDirTemplate, jobID)
	removeDirectory(filePath)
}

func getBlackListFile(jobID string) string {
	return fmt.Sprintf(FilePathTemplate, jobID, "blacklist")
}

func getTargetsFile(jobID string) string {
	return fmt.Sprintf(FilePathTemplate, jobID, "targets")
}

func getPausedFile(jobID string) string {
	return fmt.Sprintf(FilePathTemplate, jobID, "paused.conf")
}

func existFile(filePath string) bool {
	_, err := os.Stat(filePath)
	if err == nil {
		return true
	}
	return false
}

func removeFile(filePath string) {
	_, err := os.Stat(filePath)
	if err == nil {
		_ = os.Remove(filePath)
	}

}

func removeDirectory(filePath string) {
	_, err := os.Stat(filePath)
	if err == nil {
		_ = os.RemoveAll(filePath)
	}

}

func createBlackListFile(jobID string, data []string) (string, error) {
	filePath := getBlackListFile(jobID)
	removeFile(filePath)
	return createFile(filePath, data)
}

func createTargetsFile(jobID string, data []string) (string, error) {
	filePath := getTargetsFile(jobID)
	removeFile(filePath)
	return createFile(filePath, data)
}

func createFile(filePath string, data []string) (string, error) {
	file, err := os.Create(filePath)
	if err != nil {
		logger.Info("can't create file：", filePath, err)
		return "", err
	}
	defer file.Close()

	content := strings.Join(data, "\n")

	_, err = file.WriteString(content)
	if err != nil {
		logger.Info("write failed. file：", filePath, err)
		return "", err
	}
	return filePath, nil
}

func changeRate(location string, scanType uint32, rate *int32) error {
	command := ""
	if scanType == constant.TaskTypeQuick {
		command = fmt.Sprintf(`sed -i -E "s/^.*rate.*$/rate = %d/" %s`,
			*rate,
			location,
		)
	} else {
		command = fmt.Sprintf(`sed -i -E "s/--min-rate [0-9]+ --max-rate [0-9]+/--min-rate %d --max-rate %d/" %s`,
			*rate,
			*rate,
			location,
		)
	}
	cmd := exec.Command("sh", "-c", command)
	return cmd.Run()
}
