package handler

import (
	"baimaohui/portscan_new/cmd/grpc_server/config"
	"context"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/agiledragon/gomonkey/v2"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"testing"
)

type PortScannerSuite struct {
	suite.Suite
	PortScanner *PortScanner
	Context     context.Context
}

func Test_PortScannerSuite(t *testing.T) {
	s := &PortScannerSuite{}
	suite.Run(t, s)
}

func (s *PortScannerSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	s.Context = context.Background()
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	var isIpv6 = false
	cc := config.ReadConfig()
	s.PortScanner = NewPortScanner(srv, cc, "")
	scanTask := NewScanTask("", cc, s.PortScanner, &pb.TaskEvent{
		Command:    constant.CommandStart,
		TaskId:     "taskID",
		JobId:      "jobID",
		TaskType:   constant.TaskTypeQuick,
		Ports:      []string{"22", "80", "443"},
		HostInfos:  []string{"127.0.0.1"},
		Options:    &pb.TaskOption{IsIpv6: &isIpv6},
		Parameters: &pb.TransparentParameters{},
	})
	stub := getStub(scanTask)
	scanTask.stub = stub
	s.PortScanner.tasks["taskID"] = map[string]*Job{"jobID": {WorkingOn: "WorkingOn", Parameters: &pb.TransparentParameters{}, Engine: scanTask}}
}

func (s *PortScannerSuite) Test_TaskEventHandler() {
	Convey("Test_TaskEventHandler", s.T(), func() {
		Convey("unknown command", func() {
			err := s.PortScanner.TaskEventHandler(context.Background(), &pb.TaskEvent{
				Command: "unknown",
			})
			So(err, ShouldNotBeNil)
		})

		Convey("start command", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner, "Start", errors.New("start failed")).Reset()
			err := s.PortScanner.TaskEventHandler(context.Background(), &pb.TaskEvent{
				Command:    constant.CommandStart,
				TaskId:     "taskID",
				JobId:      "jobID",
				TaskType:   constant.TaskTypeQuick,
				Ports:      []string{"22", "80", "443"},
				HostInfos:  []string{"127.0.0.1"},
				Options:    &pb.TaskOption{},
				Parameters: &pb.TransparentParameters{},
			})
			So(err, ShouldNotBeNil)
		})

		Convey("pause command", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner, "Pause", errors.New("pause failed")).Reset()
			err := s.PortScanner.TaskEventHandler(context.Background(), &pb.TaskEvent{
				Command: constant.CommandPause,
				TaskId:  "taskID",
			})
			So(err, ShouldNotBeNil)
		})

		Convey("resume command", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner, "Resume", errors.New("resume failed")).Reset()
			err := s.PortScanner.TaskEventHandler(context.Background(), &pb.TaskEvent{
				Command: constant.CommandResume,
				TaskId:  "taskID",
				Options: &pb.TaskOption{
					Rate: nil,
				},
			})
			So(err, ShouldNotBeNil)
		})

		Convey("stop command", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner, "Stop", errors.New("stop failed")).Reset()
			err := s.PortScanner.TaskEventHandler(context.Background(), &pb.TaskEvent{
				Command: constant.CommandStop,
				TaskId:  "taskID",
			})
			So(err, ShouldNotBeNil)
		})
	})
}

func (s *PortScannerSuite) Test_TaskStateNotify() {
	Convey("Test_TaskStateNotify", s.T(), func() {
		Convey("normal notify 1", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			err := s.PortScanner.TaskStateNotify(context.Background(), "taskID", "jobID", constant.StateRunning, "", "", 50.0)
			So(err, ShouldNotBeNil)
		})
		Convey("normal notify 2", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			err := s.PortScanner.TaskStateNotify(context.Background(), "taskID", "jobID2", constant.StateRunning, "", "", 50.0)
			So(err, ShouldNotBeNil)
		})
	})
}

func (s *PortScannerSuite) Test_TaskResultNotify() {
	Convey("Test_TaskResultNotify", s.T(), func() {
		Convey("normal notify", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			err := s.PortScanner.TaskResultNotify(context.Background(), "taskID", "jobID", "127.0.0.1", 22, "tcp", "")
			So(err, ShouldNotBeNil)
		})

		Convey("job not exist", func() {
			err := s.PortScanner.TaskResultNotify(context.Background(), "taskID", "notExistJobID", "127.0.0.1", 22, "tcp", "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *PortScannerSuite) Test_ScannerResultNotify() {
	Convey("Test_ScannerResultNotify", s.T(), func() {
		Convey("normal notify", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			err := s.PortScanner.ScannerResultNotify(context.Background(), "taskID", "jobID", "127.0.0.1", 22, "tcp", "")
			So(err, ShouldNotBeNil)
		})

		Convey("normal notify icmp", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			err := s.PortScanner.ScannerResultNotify(context.Background(), "taskID", "jobID", "127.0.0.1", 22, "icmp", "")
			So(err, ShouldNotBeNil)
		})

		Convey("job not exist", func() {
			err := s.PortScanner.ScannerResultNotify(context.Background(), "taskID", "notExistJobID", "127.0.0.1", 22, "tcp", "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *PortScannerSuite) Test_Pause() {
	Convey("Test_Pause", s.T(), func() {
		defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()

		Convey("job not exist", func() {
			err := s.PortScanner.Pause(context.Background(), "notExistTaskID", "jobID")
			So(err, ShouldBeNil)
		})

		Convey("state not running", func() {
			s.PortScanner.tasks["taskID"]["jobID"].State = constant.StatePausing
			err := s.PortScanner.Pause(context.Background(), "taskID", "jobID")
			So(err, ShouldBeNil)
		})

		Convey("normal pause", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Stop", errors.New("stop failed")).Reset()
			job.State = constant.StateRunning
			err := s.PortScanner.Pause(context.Background(), "taskID", "jobID")
			So(err, ShouldResemble, errors.New("stop failed"))
			So(job.State, ShouldEqual, constant.StateError)
		})
	})
}

func (s *PortScannerSuite) Test_Resume() {
	Convey("Test_Resume", s.T(), func() {
		defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()

		Convey("job not exist", func() {
			err := s.PortScanner.Resume(context.Background(), "notExistTaskID", "jobID", nil)
			So(err, ShouldBeNil)
		})

		Convey("state not paused", func() {
			s.PortScanner.tasks["taskID"]["jobID"].State = constant.StatePausing
			err := s.PortScanner.Resume(context.Background(), "taskID", "jobID", nil)
			So(err, ShouldBeNil)
		})

		Convey("normal resume", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Resume", nil).Reset()
			job.State = constant.StatePaused
			err := s.PortScanner.Resume(context.Background(), "taskID", "jobID", nil)
			So(err, ShouldBeNil)
			So(job.State, ShouldEqual, constant.StateRunning)
		})

		Convey("resume failed", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Resume", errors.New("resume failed")).Reset()
			job.State = constant.StatePaused
			err := s.PortScanner.Resume(context.Background(), "taskID", "jobID", nil)
			So(err, ShouldBeNil)
			So(s.PortScanner.tasks, ShouldNotContainKey, "taskID")
		})
	})
}

func (s *PortScannerSuite) Test_Stop() {
	Convey("Test_Stop", s.T(), func() {
		defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()

		Convey("job not exist", func() {
			err := s.PortScanner.Stop(context.Background(), "notExistTaskID", "jobID")
			So(err, ShouldBeNil)
		})

		Convey("normal stop", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Stop", errors.New("stop failed")).Reset()
			job.State = constant.StateRunning
			err := s.PortScanner.Stop(context.Background(), "taskID", "jobID")
			So(err, ShouldBeNil)
			So(s.PortScanner.tasks, ShouldNotContainKey, "taskID")
		})
	})
}

func (s *PortScannerSuite) Test_Start() {
	Convey("Test_Start", s.T(), func() {
		defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
		isIpv6 := false
		event := &pb.TaskEvent{
			Command:    constant.CommandStart,
			TaskId:     "taskID",
			JobId:      "jobID",
			TaskType:   constant.TaskTypeQuick,
			Ports:      []string{"22", "80", "443"},
			HostInfos:  []string{"127.0.0.1"},
			Options:    &pb.TaskOption{IsIpv6: &isIpv6},
			Parameters: &pb.TransparentParameters{},
		}

		Convey("scan failed", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Scan", errors.New("start failed")).Reset()
			job.State = constant.StateInitial
			err := s.PortScanner.Start(context.Background(), event)
			So(err, ShouldNotBeNil)
		})

		Convey("scan failed 2", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Scan", errors.New("start failed")).Reset()
			job.State = constant.StateRunning
			err := s.PortScanner.Start(context.Background(), event)
			So(err, ShouldNotBeNil)
		})

		Convey("normal scan", func() {
			job := s.PortScanner.tasks["taskID"]["jobID"]
			defer gomonkey.ApplyMethodReturn(job.Engine, "Scan", nil).Reset()
			job.State = constant.StateInitial
			err := s.PortScanner.Start(context.Background(), event)
			So(err, ShouldNotBeNil)
			So(job.State, ShouldEqual, constant.StateRunning)
		})
	})
}

func (s *PortScannerSuite) Test_OnResult() {
	Convey("Test_OnResult", s.T(), func() {
		Convey("tcp result", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			s.PortScanner.OnResult(context.Background(), "taskID", "jobID", "127.0.0.1", 22, "tcp", "")
		})

		Convey("result to dispatcher", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			s.PortScanner.c.Worker.ResultToDispatcher = true
			s.PortScanner.OnResult(context.Background(), "taskID", "jobID", "127.0.0.1", 22, "tcp", "")
		})
	})
}

func (s *PortScannerSuite) Test_OnProgress() {
	Convey("Test_OnProgress", s.T(), func() {
		Convey("normal progress", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			s.PortScanner.OnProgress(context.Background(), "taskID", "jobID", "", 50.0)
		})
	})
}

func (s *PortScannerSuite) TestPortScanner_OnError() {
	Convey("TestPortScanner_OnError", s.T(), func() {
		Convey("error", func() {
			defer gomonkey.ApplyMethodReturn(s.PortScanner.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			s.PortScanner.OnError(context.Background(), "taskID", "jobID", "")
		})
	})
}
