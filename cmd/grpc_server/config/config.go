package config

import (
	"baimaohui/portscan_new/cmd/grpc_server/pkg/common"
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
)

type ServiceConfig struct {
	Id string
}

type NmapConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type MasscanConfig struct {
	Name       string
	Directory  string
	SourcePort int
	BlackList  string
}

type WorkerConfig struct {
	BatchSize          int
	SameIpMaxCount     int
	SendEth            string
	ReserveInnerIp     bool
	ResultToDispatcher bool
}

type Config struct {
	Service ServiceConfig
	Nmap    NmapConfig
	Masscan MasscanConfig
	Worker  WorkerConfig
}

func ReadConfig() *Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath(common.MustFind("config.yaml")))); err != nil {
		logger.Info("can't find the config.yaml file.")
	}

	cc := new(Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config failed.")
	}

	return cc
}
