package main

import (
	conf "baimaohui/portscan_new/cmd/grpc_server/config"
	"baimaohui/portscan_new/cmd/grpc_server/handler"
	"baimaohui/portscan_new/internal/util"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
	"strings"

	_ "github.com/go-micro/plugins/v4/broker/kafka"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
)

var (
	Version string
	BuildAt string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	cc := conf.ReadConfig()

	// Create serviceName
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)
	srv.Init(micro.Name(constant.ServicePortScanner))

	if len(cc.Service.Id) > 0 {
		_ = srv.Server().Init(server.Id(cc.Service.Id))
	}

	hostname, err := util.GetHost()
	if err != nil {
		logger.Fatal("[FATAL] get hostname failed", err)
	}

	portScanner := handler.NewPortScanner(srv, cc, hostname)

	// 通过队列方式，接收扫描任务
	topic := srv.Server().Options().Name + "." + strings.ReplaceAll(srv.Server().Options().Id, "-", ".")
	if err := micro.RegisterSubscriber(topic, srv.Server(), portScanner.TaskEventHandler); err != nil {
		logger.Fatal(err)
	}

	// Run serviceName
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}
}

/*
func ReadConfig() *conf.Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Info("can't find the config.yaml file.")
	}

	cc := new(conf.Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config failed.")
	}

	return cc
}*/
