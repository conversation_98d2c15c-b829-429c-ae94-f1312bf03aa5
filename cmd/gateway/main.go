package main

import (
	"api/internal"
	"api/internal/config"
	"api/internal/httptransport"
	license2 "api/internal/license"
	"api/internal/repository"
	"api/internal/service"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	_ "github.com/go-micro/plugins/v4/broker/kafka"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"github.com/go-micro/plugins/v4/store/file"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
	"go-micro.dev/v4/store"
	"net/http"
	"os"
	"path/filepath"
)

func main() {
	fmt.Println("Version: ", internal.Version)
	fmt.Println("BuildAt: ", internal.BuildAt)

	microSrv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	pwd, err := os.Getwd()

	if err != nil {
		return
	}

	file.DefaultDir = filepath.Join(pwd, "logs")

	ruleStore := file.NewStore(
		store.Database("foscan"),
		store.Table("rules"),
	)

	protocolStore := file.NewStore(
		store.Database("foscan"),
		store.Table("protocols"),
	)

	cc := config.ReadConfig()

	if len(cc.NodeID) > 0 {
		_ = microSrv.Server().Init(server.Id(cc.NodeID))
	}

	microSrv.Init()

	dispatcher := rpcx.NewDispatcherTaskService(constant.ServiceDispatcher, microSrv.Client())
	rawGrab := rpcx.NewRawGrabService(constant.ServiceRawGrab, microSrv.Client())
	dataAnalysis := rpcx.NewDataAnalysisService(constant.ServiceDataAnalysis, microSrv.Client())

	license := license2.NewLicense()

	repo := repository.NewRepository(ruleStore, protocolStore)
	srv := service.NewService(dispatcher, license, repo, rawGrab, dataAnalysis, microSrv)

	router := httptransport.NewGinRouter(srv, license)

	logger.Infof("foscan main http.ListenAndServe ON: %s", cc.Http.Addr)
	logger.Fatal(http.ListenAndServe(cc.Http.Addr, router))
}
