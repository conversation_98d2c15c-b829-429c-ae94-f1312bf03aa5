package model

import (
	"context"
	"encoding/json"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/google/uuid"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4/logger"
	"testing"
)

const DBName = "data.db"

type DBHelperSuite struct {
	suite.Suite
	helper  *DBHelper
	Context context.Context
}

func Test_DBHelperSuite(t *testing.T) {
	s := &DBHelperSuite{}
	s.helper = NewDBHelper(DBName)
	s.helper.db.AutoMigrate(&Task{}, &Job{})
	suite.Run(t, s)
}

func (s *DBHelperSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	s.Context = context.Background()
}

func (s *DBHelperSuite) Test_CreateTask() {
	<PERSON>vey("Test_CreateTask", s.T(), func() {
		Convey("normal task", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			task, err := s.helper.GetTask(taskID.String())
			So(err, ShouldBeNil)
			So(task.State, ShouldEqual, constant.StateInitial)
			So(task.CurrentProcess, ShouldEqual, constant.ServicePortScanner)
			var expect pb.DispatcherTaskStartRequest
			_ = json.Unmarshal([]byte(task.Data), &expect)
			So(data, ShouldResemble, expect)
		})
	})
}

func (s *DBHelperSuite) Test_UpdateTaskState() {
	Convey("Test_UpdateTaskState", s.T(), func() {
		Convey("normal task", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			err = s.helper.UpdateTaskState(taskID.String(), constant.StateRunning)
			So(err, ShouldBeNil)

			task, err := s.helper.GetTask(taskID.String())
			So(err, ShouldBeNil)
			So(task.State, ShouldEqual, constant.StateRunning)
		})
	})
}

func (s *DBHelperSuite) Test_DeleteTask() {
	Convey("Test_DeleteTask", s.T(), func() {
		Convey("normal", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			task, err := s.helper.GetTask(taskID.String())
			So(err, ShouldBeNil)
			So(task.State, ShouldEqual, constant.StateInitial)

			err = s.helper.DeleteTask(taskID.String())
			So(err, ShouldBeNil)

			task, err = s.helper.GetTask(taskID.String())
			So(err, ShouldNotBeNil)
		})
	})
}

func (s *DBHelperSuite) Test_CreateJob() {
	Convey("Test_CreateJob", s.T(), func() {
		Convey("normal", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			err = s.helper.CreateJob(taskID.String(), taskID.String(), &pb.TaskEvent{
				TaskId:  taskID.String(),
				JobId:   taskID.String(),
				Command: constant.CommandStart,
			}, "node_1")
			So(err, ShouldBeNil)

			job, err := s.helper.GetJob(taskID.String())
			So(err, ShouldBeNil)
			So(job.ID, ShouldEqual, taskID.String())
			So(job.NodeID, ShouldEqual, "node_1")

			jobs, err := s.helper.GetJobs(taskID.String())
			So(err, ShouldBeNil)
			So(len(jobs), ShouldBeGreaterThan, 0)
		})
	})
}

func (s *DBHelperSuite) Test_UpdateJob() {
	Convey("Test_UpdateJob", s.T(), func() {
		Convey("normal", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			err = s.helper.CreateJob(taskID.String(), taskID.String(), &pb.TaskEvent{
				TaskId:  taskID.String(),
				JobId:   taskID.String(),
				Command: constant.CommandStart,
			}, "node_1")
			So(err, ShouldBeNil)

			err = s.helper.UpdateJobState(taskID.String(), constant.StateRunning, 32.0)
			So(err, ShouldBeNil)

			job, err := s.helper.GetJob(taskID.String())
			So(err, ShouldBeNil)
			So(job.State, ShouldEqual, constant.StateRunning)
			So(job.Progress, ShouldEqual, 32.0)
		})
	})
}

func (s *DBHelperSuite) Test_DeleteJob() {
	Convey("Test_DeleteJob", s.T(), func() {
		Convey("normal", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			err = s.helper.CreateJob(taskID.String(), taskID.String(), &pb.TaskEvent{
				TaskId:  taskID.String(),
				JobId:   taskID.String(),
				Command: constant.CommandStart,
			}, "node_1")
			So(err, ShouldBeNil)

			err = s.helper.DeleteJob(taskID.String())
			So(err, ShouldBeNil)
		})
	})
}

func (s *DBHelperSuite) Test_GetUnfinishedTasks() {
	Convey("Test_GetUnfinishedTasks", s.T(), func() {
		Convey("normal", func() {
			taskID, _ := uuid.NewUUID()
			data := pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Ports:   []string{"80", "22"},
				IpLists: []string{"127.0.0.1"},
				Options: &pb.DispatcherTaskStartOptions{
					MaxAssetNum: 0,
				},
			}
			err := s.helper.CreateTask(taskID.String(), &data)
			So(err, ShouldBeNil)

			tasks, err := s.helper.GetUnfinishedTasks()
			So(err, ShouldBeNil)
			So(len(tasks), ShouldBeGreaterThan, 1)
		})
	})
}
