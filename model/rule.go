package model

type Rule struct {
	Product        string `json:"product"`
	Rule           string `json:"rule"`
	RuleID         string `json:"rule_id"`
	Level          string `json:"level"`
	Category       string `json:"category"`
	ParentCategory string `json:"parent_category"`
	Softhard       string `json:"softhard"`
	Company        string `json:"company"`
	Version        string `json:"version"`
	From           string `json:"from"`
	CachedAt       string `json:"cached_at"`
}
