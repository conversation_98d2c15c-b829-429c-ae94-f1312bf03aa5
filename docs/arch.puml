@startuml
'https://plantuml.com/component-diagram
'https://plantuml.com/common

actor HTTP
node "API" {
  HTTP - [API Gateway]
}

package "Dispatcher" {
  [ControlManager] --> [Scheduler]
  [Scheduler] --> [ControlManager]
}

component [FofaSearch]

node "DataStore" {
    [ElasticSearch]
}

node "3rd" {
    [RegistryCenter]
    [ConfigureCenter]
    [Store]
}

package "Engine" {
  [PortScanner]
  [RawGrab]
  [CheckUrl]
  [Crawler]
  [DataAnalysis]
  [QuickStore]
}

queue topic
[PortScanner] --> [RegistryCenter] : 注册服务
[ConfigureCenter] --> [PortScanner] : 获取配置
[RegistryCenter] --> [Scheduler] : 获得服务
[Scheduler] --> [Store] : 存储调度结果
[API Gateway] -right-> [FofaSearch]
[FofaSearch] -right-> [ElasticSearch]
[API Gateway] -down-> [ControlManager]
[ControlManager] -right-> [topic]
[topic] --> [PortScanner] : 1.pull task
[PortScanner] --> [topic] : 2.push result
[topic] --> [RawGrab] : 3.pull request
[RawGrab] --> [topic] : 4.push result
[topic] --> [CheckUrl] : 5.pull request
[CheckUrl] --> [topic] : 6.push result
[topic] --> [Crawler] : 7.pull request
[Crawler] --> [topic] : 8.push result
[topic] --> [DataAnalysis] : 9.pull request
[DataAnalysis] --> [topic] : 10.push result
[topic] --> [QuickStore] : 11.pull request
[QuickStore] --> [topic] : 12.push result

@enduml