@startuml
'https://plantuml.com/component-diagram

package "Customer" {
  [Foeye]
}

package "Api" {
HTTP - [Api]
}

package "PortScanner" {
  [TaskEventHandler]
  [nmap & masscan]
}

package "Dispatcher" {
  GRPC - [Dispatcher]
  [TaskNotifyHandler]
}

node "MessageQueue" {
  [Topic: dispatcher]
  [Topic: port_scanner]
  [Topic: raw_grab]
  [Topic: quick_store]
  [Topic: crawler]
}

package "RawGrab" {
  [RawGrab Handler]

  [ProtocolGrab]
}

package "Crawler" {
  [Crawler Handler]

}

package "Store" {
  [Store Handler]

}

[Foeye] --> HTTP
[Api] --> GRPC
[Dispatcher] --> [Topic: port_scanner]
[Topic: port_scanner] --> [TaskEventHandler]
[TaskEventHandler] --> [nmap & masscan]
[nmap & masscan] --> [Topic: dispatcher]
[Topic: dispatcher] --> [TaskNotifyHandler]
[TaskNotifyHandler] --> [Topic: raw_grab]
[Topic: raw_grab] --> [RawGrab Handler]
[RawGrab Handler] --> [Topic: quick_store]
[RawGrab Handler] --> [Topic: crawler]
[Topic: quick_store] --> [Store Handler]
[Topic: crawler] --> [Crawler Handler]
[Crawler Handler] --> [Topic: quick_store]

[RawGrab Handler] --> [ProtocolGrab]
@enduml
