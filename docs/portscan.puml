@startuml
'https://plantuml.com/sequence-diagram

autonumber
main -> PortScanner: 创建PortScanner对象
activate PortScanner
PortScanner --> main: 返回

main -> Broker: 订阅topic，注册TaskEventHandler方法
Broker --> main: 返回

main -> main: 服务启动

Broker -> PortScanner: 推送任务参数，回调TaskEventHandler

PortScanner -> PortScanner: 判断任务类型，分析任务参数，调用Start

alt 任务ID不存在
    PortScanner -> ScanTask: 创建任务扫描引擎
    activate ScanTask
    ScanTask -> PortscanStub: 创建Masscan/Nmap的Stub
    Activate PortscanStub
    PortscanStub --> ScanTask: 返回
    ScanTask --> PortScanner: 返回
    PortScanner -> PortScanner: 存入缓存中
else
    PortScanner -> PortScanner: 从缓存中获得
end

PortScanner -> ScanTask: 调用Scan方法
ScanTask -> PortscanStub: 调用PortscanStub的Run/RunFile方法
PortscanStub --> ScanTask: 返回
ScanTask --> PortScanner: 返回

PortscanStub -> PortscanStub: 调用Masscan/Nmap的命令进行扫描, 发现结果
PortscanStub -> ScanTask: 回调OnResult
ScanTask -> PortScanner: 回调OnResult

deactivate PortscanStub
deactivate ScanTask
deactivate PortScanner
@enduml