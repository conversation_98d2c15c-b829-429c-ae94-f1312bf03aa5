@startuml
'https://plantuml.com/sequence-diagram

autonumber

Api -> Dispatcher: start a scan task
Dispatcher -> Dispatcher: 创建task对象
Dispatcher -> Task: 调用task.start

alt 存在IpDomainRelations
Task -> CheckUrl: 将IP域名关系放入checkUrl的topic中
CheckUrl -> CheckUrl: 保存任务的IP域名关系
end

alt 端口分组模式
    Task -> Task: 按照端口分组拆分子任务
    loop 遍历所有子任务
        Task -> Broker: 将子任务Job放入portScanner的topic中
    end
else
    Task -> Task: 根据policy拆分子任务
    loop 遍历所有子任务
        Task -> Broker: 将子任务Job放入portScanner的topic中
    end
end

Task --> Dispatcher: 返回
Dispatcher --> Api: 返回

PortScanner <- Broker: 从topic获得一个子任务
PortScanner -> PortScanner: 执行扫描

@enduml