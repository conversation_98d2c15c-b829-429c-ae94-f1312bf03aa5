@startuml
'https://plantuml.com/class-diagram

interface Task {
	Start(ctx context.Context) error
	Pause(ctx context.Context) error
	Resume(ctx context.Context) error
	Stop(ctx context.Context) error
	GetID() string
	GetState() string
	GetProgress() float32
	GetWorkingOn() string
}

interface Job {
	Start(ctx context.Context) error
	Pause(ctx context.Context) error
	Resume(ctx context.Context) error
	Stop(ctx context.Context) error
	GetID() string
	GetState() string
	GetProgress() float32
	GetWorkingOn() string
	SetNode(*Node)
	GetNode() *Node
}

interface Node {
    GetName() string
    GetType() string
    GetState() string
    GetAttr(string key) string
    SetAttr(string key, string attr)
    GetJobs() []Job //正在执行的任务
    AssignJob() error
    FinishJob() error
}

interface TaskSplitPolicy {
    DoSplit(task Task) []Job
}

interface Scheduler {
    ScheduleNode(...) *Node
}

class AssetScanEngine {
}

class NodeManager {
    nodes []Node
    UpdateAttr(node Node, string key, string attr)
    Assign(job Job, node Node)
    Finish(job Job, node Node)
    StartNodeMonitor()
}

Task <|-- AssetScanTask
Job <|-- AssetScanJob
TaskSplitPolicy <|-- IpSplitPolicy
TaskSplitPolicy <|-- PortSplitPolicy
TaskSplitPolicy <|-- SmartSplitPolicy
Scheduler <|-- AttrScheduler
Scheduler <|-- NameScheduler
Node <|-- AssetScanEngine

Scheduler o.up. Node
NodeManager "1" *.up. "many" Node

AssetScanTask "1" *.up. "many" AssetScanJob

ControlManager ..> AssetScanTask : 创建对象

AssetScanTask ..> TaskSplitPolicy : 任务拆分

AssetScanTask ..> Scheduler : 获得节点

class ControlManager {
    tasks      map[string]Task

	TaskStart(...) error
	TaskPause(...) error
	TaskResume(...) error
	TaskStop(...) error
	StartTaskMonitor()
}

@enduml