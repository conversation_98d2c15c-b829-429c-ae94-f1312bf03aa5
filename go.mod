module checkurl

go 1.16

require (
	git.gobies.org/shared-platform/foscan/pkg v0.0.0-00010101000000-000000000000
	github.com/BurntSushi/toml v1.1.0
	github.com/Shopify/sarama v1.30.1
	github.com/agiledragon/gomonkey/v2 v2.10.1
	github.com/alicebob/miniredis/v2 v2.23.0
	github.com/bsm/sarama-cluster v2.1.15+incompatible
	github.com/cch123/supermonkey v1.0.1
	github.com/customerio/gospec v0.0.0-20130710230057-a5cc0e48aa39 // indirect
	github.com/garyburd/redigo v1.6.3
	github.com/go-micro/plugins/v4/broker/kafka v1.2.0
	github.com/go-micro/plugins/v4/broker/nats v1.2.0
	github.com/go-micro/plugins/v4/broker/rabbitmq v1.2.1
	github.com/go-micro/plugins/v4/broker/redis v1.2.0
	github.com/go-micro/plugins/v4/client/grpc v1.2.0
	github.com/go-micro/plugins/v4/config/encoder/yaml v1.2.0
	github.com/go-micro/plugins/v4/registry/consul v1.2.0
	github.com/go-micro/plugins/v4/registry/etcd v1.2.0
	github.com/go-micro/plugins/v4/registry/nats v1.2.1
	github.com/go-micro/plugins/v4/server/grpc v1.2.0
	github.com/goware/urlx v0.3.2
	github.com/imroc/domain v0.0.0-20170913102033-2ebadefe95c5
	github.com/jrallison/go-workers v0.0.0-20180112190529-dbf81d0b75bb
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mitchellh/mapstructure v1.4.2
	github.com/olivere/elastic v6.2.37+incompatible
	github.com/pkg/errors v0.9.1
	github.com/smartystreets/goconvey v1.6.4
	github.com/stretchr/testify v1.8.1
	go-micro.dev/v4 v4.10.2
	go.uber.org/zap v1.19.1
	golang.org/x/sync v0.1.0
	golang.org/x/text v0.8.0
	google.golang.org/protobuf v1.28.1
)

replace git.gobies.org/shared-platform/foscan/pkg => git.gobies.org/shared-platform/foscan/pkg.git v0.2.6
