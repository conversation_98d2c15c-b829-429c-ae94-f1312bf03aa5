module baimaohui/portscan_new

go 1.16

require (
	git.gobies.org/gobase/plugin v0.0.15
	git.gobies.org/shared-platform/foscan/pkg v0.0.0-00010101000000-000000000000
	github.com/BurntSushi/toml v1.1.0
	github.com/Shopify/sarama v1.30.1
	github.com/agiledragon/gomonkey/v2 v2.9.0
	github.com/alicebob/miniredis/v2 v2.23.0
	github.com/customerio/gospec v0.0.0-20130710230057-a5cc0e48aa39 // indirect
	github.com/garyburd/redigo v1.6.2
	github.com/go-micro/plugins/v4/broker/kafka v1.2.0
	github.com/go-micro/plugins/v4/broker/nats v1.2.0
	github.com/go-micro/plugins/v4/broker/rabbitmq v1.2.1
	github.com/go-micro/plugins/v4/broker/redis v1.2.0
	github.com/go-micro/plugins/v4/client/grpc v1.2.0
	github.com/go-micro/plugins/v4/config/encoder/yaml v1.2.0
	github.com/go-micro/plugins/v4/registry/consul v1.2.0
	github.com/go-micro/plugins/v4/registry/etcd v1.2.0
	github.com/go-micro/plugins/v4/registry/nats v1.2.1
	github.com/go-micro/plugins/v4/server/grpc v1.2.0
	github.com/hashicorp/go-plugin v1.4.6
	github.com/jrallison/go-workers v0.0.0-20180112190529-dbf81d0b75bb
	github.com/pkg/errors v0.9.1
	github.com/robfig/cron v1.2.0
	github.com/smartystreets/goconvey v1.7.2
	github.com/stretchr/testify v1.8.1
	go-micro.dev/v4 v4.10.2
	go.uber.org/zap v1.19.0
	google.golang.org/protobuf v1.28.1
)

replace git.gobies.org/shared-platform/foscan/pkg => git.gobies.org/shared-platform/foscan/pkg.git v0.2.13
