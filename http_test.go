package probe

import (
	"bufio"
	"errors"
	sm "github.com/cch123/supermonkey"
	"github.com/stretchr/testify/assert"
	"io"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
)

func TestCheck400Code(t *testing.T) {
	// 模拟调用
	req, err := http.NewRequest(http.MethodGet, "/222", nil)
	if err != nil {
		t.Fatal("创建Request失败")
	}

	rw := httptest.NewRecorder()
	http.DefaultServeMux.ServeHTTP(rw, req)
	http.NotFound(rw, req)
	http.Error(rw, "400 Bad Request https", 404)
	_err := check400Code(rw.Body.Bytes())

	assert.Equal(t, "not http", _err.Error())
}

func Routes() {
	http.HandleFunc("/sendhtml", SendHTML)
}

// 此处填写要匹配的文本
func SendHTML(rw http.ResponseWriter, r *http.Request) {
	noHeader := []byte(`<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
		<html><head>
		<title> Request</title>
		</head><body>
		400 Bad Request https
		</body></html>
		`)

	rw.Write(noHeader)
}

func init() {
	Routes()
}

func TestGetHTTP(t *testing.T) {

	data := []byte(`HTTP/1.1 302 Found
Server: mini_httpd
Location: http://************:80/asp/voc_error_03_IP.html
Content-Type: text/html; charset=iso-8859-1
Content-Lnegth: 135

<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>302 Moved</title></head><body>
<h1>302 Moved</h1>`)

	patch := sm.Patch(http.ReadResponse, func(r *bufio.Reader, req *http.Request) (*http.Response, error) {
		file, _ := os.Open("http_test.go")
		return &http.Response{
			Body: file,
		}, nil
	})

	patch1 := sm.Patch((*probe).GetTCPReader, func(p *probe) *probeReader {
		tcp, _ := p.GetTCP()
		r := &probeReader{
			o:         p,
			reader:    tcp,
			respBytes: data,
		}
		return r
	})
	patch2 := sm.Patch((*probe).MustWriteTCP, func(_ *probe, b []byte) {})

	//模拟err、data同时不为空
	patch3 := sm.Patch(ioutil.ReadAll, func(r io.Reader) ([]byte, error) {
		return data, errors.New("Connection closed by foreign host")
	})

	defer patch.Unpatch()
	defer patch1.Unpatch()
	defer patch2.Unpatch()
	defer patch3.Unpatch()

	info, err := HTTP("127.0.0.1:80")
	assert.Nil(t, err)
	assert.Equal(t, "http", info.Protocol)
	assert.Equal(t, true, info.Success)
}

func TestHTTP(t *testing.T) {
	NewTcpMocker(80).
		SetHttp().
		//SetResponse([]byte("\x48\x54\x54\x50\x2f\x31\x2e\x31\x20\x34\x30\x31\x20\x55\x6e\x61\x75\x74\x68\x6f\x72\x69\x7a\x65\x64\x0d\x0a\x43\x6f\x6e\x74\x65\x6e\x74\x2d\x4c\x65\x6e\x67\x74\x68\x3a\x20\x30\x0d\x0a\x53\x65\x72\x76\x65\x72\x3a\x20\x4d\x69\x63\x72\x6f\x73\x6f\x66\x74\x2d\x48\x54\x54\x50\x41\x50\x49\x2f\x32\x2e\x30\x0d\x0a\x57\x57\x57\x2d\x41\x75\x74\x68\x65\x6e\x74\x69\x63\x61\x74\x65\x3a\x20\x4e\x65\x67\x6f\x74\x69\x61\x74\x65\x0d\x0a\x57\x57\x57\x2d\x41\x75\x74\x68\x65\x6e\x74\x69\x63\x61\x74\x65\x3a\x20\x4e\x54\x4c\x4d\x0d\x0a\x44\x61\x74\x65\x3a\x20\x57\x65\x64\x2c\x20\x31\x36\x20\x4e\x6f\x76\x20\x32\x30\x32\x32\x20\x30\x39\x3a\x35\x36\x3a\x33\x36\x20\x47\x4d\x54\x0d\x0a\x0d\x0a")).
		SetResponse([]byte("\x48\x54\x54\x50\x2f\x31\x2e\x31\x20\x34\x30\x31\x20\x55\x6e\x61\x75\x74\x68\x6f\x72\x69\x7a\x65\x64\x0d\x0a\x43\x6f\x6e\x74\x65\x6e\x74\x2d\x4c\x65\x6e\x67\x74\x68\x3a\x20\x30\x0d\x0a\x53\x65\x72\x76\x65\x72\x3a\x20\x4d\x69\x63\x72\x6f\x73\x6f\x66\x74\x2d\x48\x54\x54\x50\x41\x50\x49\x2f\x32\x2e\x30\x0d\x0a\x57\x57\x57\x2d\x41\x75\x74\x68\x65\x6e\x74\x69\x63\x61\x74\x65\x3a\x20\x4e\x54\x4c\x4d\x20\x54\x6c\x52\x4d\x54\x56\x4e\x54\x55\x41\x41\x43\x41\x41\x41\x41\x42\x41\x41\x45\x41\x44\x67\x41\x41\x41\x41\x46\x67\x6f\x6d\x69\x72\x69\x39\x63\x49\x47\x36\x76\x44\x73\x59\x41\x41\x41\x41\x41\x41\x41\x41\x41\x41\x4a\x6f\x41\x6d\x67\x41\x38\x41\x41\x41\x41\x43\x67\x42\x6a\x52\x51\x41\x41\x41\x41\x39\x42\x41\x45\x51\x41\x41\x67\x41\x45\x41\x45\x45\x41\x52\x41\x41\x42\x41\x42\x6f\x41\x53\x51\x42\x55\x41\x46\x4d\x41\x4c\x51\x42\x46\x41\x46\x51\x41\x52\x77\x41\x74\x41\x45\x51\x41\x52\x51\x42\x57\x41\x44\x45\x41\x4d\x51\x41\x45\x41\x42\x51\x41\x59\x51\x42\x6b\x41\x43\x34\x41\x63\x77\x42\x35\x41\x48\x49\x41\x4c\x67\x42\x6c\x41\x47\x51\x41\x64\x51\x41\x44\x41\x44\x41\x41\x53\x51\x42\x55\x41\x46\x4d\x41\x4c\x51\x42\x46\x41\x46\x51\x41\x52\x77\x41\x74\x41\x45\x51\x41\x52\x51\x42\x57\x41\x44\x45\x41\x4d\x51\x41\x75\x41\x47\x45\x41\x5a\x41\x41\x75\x41\x48\x4d\x41\x65\x51\x42\x79\x41\x43\x34\x41\x5a\x51\x42\x6b\x41\x48\x55\x41\x42\x51\x41\x55\x41\x47\x45\x41\x5a\x41\x41\x75\x41\x48\x4d\x41\x65\x51\x42\x79\x41\x43\x34\x41\x5a\x51\x42\x6b\x41\x48\x55\x41\x42\x77\x41\x49\x41\x44\x4f\x54\x54\x37\x65\x68\x2b\x64\x67\x42\x41\x41\x41\x41\x41\x41\x3d\x3d\x0d\x0a\x44\x61\x74\x65\x3a\x20\x57\x65\x64\x2c\x20\x31\x36\x20\x4e\x6f\x76\x20\x32\x30\x32\x32\x20\x30\x39\x3a\x35\x36\x3a\x33\x36\x20\x47\x4d\x54\x0d\x0a\x0d\x0a")).
		SetProtocol("http").
		Run(HTTP).
		Test(t)
}

func TestIsHttps(t *testing.T) {
	var header, body string
	var isHttps bool

	isHttps = isHttpsProtocol(header, body)
	assert.False(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	isHttps = isHttpsProtocol(header, body)
	assert.False(t, isHttps)

	header = ""
	body = "This server uses SSL for security. Please use HTTPS to connect."
	isHttps = isHttpsProtocol(header, body)
	assert.False(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "The plain HTTP request was sent to HTTPs port"
	isHttps = isHttpsProtocol(header, body)
	assert.False(t, isHttps)

	// header第一个种情况
	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "The plain HTTP request was sent to HTTPS port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "You're speaking plain HTTP to an SSL-enabled server port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "Client sent an HTTP request to an HTTPS server"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "This server uses SSL for security. Please use HTTPS to connect."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "This combination of host and port requires TLS."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 Bad Request\nServer: Tengine\nDate: Sun, 18 Jun 2023 14:58:22 GMT\nTransfer-Encoding: chunked\nConnection: keep-alive"
	body = "This web server is running in SSL mode."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400"
	body = "This web server is running in SSL mode."
	isHttps = isHttpsProtocol(header, body)
	assert.False(t, isHttps)

	// header第二个种情况
	header = "200 Document follows"
	body = "The plain HTTP request was sent to HTTPS port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "200 Document follows"
	body = "You're speaking plain HTTP to an SSL-enabled server port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "200 Document follows"
	body = "Client sent an HTTP request to an HTTPS server"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "200 Document follows"
	body = "This server uses SSL for security. Please use HTTPS to connect."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "200 Document follows"
	body = "This combination of host and port requires TLS."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "200 Document follows"
	body = "This web server is running in SSL mode."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	// header第三个种情况
	header = "HTTP/1.1 400 "
	body = "The plain HTTP request was sent to HTTPS port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 "
	body = "You're speaking plain HTTP to an SSL-enabled server port"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 "
	body = "Client sent an HTTP request to an HTTPS server"
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 "
	body = "This server uses SSL for security. Please use HTTPS to connect."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 "
	body = "This combination of host and port requires TLS."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)

	header = "HTTP/1.1 400 "
	body = "This web server is running in SSL mode."
	isHttps = isHttpsProtocol(header, body)
	assert.True(t, isHttps)
}
