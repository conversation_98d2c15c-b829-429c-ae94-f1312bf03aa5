package config

import (
	"checkurl/internal/config"
	"checkurl/internal/mqqueue"
	"checkurl/mylog"
	"flag"
	"io/ioutil"
	"log"

	"github.com/BurntSushi/toml"
	"github.com/pkg/errors"
)

type WorkerConf struct {
	Num                int
	AddLinkHost        bool   `toml:"add_link_host"`
	DisableHostChecked bool   `toml:"disable_host_checked"`
	AlexaDomainPath    string `toml:"alexa_domain_path"`
	AlexaNum           int    `toml:"alexa_num"`
}

// config
type RedisConf struct {
	Addr             string
	Db               int
	Password         string // optional
	Prefix           string `toml:"prefix"` // optional
	CheckedPrefix    string `toml:"checked_prefix"`
	CheckedKeyExpire string `toml:"checked_key_expire"`
	PoolSize         int    // 连接池大小
}

// config
type ElasticsearchConf struct {
	Url                string
	IndexName          string `toml:"index"`
	IndexSubdomain     string `toml:"index_subdomain"`
	TypeSubdomain      string `toml:"type_subdomain"`
	EsVersion          int    `toml:"es_version"`
	DisableRecordsInfo bool   `toml:"disable_records_info"`
}

type ProducerConfig struct {
	Crawler mqqueue.SimplProducerConfig
}

type Config struct {
	Timeout       string
	Log           mylog.LogConf
	Pprof         PprofConf
	Sidekiq       *config.SidekiqCfg
	Redis         RedisConf
	Consumer      mqqueue.SimpleConsumerConfig
	Producer      ProducerConfig
	Elasticsearch ElasticsearchConf
	Worker        WorkerConf
}

func readConf(filename string) (*Config, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, errors.Wrap(err, "can not open config file")
	}
	var conf Config
	err = toml.Unmarshal(data, &conf)
	if err != nil {
		return nil, errors.Wrap(err, "config file format error")
	}
	return &conf, nil
}

var configFile string

func Init() {
	flag.StringVar(&configFile, "c", "conf.toml", "config file")
	flag.Parse()
}

func GetConfig() (*Config, error) {
	conf, err := readConf(configFile)
	if err != nil {
		return nil, errors.Wrap(err, "read config failed")
	}
	log.Printf("conf:%+v\n", conf)
	return conf, nil
}
