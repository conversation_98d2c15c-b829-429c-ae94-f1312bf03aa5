package config

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

var con *Config

func TestMain(m *testing.M) {
	var err error
	configFile = "../conf.toml"
	if con, err = GetConfig(); err != nil {
		panic(err)
		return
	}
	m.Run()
}

func TestPporf(t *testing.T) {
	assert.Equal(t, con.Pprof.Port, 8888)
	InitPporf(con.Pprof)
	con.Pprof.Enable = true
	InitPporf(con.Pprof)
	Init()
}

func TestGetConfig(t *testing.T) {
	config, err := GetConfig()
	assert.Nil(t, err)
	t.Log(config)
}
