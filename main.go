package main

import (
	"baimaohui/portscan_new/flow"
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/rpc"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/taskmgr"
	"baimaohui/portscan_new/internal/util"
	"baimaohui/portscan_new/internal/worker"
	"log"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"syscall"
)

/*
const char* build_time(void)
{
static const char* psz_build_time = "build: "__DATE__ "  " __TIME__ "";
return psz_build_time;
}
*/
import "C"

var (
	buildTime       = C.GoString(C.build_time())
	porgscanVersion = "portscan_1.4.0"
)

func version() {
	log.Println(porgscanVersion, buildTime)
	log.Println("------------------------------------")
}

func main() {
	version()

	// 读配置
	conf := conf.GetConfig()

	//if conf.Pprof.Enable {
	//	defer util.SaveMem()
	//}

	log.Printf("conf:%+v  sidekiq:%+v  producer:%+v  redis:%+v  lmtRds:%+v\n",
		conf, conf.Consumer.Sidekiq, conf.Producer.Kafka, conf.Redis, conf.LmtRds)

	tmpHostname, err := util.GetHost()
	if err != nil {
		log.Fatal("[FATAL] get hostname failed", err)
	}
	log.Println("hostname:", tmpHostname)

	// pprof
	util.InitPporf(conf.Pprof)

	// log
	util.SetupLogger(conf.Log)

	// consumer
	if conf.Consumer.Sidekiq != nil {
		conf.Consumer.Sidekiq.Hostname = tmpHostname
	}
	consumer := flow.NewConsumer(conf.Consumer)

	var gragProducer queue.SimpleProducer
	if conf.Producer.Kafka != nil {
		gragProducer = flow.NewProducer(conf.Producer)
	}

	// 判断是否foeye
	isFoeye := false
	if conf.Consumer.Sidekiq != nil && conf.Consumer.Sidekiq.Foeye {
		isFoeye = true
	}

	// 构造类的参数
	var option []worker.Option
	var mgrOption []taskmgr.MgrOption

	// hostname
	option = append(option, worker.SetHostname(tmpHostname))

	// config
	option = append(option, worker.SetSysConf(conf))

	// db
	var dbAct db.DbAction
	if conf.Redis != nil {
		dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, conf.Redis.Prefix, conf.Redis.StopTaskKey,
			conf.Redis.ExecIpKey, conf.Redis.TaskProgressKey, conf.Redis)
		dbAct = db.NewDb(dbBaseConf)
	}
	option = append(option, worker.SetDbAct(dbAct))

	// limit db
	var dbLimitAct db.DbAction
	if conf.LmtRds != nil {
		var lmtConf config.RedisConf
		lmtConf.Addr = conf.LmtRds.Addr
		lmtConf.Db = conf.LmtRds.Db
		lmtConf.Password = conf.LmtRds.Password
		lmtConf.Prefix = conf.LmtRds.Prefix

		dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, conf.LmtRds.Prefix, "", "", "", &lmtConf)
		dbLimitAct = db.NewDb(dbBaseConf)
	}
	option = append(option, worker.SetDbLimitAct(dbLimitAct))

	// task config
	ignoreProced := false
	if conf.LmtRds != nil {
		ignoreProced = conf.LmtRds.Enable
	}
	taskBaseCfg := task.NewScantaskBaseConf(0, conf.Masscan.Dir, tmpHostname, conf.Masscan.BlackipList,
		conf.Masscan.SourcePort, conf.Worker.BatchSize, conf.Worker.SameIpMaxCount, isFoeye,
		conf.Worker.ReserveInnerIp, ignoreProced)
	mgrOption = append(mgrOption, taskmgr.SetTaskBaseConf(taskBaseCfg))
	// task mgr
	taskMgr := taskmgr.NewTaskMgr(dbAct, mgrOption...)

	// rpc
	var rpcCli *rpc.RPCClient = nil
	if conf.RPC.Enable {
		rpcCli = rpc.RunRpcClient(conf.RPC.Addr)
		option = append(option, worker.SetRpcClient(rpcCli))
	}

	var types util.ProgramType
	if isFoeye {
		option = append(option, worker.SetSidekiq(conf.Consumer.Sidekiq.GrabQueue, conf.Consumer.Sidekiq.PingQueue,
			conf.Consumer.Sidekiq.CheckurlQueue))

		types = util.PTFoeye
	} else {
		types = util.PTFofa
	}

	// msg parse
	option = append(option, worker.SetMsgParse(parse.NewMsgParse(types)))

	// tasker mgr
	option = append(option, worker.SetTaskObjMgr(taskmgr.NewTaskObjMgr(types, taskMgr)))

	// base worker
	bw := worker.NewBaseWorker(consumer, gragProducer, option...)

	// worker
	var wStub worker.WorkerStub
	wStub = worker.NewInstanceWorker(types, bw)

	go wStub.Start()

	// 优雅关闭
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigs
		log.Println("server stopping...")
		wStub.Stop()
		log.Println("server stopped")
		os.Exit(0)
	}()

	log.Println("server started success")
	select {}
}
