package main

import (
	"checkurl/config"
	"checkurl/internal/db"
	"checkurl/internal/es"
	"checkurl/internal/mqqueue"
	"checkurl/internal/parse"
	"checkurl/internal/sidekiq"
	"checkurl/internal/util"
	"checkurl/internal/worker"
	"checkurl/mylog"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func handleError(err error) {
	log.Println("+++ERROR:", err)
}

func main() {
	config.Init()
	// 读配置
	conf, err := config.GetConfig()
	if err != nil {
		log.Fatal(err)
	}

	// log
	mylog.SetupLogger(conf.Log)

	// consumer
	if conf.Sidekiq != nil {
		sidekiq.Init(conf.Sidekiq.Addr, conf.Sidekiq.Password, conf.Sidekiq.Db)
	}
	var consumer mqqueue.SimpleConsumer
	var crawlerProducer mqqueue.SimpleProducer
	var progType util.ProgramType

	if sidekiq.Enable {
		progType = util.PTFoeye
		queue := "check_url"
		if conf.Sidekiq.CheckurlQueue != "" {
			queue = conf.Sidekiq.CheckurlQueue
		}
		consumer = sidekiq.NewConsumer(queue)
	} else {
		progType = util.PTFofa
		// consumer
		consumer = NewConsumer(conf.Consumer)
		// crawler producer
		crawlerProducer = NewCrawlerProducer(conf.Producer.Crawler)
	}

	// redis
	conf.Redis.PoolSize = conf.Worker.Num // 把Redis的连接池建立为处理并发数，这样在checkurl积压的时候可以通过调整并发数来加快速度
	if conf.Redis.PoolSize <= 0 {
		conf.Redis.PoolSize = 5
	}
	log.Println("  redis pool size:", conf.Redis.PoolSize)
	redis := db.NewRedis(conf.Redis)

	// es
	esCfg := es.NewElasitcBaseConf(conf.Elasticsearch.EsVersion, conf.Elasticsearch.Url, conf.Elasticsearch.IndexName,
		conf.Elasticsearch.IndexSubdomain, conf.Elasticsearch.TypeSubdomain)
	esStub := es.NewElastic(esCfg)

	// msg parse
	msgParse := parse.NewMsgParse(progType)

	bw := worker.NewWorker(*conf, esStub, redis, consumer, crawlerProducer, msgParse)
	if sidekiq.Enable {
		bw.SetSidekiq(conf.Sidekiq.CheckurlQueue, conf.Sidekiq.ProcessurlQueue)
	}

	// work stub
	wkStub := worker.NewInstanceWorker(progType, bw)

	go wkStub.Start()

	// 优雅关闭
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigs
		log.Println("server stopping...")
		wkStub.Stop()
		log.Println("server stopped")
		os.Exit(0)
	}()

	log.Println("server started success")

	select {}
}
