package main

import (
	"dispatcher/handler"
	"dispatcher/internal/foeye"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	_ "github.com/go-micro/plugins/v4/broker/kafka"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
	_ "google.golang.org/protobuf/types/known/structpb"
	"strings"
)

var (
	Version string
	BuildAt string
	Mode    string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	cc := ReadConfig()

	var srv micro.Service
	// Create service
	srv = micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	if len(cc.Service.Id) > 0 {
		_ = srv.Server().Init(server.Id(cc.Service.Id))
	}

	dispatcher := handler.NewDispatcher(srv)
	dispatcher.AppendStateChangedListener(foeye.NewTaskStateCallback(cc))

	srv.Init(
		micro.Name(constant.ServiceDispatcher),
	)

	if len(Mode) > 0 && strings.EqualFold(Mode, "foeye") {
		logger.Info("start dispatcher in foeye mode")
		limit := foeye.NewAssetsLimit(cc)
		dispatcher.WithLimitation(limit)
	}

	if cc.Service.LoadTask {
		dispatcher.LoadTasks()
	}

	// grpc服务监听，接收gateway发送的任务数据
	if err := pb.RegisterDispatcherTaskServiceHandler(srv.Server(), dispatcher); err != nil {
		logger.Fatal(err)
	}

	// 监听topic，接收扫描节点的状态notify
	if err := micro.RegisterSubscriber(constant.ServiceDispatcher, srv.Server(), dispatcher.TaskNotifyHandler); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}

}

func ReadConfig() *foeye.Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Warn("can't find the config.yaml file. ")
	}

	cc := new(foeye.Config)

	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config failed.")
	}

	logger.Infof("read config %s", cc.ToString())

	return cc
}
