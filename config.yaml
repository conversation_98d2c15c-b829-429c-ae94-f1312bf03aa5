Service:
  Id: "node1" # 注册服务id，空时系统默认生成
  LoadTask: false

ThirdSystem:
  Scheme: "http"
  Host: "127.0.0.1:3000" # 产品api服务
  PathAssets: "/api/v1/callbacks/asset_all_list"  # 产品需要提供此接口用于license资产数限制
  PathNotifyProgress: "/api/v1/callbacks/asset_scan_progress" # 任务进度回调接口
  PathNotifyStatus: "/api/v1/callbacks/asset_scan_state" # 任务状态回调接口
  FailRetryTimes: 3 # 回调失败后，重试通知次数
  FailRetryInterval: 60 #second # 失败后，重试通知频率
  Enabled: true
