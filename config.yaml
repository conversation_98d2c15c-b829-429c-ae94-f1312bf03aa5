node_id: # 多节点模式不要配置值，单节点可以配置
worker_max: 10
crawler:
  debug: false # 调试开关
  uniq_params: true # 是否唯一化url，在设置为true的情况下id=1和id=2会认为是一个
  type: "ajax" # html / ajax
  scope: 0  # 0 子目录下的链接, 1 Host, 2 ip, 3 根域名, 4 当前url
  max_links: 1000 # 最大提取链接数
  max_craw_links: 50 # 最大爬虫链接数
  max_crawl_links_per_host: 50 # 单Host最大爬虫链接数
  load_time_out: 10 # 加载页面超时时间 单位秒
  auto_scan_dirs_from_url: false # 是否自动根据url生成Dir并进行扫描
  scan_index_page_with_dir_list: false # 是否爬虫默认页面是"Index of /"目录列表的url
  exclude_url_filters: ""
