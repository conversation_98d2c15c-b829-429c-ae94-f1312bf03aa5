package probe

import (
	"regexp"
	"strings"
)

var RegRedis00 = regexp.MustCompile(`^-ERR wrong number of arguments for 'get' command\r\n`)
var RegRedis01 = regexp.MustCompile(`-ERR operation not permitted\r\n`)
var RegRedis03 = regexp.MustCompile(`^-DENIED Redis is running in protected mode because protected mode is enabled`)
var RegRedis02 = regexp.MustCompile(`^\$\d+\r\n(?:#[^\r\n]*\r\n)*redis_version:([.\d]+)\r\n`)

func CheckRedis(by []byte) bool {
	if !(strings.Contains(string(by), "-ERR unknown command") && strings.Contains(string(by), "help")) {
		if !RegRedis00.Match(by) && !RegRedis01.Match(by) && !RegRedis02.Match(by) && !RegRedis03.Match(by) {
			return false
		}
	}

	return true
}

func Redis(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "redis"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	// 写
	p.MustWriteTCPString("help\r\n")

	// 读
	b := p.MustReadTCP(4096)
	info.Banner = b

	help_str := string(b)
	if strings.Contains(help_str, "-DENIED Redis is running in protected mode because protected mode is enabled") {
		info.Success = true
		return
	}

	// 写
	p.MustWriteTCPString("info\r\n")

	// 读
	b = append(b, p.MustReadTCP(4096)...)
	info.Banner = b

	// 判断协议
	if CheckRedis(b) == false {
		return
	}

	info.Success = true
	return
}

func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:         "redis",
		RefererURL:   "https://en.wikipedia.org/wiki/Redis",
		DefaultPorts: []int{6379},
		Handle:       Redis,
		PClass:       "C",
	})
}
