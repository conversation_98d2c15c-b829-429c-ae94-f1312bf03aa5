GOPATH:=$(shell go env GOPATH)

.PHONY: init
init:
	@go get -u google.golang.org/protobuf/proto
	@go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	@go install github.com/go-micro/generator/cmd/protoc-gen-micro@latest

.PHONY: proto
proto:
	@protoc --proto_path=./proto --micro_out=. --go_out=:. proto/*.proto

.PHONY: update
update:
	@go get -u

.PHONY: tidy
tidy:
	@go mod tidy

.PHONY: build
build:
	@go build -o grpc *.go

.PHONY: test
test:
	@go test -v ./... -cover

.PHONY: docker
docker:
	@docker build -t grpc:latest .

submodule:
	git submodule update --recursive

init-all: clear-sub
	-git submodule add --name probe -- https://git.gobies.org/fofapro_bg/rawgrab_probe_all.git probe
	-ls -l ./probe

init-part: clear-sub
	-git submodule add --name probe -- https://git.gobies.org/gobase/rawgrab-probe-public.git probe
	-ls -l ./probe

clear-sub:
	-git submodule deinit -f -- ./probe
	-rm -rf .git/modules/probe
	-git rm -f ./probe
	-git rm --cached ./probe

