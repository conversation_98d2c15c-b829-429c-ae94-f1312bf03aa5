GOPATH:=$(shell go env GOPATH)

.PHONY: init
init:
	@go get -u google.golang.org/protobuf/proto
	@go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	@go install github.com/go-micro/generator/cmd/protoc-gen-micro@latest

.PHONY: proto
proto:
	@protoc --proto_path=./internal/proto --micro_out=./internal --go_out=:./internal ./internal/proto/*.proto

.PHONY: update
update:
	@go get -u

.PHONY: tidy
tidy:
	@go mod tidy

.PHONY: build
build:
	cd cmd/grpc && \
	CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o data_analysis

.PHONY: test
test:
	@go test -v ./... -cover

.PHONY: docker
docker:
	docker build -t data_anlysis .
