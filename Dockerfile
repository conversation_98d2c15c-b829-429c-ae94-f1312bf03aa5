FROM centos:7.6.1810

WORKDIR /portscan
COPY portscan .
COPY config.yaml .
COPY script/masscan /usr/bin/masscan
COPY script/libpcap-1.5.3-12.el7.x86_64.rpm .
COPY script/libpcap-devel-1.5.3-12.el7.x86_64.rpm .
COPY script/logrotate.d/foscan /etc/logrotate.d/foscan

RUN yum install -y libpcap-devel-1.5.3-12.el7.x86_64.rpm libpcap-1.5.3-12.el7.x86_64.rpm nmap && \
    ln -s /usr/lib64/libpcap.so.1.5.3 /usr/lib64/libpcap.so.0.8 && \
    mkdir -p /foscan/logs && \
    chmod +x /usr/bin/masscan && \
    rm -rf *.rpm

ENTRYPOINT ["./portscan"]
CMD []
