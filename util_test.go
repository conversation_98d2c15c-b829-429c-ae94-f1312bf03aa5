package probe

import (
	"bufio"
	"bytes"
	sm "github.com/cch123/supermonkey"
	"io"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"os"
	"testing"
	"time"
)

func TestMain(m *testing.M) {
	go func() {
		http.HandleFunc("/", route)        //设置访问的路由
		http.ListenAndServe(":50060", nil) //设置监听的端口
	}()
	time.Sleep(2 * time.Second)
	m.Run()
}

func route(w http.ResponseWriter, r *http.Request) {
	switch {
	case r.URL.Path == "/server-info":
		ServeDAAPHTTP(w, r)
	case r.URL.Path == "/state":
		ServeDgraphHttp(w, r)
	case r.URL.Path == "/health":
		ServeDgraph(w, r)
	case len(r.URL.Path) == 5:
		ServeBeaconHTTP(w, r)
	default:
		ServeHTTP(w, r)
	}
}

func ServeHTTP(w http.ResponseWriter, r *http.Request) {
	w.Write([]byte("Dgraph browser is available for running separately using the dgraph-ratel binary"))
}

func listenTcpSendByte(info []byte, port string, ch chan<- int) {
	addr, err := net.ResolveTCPAddr("tcp4", "localhost:"+port)
	if err != nil {
		log.Fatal("resolvrIPAddr error : ", err)
	}

	//监听服务器地址
	listener, err := net.ListenTCP("tcp4", addr)
	if err != nil {
		log.Fatalf("listenTCP error:%s", err.Error())
	}
	defer listener.Close()
	ch <- 1
	for {
		conn, err := listener.AcceptTCP()
		if err != nil {
			log.Fatal("listenTCP error:", err)
		}
		defer conn.Close()
		_, err = conn.Write(info)
		if err != nil {
			log.Fatal("write error:", err)
		}
	}

}

func listenUdpSendBytes(info []byte, port int, ch chan<- int) {
	//监听服务器地址
	listener, err := net.ListenUDP("udp", &net.UDPAddr{
		IP:   net.IPv4(0, 0, 0, 0),
		Port: port,
	})
	ch <- 1
	if err != nil {
		log.Fatalf("listenTCP error:%s", err.Error())
		return
	}

	var data [1024]byte
	//读取UDP数据
	_, addr, err := listener.ReadFromUDP(data[:])
	if err != nil {
		log.Printf("read udp failed, err:%v\n", err)
	}
	//返回数据
	_, err = listener.WriteToUDP([]byte(info), addr)
	if err != nil {
		log.Printf("write udp failed, err:%v\n", err)
	}
}

func ServeDAAPHTTP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("DAAP-Server", "DAAP-Server/*******")
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte("220 Welkom bij DAAP. 331 Password required for anonymous"))
}

func ServeBeaconHTTP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("HTTP", "HTTP/1.1 404 Not Found")
	w.Header().Set("Content-Type", "text/plain")
	w.Header().Set("Content", "Content-Length: 0")

	data, err := ioutil.ReadFile("lH8q")
	if err != nil {
		log.Println(err)
		return
	}
	w.Write(data)
}

func ServeDgraphHttp(w http.ResponseWriter, r *http.Request) {
	w.Write([]byte("{\"counter\":\"55\",\"groups\":{\"1\":{\"members\":{\"1\":{\"id\":\"1\",\"groupId\":1,\"addr\":\"alpha:7080\",\"leader\":true,\"amDead\":false,\"lastUpdate\":\"1611032444\",\"clusterInfoOnly\":false,\"forceGroupId\":false}},\"tablets\":{\"active\":{\"groupId\":1,\"predicate\":\"active\",\"force\":false,\"onDiskBytes\":\"27684915\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"127401918\"},\"address\":{\"groupId\":1,\"predicate\":\"address\",\"force\":false,\"onDiskBytes\":\"211502372\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"433184724\"},\"address.formatted\":{\"groupId\":1,\"predicate\":\"address.formatted\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"aliased\":{\"groupId\":1,\"predicate\":\"aliased\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"aliases\":{\"groupId\":1,\"predicate\":\"aliases\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"car.plate\":{\"groupId\":1,\"predicate\":\"car.plate\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"debt\":{\"groupId\":1,\"predicate\":\"debt\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"debtors\":{\"groupId\":1,\"predicate\":\"debtors\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.acl.rule\":{\"groupId\":1,\"predicate\":\"dgraph.acl.rule\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.cors\":{\"groupId\":1,\"predicate\":\"dgraph.cors\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.drop.op\":{\"groupId\":1,\"predicate\":\"dgraph.drop.op\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.p_query\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.p_query\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.p_sha256hash\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.p_sha256hash\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.schema\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.schema\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.schema_created_at\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.schema_created_at\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.schema_history\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.schema_history\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.graphql.xid\":{\"groupId\":1,\"predicate\":\"dgraph.graphql.xid\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.password\":{\"groupId\":1,\"predicate\":\"dgraph.password\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.rule.permission\":{\"groupId\":1,\"predicate\":\"dgraph.rule.permission\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.rule.predicate\":{\"groupId\":1,\"predicate\":\"dgraph.rule.predicate\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.type\":{\"groupId\":1,\"predicate\":\"dgraph.type\",\"force\":false,\"onDiskBytes\":\"164826066\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"808890082\"},\"dgraph.user.group\":{\"groupId\":1,\"predicate\":\"dgraph.user.group\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"dgraph.xid\":{\"groupId\":1,\"predicate\":\"dgraph.xid\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"flagged\":{\"groupId\":1,\"predicate\":\"flagged\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"flags\":{\"groupId\":1,\"predicate\":\"flags\",\"force\":false,\"onDiskBytes\":\"12553\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"44313\"},\"id\":{\"groupId\":1,\"predicate\":\"id\",\"force\":false,\"onDiskBytes\":\"965990626\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"1767013168\"},\"name\":{\"groupId\":1,\"predicate\":\"name\",\"force\":false,\"onDiskBytes\":\"918718302\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"1555895963\"},\"owners\":{\"groupId\":1,\"predicate\":\"owners\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"owns\":{\"groupId\":1,\"predicate\":\"owns\",\"force\":false,\"onDiskBytes\":\"200405145\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"529575116\"},\"participants\":{\"groupId\":1,\"predicate\":\"participants\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"person.birthday\":{\"groupId\":1,\"predicate\":\"person.birthday\",\"force\":false,\"onDiskBytes\":\"66075563\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"223328000\"},\"person.gender\":{\"groupId\":1,\"predicate\":\"person.gender\",\"force\":false,\"onDiskBytes\":\"42829622\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"188605313\"},\"residents\":{\"groupId\":1,\"predicate\":\"residents\",\"force\":false,\"onDiskBytes\":\"0\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"0\"},\"roles\":{\"groupId\":1,\"predicate\":\"roles\",\"force\":false,\"onDiskBytes\":\"86004242\",\"remove\":false,\"readOnly\":false,\"moveTs\":\"0\",\"uncompressedBytes\":\"266245314\"}},\"snapshotTs\":\"5\",\"checksum\":\"15483284741041835584\",\"checkpointTs\":\"0\"}},\"zeros\":{\"1\":{\"id\":\"1\",\"groupId\":0,\"addr\":\"zero:5080\",\"leader\":true,\"amDead\":false,\"lastUpdate\":\"0\",\"clusterInfoOnly\":false,\"forceGroupId\":false}},\"maxLeaseId\":\"10000\",\"maxTxnTs\":\"10000\",\"maxRaftId\":\"1\",\"removed\":[],\"cid\":\"9f37fe8c-91a3-441b-a2e2-e11beefe7965\",\"license\":{\"user\":\"\",\"maxNodes\":\"18446744073709551615\",\"expiryTs\":\"**********\",\"enabled\":true}}"))
}

func ServeDgraph(w http.ResponseWriter, r *http.Request) {
	w.Write([]byte("[{\"instance\":\"zero\",\"address\":\"localhost:5080\",\"status\":\"healthy\",\"group\":\"0\",\"version\":\"v20.03.0\",\"uptime\":15727702,\"lastEcho\":**********},{\"instance\":\"alpha\",\"address\":\"localhost:7080\",\"status\":\"healthy\",\"group\":\"1\",\"version\":\"v20.03.0\",\"uptime\":15727703,\"lastEcho\":**********}]"))
}

func getValueFromArrBanner(banner []byte, key string) string {
	for _, v := range bytes.Split(banner, []byte("\n")) {
		fields := bytes.Split(v, []byte(": "))
		if len(fields) == 2 && string(fields[0]) == key {
			return string(fields[1])
		}
	}
	return ""
}

func patchMustReadUDP(data []byte) *sm.PatchGuard {
	return sm.Patch((*probe).MustReadUDP, func(_ *probe, n int) []byte {
		return data
	})
}

type PacketResult struct {
	Error error
	Data  []string
	Index int
}
type TestPacketReadWrite struct {
	Result  *PacketResult
	patches []*sm.PatchGuard
}

var GlobalPacketPatch = TestPacketReadWrite{}

func (tp *TestPacketReadWrite) initPatch() {
	p := patchWriteUDP()
	tp.patches = append(tp.patches, p)
	p = patchWriteTCP()
	tp.patches = append(tp.patches, p)
	p = patchWrite()
	tp.patches = append(tp.patches, p)
	p = patchGetTcp()
	tp.patches = append(tp.patches, p)
	p = patchGetUdp()
	p = sm.Patch((*probe).ReadTCP, tp.patchRead)
	tp.patches = append(tp.patches, p)
	p = sm.Patch((*probe).ReadUDP, tp.patchRead)
	tp.patches = append(tp.patches, p)
	p = sm.Patch((*probe).Read, tp.patchRead)
	tp.patches = append(tp.patches, p)

}

func (tp *TestPacketReadWrite) initHttpPatch() {
	p := patchReadResponse()
	tp.patches = append(tp.patches, p)
	p = patchWriteTCP()
	tp.patches = append(tp.patches, p)
	p = sm.Patch((*probe).GetTCPReader, tp.patchGetTCPReader)
	tp.patches = append(tp.patches, p)
	p = sm.Patch(ioutil.ReadAll, tp.patchIoReader)
	tp.patches = append(tp.patches, p)
}

func (tp *TestPacketReadWrite) unPatch() {
	for _, v := range tp.patches {
		v.Unpatch()
	}
}

func (tp *TestPacketReadWrite) WithRead(result *PacketResult) {
	tp.Result = result
}

func (tp *TestPacketReadWrite) patchRead(_ *probe, n int) ([]byte, error) {
	if len(tp.Result.Data) <= tp.Result.Index {
		return nil, tp.Result.Error
	}
	res := []byte(tp.Result.Data[tp.Result.Index])
	if n != -1 && len(res) >= n {
		res = []byte(tp.Result.Data[tp.Result.Index][:n])
	}

	//索引向后偏移
	tp.Result.Index++
	return res, tp.Result.Error
}

func patchGetTcp() *sm.PatchGuard {
	return sm.Patch((*probe).GetTCP, func(_ *probe) (tcp *net.TCPConn, err error) {
		return &net.TCPConn{}, nil
	})
}

func patchGetUdp() *sm.PatchGuard {
	return sm.Patch((*probe).GetUDP, func(_ *probe) (udp *net.UDPConn, err error) {
		return &net.UDPConn{}, nil
	})
}

func patchWriteTCP() *sm.PatchGuard {
	return sm.Patch((*probe).WriteTCP, func(_ *probe, b []byte) error {
		return nil
	})
}

func patchWriteUDP() *sm.PatchGuard {
	return sm.Patch((*probe).WriteUDP, func(_ *probe, b []byte) error {
		return nil
	})
}

func patchWrite() *sm.PatchGuard {
	return sm.Patch((*probe).Write, func(_ *probe, b []byte) error {
		return nil
	})
}

func Fetch(data []string, port string, fun ProbeFunc) (Info, error) {
	GlobalPacketPatch.initPatch()
	GlobalPacketPatch.WithRead(&PacketResult{Data: data})

	info, err := fun("localhost:" + port)

	GlobalPacketPatch.unPatch()
	return info, err
}

func patchReadResponse() *sm.PatchGuard {
	return sm.Patch(http.ReadResponse, func(r *bufio.Reader, req *http.Request) (*http.Response, error) {
		file, _ := os.Open("http_test.go")
		return &http.Response{
			Body: file,
		}, nil
	})
}

func (tp *TestPacketReadWrite) patchGetTCPReader(p *probe) *probeReader {

	res := []byte(tp.Result.Data[tp.Result.Index])

	tcp, _ := p.GetTCP()
	r := &probeReader{
		o:         p,
		reader:    tcp,
		respBytes: res,
	}
	return r
}

func (tp *TestPacketReadWrite) patchIoReader(r io.Reader) ([]byte, error) {

	res := []byte(tp.Result.Data[tp.Result.Index])
	//索引向后偏移
	tp.Result.Index++
	return res, tp.Result.Error
}
