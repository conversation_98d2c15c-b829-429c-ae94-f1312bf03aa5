// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: common.proto

package rpcx

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/anypb"
	math "math"
)

import (
	context "context"
	api "go-micro.dev/v4/api"
	client "go-micro.dev/v4/client"
	server "go-micro.dev/v4/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for RawGrab service

func NewRawGrabEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for RawGrab service

type RawGrabService interface {
	StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error)
	FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error)
	CustomProtocolNotify(ctx context.Context, in *EmptyNotifyRequest, opts ...client.CallOption) (*EmptyNotifyResponse, error)
}

type rawGrabService struct {
	c    client.Client
	name string
}

func NewRawGrabService(name string, c client.Client) RawGrabService {
	return &rawGrabService{
		c:    c,
		name: name,
	}
}

func (c *rawGrabService) StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error) {
	req := c.c.NewRequest(c.name, "RawGrab.StateQuery", in)
	out := new(StateQueryResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rawGrabService) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "RawGrab.FinishedNotify", in)
	out := new(FinishedNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rawGrabService) CustomProtocolNotify(ctx context.Context, in *EmptyNotifyRequest, opts ...client.CallOption) (*EmptyNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "RawGrab.CustomProtocolNotify", in)
	out := new(EmptyNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for RawGrab service

type RawGrabHandler interface {
	StateQuery(context.Context, *StateQueryRequest, *StateQueryResponse) error
	FinishedNotify(context.Context, *FinishedNotifyRequest, *FinishedNotifyResponse) error
	CustomProtocolNotify(context.Context, *EmptyNotifyRequest, *EmptyNotifyResponse) error
}

func RegisterRawGrabHandler(s server.Server, hdlr RawGrabHandler, opts ...server.HandlerOption) error {
	type rawGrab interface {
		StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error
		FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error
		CustomProtocolNotify(ctx context.Context, in *EmptyNotifyRequest, out *EmptyNotifyResponse) error
	}
	type RawGrab struct {
		rawGrab
	}
	h := &rawGrabHandler{hdlr}
	return s.Handle(s.NewHandler(&RawGrab{h}, opts...))
}

type rawGrabHandler struct {
	RawGrabHandler
}

func (h *rawGrabHandler) StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error {
	return h.RawGrabHandler.StateQuery(ctx, in, out)
}

func (h *rawGrabHandler) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error {
	return h.RawGrabHandler.FinishedNotify(ctx, in, out)
}

func (h *rawGrabHandler) CustomProtocolNotify(ctx context.Context, in *EmptyNotifyRequest, out *EmptyNotifyResponse) error {
	return h.RawGrabHandler.CustomProtocolNotify(ctx, in, out)
}

// Api Endpoints for CheckURL service

func NewCheckURLEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for CheckURL service

type CheckURLService interface {
	StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error)
	SaveIPDomainRelation(ctx context.Context, in *SaveIPDomainRelationRequest, opts ...client.CallOption) (*SaveIPDomainRelationResponse, error)
	FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error)
}

type checkURLService struct {
	c    client.Client
	name string
}

func NewCheckURLService(name string, c client.Client) CheckURLService {
	return &checkURLService{
		c:    c,
		name: name,
	}
}

func (c *checkURLService) StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error) {
	req := c.c.NewRequest(c.name, "CheckURL.StateQuery", in)
	out := new(StateQueryResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkURLService) SaveIPDomainRelation(ctx context.Context, in *SaveIPDomainRelationRequest, opts ...client.CallOption) (*SaveIPDomainRelationResponse, error) {
	req := c.c.NewRequest(c.name, "CheckURL.SaveIPDomainRelation", in)
	out := new(SaveIPDomainRelationResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkURLService) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "CheckURL.FinishedNotify", in)
	out := new(FinishedNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for CheckURL service

type CheckURLHandler interface {
	StateQuery(context.Context, *StateQueryRequest, *StateQueryResponse) error
	SaveIPDomainRelation(context.Context, *SaveIPDomainRelationRequest, *SaveIPDomainRelationResponse) error
	FinishedNotify(context.Context, *FinishedNotifyRequest, *FinishedNotifyResponse) error
}

func RegisterCheckURLHandler(s server.Server, hdlr CheckURLHandler, opts ...server.HandlerOption) error {
	type checkURL interface {
		StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error
		SaveIPDomainRelation(ctx context.Context, in *SaveIPDomainRelationRequest, out *SaveIPDomainRelationResponse) error
		FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error
	}
	type CheckURL struct {
		checkURL
	}
	h := &checkURLHandler{hdlr}
	return s.Handle(s.NewHandler(&CheckURL{h}, opts...))
}

type checkURLHandler struct {
	CheckURLHandler
}

func (h *checkURLHandler) StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error {
	return h.CheckURLHandler.StateQuery(ctx, in, out)
}

func (h *checkURLHandler) SaveIPDomainRelation(ctx context.Context, in *SaveIPDomainRelationRequest, out *SaveIPDomainRelationResponse) error {
	return h.CheckURLHandler.SaveIPDomainRelation(ctx, in, out)
}

func (h *checkURLHandler) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error {
	return h.CheckURLHandler.FinishedNotify(ctx, in, out)
}

// Api Endpoints for Crawler service

func NewCrawlerEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for Crawler service

type CrawlerService interface {
	StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error)
	FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error)
}

type crawlerService struct {
	c    client.Client
	name string
}

func NewCrawlerService(name string, c client.Client) CrawlerService {
	return &crawlerService{
		c:    c,
		name: name,
	}
}

func (c *crawlerService) StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error) {
	req := c.c.NewRequest(c.name, "Crawler.StateQuery", in)
	out := new(StateQueryResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlerService) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "Crawler.FinishedNotify", in)
	out := new(FinishedNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Crawler service

type CrawlerHandler interface {
	StateQuery(context.Context, *StateQueryRequest, *StateQueryResponse) error
	FinishedNotify(context.Context, *FinishedNotifyRequest, *FinishedNotifyResponse) error
}

func RegisterCrawlerHandler(s server.Server, hdlr CrawlerHandler, opts ...server.HandlerOption) error {
	type crawler interface {
		StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error
		FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error
	}
	type Crawler struct {
		crawler
	}
	h := &crawlerHandler{hdlr}
	return s.Handle(s.NewHandler(&Crawler{h}, opts...))
}

type crawlerHandler struct {
	CrawlerHandler
}

func (h *crawlerHandler) StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error {
	return h.CrawlerHandler.StateQuery(ctx, in, out)
}

func (h *crawlerHandler) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error {
	return h.CrawlerHandler.FinishedNotify(ctx, in, out)
}

// Api Endpoints for DataAnalysis service

func NewDataAnalysisEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for DataAnalysis service

type DataAnalysisService interface {
	StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error)
	FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error)
	CustomRuleAdd(ctx context.Context, in *CustomRuleRequest, opts ...client.CallOption) (*RuleResponse, error)
	CustomRuleUpdate(ctx context.Context, in *CustomRuleRequest, opts ...client.CallOption) (*RuleResponse, error)
	CustomRuleDelete(ctx context.Context, in *CustomRuleIDRequest, opts ...client.CallOption) (*RuleResponse, error)
}

type dataAnalysisService struct {
	c    client.Client
	name string
}

func NewDataAnalysisService(name string, c client.Client) DataAnalysisService {
	return &dataAnalysisService{
		c:    c,
		name: name,
	}
}

func (c *dataAnalysisService) StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error) {
	req := c.c.NewRequest(c.name, "DataAnalysis.StateQuery", in)
	out := new(StateQueryResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAnalysisService) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "DataAnalysis.FinishedNotify", in)
	out := new(FinishedNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAnalysisService) CustomRuleAdd(ctx context.Context, in *CustomRuleRequest, opts ...client.CallOption) (*RuleResponse, error) {
	req := c.c.NewRequest(c.name, "DataAnalysis.CustomRuleAdd", in)
	out := new(RuleResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAnalysisService) CustomRuleUpdate(ctx context.Context, in *CustomRuleRequest, opts ...client.CallOption) (*RuleResponse, error) {
	req := c.c.NewRequest(c.name, "DataAnalysis.CustomRuleUpdate", in)
	out := new(RuleResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAnalysisService) CustomRuleDelete(ctx context.Context, in *CustomRuleIDRequest, opts ...client.CallOption) (*RuleResponse, error) {
	req := c.c.NewRequest(c.name, "DataAnalysis.CustomRuleDelete", in)
	out := new(RuleResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for DataAnalysis service

type DataAnalysisHandler interface {
	StateQuery(context.Context, *StateQueryRequest, *StateQueryResponse) error
	FinishedNotify(context.Context, *FinishedNotifyRequest, *FinishedNotifyResponse) error
	CustomRuleAdd(context.Context, *CustomRuleRequest, *RuleResponse) error
	CustomRuleUpdate(context.Context, *CustomRuleRequest, *RuleResponse) error
	CustomRuleDelete(context.Context, *CustomRuleIDRequest, *RuleResponse) error
}

func RegisterDataAnalysisHandler(s server.Server, hdlr DataAnalysisHandler, opts ...server.HandlerOption) error {
	type dataAnalysis interface {
		StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error
		FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error
		CustomRuleAdd(ctx context.Context, in *CustomRuleRequest, out *RuleResponse) error
		CustomRuleUpdate(ctx context.Context, in *CustomRuleRequest, out *RuleResponse) error
		CustomRuleDelete(ctx context.Context, in *CustomRuleIDRequest, out *RuleResponse) error
	}
	type DataAnalysis struct {
		dataAnalysis
	}
	h := &dataAnalysisHandler{hdlr}
	return s.Handle(s.NewHandler(&DataAnalysis{h}, opts...))
}

type dataAnalysisHandler struct {
	DataAnalysisHandler
}

func (h *dataAnalysisHandler) StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error {
	return h.DataAnalysisHandler.StateQuery(ctx, in, out)
}

func (h *dataAnalysisHandler) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error {
	return h.DataAnalysisHandler.FinishedNotify(ctx, in, out)
}

func (h *dataAnalysisHandler) CustomRuleAdd(ctx context.Context, in *CustomRuleRequest, out *RuleResponse) error {
	return h.DataAnalysisHandler.CustomRuleAdd(ctx, in, out)
}

func (h *dataAnalysisHandler) CustomRuleUpdate(ctx context.Context, in *CustomRuleRequest, out *RuleResponse) error {
	return h.DataAnalysisHandler.CustomRuleUpdate(ctx, in, out)
}

func (h *dataAnalysisHandler) CustomRuleDelete(ctx context.Context, in *CustomRuleIDRequest, out *RuleResponse) error {
	return h.DataAnalysisHandler.CustomRuleDelete(ctx, in, out)
}

// Api Endpoints for QuickStore service

func NewQuickStoreEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for QuickStore service

type QuickStoreService interface {
	StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error)
	FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error)
}

type quickStoreService struct {
	c    client.Client
	name string
}

func NewQuickStoreService(name string, c client.Client) QuickStoreService {
	return &quickStoreService{
		c:    c,
		name: name,
	}
}

func (c *quickStoreService) StateQuery(ctx context.Context, in *StateQueryRequest, opts ...client.CallOption) (*StateQueryResponse, error) {
	req := c.c.NewRequest(c.name, "QuickStore.StateQuery", in)
	out := new(StateQueryResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quickStoreService) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, opts ...client.CallOption) (*FinishedNotifyResponse, error) {
	req := c.c.NewRequest(c.name, "QuickStore.FinishedNotify", in)
	out := new(FinishedNotifyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for QuickStore service

type QuickStoreHandler interface {
	StateQuery(context.Context, *StateQueryRequest, *StateQueryResponse) error
	FinishedNotify(context.Context, *FinishedNotifyRequest, *FinishedNotifyResponse) error
}

func RegisterQuickStoreHandler(s server.Server, hdlr QuickStoreHandler, opts ...server.HandlerOption) error {
	type quickStore interface {
		StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error
		FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error
	}
	type QuickStore struct {
		quickStore
	}
	h := &quickStoreHandler{hdlr}
	return s.Handle(s.NewHandler(&QuickStore{h}, opts...))
}

type quickStoreHandler struct {
	QuickStoreHandler
}

func (h *quickStoreHandler) StateQuery(ctx context.Context, in *StateQueryRequest, out *StateQueryResponse) error {
	return h.QuickStoreHandler.StateQuery(ctx, in, out)
}

func (h *quickStoreHandler) FinishedNotify(ctx context.Context, in *FinishedNotifyRequest, out *FinishedNotifyResponse) error {
	return h.QuickStoreHandler.FinishedNotify(ctx, in, out)
}

// Api Endpoints for DispatcherTaskService service

func NewDispatcherTaskServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for DispatcherTaskService service

type DispatcherTaskService interface {
	Start(ctx context.Context, in *DispatcherTaskStartRequest, opts ...client.CallOption) (*DispatcherTaskResponse, error)
	Pause(ctx context.Context, in *DispatcherTaskPauseRequest, opts ...client.CallOption) (*DispatcherTaskPauseResponse, error)
	Resume(ctx context.Context, in *DispatcherTaskResumeRequest, opts ...client.CallOption) (*DispatcherTaskResumeResponse, error)
	Stop(ctx context.Context, in *DispatcherTaskStopRequest, opts ...client.CallOption) (*DispatcherTaskStopResponse, error)
	State(ctx context.Context, in *DispatcherTaskStateRequest, opts ...client.CallOption) (*DispatcherTaskStateResponse, error)
}

type dispatcherTaskService struct {
	c    client.Client
	name string
}

func NewDispatcherTaskService(name string, c client.Client) DispatcherTaskService {
	return &dispatcherTaskService{
		c:    c,
		name: name,
	}
}

func (c *dispatcherTaskService) Start(ctx context.Context, in *DispatcherTaskStartRequest, opts ...client.CallOption) (*DispatcherTaskResponse, error) {
	req := c.c.NewRequest(c.name, "DispatcherTaskService.Start", in)
	out := new(DispatcherTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherTaskService) Pause(ctx context.Context, in *DispatcherTaskPauseRequest, opts ...client.CallOption) (*DispatcherTaskPauseResponse, error) {
	req := c.c.NewRequest(c.name, "DispatcherTaskService.Pause", in)
	out := new(DispatcherTaskPauseResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherTaskService) Resume(ctx context.Context, in *DispatcherTaskResumeRequest, opts ...client.CallOption) (*DispatcherTaskResumeResponse, error) {
	req := c.c.NewRequest(c.name, "DispatcherTaskService.Resume", in)
	out := new(DispatcherTaskResumeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherTaskService) Stop(ctx context.Context, in *DispatcherTaskStopRequest, opts ...client.CallOption) (*DispatcherTaskStopResponse, error) {
	req := c.c.NewRequest(c.name, "DispatcherTaskService.Stop", in)
	out := new(DispatcherTaskStopResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherTaskService) State(ctx context.Context, in *DispatcherTaskStateRequest, opts ...client.CallOption) (*DispatcherTaskStateResponse, error) {
	req := c.c.NewRequest(c.name, "DispatcherTaskService.State", in)
	out := new(DispatcherTaskStateResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for DispatcherTaskService service

type DispatcherTaskServiceHandler interface {
	Start(context.Context, *DispatcherTaskStartRequest, *DispatcherTaskResponse) error
	Pause(context.Context, *DispatcherTaskPauseRequest, *DispatcherTaskPauseResponse) error
	Resume(context.Context, *DispatcherTaskResumeRequest, *DispatcherTaskResumeResponse) error
	Stop(context.Context, *DispatcherTaskStopRequest, *DispatcherTaskStopResponse) error
	State(context.Context, *DispatcherTaskStateRequest, *DispatcherTaskStateResponse) error
}

func RegisterDispatcherTaskServiceHandler(s server.Server, hdlr DispatcherTaskServiceHandler, opts ...server.HandlerOption) error {
	type dispatcherTaskService interface {
		Start(ctx context.Context, in *DispatcherTaskStartRequest, out *DispatcherTaskResponse) error
		Pause(ctx context.Context, in *DispatcherTaskPauseRequest, out *DispatcherTaskPauseResponse) error
		Resume(ctx context.Context, in *DispatcherTaskResumeRequest, out *DispatcherTaskResumeResponse) error
		Stop(ctx context.Context, in *DispatcherTaskStopRequest, out *DispatcherTaskStopResponse) error
		State(ctx context.Context, in *DispatcherTaskStateRequest, out *DispatcherTaskStateResponse) error
	}
	type DispatcherTaskService struct {
		dispatcherTaskService
	}
	h := &dispatcherTaskServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&DispatcherTaskService{h}, opts...))
}

type dispatcherTaskServiceHandler struct {
	DispatcherTaskServiceHandler
}

func (h *dispatcherTaskServiceHandler) Start(ctx context.Context, in *DispatcherTaskStartRequest, out *DispatcherTaskResponse) error {
	return h.DispatcherTaskServiceHandler.Start(ctx, in, out)
}

func (h *dispatcherTaskServiceHandler) Pause(ctx context.Context, in *DispatcherTaskPauseRequest, out *DispatcherTaskPauseResponse) error {
	return h.DispatcherTaskServiceHandler.Pause(ctx, in, out)
}

func (h *dispatcherTaskServiceHandler) Resume(ctx context.Context, in *DispatcherTaskResumeRequest, out *DispatcherTaskResumeResponse) error {
	return h.DispatcherTaskServiceHandler.Resume(ctx, in, out)
}

func (h *dispatcherTaskServiceHandler) Stop(ctx context.Context, in *DispatcherTaskStopRequest, out *DispatcherTaskStopResponse) error {
	return h.DispatcherTaskServiceHandler.Stop(ctx, in, out)
}

func (h *dispatcherTaskServiceHandler) State(ctx context.Context, in *DispatcherTaskStateRequest, out *DispatcherTaskStateResponse) error {
	return h.DispatcherTaskServiceHandler.State(ctx, in, out)
}
