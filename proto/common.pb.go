// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.17.3
// source: common.proto

package rpcx

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StateQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"` // 任务id
}

func (x *StateQueryRequest) Reset() {
	*x = StateQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateQueryRequest) ProtoMessage() {}

func (x *StateQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateQueryRequest.ProtoReflect.Descriptor instead.
func (*StateQueryRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *StateQueryRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type StateQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total  int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`   // 任务总数
	Remain int64 `protobuf:"varint,2,opt,name=remain,proto3" json:"remain"` // 剩余任务数
}

func (x *StateQueryResponse) Reset() {
	*x = StateQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateQueryResponse) ProtoMessage() {}

func (x *StateQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateQueryResponse.ProtoReflect.Descriptor instead.
func (*StateQueryResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *StateQueryResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StateQueryResponse) GetRemain() int64 {
	if x != nil {
		return x.Remain
	}
	return 0
}

type FinishedNotifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"` // 任务id
}

func (x *FinishedNotifyRequest) Reset() {
	*x = FinishedNotifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishedNotifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishedNotifyRequest) ProtoMessage() {}

func (x *FinishedNotifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishedNotifyRequest.ProtoReflect.Descriptor instead.
func (*FinishedNotifyRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *FinishedNotifyRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type SaveIPDomainRelationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId            string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	IpDomainRelations map[string]*StringList `protobuf:"bytes,2,rep,name=ip_domain_relations,json=ipDomainRelations,proto3" json:"ip_domain_relations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SaveIPDomainRelationRequest) Reset() {
	*x = SaveIPDomainRelationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveIPDomainRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveIPDomainRelationRequest) ProtoMessage() {}

func (x *SaveIPDomainRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveIPDomainRelationRequest.ProtoReflect.Descriptor instead.
func (*SaveIPDomainRelationRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

func (x *SaveIPDomainRelationRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SaveIPDomainRelationRequest) GetIpDomainRelations() map[string]*StringList {
	if x != nil {
		return x.IpDomainRelations
	}
	return nil
}

type StringList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domains []string `protobuf:"bytes,1,rep,name=domains,proto3" json:"domains"`
}

func (x *StringList) Reset() {
	*x = StringList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringList) ProtoMessage() {}

func (x *StringList) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringList.ProtoReflect.Descriptor instead.
func (*StringList) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *StringList) GetDomains() []string {
	if x != nil {
		return x.Domains
	}
	return nil
}

type SaveIPDomainRelationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
}

func (x *SaveIPDomainRelationResponse) Reset() {
	*x = SaveIPDomainRelationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveIPDomainRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveIPDomainRelationResponse) ProtoMessage() {}

func (x *SaveIPDomainRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveIPDomainRelationResponse.ProtoReflect.Descriptor instead.
func (*SaveIPDomainRelationResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

func (x *SaveIPDomainRelationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type FinishedNotifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FinishedNotifyResponse) Reset() {
	*x = FinishedNotifyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishedNotifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishedNotifyResponse) ProtoMessage() {}

func (x *FinishedNotifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishedNotifyResponse.ProtoReflect.Descriptor instead.
func (*FinishedNotifyResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

type CustomRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product        string `protobuf:"bytes,1,opt,name=product,proto3" json:"product"`
	Rule           string `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule"`
	RuleId         string `protobuf:"bytes,3,opt,name=rule_id,json=ruleId,proto3" json:"rule_id"`
	Level          string `protobuf:"bytes,5,opt,name=level,proto3" json:"level"`
	Category       string `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	ParentCategory string `protobuf:"bytes,7,opt,name=parent_category,json=parentCategory,proto3" json:"parent_category"`
	Softhard       string `protobuf:"bytes,8,opt,name=softhard,proto3" json:"softhard"`
	Company        string `protobuf:"bytes,9,opt,name=company,proto3" json:"company"`
	Version        string `protobuf:"bytes,10,opt,name=version,proto3" json:"version"`
	From           string `protobuf:"bytes,11,opt,name=from,proto3" json:"from"`
	CachedAt       string `protobuf:"bytes,12,opt,name=cached_at,json=cachedAt,proto3" json:"cached_at"`
}

func (x *CustomRuleRequest) Reset() {
	*x = CustomRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRuleRequest) ProtoMessage() {}

func (x *CustomRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRuleRequest.ProtoReflect.Descriptor instead.
func (*CustomRuleRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *CustomRuleRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *CustomRuleRequest) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

func (x *CustomRuleRequest) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *CustomRuleRequest) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *CustomRuleRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CustomRuleRequest) GetParentCategory() string {
	if x != nil {
		return x.ParentCategory
	}
	return ""
}

func (x *CustomRuleRequest) GetSofthard() string {
	if x != nil {
		return x.Softhard
	}
	return ""
}

func (x *CustomRuleRequest) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *CustomRuleRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CustomRuleRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *CustomRuleRequest) GetCachedAt() string {
	if x != nil {
		return x.CachedAt
	}
	return ""
}

type CustomRuleIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleId string `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id"`
}

func (x *CustomRuleIDRequest) Reset() {
	*x = CustomRuleIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomRuleIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRuleIDRequest) ProtoMessage() {}

func (x *CustomRuleIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRuleIDRequest.ProtoReflect.Descriptor instead.
func (*CustomRuleIDRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

func (x *CustomRuleIDRequest) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

type RuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
}

func (x *RuleResponse) Reset() {
	*x = RuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleResponse) ProtoMessage() {}

func (x *RuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleResponse.ProtoReflect.Descriptor instead.
func (*RuleResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *RuleResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type EmptyNotifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyNotifyRequest) Reset() {
	*x = EmptyNotifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyNotifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyNotifyRequest) ProtoMessage() {}

func (x *EmptyNotifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyNotifyRequest.ProtoReflect.Descriptor instead.
func (*EmptyNotifyRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{10}
}

type EmptyNotifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyNotifyResponse) Reset() {
	*x = EmptyNotifyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyNotifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyNotifyResponse) ProtoMessage() {}

func (x *EmptyNotifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyNotifyResponse.ProtoReflect.Descriptor instead.
func (*EmptyNotifyResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{11}
}

type RawGrabEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo     *TaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
	JobId        string    `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	Origin       string    `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin"`
	Ip           string    `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Port         uint32    `protobuf:"varint,5,opt,name=port,proto3" json:"port"`
	BaseProtocol string    `protobuf:"bytes,6,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol"`
	BindProtocol string    `protobuf:"bytes,7,opt,name=bind_protocol,json=bindProtocol,proto3" json:"bind_protocol"`
}

func (x *RawGrabEvent) Reset() {
	*x = RawGrabEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawGrabEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawGrabEvent) ProtoMessage() {}

func (x *RawGrabEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawGrabEvent.ProtoReflect.Descriptor instead.
func (*RawGrabEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{12}
}

func (x *RawGrabEvent) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *RawGrabEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *RawGrabEvent) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *RawGrabEvent) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *RawGrabEvent) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *RawGrabEvent) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

func (x *RawGrabEvent) GetBindProtocol() string {
	if x != nil {
		return x.BindProtocol
	}
	return ""
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId              string     `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	IsIpv6              bool       `protobuf:"varint,2,opt,name=is_ipv6,json=isIpv6,proto3" json:"is_ipv6"`                                            // 是否是ipv6扫描
	TaskType            int32      `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3" json:"task_type"`                                      // 任务类型
	UnknownProtocolInDb bool       `protobuf:"varint,7,opt,name=unknown_protocol_in_db,json=unknownProtocolInDb,proto3" json:"unknown_protocol_in_db"` // 未知协议是否入库
	IsCrawlerAllUrl     bool       `protobuf:"varint,8,opt,name=is_crawler_all_url,json=isCrawlerAllUrl,proto3" json:"is_crawler_all_url"`             // 是否开启全站爬虫（调用goby爬虫）
	CrawlerSpecificUrl  string     `protobuf:"bytes,9,opt,name=crawler_specific_url,json=crawlerSpecificUrl,proto3" json:"crawler_specific_url"`       // 爬取指定URL
	CrawlerUrlBlackKey  string     `protobuf:"bytes,10,opt,name=crawler_url_black_key,json=crawlerUrlBlackKey,proto3" json:"crawler_url_black_key"`    // URL黑名单
	Extra               *anypb.Any `protobuf:"bytes,11,opt,name=extra,proto3,oneof" json:"extra"`                                                      // 透传字段，会原样写入到service、subdomain
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{13}
}

func (x *TaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskInfo) GetIsIpv6() bool {
	if x != nil {
		return x.IsIpv6
	}
	return false
}

func (x *TaskInfo) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *TaskInfo) GetUnknownProtocolInDb() bool {
	if x != nil {
		return x.UnknownProtocolInDb
	}
	return false
}

func (x *TaskInfo) GetIsCrawlerAllUrl() bool {
	if x != nil {
		return x.IsCrawlerAllUrl
	}
	return false
}

func (x *TaskInfo) GetCrawlerSpecificUrl() string {
	if x != nil {
		return x.CrawlerSpecificUrl
	}
	return ""
}

func (x *TaskInfo) GetCrawlerUrlBlackKey() string {
	if x != nil {
		return x.CrawlerUrlBlackKey
	}
	return ""
}

func (x *TaskInfo) GetExtra() *anypb.Any {
	if x != nil {
		return x.Extra
	}
	return nil
}

type QuickStoreEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo *TaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
	JobId    string    `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	Origin   string    `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin"`
	// Types that are assignable to Data:
	//	*QuickStoreEvent_Normal
	//	*QuickStoreEvent_FullSiteCrawler
	Data isQuickStoreEvent_Data `protobuf_oneof:"data"`
}

func (x *QuickStoreEvent) Reset() {
	*x = QuickStoreEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickStoreEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickStoreEvent) ProtoMessage() {}

func (x *QuickStoreEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickStoreEvent.ProtoReflect.Descriptor instead.
func (*QuickStoreEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{14}
}

func (x *QuickStoreEvent) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *QuickStoreEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *QuickStoreEvent) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (m *QuickStoreEvent) GetData() isQuickStoreEvent_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *QuickStoreEvent) GetNormal() *Normal {
	if x, ok := x.GetData().(*QuickStoreEvent_Normal); ok {
		return x.Normal
	}
	return nil
}

func (x *QuickStoreEvent) GetFullSiteCrawler() *FullSiteCrawler {
	if x, ok := x.GetData().(*QuickStoreEvent_FullSiteCrawler); ok {
		return x.FullSiteCrawler
	}
	return nil
}

type isQuickStoreEvent_Data interface {
	isQuickStoreEvent_Data()
}

type QuickStoreEvent_Normal struct {
	Normal *Normal `protobuf:"bytes,4,opt,name=normal,proto3,oneof"`
}

type QuickStoreEvent_FullSiteCrawler struct {
	FullSiteCrawler *FullSiteCrawler `protobuf:"bytes,5,opt,name=full_site_crawler,json=fullSiteCrawler,proto3,oneof"`
}

func (*QuickStoreEvent_Normal) isQuickStoreEvent_Data() {}

func (*QuickStoreEvent_FullSiteCrawler) isQuickStoreEvent_Data() {}

type DataAnalysisEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo *TaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
	JobId    string    `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	Origin   string    `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin"`
	Normal   *Normal   `protobuf:"bytes,4,opt,name=normal,proto3" json:"normal"`
}

func (x *DataAnalysisEvent) Reset() {
	*x = DataAnalysisEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataAnalysisEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataAnalysisEvent) ProtoMessage() {}

func (x *DataAnalysisEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataAnalysisEvent.ProtoReflect.Descriptor instead.
func (*DataAnalysisEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{15}
}

func (x *DataAnalysisEvent) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *DataAnalysisEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *DataAnalysisEvent) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *DataAnalysisEvent) GetNormal() *Normal {
	if x != nil {
		return x.Normal
	}
	return nil
}

type CheckURLEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo     *TaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
	JobId        string    `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	Ip           string    `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip"`
	Port         uint32    `protobuf:"varint,4,opt,name=port,proto3" json:"port"`
	Protocol     string    `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol"`
	BaseProtocol string    `protobuf:"bytes,6,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol"`
}

func (x *CheckURLEvent) Reset() {
	*x = CheckURLEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckURLEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckURLEvent) ProtoMessage() {}

func (x *CheckURLEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckURLEvent.ProtoReflect.Descriptor instead.
func (*CheckURLEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{16}
}

func (x *CheckURLEvent) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *CheckURLEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *CheckURLEvent) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CheckURLEvent) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CheckURLEvent) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *CheckURLEvent) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

type CrawlerEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo     *TaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
	JobId        string    `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	Ip           string    `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip"`
	Port         uint32    `protobuf:"varint,4,opt,name=port,proto3" json:"port"`
	Protocol     string    `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol"`
	BaseProtocol string    `protobuf:"bytes,6,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol"`
	Url          string    `protobuf:"bytes,7,opt,name=url,proto3" json:"url"` // 要抓取的完整url
	Domain       string    `protobuf:"bytes,8,opt,name=domain,proto3" json:"domain"`
	Subdomain    string    `protobuf:"bytes,9,opt,name=subdomain,proto3" json:"subdomain"`
	Host         string    `protobuf:"bytes,10,opt,name=host,proto3" json:"host"`
}

func (x *CrawlerEvent) Reset() {
	*x = CrawlerEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrawlerEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrawlerEvent) ProtoMessage() {}

func (x *CrawlerEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrawlerEvent.ProtoReflect.Descriptor instead.
func (*CrawlerEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{17}
}

func (x *CrawlerEvent) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *CrawlerEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *CrawlerEvent) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CrawlerEvent) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CrawlerEvent) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *CrawlerEvent) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

func (x *CrawlerEvent) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CrawlerEvent) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CrawlerEvent) GetSubdomain() string {
	if x != nil {
		return x.Subdomain
	}
	return ""
}

func (x *CrawlerEvent) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type Normal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip           string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip"`
	Port         uint32 `protobuf:"varint,2,opt,name=port,proto3" json:"port"`
	Protocol     string `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol"`
	BaseProtocol string `protobuf:"bytes,4,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol"`
	IsIpv6       bool   `protobuf:"varint,5,opt,name=is_ipv6,json=isIpv6,proto3" json:"is_ipv6"`
	Banner       string `protobuf:"bytes,6,opt,name=banner,proto3" json:"banner"`
	BannerLen    uint32 `protobuf:"varint,7,opt,name=banner_len,json=bannerLen,proto3" json:"banner_len"`
	Timestamp    string `protobuf:"bytes,8,opt,name=timestamp,proto3" json:"timestamp"`
	// website's string cert
	Cert *string `protobuf:"bytes,9,opt,name=cert,proto3,oneof" json:"cert"`
	// the ip's hostname array
	Hostnames []string    `protobuf:"bytes,10,rep,name=hostnames,proto3" json:"hostnames"`
	Certs     *CertObject `protobuf:"bytes,11,opt,name=certs,proto3,oneof" json:"certs"`
	Appserver []string    `protobuf:"bytes,12,rep,name=appserver,proto3" json:"appserver"`
	// website dom object
	Dom    *anypb.Any `protobuf:"bytes,13,opt,name=dom,proto3,oneof" json:"dom"`
	Domain *string    `protobuf:"bytes,14,opt,name=domain,proto3,oneof" json:"domain"`
	// website icon object
	Favicon *anypb.Any `protobuf:"bytes,15,opt,name=favicon,proto3,oneof" json:"favicon"`
	// website's dom fid by EHash calculate
	Fid     *string  `protobuf:"bytes,16,opt,name=fid,proto3,oneof" json:"fid"`
	Header  *string  `protobuf:"bytes,17,opt,name=header,proto3,oneof" json:"header"`
	Host    *string  `protobuf:"bytes,18,opt,name=host,proto3,oneof" json:"host"`
	Body    *string  `protobuf:"bytes,19,opt,name=body,proto3,oneof" json:"body"`
	Title   *string  `protobuf:"bytes,20,opt,name=title,proto3,oneof" json:"title"`
	Version []string `protobuf:"bytes,21,rep,name=version,proto3" json:"version"`
	// 是否是垃圾网站
	IsFraud *bool `protobuf:"varint,22,opt,name=is_fraud,json=isFraud,proto3,oneof" json:"is_fraud"`
	// 垃圾网站名称
	FraudName *string `protobuf:"bytes,23,opt,name=fraud_name,json=fraudName,proto3,oneof" json:"fraud_name"`
	// 是否是蜜罐
	IsHoneypot *bool `protobuf:"varint,24,opt,name=is_honeypot,json=isHoneypot,proto3,oneof" json:"is_honeypot"`
	//蜜罐名称
	HoneypotName *string `protobuf:"bytes,25,opt,name=honeypot_name,json=honeypotName,proto3,oneof" json:"honeypot_name"`
	Server       *string `protobuf:"bytes,26,opt,name=server,proto3,oneof" json:"server"`
	// 子域名
	Subdomain *string `protobuf:"bytes,27,opt,name=subdomain,proto3,oneof" json:"subdomain"`
	// 是否是域名
	IsDomain *bool `protobuf:"varint,28,opt,name=is_domain,json=isDomain,proto3,oneof" json:"is_domain"`
	// 网站编码
	Charset *string `protobuf:"bytes,29,opt,name=charset,proto3,oneof" json:"charset"`
	// response http code
	StatusCode uint32 `protobuf:"varint,30,opt,name=status_code,json=statusCode,proto3" json:"status_code"`
	Utf8Html   string `protobuf:"bytes,31,opt,name=utf8html,proto3" json:"utf8html"`
	SubBody    string `protobuf:"bytes,32,opt,name=sub_body,json=subBody,proto3" json:"sub_body"`
	// 开发语言
	Language []string `protobuf:"bytes,33,rep,name=language,proto3" json:"language"`
	// 中间件
	Middleware []string `protobuf:"bytes,34,rep,name=middleware,proto3" json:"middleware"`
	// 操作系统
	Os []string `protobuf:"bytes,35,rep,name=os,proto3" json:"os"`
	// 硬件产品型号
	Modal       []string   `protobuf:"bytes,36,rep,name=modal,proto3" json:"modal"`
	LocationUrl string     `protobuf:"bytes,37,opt,name=location_url,json=locationUrl,proto3" json:"location_url"`
	Ipcnet      string     `protobuf:"bytes,38,opt,name=ipcnet,proto3" json:"ipcnet"`
	Geoip       *Geoip     `protobuf:"bytes,39,opt,name=geoip,proto3" json:"geoip"`
	Product     []string   `protobuf:"bytes,40,rep,name=product,proto3" json:"product"`
	RuleTags    []*RuleTag `protobuf:"bytes,41,rep,name=rule_tags,json=ruleTags,proto3" json:"rule_tags"`
	Asn         *ASN       `protobuf:"bytes,42,opt,name=asn,proto3" json:"asn"`
	Mac         string     `protobuf:"bytes,43,opt,name=mac,proto3" json:"mac"`
	NetbiosName string     `protobuf:"bytes,44,opt,name=netbios_name,json=netbiosName,proto3" json:"netbios_name"`
	JsInfo      *JsInfo    `protobuf:"bytes,45,opt,name=js_info,json=jsInfo,proto3" json:"js_info"`
	IsSensitive bool       `protobuf:"varint,46,opt,name=is_sensitive,json=isSensitive,proto3" json:"is_sensitive"`
	Jarm        *Jarm      `protobuf:"bytes,47,opt,name=jarm,proto3" json:"jarm"`
	Url         *string    `protobuf:"bytes,48,opt,name=url,proto3,oneof" json:"url"`
}

func (x *Normal) Reset() {
	*x = Normal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Normal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Normal) ProtoMessage() {}

func (x *Normal) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Normal.ProtoReflect.Descriptor instead.
func (*Normal) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{18}
}

func (x *Normal) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Normal) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Normal) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *Normal) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

func (x *Normal) GetIsIpv6() bool {
	if x != nil {
		return x.IsIpv6
	}
	return false
}

func (x *Normal) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *Normal) GetBannerLen() uint32 {
	if x != nil {
		return x.BannerLen
	}
	return 0
}

func (x *Normal) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *Normal) GetCert() string {
	if x != nil && x.Cert != nil {
		return *x.Cert
	}
	return ""
}

func (x *Normal) GetHostnames() []string {
	if x != nil {
		return x.Hostnames
	}
	return nil
}

func (x *Normal) GetCerts() *CertObject {
	if x != nil {
		return x.Certs
	}
	return nil
}

func (x *Normal) GetAppserver() []string {
	if x != nil {
		return x.Appserver
	}
	return nil
}

func (x *Normal) GetDom() *anypb.Any {
	if x != nil {
		return x.Dom
	}
	return nil
}

func (x *Normal) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *Normal) GetFavicon() *anypb.Any {
	if x != nil {
		return x.Favicon
	}
	return nil
}

func (x *Normal) GetFid() string {
	if x != nil && x.Fid != nil {
		return *x.Fid
	}
	return ""
}

func (x *Normal) GetHeader() string {
	if x != nil && x.Header != nil {
		return *x.Header
	}
	return ""
}

func (x *Normal) GetHost() string {
	if x != nil && x.Host != nil {
		return *x.Host
	}
	return ""
}

func (x *Normal) GetBody() string {
	if x != nil && x.Body != nil {
		return *x.Body
	}
	return ""
}

func (x *Normal) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Normal) GetVersion() []string {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *Normal) GetIsFraud() bool {
	if x != nil && x.IsFraud != nil {
		return *x.IsFraud
	}
	return false
}

func (x *Normal) GetFraudName() string {
	if x != nil && x.FraudName != nil {
		return *x.FraudName
	}
	return ""
}

func (x *Normal) GetIsHoneypot() bool {
	if x != nil && x.IsHoneypot != nil {
		return *x.IsHoneypot
	}
	return false
}

func (x *Normal) GetHoneypotName() string {
	if x != nil && x.HoneypotName != nil {
		return *x.HoneypotName
	}
	return ""
}

func (x *Normal) GetServer() string {
	if x != nil && x.Server != nil {
		return *x.Server
	}
	return ""
}

func (x *Normal) GetSubdomain() string {
	if x != nil && x.Subdomain != nil {
		return *x.Subdomain
	}
	return ""
}

func (x *Normal) GetIsDomain() bool {
	if x != nil && x.IsDomain != nil {
		return *x.IsDomain
	}
	return false
}

func (x *Normal) GetCharset() string {
	if x != nil && x.Charset != nil {
		return *x.Charset
	}
	return ""
}

func (x *Normal) GetStatusCode() uint32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *Normal) GetUtf8Html() string {
	if x != nil {
		return x.Utf8Html
	}
	return ""
}

func (x *Normal) GetSubBody() string {
	if x != nil {
		return x.SubBody
	}
	return ""
}

func (x *Normal) GetLanguage() []string {
	if x != nil {
		return x.Language
	}
	return nil
}

func (x *Normal) GetMiddleware() []string {
	if x != nil {
		return x.Middleware
	}
	return nil
}

func (x *Normal) GetOs() []string {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *Normal) GetModal() []string {
	if x != nil {
		return x.Modal
	}
	return nil
}

func (x *Normal) GetLocationUrl() string {
	if x != nil {
		return x.LocationUrl
	}
	return ""
}

func (x *Normal) GetIpcnet() string {
	if x != nil {
		return x.Ipcnet
	}
	return ""
}

func (x *Normal) GetGeoip() *Geoip {
	if x != nil {
		return x.Geoip
	}
	return nil
}

func (x *Normal) GetProduct() []string {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *Normal) GetRuleTags() []*RuleTag {
	if x != nil {
		return x.RuleTags
	}
	return nil
}

func (x *Normal) GetAsn() *ASN {
	if x != nil {
		return x.Asn
	}
	return nil
}

func (x *Normal) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *Normal) GetNetbiosName() string {
	if x != nil {
		return x.NetbiosName
	}
	return ""
}

func (x *Normal) GetJsInfo() *JsInfo {
	if x != nil {
		return x.JsInfo
	}
	return nil
}

func (x *Normal) GetIsSensitive() bool {
	if x != nil {
		return x.IsSensitive
	}
	return false
}

func (x *Normal) GetJarm() *Jarm {
	if x != nil {
		return x.Jarm
	}
	return nil
}

func (x *Normal) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

type JsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5  []string `protobuf:"bytes,1,rep,name=md5,proto3" json:"md5"`
	Len  []int64  `protobuf:"varint,2,rep,packed,name=len,proto3" json:"len"`
	Name []string `protobuf:"bytes,3,rep,name=name,proto3" json:"name"`
	Num  int64    `protobuf:"varint,4,opt,name=num,proto3" json:"num"`
}

func (x *JsInfo) Reset() {
	*x = JsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JsInfo) ProtoMessage() {}

func (x *JsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JsInfo.ProtoReflect.Descriptor instead.
func (*JsInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{19}
}

func (x *JsInfo) GetMd5() []string {
	if x != nil {
		return x.Md5
	}
	return nil
}

func (x *JsInfo) GetLen() []int64 {
	if x != nil {
		return x.Len
	}
	return nil
}

func (x *JsInfo) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *JsInfo) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type Jarm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash  string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash"`
	Group string `protobuf:"bytes,2,opt,name=group,proto3" json:"group"`
}

func (x *Jarm) Reset() {
	*x = Jarm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Jarm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Jarm) ProtoMessage() {}

func (x *Jarm) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Jarm.ProtoReflect.Descriptor instead.
func (*Jarm) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{20}
}

func (x *Jarm) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *Jarm) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

type FullSiteCrawler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url          string        `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	Type         string        `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`                                       // link, xhr, form
	Params       string        `protobuf:"bytes,3,opt,name=params,proto3" json:"params"`                                   //  url query
	Data         string        `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`                                       //  POST data
	Method       string        `protobuf:"bytes,5,opt,name=method,proto3" json:"method"`                                   // GET, POST
	PostDataType string        `protobuf:"bytes,6,opt,name=post_data_type,json=postDataType,proto3" json:"post_data_type"` //  xml? json?
	Hash         string        `protobuf:"bytes,7,opt,name=hash,proto3" json:"hash"`                                       //  uniq hash
	ExtraHeaders *anypb.Any    `protobuf:"bytes,8,opt,name=extra_headers,json=extraHeaders,proto3" json:"extra_headers"`   //额外的header
	ContentType  string        `protobuf:"bytes,9,opt,name=content_type,json=contentType,proto3" json:"content_type"`      // 类型，一般是响应后的数据
	Referer      string        `protobuf:"bytes,10,opt,name=referer,proto3" json:"referer"`                                // 来源
	State        uint32        `protobuf:"varint,11,opt,name=state,proto3" json:"state"`                                   //  状态，0未处理，1是完成扫描，2是错误
	StatusCode   uint32        `protobuf:"varint,12,opt,name=status_code,json=statusCode,proto3" json:"status_code"`       //  响应状态码
	HostInfo     string        `protobuf:"bytes,13,opt,name=host_info,json=hostInfo,proto3" json:"host_info"`              //  hostinfo
	Path         string        `protobuf:"bytes,14,opt,name=path,proto3" json:"path"`                                      //  path
	Dir          string        `protobuf:"bytes,15,opt,name=dir,proto3" json:"dir"`                                        //  dir
	File         string        `protobuf:"bytes,16,opt,name=file,proto3" json:"file"`                                      //  file
	U            *AllURLFixURL `protobuf:"bytes,17,opt,name=u,proto3" json:"u"`
	Ip           string        `protobuf:"bytes,18,opt,name=ip,proto3" json:"ip"`
	IsIpv6       bool          `protobuf:"varint,19,opt,name=is_ipv6,json=isIpv6,proto3" json:"is_ipv6"`
}

func (x *FullSiteCrawler) Reset() {
	*x = FullSiteCrawler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullSiteCrawler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullSiteCrawler) ProtoMessage() {}

func (x *FullSiteCrawler) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullSiteCrawler.ProtoReflect.Descriptor instead.
func (*FullSiteCrawler) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{21}
}

func (x *FullSiteCrawler) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FullSiteCrawler) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FullSiteCrawler) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

func (x *FullSiteCrawler) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *FullSiteCrawler) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *FullSiteCrawler) GetPostDataType() string {
	if x != nil {
		return x.PostDataType
	}
	return ""
}

func (x *FullSiteCrawler) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *FullSiteCrawler) GetExtraHeaders() *anypb.Any {
	if x != nil {
		return x.ExtraHeaders
	}
	return nil
}

func (x *FullSiteCrawler) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *FullSiteCrawler) GetReferer() string {
	if x != nil {
		return x.Referer
	}
	return ""
}

func (x *FullSiteCrawler) GetState() uint32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *FullSiteCrawler) GetStatusCode() uint32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *FullSiteCrawler) GetHostInfo() string {
	if x != nil {
		return x.HostInfo
	}
	return ""
}

func (x *FullSiteCrawler) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FullSiteCrawler) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

func (x *FullSiteCrawler) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *FullSiteCrawler) GetU() *AllURLFixURL {
	if x != nil {
		return x.U
	}
	return nil
}

func (x *FullSiteCrawler) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FullSiteCrawler) GetIsIpv6() bool {
	if x != nil {
		return x.IsIpv6
	}
	return false
}

type AllURLFixURL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostInfo        string     `protobuf:"bytes,1,opt,name=host_info,json=hostInfo,proto3" json:"host_info"`                  //  ip:port 肯定带端口
	FixedHostInfo   string     `protobuf:"bytes,2,opt,name=fixed_host_info,json=fixedHostInfo,proto3" json:"fixed_host_info"` //  scheme://host 不一定带端口，默认情况下会省略
	Ip              string     `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip"`
	Port            uint32     `protobuf:"varint,4,opt,name=port,proto3" json:"port"`
	Path            string     `protobuf:"bytes,5,opt,name=path,proto3" json:"path"`
	Dir             string     `protobuf:"bytes,6,opt,name=dir,proto3" json:"dir"`   //  解析后的
	File            string     `protobuf:"bytes,7,opt,name=file,proto3" json:"file"` //  解析后的
	Ext             string     `protobuf:"bytes,8,opt,name=ext,proto3" json:"ext"`   //  解析后的
	Method          string     `protobuf:"bytes,9,opt,name=method,proto3" json:"method"`
	ContentType     string     `protobuf:"bytes,10,opt,name=content_type,json=contentType,proto3" json:"content_type"`
	Data            string     `protobuf:"bytes,11,opt,name=data,proto3" json:"data"`
	PostDataType    string     `protobuf:"bytes,12,opt,name=post_data_type,json=postDataType,proto3" json:"post_data_type"`
	MustIp          string     `protobuf:"bytes,13,opt,name=must_ip,json=mustIp,proto3" json:"must_ip"`
	U               *anypb.Any `protobuf:"bytes,14,opt,name=u,proto3" json:"u"`                                                       //  *url.URL
	ParseWithScheme bool       `protobuf:"varint,15,opt,name=parse_with_scheme,json=parseWithScheme,proto3" json:"parse_with_scheme"` //  是否解析的时候自带scheme
}

func (x *AllURLFixURL) Reset() {
	*x = AllURLFixURL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllURLFixURL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllURLFixURL) ProtoMessage() {}

func (x *AllURLFixURL) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllURLFixURL.ProtoReflect.Descriptor instead.
func (*AllURLFixURL) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{22}
}

func (x *AllURLFixURL) GetHostInfo() string {
	if x != nil {
		return x.HostInfo
	}
	return ""
}

func (x *AllURLFixURL) GetFixedHostInfo() string {
	if x != nil {
		return x.FixedHostInfo
	}
	return ""
}

func (x *AllURLFixURL) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AllURLFixURL) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *AllURLFixURL) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *AllURLFixURL) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

func (x *AllURLFixURL) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *AllURLFixURL) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *AllURLFixURL) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *AllURLFixURL) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *AllURLFixURL) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *AllURLFixURL) GetPostDataType() string {
	if x != nil {
		return x.PostDataType
	}
	return ""
}

func (x *AllURLFixURL) GetMustIp() string {
	if x != nil {
		return x.MustIp
	}
	return ""
}

func (x *AllURLFixURL) GetU() *anypb.Any {
	if x != nil {
		return x.U
	}
	return nil
}

func (x *AllURLFixURL) GetParseWithScheme() bool {
	if x != nil {
		return x.ParseWithScheme
	}
	return false
}

type CertObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 证书日期
	CertDate string `protobuf:"bytes,1,opt,name=cert_date,json=certDate,proto3" json:"cert_date"`
	// 证书个数
	CertNum int32  `protobuf:"varint,2,opt,name=cert_num,json=certNum,proto3" json:"cert_num"`
	Domain  string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain"`
	// 是否受信任
	IsValid   bool     `protobuf:"varint,4,opt,name=is_valid,json=isValid,proto3" json:"is_valid"`
	IssuerCn  string   `protobuf:"bytes,5,opt,name=issuer_cn,json=issuerCn,proto3" json:"issuer_cn"`
	IssuerCns []string `protobuf:"bytes,6,rep,name=issuer_cns,json=issuerCns,proto3" json:"issuer_cns"`
	IssuerOrg []string `protobuf:"bytes,7,rep,name=issuer_org,json=issuerOrg,proto3" json:"issuer_org"`
	NotAfter  string   `protobuf:"bytes,8,opt,name=not_after,json=notAfter,proto3" json:"not_after"`
	NotBefore string   `protobuf:"bytes,9,opt,name=not_before,json=notBefore,proto3" json:"not_before"`
	SigAlth   string   `protobuf:"bytes,10,opt,name=sig_alth,json=sigAlth,proto3" json:"sig_alth"`
	Sn        string   `protobuf:"bytes,11,opt,name=sn,proto3" json:"sn"`
	SubjectCn string   `protobuf:"bytes,12,opt,name=subject_cn,json=subjectCn,proto3" json:"subject_cn"`
	V         string   `protobuf:"bytes,13,opt,name=v,proto3" json:"v"`
	// 可信类型
	ValidType    string   `protobuf:"bytes,14,opt,name=valid_type,json=validType,proto3" json:"valid_type"`
	SubjectOrg   []string `protobuf:"bytes,15,rep,name=subject_org,json=subjectOrg,proto3" json:"subject_org"`
	SubjectCns   []string `protobuf:"bytes,16,rep,name=subject_cns,json=subjectCns,proto3" json:"subject_cns"`
	SubjectNames []string `protobuf:"bytes,17,rep,name=subject_names,json=subjectNames,proto3" json:"subject_names"`
	IsMatch      bool     `protobuf:"varint,18,opt,name=is_match,json=isMatch,proto3" json:"is_match"`
	IsExpired    bool     `protobuf:"varint,19,opt,name=is_expired,json=isExpired,proto3" json:"is_expired"`
}

func (x *CertObject) Reset() {
	*x = CertObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CertObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CertObject) ProtoMessage() {}

func (x *CertObject) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CertObject.ProtoReflect.Descriptor instead.
func (*CertObject) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{23}
}

func (x *CertObject) GetCertDate() string {
	if x != nil {
		return x.CertDate
	}
	return ""
}

func (x *CertObject) GetCertNum() int32 {
	if x != nil {
		return x.CertNum
	}
	return 0
}

func (x *CertObject) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CertObject) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *CertObject) GetIssuerCn() string {
	if x != nil {
		return x.IssuerCn
	}
	return ""
}

func (x *CertObject) GetIssuerCns() []string {
	if x != nil {
		return x.IssuerCns
	}
	return nil
}

func (x *CertObject) GetIssuerOrg() []string {
	if x != nil {
		return x.IssuerOrg
	}
	return nil
}

func (x *CertObject) GetNotAfter() string {
	if x != nil {
		return x.NotAfter
	}
	return ""
}

func (x *CertObject) GetNotBefore() string {
	if x != nil {
		return x.NotBefore
	}
	return ""
}

func (x *CertObject) GetSigAlth() string {
	if x != nil {
		return x.SigAlth
	}
	return ""
}

func (x *CertObject) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *CertObject) GetSubjectCn() string {
	if x != nil {
		return x.SubjectCn
	}
	return ""
}

func (x *CertObject) GetV() string {
	if x != nil {
		return x.V
	}
	return ""
}

func (x *CertObject) GetValidType() string {
	if x != nil {
		return x.ValidType
	}
	return ""
}

func (x *CertObject) GetSubjectOrg() []string {
	if x != nil {
		return x.SubjectOrg
	}
	return nil
}

func (x *CertObject) GetSubjectCns() []string {
	if x != nil {
		return x.SubjectCns
	}
	return nil
}

func (x *CertObject) GetSubjectNames() []string {
	if x != nil {
		return x.SubjectNames
	}
	return nil
}

func (x *CertObject) GetIsMatch() bool {
	if x != nil {
		return x.IsMatch
	}
	return false
}

func (x *CertObject) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

type RuleTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category         string  `protobuf:"bytes,1,opt,name=category,proto3" json:"category"`
	CnCategory       string  `protobuf:"bytes,2,opt,name=cn_category,json=cnCategory,proto3" json:"cn_category"`
	CnCompany        string  `protobuf:"bytes,3,opt,name=cn_company,json=cnCompany,proto3" json:"cn_company"`
	CnParentCategory string  `protobuf:"bytes,4,opt,name=cn_parent_category,json=cnParentCategory,proto3" json:"cn_parent_category"`
	CnProduct        string  `protobuf:"bytes,5,opt,name=cn_product,json=cnProduct,proto3" json:"cn_product"`
	Company          string  `protobuf:"bytes,6,opt,name=company,proto3" json:"company"`
	Level            string  `protobuf:"bytes,7,opt,name=level,proto3" json:"level"`
	ParentCategory   string  `protobuf:"bytes,8,opt,name=parent_category,json=parentCategory,proto3" json:"parent_category"`
	Product          string  `protobuf:"bytes,9,opt,name=product,proto3" json:"product"`
	RuleId           string  `protobuf:"bytes,10,opt,name=rule_id,json=ruleId,proto3" json:"rule_id"`
	Softhard         string  `protobuf:"bytes,11,opt,name=softhard,proto3" json:"softhard"`
	VersionRaw       string  `protobuf:"bytes,12,opt,name=version_raw,json=versionRaw,proto3" json:"version_raw"`
	VersionNum       float64 `protobuf:"fixed64,13,opt,name=version_num,json=versionNum,proto3" json:"version_num"`
}

func (x *RuleTag) Reset() {
	*x = RuleTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleTag) ProtoMessage() {}

func (x *RuleTag) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleTag.ProtoReflect.Descriptor instead.
func (*RuleTag) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{24}
}

func (x *RuleTag) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *RuleTag) GetCnCategory() string {
	if x != nil {
		return x.CnCategory
	}
	return ""
}

func (x *RuleTag) GetCnCompany() string {
	if x != nil {
		return x.CnCompany
	}
	return ""
}

func (x *RuleTag) GetCnParentCategory() string {
	if x != nil {
		return x.CnParentCategory
	}
	return ""
}

func (x *RuleTag) GetCnProduct() string {
	if x != nil {
		return x.CnProduct
	}
	return ""
}

func (x *RuleTag) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *RuleTag) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *RuleTag) GetParentCategory() string {
	if x != nil {
		return x.ParentCategory
	}
	return ""
}

func (x *RuleTag) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *RuleTag) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *RuleTag) GetSofthard() string {
	if x != nil {
		return x.Softhard
	}
	return ""
}

func (x *RuleTag) GetVersionRaw() string {
	if x != nil {
		return x.VersionRaw
	}
	return ""
}

func (x *RuleTag) GetVersionNum() float64 {
	if x != nil {
		return x.VersionNum
	}
	return 0
}

type Geoip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CityName       string    `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name"`
	ContinentCode  string    `protobuf:"bytes,2,opt,name=continent_code,json=continentCode,proto3" json:"continent_code"`
	CountryCode2   string    `protobuf:"bytes,3,opt,name=country_code2,json=countryCode2,proto3" json:"country_code2"`
	CountryCode3   string    `protobuf:"bytes,4,opt,name=country_code3,json=countryCode3,proto3" json:"country_code3"`
	CountryName    string    `protobuf:"bytes,5,opt,name=country_name,json=countryName,proto3" json:"country_name"`
	DmaCode        int32     `protobuf:"varint,6,opt,name=dma_code,json=dmaCode,proto3" json:"dma_code"`
	Latitude       float64   `protobuf:"fixed64,7,opt,name=latitude,proto3" json:"latitude"`
	Location       *Location `protobuf:"bytes,8,opt,name=location,proto3" json:"location"`
	Longitude      float64   `protobuf:"fixed64,9,opt,name=longitude,proto3" json:"longitude"`
	PostalCode     string    `protobuf:"bytes,10,opt,name=postal_code,json=postalCode,proto3" json:"postal_code"`
	RealRegionName string    `protobuf:"bytes,11,opt,name=real_region_name,json=realRegionName,proto3" json:"real_region_name"`
	RegionName     string    `protobuf:"bytes,12,opt,name=region_name,json=regionName,proto3" json:"region_name"`
	Timezone       string    `protobuf:"bytes,13,opt,name=timezone,proto3" json:"timezone"`
}

func (x *Geoip) Reset() {
	*x = Geoip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Geoip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Geoip) ProtoMessage() {}

func (x *Geoip) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Geoip.ProtoReflect.Descriptor instead.
func (*Geoip) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{25}
}

func (x *Geoip) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *Geoip) GetContinentCode() string {
	if x != nil {
		return x.ContinentCode
	}
	return ""
}

func (x *Geoip) GetCountryCode2() string {
	if x != nil {
		return x.CountryCode2
	}
	return ""
}

func (x *Geoip) GetCountryCode3() string {
	if x != nil {
		return x.CountryCode3
	}
	return ""
}

func (x *Geoip) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *Geoip) GetDmaCode() int32 {
	if x != nil {
		return x.DmaCode
	}
	return 0
}

func (x *Geoip) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Geoip) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *Geoip) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *Geoip) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *Geoip) GetRealRegionName() string {
	if x != nil {
		return x.RealRegionName
	}
	return ""
}

func (x *Geoip) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *Geoip) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat"`
	Lon float64 `protobuf:"fixed64,2,opt,name=lon,proto3" json:"lon"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{26}
}

func (x *Location) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Location) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

type ASN struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AsNumber       uint32 `protobuf:"varint,1,opt,name=as_number,json=asNumber,proto3" json:"as_number"`
	AsOrganization string `protobuf:"bytes,2,opt,name=as_organization,json=asOrganization,proto3" json:"as_organization"`
}

func (x *ASN) Reset() {
	*x = ASN{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ASN) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ASN) ProtoMessage() {}

func (x *ASN) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ASN.ProtoReflect.Descriptor instead.
func (*ASN) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{27}
}

func (x *ASN) GetAsNumber() uint32 {
	if x != nil {
		return x.AsNumber
	}
	return 0
}

func (x *ASN) GetAsOrganization() string {
	if x != nil {
		return x.AsOrganization
	}
	return ""
}

// 端口扫描任务的事件
type TaskEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`        // 任务ID
	JobId      string                 `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id"`           // JobID
	Command    string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command"`                    // 命令
	TaskType   uint32                 `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3" json:"task_type"` // 任务类型
	Ports      []string               `protobuf:"bytes,5,rep,name=ports,proto3" json:"ports"`
	HostInfos  []string               `protobuf:"bytes,6,rep,name=host_infos,json=hostInfos,proto3" json:"host_infos"`
	Options    *TaskOption            `protobuf:"bytes,7,opt,name=options,proto3" json:"options"`
	Parameters *TransparentParameters `protobuf:"bytes,8,opt,name=parameters,proto3" json:"parameters"`
}

func (x *TaskEvent) Reset() {
	*x = TaskEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskEvent) ProtoMessage() {}

func (x *TaskEvent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskEvent.ProtoReflect.Descriptor instead.
func (*TaskEvent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{28}
}

func (x *TaskEvent) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskEvent) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *TaskEvent) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *TaskEvent) GetTaskType() uint32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *TaskEvent) GetPorts() []string {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *TaskEvent) GetHostInfos() []string {
	if x != nil {
		return x.HostInfos
	}
	return nil
}

func (x *TaskEvent) GetOptions() *TaskOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *TaskEvent) GetParameters() *TransparentParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type TaskOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rate the max bandwidth used to scan
	Rate *int32 `protobuf:"varint,1,opt,name=rate,proto3,oneof" json:"rate"`
	// blacklist ips will not scan.
	Blacklist *string `protobuf:"bytes,2,opt,name=blacklist,proto3,oneof" json:"blacklist"`
	// use ping to scan the ip
	PingScan *bool `protobuf:"varint,3,opt,name=ping_scan,json=pingScan,proto3,oneof" json:"ping_scan"`
	// use gateway on mac
	GatewayMac *string `protobuf:"bytes,4,opt,name=gateway_mac,json=gatewayMac,proto3,oneof" json:"gateway_mac"`
	SendEth    *string `protobuf:"bytes,5,opt,name=send_eth,json=sendEth,proto3,oneof" json:"send_eth"`
	// retry times
	Retries *int32 `protobuf:"varint,6,opt,name=retries,proto3,oneof" json:"retries"`
	// parse the OS info
	DeepGetOs *bool `protobuf:"varint,7,opt,name=deep_get_os,json=deepGetOs,proto3,oneof" json:"deep_get_os"`
	// parse the mac info
	DeepGetMac *bool `protobuf:"varint,8,opt,name=deep_get_mac,json=deepGetMac,proto3,oneof" json:"deep_get_mac"`
	// indicate is ipv6 ip list.
	IsIpv6 *bool `protobuf:"varint,9,opt,name=is_ipv6,json=isIpv6,proto3,oneof" json:"is_ipv6"`
	// pick the device by deviceName
	DeviceName *string `protobuf:"bytes,10,opt,name=deviceName,proto3,oneof" json:"deviceName"`
	TreckScan  *bool   `protobuf:"varint,11,opt,name=treck_scan,json=treckScan,proto3,oneof" json:"treck_scan"`
}

func (x *TaskOption) Reset() {
	*x = TaskOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskOption) ProtoMessage() {}

func (x *TaskOption) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskOption.ProtoReflect.Descriptor instead.
func (*TaskOption) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{29}
}

func (x *TaskOption) GetRate() int32 {
	if x != nil && x.Rate != nil {
		return *x.Rate
	}
	return 0
}

func (x *TaskOption) GetBlacklist() string {
	if x != nil && x.Blacklist != nil {
		return *x.Blacklist
	}
	return ""
}

func (x *TaskOption) GetPingScan() bool {
	if x != nil && x.PingScan != nil {
		return *x.PingScan
	}
	return false
}

func (x *TaskOption) GetGatewayMac() string {
	if x != nil && x.GatewayMac != nil {
		return *x.GatewayMac
	}
	return ""
}

func (x *TaskOption) GetSendEth() string {
	if x != nil && x.SendEth != nil {
		return *x.SendEth
	}
	return ""
}

func (x *TaskOption) GetRetries() int32 {
	if x != nil && x.Retries != nil {
		return *x.Retries
	}
	return 0
}

func (x *TaskOption) GetDeepGetOs() bool {
	if x != nil && x.DeepGetOs != nil {
		return *x.DeepGetOs
	}
	return false
}

func (x *TaskOption) GetDeepGetMac() bool {
	if x != nil && x.DeepGetMac != nil {
		return *x.DeepGetMac
	}
	return false
}

func (x *TaskOption) GetIsIpv6() bool {
	if x != nil && x.IsIpv6 != nil {
		return *x.IsIpv6
	}
	return false
}

func (x *TaskOption) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *TaskOption) GetTreckScan() bool {
	if x != nil && x.TreckScan != nil {
		return *x.TreckScan
	}
	return false
}

// 透传参数
type TransparentParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GrabConcurrent      int32  `protobuf:"varint,1,opt,name=grab_concurrent,json=grabConcurrent,proto3" json:"grab_concurrent"`
	ProtocolUpdateCycle int32  `protobuf:"varint,2,opt,name=protocol_update_cycle,json=protocolUpdateCycle,proto3" json:"protocol_update_cycle"`
	UnknownProtocolIndb bool   `protobuf:"varint,3,opt,name=unknown_protocol_indb,json=unknownProtocolIndb,proto3" json:"unknown_protocol_indb"`
	MaxAssetNum         int32  `protobuf:"varint,4,opt,name=max_asset_num,json=maxAssetNum,proto3" json:"max_asset_num"`
	CrawlerAllUrl       bool   `protobuf:"varint,5,opt,name=crawler_all_url,json=crawlerAllUrl,proto3" json:"crawler_all_url"`
	CrawlerUrlBlackKey  string `protobuf:"bytes,6,opt,name=crawler_url_black_key,json=crawlerUrlBlackKey,proto3" json:"crawler_url_black_key"`
	CrawlerSpecificUrl  string `protobuf:"bytes,7,opt,name=crawler_specific_url,json=crawlerSpecificUrl,proto3" json:"crawler_specific_url"`
	FullProtocolDetect  bool   `protobuf:"varint,8,opt,name=full_protocol_detect,json=fullProtocolDetect,proto3" json:"full_protocol_detect"`
	ResolveHost         bool   `protobuf:"varint,9,opt,name=resolve_host,json=resolveHost,proto3" json:"resolve_host"`
	NameServer          string `protobuf:"bytes,10,opt,name=name_server,json=nameServer,proto3" json:"name_server"`
	IsIpv6              bool   `protobuf:"varint,11,opt,name=is_ipv6,json=isIpv6,proto3" json:"is_ipv6"`
}

func (x *TransparentParameters) Reset() {
	*x = TransparentParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransparentParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransparentParameters) ProtoMessage() {}

func (x *TransparentParameters) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransparentParameters.ProtoReflect.Descriptor instead.
func (*TransparentParameters) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{30}
}

func (x *TransparentParameters) GetGrabConcurrent() int32 {
	if x != nil {
		return x.GrabConcurrent
	}
	return 0
}

func (x *TransparentParameters) GetProtocolUpdateCycle() int32 {
	if x != nil {
		return x.ProtocolUpdateCycle
	}
	return 0
}

func (x *TransparentParameters) GetUnknownProtocolIndb() bool {
	if x != nil {
		return x.UnknownProtocolIndb
	}
	return false
}

func (x *TransparentParameters) GetMaxAssetNum() int32 {
	if x != nil {
		return x.MaxAssetNum
	}
	return 0
}

func (x *TransparentParameters) GetCrawlerAllUrl() bool {
	if x != nil {
		return x.CrawlerAllUrl
	}
	return false
}

func (x *TransparentParameters) GetCrawlerUrlBlackKey() string {
	if x != nil {
		return x.CrawlerUrlBlackKey
	}
	return ""
}

func (x *TransparentParameters) GetCrawlerSpecificUrl() string {
	if x != nil {
		return x.CrawlerSpecificUrl
	}
	return ""
}

func (x *TransparentParameters) GetFullProtocolDetect() bool {
	if x != nil {
		return x.FullProtocolDetect
	}
	return false
}

func (x *TransparentParameters) GetResolveHost() bool {
	if x != nil {
		return x.ResolveHost
	}
	return false
}

func (x *TransparentParameters) GetNameServer() string {
	if x != nil {
		return x.NameServer
	}
	return ""
}

func (x *TransparentParameters) GetIsIpv6() bool {
	if x != nil {
		return x.IsIpv6
	}
	return false
}

type TaskNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node   *ServiceNode `protobuf:"bytes,1,opt,name=Node,proto3" json:"Node"`
	TaskId string       `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	JobId  string       `protobuf:"bytes,3,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	// Types that are assignable to Data:
	//	*TaskNotify_State
	//	*TaskNotify_Result
	Data isTaskNotify_Data `protobuf_oneof:"data"`
}

func (x *TaskNotify) Reset() {
	*x = TaskNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskNotify) ProtoMessage() {}

func (x *TaskNotify) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskNotify.ProtoReflect.Descriptor instead.
func (*TaskNotify) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{31}
}

func (x *TaskNotify) GetNode() *ServiceNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *TaskNotify) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskNotify) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (m *TaskNotify) GetData() isTaskNotify_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *TaskNotify) GetState() *StateNotify {
	if x, ok := x.GetData().(*TaskNotify_State); ok {
		return x.State
	}
	return nil
}

func (x *TaskNotify) GetResult() *ResultNotify {
	if x, ok := x.GetData().(*TaskNotify_Result); ok {
		return x.Result
	}
	return nil
}

type isTaskNotify_Data interface {
	isTaskNotify_Data()
}

type TaskNotify_State struct {
	State *StateNotify `protobuf:"bytes,4,opt,name=state,proto3,oneof"`
}

type TaskNotify_Result struct {
	Result *ResultNotify `protobuf:"bytes,5,opt,name=result,proto3,oneof"`
}

func (*TaskNotify_State) isTaskNotify_Data() {}

func (*TaskNotify_Result) isTaskNotify_Data() {}

type StateNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State      string  `protobuf:"bytes,1,opt,name=state,proto3" json:"state"`
	Progress   float32 `protobuf:"fixed32,2,opt,name=progress,proto3" json:"progress"`
	WorkingOn  string  `protobuf:"bytes,3,opt,name=workingOn,proto3" json:"workingOn"`
	RemainTime string  `protobuf:"bytes,4,opt,name=remainTime,proto3" json:"remainTime"`
	Message    string  `protobuf:"bytes,5,opt,name=message,proto3" json:"message"`
}

func (x *StateNotify) Reset() {
	*x = StateNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateNotify) ProtoMessage() {}

func (x *StateNotify) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateNotify.ProtoReflect.Descriptor instead.
func (*StateNotify) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{32}
}

func (x *StateNotify) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *StateNotify) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *StateNotify) GetWorkingOn() string {
	if x != nil {
		return x.WorkingOn
	}
	return ""
}

func (x *StateNotify) GetRemainTime() string {
	if x != nil {
		return x.RemainTime
	}
	return ""
}

func (x *StateNotify) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ResultNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseProtocol string `protobuf:"bytes,1,opt,name=base_protocol,json=baseProtocol,proto3" json:"base_protocol"`
	Ip           string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip"`
	Port         uint32 `protobuf:"varint,3,opt,name=port,proto3" json:"port"`
	BindProtocol string `protobuf:"bytes,4,opt,name=bind_protocol,json=bindProtocol,proto3" json:"bind_protocol"`
}

func (x *ResultNotify) Reset() {
	*x = ResultNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResultNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultNotify) ProtoMessage() {}

func (x *ResultNotify) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultNotify.ProtoReflect.Descriptor instead.
func (*ResultNotify) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{33}
}

func (x *ResultNotify) GetBaseProtocol() string {
	if x != nil {
		return x.BaseProtocol
	}
	return ""
}

func (x *ResultNotify) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ResultNotify) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ResultNotify) GetBindProtocol() string {
	if x != nil {
		return x.BindProtocol
	}
	return ""
}

type ServiceNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId      string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id"`
	ServiceName    string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name"`
	ServiceVersion string `protobuf:"bytes,3,opt,name=service_version,json=serviceVersion,proto3" json:"service_version"`
}

func (x *ServiceNode) Reset() {
	*x = ServiceNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceNode) ProtoMessage() {}

func (x *ServiceNode) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceNode.ProtoReflect.Descriptor instead.
func (*ServiceNode) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{34}
}

func (x *ServiceNode) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceNode) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceNode) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

type DispatcherTaskStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskStateRequest) Reset() {
	*x = DispatcherTaskStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStateRequest) ProtoMessage() {}

func (x *DispatcherTaskStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStateRequest.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStateRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{35}
}

func (x *DispatcherTaskStateRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    string      `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	State     string      `protobuf:"bytes,2,opt,name=state,proto3" json:"state"`
	Progress  float32     `protobuf:"fixed32,3,opt,name=progress,proto3" json:"progress"`
	WorkingOn string      `protobuf:"bytes,4,opt,name=workingOn,proto3" json:"workingOn"`
	Jobs      []*JobState `protobuf:"bytes,5,rep,name=jobs,proto3" json:"jobs"`
}

func (x *DispatcherTaskStateResponse) Reset() {
	*x = DispatcherTaskStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStateResponse) ProtoMessage() {}

func (x *DispatcherTaskStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStateResponse.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStateResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{36}
}

func (x *DispatcherTaskStateResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DispatcherTaskStateResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DispatcherTaskStateResponse) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *DispatcherTaskStateResponse) GetWorkingOn() string {
	if x != nil {
		return x.WorkingOn
	}
	return ""
}

func (x *DispatcherTaskStateResponse) GetJobs() []*JobState {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type JobState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId     string  `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id"`
	State     string  `protobuf:"bytes,2,opt,name=state,proto3" json:"state"`
	Progress  float32 `protobuf:"fixed32,3,opt,name=progress,proto3" json:"progress"`
	WorkingOn string  `protobuf:"bytes,4,opt,name=workingOn,proto3" json:"workingOn"`
	NodeID    string  `protobuf:"bytes,5,opt,name=nodeID,proto3" json:"nodeID"`
}

func (x *JobState) Reset() {
	*x = JobState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobState) ProtoMessage() {}

func (x *JobState) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobState.ProtoReflect.Descriptor instead.
func (*JobState) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{37}
}

func (x *JobState) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobState) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *JobState) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *JobState) GetWorkingOn() string {
	if x != nil {
		return x.WorkingOn
	}
	return ""
}

func (x *JobState) GetNodeID() string {
	if x != nil {
		return x.NodeID
	}
	return ""
}

type DispatcherTaskStartRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        string                       `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Ports         []string                     `protobuf:"bytes,2,rep,name=ports,proto3" json:"ports"`
	IpLists       []string                     `protobuf:"bytes,3,rep,name=ip_lists,json=ipLists,proto3" json:"ip_lists"`
	HostInfos     []string                     `protobuf:"bytes,4,rep,name=host_infos,json=hostInfos,proto3" json:"host_infos"`
	PortScanner   *DispatcherPortScannerConfig `protobuf:"bytes,5,opt,name=port_scanner,json=portScanner,proto3" json:"port_scanner"`
	Vulnerability *VulnerabilityConfig         `protobuf:"bytes,6,opt,name=vulnerability,proto3" json:"vulnerability"`
	Options       *DispatcherTaskStartOptions  `protobuf:"bytes,7,opt,name=options,proto3" json:"options"`
}

func (x *DispatcherTaskStartRequest) Reset() {
	*x = DispatcherTaskStartRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStartRequest) ProtoMessage() {}

func (x *DispatcherTaskStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStartRequest.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStartRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{38}
}

func (x *DispatcherTaskStartRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DispatcherTaskStartRequest) GetPorts() []string {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *DispatcherTaskStartRequest) GetIpLists() []string {
	if x != nil {
		return x.IpLists
	}
	return nil
}

func (x *DispatcherTaskStartRequest) GetHostInfos() []string {
	if x != nil {
		return x.HostInfos
	}
	return nil
}

func (x *DispatcherTaskStartRequest) GetPortScanner() *DispatcherPortScannerConfig {
	if x != nil {
		return x.PortScanner
	}
	return nil
}

func (x *DispatcherTaskStartRequest) GetVulnerability() *VulnerabilityConfig {
	if x != nil {
		return x.Vulnerability
	}
	return nil
}

func (x *DispatcherTaskStartRequest) GetOptions() *DispatcherTaskStartOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type DispatcherPortScannerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskType uint32                       `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	Options  *DispatcherPortScannerOption `protobuf:"bytes,2,opt,name=options,proto3" json:"options"`
}

func (x *DispatcherPortScannerConfig) Reset() {
	*x = DispatcherPortScannerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherPortScannerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherPortScannerConfig) ProtoMessage() {}

func (x *DispatcherPortScannerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherPortScannerConfig.ProtoReflect.Descriptor instead.
func (*DispatcherPortScannerConfig) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{39}
}

func (x *DispatcherPortScannerConfig) GetTaskType() uint32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *DispatcherPortScannerConfig) GetOptions() *DispatcherPortScannerOption {
	if x != nil {
		return x.Options
	}
	return nil
}

type DispatcherTaskStartOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GrabConcurrent      int32  `protobuf:"varint,1,opt,name=grab_concurrent,json=grabConcurrent,proto3" json:"grab_concurrent"`
	ProtocolUpdateCycle int32  `protobuf:"varint,2,opt,name=protocol_update_cycle,json=protocolUpdateCycle,proto3" json:"protocol_update_cycle"`
	UnknownProtocolIndb bool   `protobuf:"varint,3,opt,name=unknown_protocol_indb,json=unknownProtocolIndb,proto3" json:"unknown_protocol_indb"`
	MaxAssetNum         int32  `protobuf:"varint,4,opt,name=max_asset_num,json=maxAssetNum,proto3" json:"max_asset_num"`
	CrawlerAllUrl       bool   `protobuf:"varint,5,opt,name=crawler_all_url,json=crawlerAllUrl,proto3" json:"crawler_all_url"`
	CrawlerUrlBlackKey  string `protobuf:"bytes,6,opt,name=crawler_url_black_key,json=crawlerUrlBlackKey,proto3" json:"crawler_url_black_key"`
	// 爬取指定URL
	CrawlerSpecificUrl string                 `protobuf:"bytes,7,opt,name=crawler_specific_url,json=crawlerSpecificUrl,proto3" json:"crawler_specific_url"`
	FullProtocolDetect bool                   `protobuf:"varint,8,opt,name=full_protocol_detect,json=fullProtocolDetect,proto3" json:"full_protocol_detect"`
	ResolveHost        bool                   `protobuf:"varint,9,opt,name=resolve_host,json=resolveHost,proto3" json:"resolve_host"`
	NameServer         string                 `protobuf:"bytes,10,opt,name=name_server,json=nameServer,proto3" json:"name_server"`
	PortGroup          []*PortGroupItem       `protobuf:"bytes,11,rep,name=port_group,json=portGroup,proto3" json:"port_group"`
	IpDomainRelations  map[string]*StringList `protobuf:"bytes,12,rep,name=ip_domain_relations,json=ipDomainRelations,proto3" json:"ip_domain_relations" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra              *anypb.Any             `protobuf:"bytes,13,opt,name=extra,proto3,oneof" json:"extra"`
	SplitPolicy        int32                  `protobuf:"varint,14,opt,name=split_policy,json=splitPolicy,proto3" json:"split_policy"`                // 拆分策略 0: 默认/不拆分 1: 按IP拆分 2: 按端口拆分
	SplitNum           int32                  `protobuf:"varint,15,opt,name=split_num,json=splitNum,proto3" json:"split_num"`                         // 拆分个数 0: 使用最大可拆分数，非0: 拆分个数
	ScheduleStrategy   int32                  `protobuf:"varint,16,opt,name=schedule_strategy,json=scheduleStrategy,proto3" json:"schedule_strategy"` // 调度策略：0:默认策略 1:随机可用节点 2: 按标签选择节点 3: 指定特定节点
	SelectNodes        []string               `protobuf:"bytes,17,rep,name=select_nodes,json=selectNodes,proto3" json:"select_nodes"`                 // 选择的特定节点ID
	SelectLabels       []string               `protobuf:"bytes,18,rep,name=select_labels,json=selectLabels,proto3" json:"select_labels"`              // 选择节点标签
}

func (x *DispatcherTaskStartOptions) Reset() {
	*x = DispatcherTaskStartOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStartOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStartOptions) ProtoMessage() {}

func (x *DispatcherTaskStartOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStartOptions.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStartOptions) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{40}
}

func (x *DispatcherTaskStartOptions) GetGrabConcurrent() int32 {
	if x != nil {
		return x.GrabConcurrent
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetProtocolUpdateCycle() int32 {
	if x != nil {
		return x.ProtocolUpdateCycle
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetUnknownProtocolIndb() bool {
	if x != nil {
		return x.UnknownProtocolIndb
	}
	return false
}

func (x *DispatcherTaskStartOptions) GetMaxAssetNum() int32 {
	if x != nil {
		return x.MaxAssetNum
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetCrawlerAllUrl() bool {
	if x != nil {
		return x.CrawlerAllUrl
	}
	return false
}

func (x *DispatcherTaskStartOptions) GetCrawlerUrlBlackKey() string {
	if x != nil {
		return x.CrawlerUrlBlackKey
	}
	return ""
}

func (x *DispatcherTaskStartOptions) GetCrawlerSpecificUrl() string {
	if x != nil {
		return x.CrawlerSpecificUrl
	}
	return ""
}

func (x *DispatcherTaskStartOptions) GetFullProtocolDetect() bool {
	if x != nil {
		return x.FullProtocolDetect
	}
	return false
}

func (x *DispatcherTaskStartOptions) GetResolveHost() bool {
	if x != nil {
		return x.ResolveHost
	}
	return false
}

func (x *DispatcherTaskStartOptions) GetNameServer() string {
	if x != nil {
		return x.NameServer
	}
	return ""
}

func (x *DispatcherTaskStartOptions) GetPortGroup() []*PortGroupItem {
	if x != nil {
		return x.PortGroup
	}
	return nil
}

func (x *DispatcherTaskStartOptions) GetIpDomainRelations() map[string]*StringList {
	if x != nil {
		return x.IpDomainRelations
	}
	return nil
}

func (x *DispatcherTaskStartOptions) GetExtra() *anypb.Any {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *DispatcherTaskStartOptions) GetSplitPolicy() int32 {
	if x != nil {
		return x.SplitPolicy
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetSplitNum() int32 {
	if x != nil {
		return x.SplitNum
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetScheduleStrategy() int32 {
	if x != nil {
		return x.ScheduleStrategy
	}
	return 0
}

func (x *DispatcherTaskStartOptions) GetSelectNodes() []string {
	if x != nil {
		return x.SelectNodes
	}
	return nil
}

func (x *DispatcherTaskStartOptions) GetSelectLabels() []string {
	if x != nil {
		return x.SelectLabels
	}
	return nil
}

type PortGroupItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpList []string `protobuf:"bytes,1,rep,name=ip_list,json=ipList,proto3" json:"ip_list"`
	Ports  string   `protobuf:"bytes,2,opt,name=ports,proto3" json:"ports"`
}

func (x *PortGroupItem) Reset() {
	*x = PortGroupItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortGroupItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortGroupItem) ProtoMessage() {}

func (x *PortGroupItem) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortGroupItem.ProtoReflect.Descriptor instead.
func (*PortGroupItem) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{41}
}

func (x *PortGroupItem) GetIpList() []string {
	if x != nil {
		return x.IpList
	}
	return nil
}

func (x *PortGroupItem) GetPorts() string {
	if x != nil {
		return x.Ports
	}
	return ""
}

type VulnerabilityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FofaUrl     string   `protobuf:"bytes,1,opt,name=fofa_url,json=fofaUrl,proto3" json:"fofa_url"`
	ExploitName []string `protobuf:"bytes,2,rep,name=exploit_name,json=exploitName,proto3" json:"exploit_name"`
	ExploitFile []string `protobuf:"bytes,3,rep,name=exploit_file,json=exploitFile,proto3" json:"exploit_file"`
	Target      []string `protobuf:"bytes,4,rep,name=target,proto3" json:"target"`
	//Operation 可选值：s 和 e 。s为poc扫描，e为执行exp
	Operation string `protobuf:"bytes,5,opt,name=operation,proto3" json:"operation"`
}

func (x *VulnerabilityConfig) Reset() {
	*x = VulnerabilityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulnerabilityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnerabilityConfig) ProtoMessage() {}

func (x *VulnerabilityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnerabilityConfig.ProtoReflect.Descriptor instead.
func (*VulnerabilityConfig) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{42}
}

func (x *VulnerabilityConfig) GetFofaUrl() string {
	if x != nil {
		return x.FofaUrl
	}
	return ""
}

func (x *VulnerabilityConfig) GetExploitName() []string {
	if x != nil {
		return x.ExploitName
	}
	return nil
}

func (x *VulnerabilityConfig) GetExploitFile() []string {
	if x != nil {
		return x.ExploitFile
	}
	return nil
}

func (x *VulnerabilityConfig) GetTarget() []string {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *VulnerabilityConfig) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

type DispatcherTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskResponse) Reset() {
	*x = DispatcherTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskResponse) ProtoMessage() {}

func (x *DispatcherTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskResponse.ProtoReflect.Descriptor instead.
func (*DispatcherTaskResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{43}
}

func (x *DispatcherTaskResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskPauseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskPauseRequest) Reset() {
	*x = DispatcherTaskPauseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskPauseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskPauseRequest) ProtoMessage() {}

func (x *DispatcherTaskPauseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskPauseRequest.ProtoReflect.Descriptor instead.
func (*DispatcherTaskPauseRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{44}
}

func (x *DispatcherTaskPauseRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskPauseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskPauseResponse) Reset() {
	*x = DispatcherTaskPauseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskPauseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskPauseResponse) ProtoMessage() {}

func (x *DispatcherTaskPauseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskPauseResponse.ProtoReflect.Descriptor instead.
func (*DispatcherTaskPauseResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{45}
}

func (x *DispatcherTaskPauseResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskResumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Rate   *int32 `protobuf:"varint,2,opt,name=rate,proto3,oneof" json:"rate"`
}

func (x *DispatcherTaskResumeRequest) Reset() {
	*x = DispatcherTaskResumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskResumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskResumeRequest) ProtoMessage() {}

func (x *DispatcherTaskResumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskResumeRequest.ProtoReflect.Descriptor instead.
func (*DispatcherTaskResumeRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{46}
}

func (x *DispatcherTaskResumeRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DispatcherTaskResumeRequest) GetRate() int32 {
	if x != nil && x.Rate != nil {
		return *x.Rate
	}
	return 0
}

type DispatcherTaskResumeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskResumeResponse) Reset() {
	*x = DispatcherTaskResumeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskResumeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskResumeResponse) ProtoMessage() {}

func (x *DispatcherTaskResumeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskResumeResponse.ProtoReflect.Descriptor instead.
func (*DispatcherTaskResumeResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{47}
}

func (x *DispatcherTaskResumeResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskStopRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskStopRequest) Reset() {
	*x = DispatcherTaskStopRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStopRequest) ProtoMessage() {}

func (x *DispatcherTaskStopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStopRequest.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStopRequest) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{48}
}

func (x *DispatcherTaskStopRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherTaskStopResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *DispatcherTaskStopResponse) Reset() {
	*x = DispatcherTaskStopResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherTaskStopResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherTaskStopResponse) ProtoMessage() {}

func (x *DispatcherTaskStopResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherTaskStopResponse.ProtoReflect.Descriptor instead.
func (*DispatcherTaskStopResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{49}
}

func (x *DispatcherTaskStopResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type DispatcherPortScannerOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rate the max bandwidth used to scan
	Rate *int32 `protobuf:"varint,1,opt,name=rate,proto3,oneof" json:"rate"`
	// blacklist ips will not scan.
	Blacklist *string `protobuf:"bytes,2,opt,name=blacklist,proto3,oneof" json:"blacklist"`
	// use ping to scan the ip
	PingScan *bool `protobuf:"varint,3,opt,name=ping_scan,json=pingScan,proto3,oneof" json:"ping_scan"`
	// use gateway on mac
	GatewayMac *string `protobuf:"bytes,4,opt,name=gateway_mac,json=gatewayMac,proto3,oneof" json:"gateway_mac"`
	SendEth    *string `protobuf:"bytes,5,opt,name=send_eth,json=sendEth,proto3,oneof" json:"send_eth"`
	// retry times
	Retries *int32 `protobuf:"varint,6,opt,name=retries,proto3,oneof" json:"retries"`
	// parse the OS info
	DeepGetOs *bool `protobuf:"varint,7,opt,name=deep_get_os,json=deepGetOs,proto3,oneof" json:"deep_get_os"`
	// parse the mac info
	DeepGetMac *bool `protobuf:"varint,8,opt,name=deep_get_mac,json=deepGetMac,proto3,oneof" json:"deep_get_mac"`
	// indicate is ipv6 ip list.
	IsIpv6 *bool `protobuf:"varint,9,opt,name=is_ipv6,json=isIpv6,proto3,oneof" json:"is_ipv6"`
	//pick the device by deviceName
	DeviceName *string `protobuf:"bytes,10,opt,name=deviceName,proto3,oneof" json:"deviceName"`
	TreckScan  *bool   `protobuf:"varint,11,opt,name=treck_scan,json=treckScan,proto3,oneof" json:"treck_scan"`
}

func (x *DispatcherPortScannerOption) Reset() {
	*x = DispatcherPortScannerOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DispatcherPortScannerOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatcherPortScannerOption) ProtoMessage() {}

func (x *DispatcherPortScannerOption) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatcherPortScannerOption.ProtoReflect.Descriptor instead.
func (*DispatcherPortScannerOption) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{50}
}

func (x *DispatcherPortScannerOption) GetRate() int32 {
	if x != nil && x.Rate != nil {
		return *x.Rate
	}
	return 0
}

func (x *DispatcherPortScannerOption) GetBlacklist() string {
	if x != nil && x.Blacklist != nil {
		return *x.Blacklist
	}
	return ""
}

func (x *DispatcherPortScannerOption) GetPingScan() bool {
	if x != nil && x.PingScan != nil {
		return *x.PingScan
	}
	return false
}

func (x *DispatcherPortScannerOption) GetGatewayMac() string {
	if x != nil && x.GatewayMac != nil {
		return *x.GatewayMac
	}
	return ""
}

func (x *DispatcherPortScannerOption) GetSendEth() string {
	if x != nil && x.SendEth != nil {
		return *x.SendEth
	}
	return ""
}

func (x *DispatcherPortScannerOption) GetRetries() int32 {
	if x != nil && x.Retries != nil {
		return *x.Retries
	}
	return 0
}

func (x *DispatcherPortScannerOption) GetDeepGetOs() bool {
	if x != nil && x.DeepGetOs != nil {
		return *x.DeepGetOs
	}
	return false
}

func (x *DispatcherPortScannerOption) GetDeepGetMac() bool {
	if x != nil && x.DeepGetMac != nil {
		return *x.DeepGetMac
	}
	return false
}

func (x *DispatcherPortScannerOption) GetIsIpv6() bool {
	if x != nil && x.IsIpv6 != nil {
		return *x.IsIpv6
	}
	return false
}

func (x *DispatcherPortScannerOption) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *DispatcherPortScannerOption) GetTreckScan() bool {
	if x != nil && x.TreckScan != nil {
		return *x.TreckScan
	}
	return false
}

var File_common_proto protoreflect.FileDescriptor

var file_common_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x72, 0x70, 0x63, 0x78, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x2c, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x42, 0x0a,
	0x12, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x22, 0x30, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x1b, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x13,
	0x69, 0x70, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x72, 0x70, 0x63, 0x78,
	0x2e, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x70, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x11, 0x69, 0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x56, 0x0a, 0x16, 0x49, 0x70, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26,
	0x0a, 0x0a, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x22, 0x38, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x22, 0x18, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb6, 0x02, 0x0a, 0x11, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x68, 0x61, 0x72, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x68, 0x61, 0x72, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x2e, 0x0a, 0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c,
	0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x0c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x14, 0x0a, 0x12, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x15, 0x0a, 0x13, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x0c, 0x52, 0x61, 0x77, 0x47, 0x72,
	0x61, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70, 0x63,
	0x78, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x22, 0xdb, 0x02, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69, 0x70,
	0x76, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x49, 0x70, 0x76, 0x36,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a,
	0x16, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x5f, 0x69, 0x6e, 0x5f, 0x64, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x49, 0x6e,
	0x44, 0x62, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72,
	0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x69, 0x73, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x12,
	0x30, 0x0a, 0x14, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63,
	0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x55, 0x72,
	0x6c, 0x12, 0x31, 0x0a, 0x15, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x4b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22,
	0xe2, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12,
	0x26, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x48, 0x00, 0x52,
	0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x43, 0x0a, 0x11, 0x66, 0x75, 0x6c, 0x6c, 0x5f,
	0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x69,
	0x74, 0x65, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x75, 0x6c,
	0x6c, 0x53, 0x69, 0x74, 0x65, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x42, 0x06, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x22, 0xb8, 0x01, 0x0a,
	0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6a,
	0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62,
	0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x93, 0x02, 0x0a, 0x0c, 0x43, 0x72, 0x61, 0x77,
	0x6c, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70,
	0x63, 0x78, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x75, 0x62, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x75, 0x62, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x22, 0x97, 0x0d,
	0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x49, 0x70, 0x76, 0x36, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x65, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x17, 0x0a, 0x04, 0x63,
	0x65, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x65, 0x72,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x63, 0x65, 0x72, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x43, 0x65, 0x72, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x48, 0x01, 0x52, 0x05, 0x63, 0x65, 0x72, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2b, 0x0a,
	0x03, 0x64, 0x6f, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79,
	0x48, 0x02, 0x52, 0x03, 0x64, 0x6f, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x07, 0x66, 0x61, 0x76, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x48, 0x04,
	0x52, 0x07, 0x66, 0x61, 0x76, 0x69, 0x63, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x66, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x03, 0x66, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x72,
	0x61, 0x75, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0a, 0x52, 0x07, 0x69, 0x73, 0x46,
	0x72, 0x61, 0x75, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x75, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x09, 0x66,
	0x72, 0x61, 0x75, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x68, 0x6f, 0x6e, 0x65, 0x79, 0x70, 0x6f, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x0c, 0x52, 0x0a, 0x69, 0x73, 0x48, 0x6f, 0x6e, 0x65, 0x79, 0x70, 0x6f, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x28, 0x0a, 0x0d, 0x68, 0x6f, 0x6e, 0x65, 0x79, 0x70, 0x6f, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x0c, 0x68, 0x6f, 0x6e, 0x65,
	0x79, 0x70, 0x6f, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0e, 0x52, 0x06, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x10,
	0x52, 0x08, 0x69, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x72, 0x73, 0x65, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x11,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x72, 0x73, 0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x74, 0x66, 0x38, 0x68, 0x74, 0x6d, 0x6c, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x74, 0x66, 0x38, 0x68, 0x74, 0x6d, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x42, 0x6f, 0x64, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x18, 0x22,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x18, 0x24, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x70, 0x63,
	0x6e, 0x65, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x70, 0x63, 0x6e, 0x65,
	0x74, 0x12, 0x21, 0x0a, 0x05, 0x67, 0x65, 0x6f, 0x69, 0x70, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x6f, 0x69, 0x70, 0x52, 0x05, 0x67,
	0x65, 0x6f, 0x69, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18,
	0x28, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x2a,
	0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67,
	0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x03, 0x61, 0x73,
	0x6e, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x41,
	0x53, 0x4e, 0x52, 0x03, 0x61, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74,
	0x62, 0x69, 0x6f, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6e, 0x65, 0x74, 0x62, 0x69, 0x6f, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07,
	0x6a, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x4a, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x6a, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1e, 0x0a, 0x04, 0x6a, 0x61, 0x72, 0x6d, 0x18, 0x2f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x4a, 0x61, 0x72, 0x6d,
	0x52, 0x04, 0x6a, 0x61, 0x72, 0x6d, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x30, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x12, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x73,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x64, 0x6f, 0x6d, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x61, 0x76, 0x69, 0x63, 0x6f, 0x6e, 0x42,
	0x06, 0x0a, 0x04, 0x5f, 0x66, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x62, 0x6f, 0x64, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x66, 0x72, 0x61, 0x75, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69,
	0x73, 0x5f, 0x68, 0x6f, 0x6e, 0x65, 0x79, 0x70, 0x6f, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x68,
	0x6f, 0x6e, 0x65, 0x79, 0x70, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x75, 0x62, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x73, 0x65, 0x74, 0x42,
	0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x52, 0x0a, 0x06, 0x4a, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x30, 0x0a, 0x04, 0x4a,
	0x61, 0x72, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x86, 0x04,
	0x0a, 0x0f, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x39, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41,
	0x6e, 0x79, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x01, 0x75,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x41, 0x6c,
	0x6c, 0x55, 0x52, 0x4c, 0x46, 0x69, 0x78, 0x55, 0x52, 0x4c, 0x52, 0x01, 0x75, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x49, 0x70, 0x76, 0x36, 0x22, 0xa1, 0x03, 0x0a, 0x0c, 0x41, 0x6c, 0x6c, 0x55, 0x52,
	0x4c, 0x46, 0x69, 0x78, 0x55, 0x52, 0x4c, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x68, 0x6f,
	0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x75, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x75, 0x73, 0x74, 0x49, 0x70, 0x12, 0x22, 0x0a, 0x01, 0x75, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x01, 0x75, 0x12, 0x2a,
	0x0a, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x22, 0xa6, 0x04, 0x0a, 0x0a, 0x43,
	0x65, 0x72, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x65, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x65,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x75,
	0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x63,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x43,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x63, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x43, 0x6e, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x4f, 0x72, 0x67, 0x12,
	0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x6f, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x6f, 0x74, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x69, 0x67, 0x5f, 0x61, 0x6c, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x69, 0x67, 0x41, 0x6c, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x63, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x43, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x01, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x72,
	0x67, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x4f, 0x72, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63,
	0x6e, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x43, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x22, 0x9c, 0x03, 0x0a, 0x07, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x63,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6e, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6e, 0x5f,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x68, 0x61, 0x72, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x66, 0x74, 0x68, 0x61, 0x72, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x77, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e,
	0x75, 0x6d, 0x22, 0xc1, 0x03, 0x0a, 0x05, 0x47, 0x65, 0x6f, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x32, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x33, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6d, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x64, 0x6d, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x2e, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x22, 0x4b, 0x0a, 0x03, 0x41, 0x53, 0x4e, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x61, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x73,
	0x5f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x90, 0x02, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2a, 0x0a,
	0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x9b, 0x04, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x21,
	0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d,
	0x61, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x65, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x07, 0x73,
	0x65, 0x6e, 0x64, 0x45, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05, 0x52, 0x07, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0b, 0x64, 0x65, 0x65, 0x70,
	0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52,
	0x09, 0x64, 0x65, 0x65, 0x70, 0x47, 0x65, 0x74, 0x4f, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0c, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x63, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08, 0x52, 0x06, 0x69, 0x73, 0x49, 0x70, 0x76, 0x36, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x72, 0x65, 0x63, 0x6b,
	0x5f, 0x73, 0x63, 0x61, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0a, 0x52, 0x09, 0x74,
	0x72, 0x65, 0x63, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x73, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6e,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x74, 0x68, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x69,
	0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x72, 0x65, 0x63, 0x6b, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x22, 0xe8, 0x03, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x67, 0x72, 0x61, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x67, 0x72, 0x61, 0x62, 0x43, 0x6f, 0x6e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f,
	0x69, 0x6e, 0x64, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x49, 0x6e, 0x64, 0x62, 0x12,
	0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x72,
	0x61, 0x77, 0x6c, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x31, 0x0a, 0x15, 0x63,
	0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x72, 0x61, 0x77,
	0x6c, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x30,
	0x0a, 0x14, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x72,
	0x61, 0x77, 0x6c, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x55, 0x72, 0x6c,
	0x12, 0x30, 0x0a, 0x14, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x66, 0x75, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x5f, 0x68, 0x6f,
	0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76,
	0x36, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x49, 0x70, 0x76, 0x36, 0x22,
	0xc4, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x25,
	0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72,
	0x70, 0x63, 0x78, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x06,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x97, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x7c, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x69, 0x6e,
	0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x78,
	0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22,
	0xaa, 0x01, 0x0a, 0x1b, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x4a, 0x6f,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x22, 0x89, 0x01, 0x0a,
	0x08, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x22, 0xc8, 0x02, 0x0a, 0x1a, 0x44, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x70, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x69, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x12, 0x44, 0x0a, 0x0c, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0d, 0x76, 0x75, 0x6c, 0x6e, 0x65, 0x72,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x76, 0x75, 0x6c, 0x6e, 0x65, 0x72,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x77, 0x0a, 0x1b, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb9, 0x07, 0x0a,
	0x1a, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x67,
	0x72, 0x61, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x67, 0x72, 0x61, 0x62, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x69, 0x6e, 0x64,
	0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x49, 0x6e, 0x64, 0x62, 0x12, 0x22, 0x0a, 0x0d,
	0x6d, 0x61, 0x78, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x72, 0x61, 0x77, 0x6c,
	0x65, 0x72, 0x41, 0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x31, 0x0a, 0x15, 0x63, 0x72, 0x61, 0x77,
	0x6c, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x72,
	0x55, 0x72, 0x6c, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x63,
	0x72, 0x61, 0x77, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x72, 0x61, 0x77, 0x6c,
	0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x30, 0x0a,
	0x14, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x66, 0x75, 0x6c,
	0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x48, 0x6f,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0a, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x50,
	0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x70, 0x6f,
	0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x67, 0x0a, 0x13, 0x69, 0x70, 0x5f, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x49, 0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x69,
	0x70, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x2f, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x41, 0x6e, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01,
	0x01, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x4e, 0x75,
	0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x56, 0x0a, 0x16, 0x49, 0x70, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x3e, 0x0a, 0x0d, 0x50, 0x6f, 0x72, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x70, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x13, 0x56, 0x75, 0x6c,
	0x6e, 0x65, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x6f, 0x66, 0x61, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x66, 0x61, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x65,
	0x78, 0x70, 0x6c, 0x6f, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x69, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x69, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x31, 0x0a, 0x16, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x75, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x22, 0x36, 0x0a, 0x1b, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x61, 0x75, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x1b, 0x44, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x00, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x22, 0x37, 0x0a, 0x1c, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x19,
	0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0xac, 0x04, 0x0a, 0x1b, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63,
	0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a,
	0x08, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x04, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x45, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a,
	0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05,
	0x52, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0b,
	0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x06, 0x52, 0x09, 0x64, 0x65, 0x65, 0x70, 0x47, 0x65, 0x74, 0x4f, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x25, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61,
	0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x63, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69,
	0x70, 0x76, 0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08, 0x52, 0x06, 0x69, 0x73, 0x49,
	0x70, 0x76, 0x36, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74,
	0x72, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x0a, 0x52, 0x09, 0x74, 0x72, 0x65, 0x63, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x62, 0x6c, 0x61,
	0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x5f, 0x6d, 0x61, 0x63, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x74,
	0x68, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6f, 0x73, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x69, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x72,
	0x65, 0x63, 0x6b, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x32, 0xea, 0x01, 0x0a, 0x07, 0x52, 0x61, 0x77,
	0x47, 0x72, 0x61, 0x62, 0x12, 0x41, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70,
	0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x1b, 0x2e, 0x72, 0x70, 0x63, 0x78,
	0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x18,
	0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x32, 0xfd, 0x01, 0x0a, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x52, 0x4c, 0x12, 0x41, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x14, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x49, 0x50, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x1b, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x32, 0x9b, 0x01, 0x0a, 0x07, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65,
	0x72, 0x12, 0x41, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x1b, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x32, 0xe8, 0x02, 0x0a, 0x0c, 0x44, 0x61, 0x74, 0x61, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70,
	0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x1b, 0x2e, 0x72, 0x70, 0x63, 0x78,
	0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x0d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x75, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x12, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x72, 0x70, 0x63,
	0x78, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x10, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x19, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x32, 0x9e,
	0x01, 0x0a, 0x0a, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x41, 0x0a,
	0x0a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x17, 0x2e, 0x72, 0x70,
	0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4d, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x12, 0x1b, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x32,
	0xa2, 0x03, 0x0a, 0x15, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x20, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x05, 0x50, 0x61, 0x75, 0x73, 0x65, 0x12, 0x20, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x61, 0x75, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x75, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x21,
	0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x04, 0x53, 0x74, 0x6f, 0x70, 0x12,
	0x1f, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x2e,
	0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x72, 0x70, 0x63, 0x78, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x0e, 0x5a, 0x0c, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b,
	0x72, 0x70, 0x63, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData = file_common_proto_rawDesc
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_proto_rawDescData)
	})
	return file_common_proto_rawDescData
}

var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_common_proto_goTypes = []interface{}{
	(*StateQueryRequest)(nil),            // 0: rpcx.StateQueryRequest
	(*StateQueryResponse)(nil),           // 1: rpcx.StateQueryResponse
	(*FinishedNotifyRequest)(nil),        // 2: rpcx.FinishedNotifyRequest
	(*SaveIPDomainRelationRequest)(nil),  // 3: rpcx.SaveIPDomainRelationRequest
	(*StringList)(nil),                   // 4: rpcx.StringList
	(*SaveIPDomainRelationResponse)(nil), // 5: rpcx.SaveIPDomainRelationResponse
	(*FinishedNotifyResponse)(nil),       // 6: rpcx.FinishedNotifyResponse
	(*CustomRuleRequest)(nil),            // 7: rpcx.CustomRuleRequest
	(*CustomRuleIDRequest)(nil),          // 8: rpcx.CustomRuleIDRequest
	(*RuleResponse)(nil),                 // 9: rpcx.RuleResponse
	(*EmptyNotifyRequest)(nil),           // 10: rpcx.EmptyNotifyRequest
	(*EmptyNotifyResponse)(nil),          // 11: rpcx.EmptyNotifyResponse
	(*RawGrabEvent)(nil),                 // 12: rpcx.RawGrabEvent
	(*TaskInfo)(nil),                     // 13: rpcx.TaskInfo
	(*QuickStoreEvent)(nil),              // 14: rpcx.QuickStoreEvent
	(*DataAnalysisEvent)(nil),            // 15: rpcx.DataAnalysisEvent
	(*CheckURLEvent)(nil),                // 16: rpcx.CheckURLEvent
	(*CrawlerEvent)(nil),                 // 17: rpcx.CrawlerEvent
	(*Normal)(nil),                       // 18: rpcx.Normal
	(*JsInfo)(nil),                       // 19: rpcx.JsInfo
	(*Jarm)(nil),                         // 20: rpcx.Jarm
	(*FullSiteCrawler)(nil),              // 21: rpcx.FullSiteCrawler
	(*AllURLFixURL)(nil),                 // 22: rpcx.AllURLFixURL
	(*CertObject)(nil),                   // 23: rpcx.CertObject
	(*RuleTag)(nil),                      // 24: rpcx.RuleTag
	(*Geoip)(nil),                        // 25: rpcx.Geoip
	(*Location)(nil),                     // 26: rpcx.Location
	(*ASN)(nil),                          // 27: rpcx.ASN
	(*TaskEvent)(nil),                    // 28: rpcx.TaskEvent
	(*TaskOption)(nil),                   // 29: rpcx.TaskOption
	(*TransparentParameters)(nil),        // 30: rpcx.TransparentParameters
	(*TaskNotify)(nil),                   // 31: rpcx.TaskNotify
	(*StateNotify)(nil),                  // 32: rpcx.StateNotify
	(*ResultNotify)(nil),                 // 33: rpcx.ResultNotify
	(*ServiceNode)(nil),                  // 34: rpcx.ServiceNode
	(*DispatcherTaskStateRequest)(nil),   // 35: rpcx.DispatcherTaskStateRequest
	(*DispatcherTaskStateResponse)(nil),  // 36: rpcx.DispatcherTaskStateResponse
	(*JobState)(nil),                     // 37: rpcx.JobState
	(*DispatcherTaskStartRequest)(nil),   // 38: rpcx.DispatcherTaskStartRequest
	(*DispatcherPortScannerConfig)(nil),  // 39: rpcx.DispatcherPortScannerConfig
	(*DispatcherTaskStartOptions)(nil),   // 40: rpcx.DispatcherTaskStartOptions
	(*PortGroupItem)(nil),                // 41: rpcx.PortGroupItem
	(*VulnerabilityConfig)(nil),          // 42: rpcx.VulnerabilityConfig
	(*DispatcherTaskResponse)(nil),       // 43: rpcx.DispatcherTaskResponse
	(*DispatcherTaskPauseRequest)(nil),   // 44: rpcx.DispatcherTaskPauseRequest
	(*DispatcherTaskPauseResponse)(nil),  // 45: rpcx.DispatcherTaskPauseResponse
	(*DispatcherTaskResumeRequest)(nil),  // 46: rpcx.DispatcherTaskResumeRequest
	(*DispatcherTaskResumeResponse)(nil), // 47: rpcx.DispatcherTaskResumeResponse
	(*DispatcherTaskStopRequest)(nil),    // 48: rpcx.DispatcherTaskStopRequest
	(*DispatcherTaskStopResponse)(nil),   // 49: rpcx.DispatcherTaskStopResponse
	(*DispatcherPortScannerOption)(nil),  // 50: rpcx.DispatcherPortScannerOption
	nil,                                  // 51: rpcx.SaveIPDomainRelationRequest.IpDomainRelationsEntry
	nil,                                  // 52: rpcx.DispatcherTaskStartOptions.IpDomainRelationsEntry
	(*anypb.Any)(nil),                    // 53: google.protobuf.Any
}
var file_common_proto_depIdxs = []int32{
	51, // 0: rpcx.SaveIPDomainRelationRequest.ip_domain_relations:type_name -> rpcx.SaveIPDomainRelationRequest.IpDomainRelationsEntry
	13, // 1: rpcx.RawGrabEvent.task_info:type_name -> rpcx.TaskInfo
	53, // 2: rpcx.TaskInfo.extra:type_name -> google.protobuf.Any
	13, // 3: rpcx.QuickStoreEvent.task_info:type_name -> rpcx.TaskInfo
	18, // 4: rpcx.QuickStoreEvent.normal:type_name -> rpcx.Normal
	21, // 5: rpcx.QuickStoreEvent.full_site_crawler:type_name -> rpcx.FullSiteCrawler
	13, // 6: rpcx.DataAnalysisEvent.task_info:type_name -> rpcx.TaskInfo
	18, // 7: rpcx.DataAnalysisEvent.normal:type_name -> rpcx.Normal
	13, // 8: rpcx.CheckURLEvent.task_info:type_name -> rpcx.TaskInfo
	13, // 9: rpcx.CrawlerEvent.task_info:type_name -> rpcx.TaskInfo
	23, // 10: rpcx.Normal.certs:type_name -> rpcx.CertObject
	53, // 11: rpcx.Normal.dom:type_name -> google.protobuf.Any
	53, // 12: rpcx.Normal.favicon:type_name -> google.protobuf.Any
	25, // 13: rpcx.Normal.geoip:type_name -> rpcx.Geoip
	24, // 14: rpcx.Normal.rule_tags:type_name -> rpcx.RuleTag
	27, // 15: rpcx.Normal.asn:type_name -> rpcx.ASN
	19, // 16: rpcx.Normal.js_info:type_name -> rpcx.JsInfo
	20, // 17: rpcx.Normal.jarm:type_name -> rpcx.Jarm
	53, // 18: rpcx.FullSiteCrawler.extra_headers:type_name -> google.protobuf.Any
	22, // 19: rpcx.FullSiteCrawler.u:type_name -> rpcx.AllURLFixURL
	53, // 20: rpcx.AllURLFixURL.u:type_name -> google.protobuf.Any
	26, // 21: rpcx.Geoip.location:type_name -> rpcx.Location
	29, // 22: rpcx.TaskEvent.options:type_name -> rpcx.TaskOption
	30, // 23: rpcx.TaskEvent.parameters:type_name -> rpcx.TransparentParameters
	34, // 24: rpcx.TaskNotify.Node:type_name -> rpcx.ServiceNode
	32, // 25: rpcx.TaskNotify.state:type_name -> rpcx.StateNotify
	33, // 26: rpcx.TaskNotify.result:type_name -> rpcx.ResultNotify
	37, // 27: rpcx.DispatcherTaskStateResponse.jobs:type_name -> rpcx.JobState
	39, // 28: rpcx.DispatcherTaskStartRequest.port_scanner:type_name -> rpcx.DispatcherPortScannerConfig
	42, // 29: rpcx.DispatcherTaskStartRequest.vulnerability:type_name -> rpcx.VulnerabilityConfig
	40, // 30: rpcx.DispatcherTaskStartRequest.options:type_name -> rpcx.DispatcherTaskStartOptions
	50, // 31: rpcx.DispatcherPortScannerConfig.options:type_name -> rpcx.DispatcherPortScannerOption
	41, // 32: rpcx.DispatcherTaskStartOptions.port_group:type_name -> rpcx.PortGroupItem
	52, // 33: rpcx.DispatcherTaskStartOptions.ip_domain_relations:type_name -> rpcx.DispatcherTaskStartOptions.IpDomainRelationsEntry
	53, // 34: rpcx.DispatcherTaskStartOptions.extra:type_name -> google.protobuf.Any
	4,  // 35: rpcx.SaveIPDomainRelationRequest.IpDomainRelationsEntry.value:type_name -> rpcx.StringList
	4,  // 36: rpcx.DispatcherTaskStartOptions.IpDomainRelationsEntry.value:type_name -> rpcx.StringList
	0,  // 37: rpcx.RawGrab.StateQuery:input_type -> rpcx.StateQueryRequest
	2,  // 38: rpcx.RawGrab.FinishedNotify:input_type -> rpcx.FinishedNotifyRequest
	10, // 39: rpcx.RawGrab.CustomProtocolNotify:input_type -> rpcx.EmptyNotifyRequest
	0,  // 40: rpcx.CheckURL.StateQuery:input_type -> rpcx.StateQueryRequest
	3,  // 41: rpcx.CheckURL.SaveIPDomainRelation:input_type -> rpcx.SaveIPDomainRelationRequest
	2,  // 42: rpcx.CheckURL.FinishedNotify:input_type -> rpcx.FinishedNotifyRequest
	0,  // 43: rpcx.Crawler.StateQuery:input_type -> rpcx.StateQueryRequest
	2,  // 44: rpcx.Crawler.FinishedNotify:input_type -> rpcx.FinishedNotifyRequest
	0,  // 45: rpcx.DataAnalysis.StateQuery:input_type -> rpcx.StateQueryRequest
	2,  // 46: rpcx.DataAnalysis.FinishedNotify:input_type -> rpcx.FinishedNotifyRequest
	7,  // 47: rpcx.DataAnalysis.CustomRuleAdd:input_type -> rpcx.CustomRuleRequest
	7,  // 48: rpcx.DataAnalysis.CustomRuleUpdate:input_type -> rpcx.CustomRuleRequest
	8,  // 49: rpcx.DataAnalysis.CustomRuleDelete:input_type -> rpcx.CustomRuleIDRequest
	0,  // 50: rpcx.QuickStore.StateQuery:input_type -> rpcx.StateQueryRequest
	2,  // 51: rpcx.QuickStore.FinishedNotify:input_type -> rpcx.FinishedNotifyRequest
	38, // 52: rpcx.DispatcherTaskService.Start:input_type -> rpcx.DispatcherTaskStartRequest
	44, // 53: rpcx.DispatcherTaskService.Pause:input_type -> rpcx.DispatcherTaskPauseRequest
	46, // 54: rpcx.DispatcherTaskService.Resume:input_type -> rpcx.DispatcherTaskResumeRequest
	48, // 55: rpcx.DispatcherTaskService.Stop:input_type -> rpcx.DispatcherTaskStopRequest
	35, // 56: rpcx.DispatcherTaskService.State:input_type -> rpcx.DispatcherTaskStateRequest
	1,  // 57: rpcx.RawGrab.StateQuery:output_type -> rpcx.StateQueryResponse
	6,  // 58: rpcx.RawGrab.FinishedNotify:output_type -> rpcx.FinishedNotifyResponse
	11, // 59: rpcx.RawGrab.CustomProtocolNotify:output_type -> rpcx.EmptyNotifyResponse
	1,  // 60: rpcx.CheckURL.StateQuery:output_type -> rpcx.StateQueryResponse
	5,  // 61: rpcx.CheckURL.SaveIPDomainRelation:output_type -> rpcx.SaveIPDomainRelationResponse
	6,  // 62: rpcx.CheckURL.FinishedNotify:output_type -> rpcx.FinishedNotifyResponse
	1,  // 63: rpcx.Crawler.StateQuery:output_type -> rpcx.StateQueryResponse
	6,  // 64: rpcx.Crawler.FinishedNotify:output_type -> rpcx.FinishedNotifyResponse
	1,  // 65: rpcx.DataAnalysis.StateQuery:output_type -> rpcx.StateQueryResponse
	6,  // 66: rpcx.DataAnalysis.FinishedNotify:output_type -> rpcx.FinishedNotifyResponse
	9,  // 67: rpcx.DataAnalysis.CustomRuleAdd:output_type -> rpcx.RuleResponse
	9,  // 68: rpcx.DataAnalysis.CustomRuleUpdate:output_type -> rpcx.RuleResponse
	9,  // 69: rpcx.DataAnalysis.CustomRuleDelete:output_type -> rpcx.RuleResponse
	1,  // 70: rpcx.QuickStore.StateQuery:output_type -> rpcx.StateQueryResponse
	6,  // 71: rpcx.QuickStore.FinishedNotify:output_type -> rpcx.FinishedNotifyResponse
	43, // 72: rpcx.DispatcherTaskService.Start:output_type -> rpcx.DispatcherTaskResponse
	45, // 73: rpcx.DispatcherTaskService.Pause:output_type -> rpcx.DispatcherTaskPauseResponse
	47, // 74: rpcx.DispatcherTaskService.Resume:output_type -> rpcx.DispatcherTaskResumeResponse
	49, // 75: rpcx.DispatcherTaskService.Stop:output_type -> rpcx.DispatcherTaskStopResponse
	36, // 76: rpcx.DispatcherTaskService.State:output_type -> rpcx.DispatcherTaskStateResponse
	57, // [57:77] is the sub-list for method output_type
	37, // [37:57] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishedNotifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveIPDomainRelationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveIPDomainRelationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishedNotifyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomRuleIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyNotifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyNotifyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawGrabEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickStoreEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataAnalysisEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckURLEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrawlerEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Normal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Jarm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullSiteCrawler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllURLFixURL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CertObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Geoip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ASN); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransparentParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResultNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStartRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherPortScannerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStartOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortGroupItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulnerabilityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskPauseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskPauseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskResumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskResumeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStopRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherTaskStopResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DispatcherPortScannerOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_common_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_common_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*QuickStoreEvent_Normal)(nil),
		(*QuickStoreEvent_FullSiteCrawler)(nil),
	}
	file_common_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_common_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_common_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*TaskNotify_State)(nil),
		(*TaskNotify_Result)(nil),
	}
	file_common_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_common_proto_msgTypes[46].OneofWrappers = []interface{}{}
	file_common_proto_msgTypes[50].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   6,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_rawDesc = nil
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
