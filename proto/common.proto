syntax = "proto3";

package rpcx;
import "google/protobuf/any.proto";
option go_package = "./proto;rpcx";

service RawGrab {
  rpc StateQuery(StateQueryRequest) returns (StateQueryResponse){}
  rpc FinishedNotify(FinishedNotifyRequest) returns (FinishedNotifyResponse){}
  rpc CustomProtocolNotify(EmptyNotifyRequest) returns (EmptyNotifyResponse){}
}

service CheckURL {
  rpc StateQuery(StateQueryRequest) returns (StateQueryResponse){}
  rpc SaveIPDomainRelation(SaveIPDomainRelationRequest) returns (SaveIPDomainRelationResponse){}
  rpc FinishedNotify(FinishedNotifyRequest) returns (FinishedNotifyResponse){}
}

service Crawler {
  rpc StateQuery(StateQueryRequest) returns (StateQueryResponse){}
  rpc FinishedNotify(FinishedNotifyRequest) returns (FinishedNotifyResponse){}
}

service DataAnalysis {
  rpc StateQuery(StateQueryRequest) returns (StateQueryResponse){}
  rpc FinishedNotify(FinishedNotifyRequest) returns (FinishedNotifyResponse){}

  rpc CustomRuleAdd(CustomRuleRequest) returns (RuleResponse){}
  rpc CustomRuleUpdate(CustomRuleRequest) returns (RuleResponse){}
  rpc CustomRuleDelete(CustomRuleIDRequest) returns (RuleResponse){}
}

service QuickStore {
  rpc StateQuery(StateQueryRequest) returns (StateQueryResponse){}
  rpc FinishedNotify(FinishedNotifyRequest) returns (FinishedNotifyResponse){}
}

message StateQueryRequest {
  string task_id = 1; // 任务id
}

message StateQueryResponse {
  int64 total = 1; // 任务总数
  int64 remain = 2; // 剩余任务数
}

message FinishedNotifyRequest {
  string task_id = 1; // 任务id
}

message SaveIPDomainRelationRequest {
  string task_id = 1;
  map<string, StringList> ip_domain_relations = 2;
}

message StringList {
  repeated string domains = 1;
}

message SaveIPDomainRelationResponse {
  bool success = 1;
}

message FinishedNotifyResponse{}

message CustomRuleRequest {
  string product = 1;
  string rule = 2;
  string rule_id = 3;
  string level = 5;
  string category = 6;
  string parent_category = 7;
  string softhard = 8;
  string company = 9;
  string version = 10;
  string from = 11;
  string cached_at = 12;
}

message CustomRuleIDRequest {
  string rule_id = 1;
}

message RuleResponse {
  int64 total = 1;
}

message EmptyNotifyRequest {}
message EmptyNotifyResponse {}

message RawGrabEvent {
  TaskInfo task_info = 1;
  string job_id = 2;
  string origin = 3;
  string ip = 4;
  uint32 port = 5;
  string base_protocol = 6;
  string bind_protocol = 7;
}

message TaskInfo {
  string task_id = 1;
  bool is_ipv6 = 2; // 是否是ipv6扫描
  int32 task_type = 3; // 任务类型
  bool unknown_protocol_in_db = 7; // 未知协议是否入库
  bool is_crawler_all_url = 8; // 是否开启全站爬虫（调用goby爬虫）
  string crawler_specific_url = 9; // 爬取指定URL
  string crawler_url_black_key = 10; // URL黑名单
  optional google.protobuf.Any extra = 11; // 透传字段，会原样写入到service、subdomain
}


message QuickStoreEvent {
  TaskInfo task_info = 1;
  string job_id = 2;
  string origin = 3;
  oneof data {
    Normal normal = 4;
    FullSiteCrawler full_site_crawler = 5;
  }
}

message DataAnalysisEvent {
  TaskInfo task_info = 1;
  string job_id = 2;
  string origin = 3;
  Normal normal = 4;
}

message CheckURLEvent {
  TaskInfo task_info = 1;
  string job_id = 2;
  string ip = 3;
  uint32 port = 4;
  string protocol = 5;
  string base_protocol = 6;
}

message CrawlerEvent {
  TaskInfo task_info = 1;
  string job_id = 2;
  string ip = 3;
  uint32 port = 4;
  string protocol = 5;
  string base_protocol = 6;
  string url = 7; // 要抓取的完整url
  string domain = 8;
  string subdomain = 9;
  string host = 10;
}

message Normal {
  string ip = 1;
  uint32 port = 2;
  string protocol = 3;
  string base_protocol = 4;
  bool is_ipv6 = 5;
  string banner = 6;
  uint32 banner_len = 7;
  string timestamp = 8;
  // website's string cert
  optional string cert = 9;
  // the ip's hostname array
  repeated string hostnames = 10;
  optional CertObject certs = 11;
  repeated string appserver = 12;
  // website dom object
  optional google.protobuf.Any dom = 13;
  optional string domain = 14;
  // website icon object
  optional google.protobuf.Any favicon = 15;
  // website's dom fid by EHash calculate
  optional string fid = 16;
  optional string header = 17;
  optional string host = 18;
  optional string body = 19;
  optional string title = 20;
  repeated string version = 21;
  // 是否是垃圾网站
  optional bool is_fraud = 22;
  // 垃圾网站名称
  optional string fraud_name = 23;
  // 是否是蜜罐
  optional bool is_honeypot = 24;
  //蜜罐名称
  optional string honeypot_name = 25;
  optional string server = 26;
  // 子域名
  optional string subdomain = 27;
  // 是否是域名
  optional bool is_domain = 28;
  // 网站编码
  optional string charset = 29;

  // response http code
  uint32 status_code = 30;
  string utf8html = 31;
  string sub_body = 32;
  // 开发语言
  repeated string language = 33;
  // 中间件
  repeated string middleware = 34;
  // 操作系统
  repeated string os = 35;
  // 硬件产品型号
  repeated string modal = 36;
  string location_url = 37;
  string ipcnet = 38;
  Geoip geoip = 39;
  repeated string product = 40;
  repeated RuleTag rule_tags = 41;
  ASN asn = 42;
  string mac = 43;
  string netbios_name = 44;
  JsInfo js_info = 45;
  bool is_sensitive = 46;
  Jarm jarm = 47;
  optional string url = 48;
}

message JsInfo {
  repeated string md5 = 1;
  repeated int64 len = 2;
  repeated string name = 3;
  int64 num = 4;
}

message Jarm {
  string hash = 1;
  string group = 2;
}


message FullSiteCrawler {
  string url = 1;
  string type = 2; // link, xhr, form
  string params = 3; //  url query
  string data = 4; //  POST data
  string method = 5; // GET, POST
  string post_data_type = 6; //  xml? json?
  string hash = 7; //  uniq hash
  google.protobuf.Any extra_headers = 8; //额外的header
  string content_type = 9; // 类型，一般是响应后的数据
  string referer = 10;  // 来源
  uint32 state = 11;  //  状态，0未处理，1是完成扫描，2是错误
  uint32 status_code = 12;  //  响应状态码
  string host_info = 13;  //  hostinfo
  string path = 14;  //  path
  string dir = 15;  //  dir
  string file = 16;  //  file
  AllURLFixURL u = 17;
  string ip = 18;
  bool is_ipv6 = 19;
}

message AllURLFixURL {
  string host_info = 1; //  ip:port 肯定带端口
  string fixed_host_info = 2; //  scheme://host 不一定带端口，默认情况下会省略
  string ip = 3;
  uint32 port = 4;
  string path = 5;
  string dir = 6; //  解析后的
  string file = 7;  //  解析后的
  string ext = 8;  //  解析后的
  string method = 9;
  string content_type = 10;
  string data = 11;
  string post_data_type = 12;
  string must_ip = 13;
  google.protobuf.Any u = 14; //  *url.URL
  bool parse_with_scheme = 15; //  是否解析的时候自带scheme
}


message CertObject {
  // 证书日期
  string cert_date = 1;
  // 证书个数
  int32 cert_num = 2;
  string domain = 3;

  // 是否受信任
  bool is_valid = 4;
  string issuer_cn = 5;
  repeated string issuer_cns = 6;
  repeated string issuer_org = 7;
  string not_after = 8;
  string not_before = 9;
  string sig_alth = 10;
  string sn = 11;
  string subject_cn = 12;
  string v = 13;
  // 可信类型
  string valid_type = 14;
  repeated string subject_org = 15;
  repeated string subject_cns = 16;
  repeated string subject_names = 17;
  bool is_match = 18;
  bool is_expired = 19;
}
message RuleTag {
  string category = 1;
  string cn_category = 2;
  string cn_company = 3;
  string cn_parent_category = 4;
  string cn_product = 5;
  string company = 6;
  string level = 7;
  string parent_category = 8;
  string product = 9;
  string rule_id = 10;
  string softhard = 11;
  string version_raw = 12;
  double version_num = 13;
}

message Geoip {
  string city_name = 1;
  string continent_code = 2;
  string country_code2 = 3;
  string country_code3 = 4;
  string country_name = 5;
  int32 dma_code = 6;
  double latitude = 7;
  Location location = 8;
  double longitude = 9;
  string postal_code = 10;
  string real_region_name = 11;
  string region_name = 12;
  string timezone = 13;
}

message Location {
  double lat = 1;
  double lon = 2;
}

message ASN {
  uint32 as_number = 1;
  string as_organization = 2;
}

// 端口扫描任务的事件
message TaskEvent {
  string task_id = 1;   // 任务ID
  string job_id = 2;    // JobID
  string command = 3;   // 命令
  uint32  task_type = 4; // 任务类型
  repeated string ports = 5;
  repeated string host_infos = 6;
  TaskOption options = 7;
  TransparentParameters parameters = 8;
}

message TaskOption {
  // rate the max bandwidth used to scan
  optional int32 rate = 1;
  // blacklist ips will not scan.
  optional string blacklist = 2;
  // use ping to scan the ip
  optional bool ping_scan = 3;
  // use gateway on mac
  optional string gateway_mac = 4;

  optional string send_eth = 5;
  // retry times
  optional int32 retries = 6;

  // parse the OS info
  optional bool deep_get_os = 7;
  // parse the mac info
  optional bool deep_get_mac = 8;
  // indicate is ipv6 ip list.
  optional bool is_ipv6 = 9;
  // pick the device by deviceName
  optional string deviceName = 10;

  optional bool treck_scan = 11;
}

// 透传参数
message TransparentParameters {
  int32 grab_concurrent = 1;
  int32 protocol_update_cycle = 2;
  bool unknown_protocol_indb = 3;
  int32 max_asset_num = 4;
  bool crawler_all_url = 5;
  string crawler_url_black_key = 6;
  string crawler_specific_url = 7;
  bool full_protocol_detect = 8;
  bool resolve_host = 9;
  string name_server = 10;
  bool is_ipv6 = 11;
}


message TaskNotify {
  ServiceNode Node = 1;
  string task_id = 2;
  string job_id = 3;
  oneof data {
    StateNotify state = 4;
    ResultNotify result = 5;
  }
}

message StateNotify {
  string state = 1;
  float progress = 2;
  string workingOn = 3;
  string remainTime = 4;
  string message = 5;
}

message ResultNotify {
  string base_protocol = 1;
  string ip = 2;
  uint32 port = 3;
  string bind_protocol = 4;
}

message ServiceNode {
  string service_id = 1;
  string service_name = 2;
  string service_version = 3;
}

service DispatcherTaskService {
  rpc Start(DispatcherTaskStartRequest) returns (DispatcherTaskResponse) {}
  rpc Pause(DispatcherTaskPauseRequest) returns (DispatcherTaskPauseResponse) {}
  rpc Resume(DispatcherTaskResumeRequest) returns (DispatcherTaskResumeResponse) {}
  rpc Stop(DispatcherTaskStopRequest) returns (DispatcherTaskStopResponse) {}
  rpc State(DispatcherTaskStateRequest) returns (DispatcherTaskStateResponse) {}
}

message DispatcherTaskStateRequest {
  string task_id = 1;
}

message DispatcherTaskStateResponse {
  string task_id = 1;
  string state = 2;
  float progress = 3;
  string workingOn = 4;
  repeated JobState jobs = 5;
}

message JobState {
  string job_id = 1;
  string state = 2;
  float progress = 3;
  string workingOn = 4;
  string nodeID = 5;
}

message DispatcherTaskStartRequest {
  string task_id = 1;
  repeated string ports = 2;
  repeated string ip_lists = 3;
  repeated string host_infos = 4;
  DispatcherPortScannerConfig port_scanner = 5;
  VulnerabilityConfig vulnerability = 6;
  DispatcherTaskStartOptions options = 7;
}

message DispatcherPortScannerConfig {
  uint32  task_type = 1;
  DispatcherPortScannerOption options = 2;
}

message DispatcherTaskStartOptions{
  int32 grab_concurrent = 1;
  int32 protocol_update_cycle = 2;
  bool unknown_protocol_indb = 3;
  int32 max_asset_num = 4;
  bool crawler_all_url = 5;
  string crawler_url_black_key = 6;
  // 爬取指定URL
  string crawler_specific_url = 7;
  bool full_protocol_detect = 8;
  bool resolve_host = 9;
  string name_server = 10;
  repeated PortGroupItem port_group = 11;
  map<string, StringList> ip_domain_relations = 12;
  optional google.protobuf.Any extra = 13;
  int32 split_policy = 14; // 拆分策略 0: 默认/不拆分 1: 按IP拆分 2: 按端口拆分
  int32 split_num = 15; // 拆分个数 0: 使用最大可拆分数，非0: 拆分个数
  int32 schedule_strategy = 16; // 调度策略：0:默认策略 1:随机可用节点 2: 按标签选择节点 3: 指定特定节点
  repeated string select_nodes = 17; // 选择的特定节点ID
  repeated string select_labels = 18; // 选择节点标签
}

message PortGroupItem {
  repeated string ip_list = 1;
  string ports = 2;
}

message VulnerabilityConfig {
  string fofa_url = 1;
  repeated string exploit_name = 2;
  repeated string exploit_file = 3;
  repeated string target = 4;
  //Operation 可选值：s 和 e 。s为poc扫描，e为执行exp
  string operation = 5;
}

message DispatcherTaskResponse {
  string task_id = 1;
}

message DispatcherTaskPauseRequest {
  string task_id = 1;
}

message DispatcherTaskPauseResponse {
  string task_id = 1;
}

message DispatcherTaskResumeRequest {
  string task_id = 1;
  optional int32  rate = 2 ;
}

message DispatcherTaskResumeResponse {
  string task_id = 1 ;
}

message DispatcherTaskStopRequest {
  string task_id = 1 ;
}

message DispatcherTaskStopResponse {
  string task_id = 1 ;
}


message DispatcherPortScannerOption {
  // rate the max bandwidth used to scan
  optional int32 rate = 1;
  // blacklist ips will not scan.
  optional string blacklist = 2;
  // use ping to scan the ip
  optional bool ping_scan = 3;
  // use gateway on mac
  optional string gateway_mac = 4;

  optional string send_eth = 5;
  // retry times
  optional int32 retries = 6;
  // parse the OS info
  optional bool deep_get_os = 7;
  // parse the mac info
  optional bool deep_get_mac = 8;
  // indicate is ipv6 ip list.
  optional bool is_ipv6 = 9;

  //pick the device by deviceName
  optional string deviceName = 10;

  optional bool treck_scan = 11;
}
