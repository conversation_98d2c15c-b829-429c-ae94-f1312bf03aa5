package probe

import (
	"math/rand"
	"time"
	"unsafe"
)

var (
	src = rand.NewSource(time.Now().UnixNano())
)

const letterBytes = "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0fabcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
const (
	letterIdxBits = 6                    // 6 bits to represent a letter index
	letterIdxMask = 1<<letterIdxBits - 1 // All 1-bits, as many as letterIdxBits
	letterIdxMax  = 63 / letterIdxBits   // # of letter indices fitting in 63 bits
)

func randStringBytesMaskImprSrcUnsafe(n int) string {
	b := make([]byte, n)
	// A src.Int63() generates 63 random bits, enough for letterIdxMax characters!
	for i, cache, remain := n-1, src.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = src.Int63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(letterBytes) {
			b[i] = letterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return *(*string)(unsafe.Pointer(&b))
}

/*
这个协议只是为了解决发送随机字符目标会返回相应的情况
比如https可以返回，http请求的情况下不返回

- 无返回
	echo "GET / HTTP/1.0\r\n\r\n" | nc qq.com 80
- 有返回
	echo "GET / HTTP/1.0\r\nHost: qq.com\r\n" | nc qq.com 80
- 无返回
	echo "GET / HTTP/1.0\r\nHost: *******\r\n" | nc qq.com 80
- 随机发送内容也有返回
	echo "aaa\r\n\r\n" | nc qq.com 80
*/
func Goby(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "goby"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	// 写
	p.MustWriteTCPString(randStringBytesMaskImprSrcUnsafe(10) + "\r\n\r\n")

	// 读
	b := p.MustReadTCP(-1)
	info.Banner = b
	return
}

func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:         "goby",
		RefererURL:   "https://fofa.so",
		DefaultPorts: []int{42873},
		Handle:       Goby,
	})
}
