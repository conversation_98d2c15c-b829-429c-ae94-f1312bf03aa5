package probe

import (
	"regexp"
	"strconv"
	"strings"
)

var regFtp = regexp.MustCompile(`(?i)^\d{3}(\S*\s*)*ftp`)

func FTP(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "ftp"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	// 请求
	reqBytes := []byte("USER anonymous\r\n")
	p.MustWriteTCP(reqBytes)

	// 响应
	respBytes := p.MustReadTCP(1024)
	info.Banner = respBytes

	if !CheckFTP(respBytes) {
		return
	}

	info.Success = true
	return
}

func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:         "ftp",
		RefererURL:   "https://tools.ietf.org/html/rfc959",
		DefaultPorts: []int{20, 21, 2121, 14147, 15},
		Handle:       FTP,
		PClass:       "B",
	})
}

func CheckFTP(respBytes []byte) bool {
	if len(respBytes) < 4 {
		return false
	}

	// 第4个字符不为空格或横线的不是
	part_4str := string(respBytes[3])
	if part_4str != " " && part_4str != "-" {
		return false
	}

	// 包含mail/smtp的排除
	respStr := strings.ToLower(string(respBytes))
	if strings.Contains(respStr, "mail") || strings.Contains(respStr, "smtp") {
		return false
	}

	// 这个信息应该不是
	lowerRespStr := strings.ToLower(respStr)
	if strings.Contains(lowerRespStr, "syntax error") && !strings.Contains(lowerRespStr, "ftp") &&
		!strings.Contains(lowerRespStr, "password required") {
		return false
	}

	if RegPoppassd.Match(respBytes) {
		return false
	}

	// 判断协议
	code, _ := strconv.Atoi(string(respBytes[:3]))
	if code <= 0 {
		return false
	}

	//编码不对的也先排除
	if code != 200 && code != 220 && code != 530 && code != 500 && code != 501 && code != 421 && code != 331 && code != 332 {
		return false
	}

	// 这些内容也不是：
	if strings.Contains(string(respBytes), "220 VMware Authentication Daemon Version ") {
		return false
	}

	return true
}
