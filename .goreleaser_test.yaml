# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: data_analysis_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp GeoLite2-City.mmdb {{ dir .Path}}/GeoLite2-City.mmdb
        - cp GeoLite2-ASN.mmdb {{ dir .Path}}/GeoLite2-ASN.mmdb
        - \cp -rf {{ dir .Path}}/data_analysis  /home/<USER>/builds/foradar/docker-all-in-one/foscan/data_analysis/
        - \cp -rf {{ dir .Path}}/data_analysis  /home/<USER>/builds/foeye/foscan/releases/data_analysis/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: data_analysis_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp GeoLite2-City.mmdb {{ dir .Path}}/GeoLite2-City.mmdb
        - cp GeoLite2-ASN.mmdb {{ dir .Path}}/GeoLite2-ASN.mmdb
        - \cp -rf {{ dir .Path}}/data_analysis  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/data_analysis/
    goos:
      - linux
    goarch:
      - arm64

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/data_analysis
    ids:
      - data_analysis_amd
    extra_files:
      - config.yaml
      - GeoLite2-City.mmdb
      - GeoLite2-ASN.mmdb
      - script/logrotate.d/foscan
archives:
  - format: binary
