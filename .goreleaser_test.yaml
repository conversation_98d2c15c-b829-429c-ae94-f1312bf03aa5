# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: quickstore
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
    goos:
      - linux
    goarch:
      - amd64
      - arm64

dockers:
  - image_templates:
      - "harbor.fofa.info/fobase/foscan/develop/quickstore:latest"
    ids:
      - quickstore
    extra_files:
      - config.yaml
      - script/logrotate.d/foscan
      -
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
