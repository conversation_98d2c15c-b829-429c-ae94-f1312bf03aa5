# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true
  draft: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/gateway/
    id: api_foradar_amd_sass
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    flags:
      - -tags=foradar
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/gateway/
    id: api_foradar_amd

    ldflags:
      - -X 'api/internal.LicenseVerifySwitch=true'
      - -X 'api/internal.Version={{ .Tag }}'
      - -X 'api/internal.BuildAt={{ .Date }}'
      - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
      - -X 'api/internal.SHA265={{ .Env.PRIVATE_FORADAR_LICENSE_SHA265 }}'
    flags:
      - -tags=foradar
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: mkdir -p /home/<USER>/builds/foradar/docker-all-in-one/foscan/api/data
        - cmd: \cp -rf config.yaml /home/<USER>/builds/foradar/docker-all-in-one/foscan/api/
        - cmd: \cp -rf {{ dir .Path}}/api  /home/<USER>/builds/foradar/docker-all-in-one/foscan/api/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=1
      - CC=aarch64-linux-gnu-gcc
      - CXX=aarch64-linux-gnu-g++
      - AR=aarch64-linux-gnu-ar
    dir: cmd/gateway/
    id: api_foradar_arm
    ldflags:
      - -X 'api/internal.LicenseVerifySwitch=true'
      - -X 'api/internal.Version={{ .Tag }}'
      - -X 'api/internal.BuildAt={{ .Date }}'
      - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
      - -X 'api/internal.SHA265={{ .Env.PRIVATE_FORADAR_LICENSE_SHA265 }}'
    flags:
      - -tags=foradar
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: mkdir -p /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/api/data
        - cmd: \cp -rf config.yaml /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/api/
        - cmd: \cp -rf {{ dir .Path}}/api /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/api/
    goos:
      - linux
    goarch:
      - arm64
  - env:
      - CGO_ENABLED=1
    dir: cmd/gateway/
    id: api_foeye_amd
    ldflags:
      - -X 'api/internal.LicenseVerifySwitch=true'
      - -X 'api/internal.Version={{ .Tag }}'
      - -X 'api/internal.BuildAt={{ .Date }}'
      - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
      - -X 'api/internal.SHA265={{ .Env.PRIVATE_FOEYE_LICENSE_SHA265 }}'
    flags:
      - -tags=foeye
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: mkdir -p /home/<USER>/builds/foeye/foscan/releases/api/data
        - cmd: \cp -rf config.yaml /home/<USER>/builds/foeye/foscan/releases/api/
        - cmd: \cp -rf {{ dir .Path}}/api  /home/<USER>/builds/foeye/foscan/releases/api/
    goos:
      - linux
    goarch:
      - amd64

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/api:latest
    ids:
      - api_foradar_amd_sass
    goarch: amd64
    extra_files:
      - config.yaml
#      - data/foscan.db
      - script/logrotate.d/foscan
archives:
  - format: binary
