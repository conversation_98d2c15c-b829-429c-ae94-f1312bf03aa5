# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: checkurl_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - \cp -rf {{ dir .Path}}/checkurl  /home/<USER>/builds/foradar/docker-all-in-one/foscan/checkurl/
        - \cp -rf {{ dir .Path}}/checkurl  /home/<USER>/builds/foeye/foscan/releases/checkurl/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: checkurl_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - \cp -rf {{ dir .Path}}/checkurl  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/checkurl/
    goos:
      - linux
    goarch:
      - arm64
dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/checkurl:latest
    ids:
      - checkurl_amd
    extra_files:
      - script/logrotate.d/foscan
      - config.yaml
