# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc_server/
    id: portscan_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    binary: portscan
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: \cp -rf config.yaml /home/<USER>/builds/foradar/docker-all-in-one/foscan/portscan/
        - cmd: \cp -rf {{ dir .Path}}/portscan  /home/<USER>/builds/foradar/docker-all-in-one/foscan/portscan/
        - cmd: \cp -rf {{ dir .Path}}/portscan  /home/<USER>/builds/foeye/foscan/releases/portscan/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc_server/
    id: portscan_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    binary: portscan
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: \cp -rf config.yaml  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/portscan/
        - cmd: \cp -rf {{ dir .Path}}/portscan  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/portscan/
    goos:
      - linux
    goarch:
      - arm64

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/portscan:latest
    ids:
      - portscan_amd
    extra_files:
      - config.yaml
      - script/masscan
      - script/logrotate.d/foscan
      - script/libpcap-1.5.3-12.el7.x86_64.rpm
      - script/libpcap-devel-1.5.3-12.el7.x86_64.rpm

