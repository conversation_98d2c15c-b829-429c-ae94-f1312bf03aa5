# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=1
    id: dispatcher_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=1
    id: dispatcher_asset_limit
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: \cp -rf {{ dir .Path}}/dispatcher /home/<USER>/builds/foeye/foscan/releases/dispatcher/
        - cmd: \cp -rf {{ dir .Path}}/dispatcher  /home/<USER>/builds/foradar/docker-all-in-one/foscan/dispatcher/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=1
      - CC=aarch64-linux-gnu-gcc
      - CXX=aarch64-linux-gnu-g++
      - AR=aarch64-linux-gnu-ar
    id: dispatcher_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cmd: cp config.yaml {{ dir .Path}}/config.yaml
        - cmd: \cp -rf {{ dir .Path}}/dispatcher  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/dispatcher/
    goos:
      - linux
    goarch:
      - arm64

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/dispatcher:latest
    ids:
      - dispatcher_amd
    extra_files:
      - config.yaml
      - script/logrotate.d/foscan
archives:
  - format: binary
