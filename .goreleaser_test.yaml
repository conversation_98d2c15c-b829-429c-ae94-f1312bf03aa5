# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

release:
  # You can disable this pipe in order to not create the release on any SCM.
  # Keep in mind that this might also break things that depend on the release
  # URL, for instance, homebrew taps.
  #
  # Templates: allowed (since v1.15)
  disable: true

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: rawgrab_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp trusted_cert_list.txt {{ dir .Path}}/trusted_cert_list.txt
        - \cp -rf {{ dir .Path}}/rawgrab  /home/<USER>/builds/foradar/docker-all-in-one/foscan/rawgrab/
        - \cp -rf {{ dir .Path}}/rawgrab  /home/<USER>/builds/foeye/foscan/releases/rawgrab/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: rawgrab_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp trusted_cert_list.txt {{ dir .Path}}/trusted_cert_list.txt
        - \cp -rf {{ dir .Path}}/rawgrab  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/rawgrab/
    goos:
      - linux
    goarch:
      - arm64

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/rawgrab:latest
    ids:
      - rawgrab_amd
    extra_files:
      - config.yaml
      - trusted_cert_list.txt
      - script/logrotate.d/foscan
archives:
  - format: binary
