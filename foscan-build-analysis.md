# Foscan API 构建机制详解

## 概述

本文档详细分析了 Foscan API 项目的构建控制机制，包括许可证验证控制、CI/CD 流程、以及不同版本的构建策略。

## 问题1：构建控制机制解析

### 命令解析
执行命令 `make build foradar LicenseVerifySwitch=false` 的工作原理：
- 调用 Makefile 中的 `build_foradar` 目标
- 传递参数 `LicenseVerifySwitch=false` 覆盖默认值 `true`

### Makefile 构建流程

```makefile
LicenseVerifySwitch = true

foradar_flags="-s -w -X 'api/internal.LicenseVerifySwitch=${LicenseVerifySwitch}' -X 'api/internal.Version=${version}' -X 'api/internal.BuildAt=${buildAt}' -X 'api/internal.Private=${PRIVATE_FOEYE_LICENSE_KEY}' -X 'api/internal.SHA265=${PRIVATE_FORADAR_LICENSE_SHA265}'"

build_foradar:
	cd cmd/gateway && \
	GOOS=linux CGO_ENABLE=0 go build --tags foradar -ldflags=${foradar_flags} -o api
```

**关键机制：**
1. **默认值**：`LicenseVerifySwitch = true`
2. **参数覆盖**：命令行传入的值会覆盖默认值
3. **编译时注入**：通过 `-ldflags` 的 `-X` 参数将变量值注入到编译后的二进制文件中

### 构建标签（Build Tags）控制

项目使用 Go 的构建标签来区分不同产品版本：

**foradar 版本：**
```go
//go:build foradar
// +build foradar

func NewLicense() *License {
	sec := secret.NewSecret(internal.Private, internal.SHA265, embeds, "embed/rsa/id_rsa_foradar.pub")
	// ...
}
```

**foeye 版本：**
```go
//go:build foeye
// +build foeye

func NewLicense() *License {
	sec := secret.NewSecret(internal.Private, internal.SHA265, embeds, "embed/rsa/id_rsa.pub")
	// ...
}
```

**区别：**
- `foradar` 版本使用 `id_rsa_foradar.pub` 公钥
- `foeye` 版本使用 `id_rsa.pub` 公钥

### 许可证验证控制逻辑

**路由中间件控制：**
```go
licenseVerifySwitch, err := strconv.ParseBool(internal.LicenseVerifySwitch)

if err == nil && licenseVerifySwitch {
    v1.Use(middleware.License(license))
}
```

**系统信息接口控制：**
```go
func (h *handle) getSystemInfo(ctx *gin.Context) {
	licenseVerifySwitch, err := strconv.ParseBool(internal.LicenseVerifySwitch)
	if err != nil || !licenseVerifySwitch {
		// 返回模拟数据
		response.Return(ctx, http.StatusOK, "", response2.GetSystemInfoResponse{
			Version:          internal.Version,
			LicenseState:     true,
			AssetLimitNum:    100000,
			ProductLimitDate: "2025-01-01",
			UpgradeLimitDate: "2025-01-01",
			Modules:          response2.Modules{},
		})
		return
	}
	// 正常许可证验证逻辑
}
```

### 控制效果

**当 `LicenseVerifySwitch=false` 时：**
1. **跳过中间件验证**：API 路由不会应用许可证验证中间件
2. **系统信息接口返回模拟数据**：
   - `LicenseState: true`（许可证状态为有效）
   - `AssetLimitNum: 100000`（资产数量限制）
   - `ProductLimitDate: "2025-01-01"`（产品有效期）
   - `UpgradeLimitDate: "2025-01-01"`（升级有效期）

**当 `LicenseVerifySwitch=true` 时：**
1. **启用完整许可证验证**：所有 API 都需要通过许可证验证
2. **真实许可证信息**：返回实际的许可证状态和限制信息

## 问题2：CI/CD 构建触发机制

### `make build foradar` 不是直接通过CI/CD触发的

从分析来看，**`make build foradar` 命令本身不是通过CI/CD自动触发的**，而是有以下几种触发方式：

### 实际的CI/CD构建流程

#### A. 开发测试构建（developer_test）
```yaml
developer_test:
  stage: developer_test
  when: manual
  script:
    - goreleaser --skip-validate --clean -f .goreleaser_test.yaml
```

- **触发方式**：**手动触发**（`when: manual`）
- **使用工具**：`goreleaser` + `.goreleaser_test.yaml` 配置
- **构建内容**：同时构建 foradar 和 foeye 版本
- **许可证设置**：`LicenseVerifySwitch=true`（启用许可证验证）

#### B. 正式发布构建（release）
```yaml
release:
  stage: release
  only:
    refs:
      - tags
  script:
    - goreleaser release --clean
```

- **触发方式**：**自动触发**（当推送 git tag 时）
- **使用工具**：`goreleaser` + `.goreleaser.yaml` 配置
- **构建内容**：主要构建 foradar 版本
- **许可证设置**：`LicenseVerifySwitch=false`（禁用许可证验证）

#### C. Docker All-in-One 构建
```yaml
docker-all-in-one_develop:
  stage: docker-all-in-one
  when: manual
  script:
    - cd /home/<USER>/builds/foradar/docker-all-in-one/
    - make amd
    - make arm
```

- **触发方式**：**手动触发**
- **执行位置**：在 GitLab Runner 的特定目录执行
- **构建内容**：调用外部 Makefile 构建完整的 Docker 镜像

### Harbor镜像推送

**开发环境镜像构建（手动触发）：**
1. 在GitLab项目页面手动触发 `developer_test` job
2. 执行 `goreleaser -f .goreleaser_test.yaml`
3. 构建foradar版本的API（启用许可证验证）
4. 构建Docker镜像
5. 推送到 `harbor.fofa.info/fobase/foscan/develop/api:latest`

**正式环境镜像构建（自动触发）：**
1. 推送git tag到仓库
2. 自动触发 `release` job
3. 执行 `goreleaser release`
4. 构建foradar版本的API（禁用许可证验证）
5. 构建Docker镜像
6. 推送到 `harbor.fofa.info/fobase/foscan/api`

### 总结

**`make build foradar` 的触发方式：**

1. **本地开发**：开发者在本地手动执行 `make build_foradar`
2. **CI/CD 开发测试**：手动触发 `developer_test` job，使用 `goreleaser` 构建
3. **CI/CD 正式发布**：推送 git tag 自动触发 `release` job
4. **Docker All-in-One**：手动触发相应 job，在 GitLab Runner 机器上执行外部 Makefile

**关键点：**
- **大部分 CI/CD 构建都是手动触发的**（`when: manual`）
- **只有正式发布是自动触发的**（推送 tag 时）
- **实际构建使用的是 `goreleaser` 工具，不是直接调用 `make build foradar`**
- **不同环境使用不同的许可证验证设置**

**答案：`make build foradar` 主要是手动打包，CI/CD 中的构建使用的是 `goreleaser` 工具，而不是直接调用 Makefile 中的构建命令。**

## 问题3：SaaS foradar（无license验证）vs All-in-One（有license验证）实现机制

### 两种不同的构建配置

#### A. SaaS版本 - 无license验证
```yaml
# SaaS版本构建配置
- id: api_foradar_amd_sass
  ldflags:
    - -X 'main.Version={{ .Tag }}'
    - -X 'main.BuildAt={{ .Date }}'
    # 注意：没有设置 LicenseVerifySwitch，默认为空字符串
  flags:
    - -tags=foradar
```

#### B. All-in-One版本 - 有license验证
```yaml
# All-in-One版本构建配置
- id: api_foradar_amd
  ldflags:
    - -X 'api/internal.LicenseVerifySwitch=true'  # 明确启用license验证
    - -X 'api/internal.Version={{ .Tag }}'
    - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
    - -X 'api/internal.SHA265={{ .Env.PRIVATE_FORADAR_LICENSE_SHA265 }}'
  hooks:
    post:
      # 构建完成后复制到all-in-one目录
      - cmd: \cp -rf {{ dir .Path}}/api  /home/<USER>/builds/foradar/docker-all-in-one/foscan/api/
```

### 实现原理

#### SaaS版本（api_foradar_amd_sass）：
1. **不设置** `LicenseVerifySwitch` 变量
2. `internal.LicenseVerifySwitch` 保持默认空字符串 `""`
3. `strconv.ParseBool("")` 会返回错误
4. 触发 `err != nil` 条件，**跳过license验证**
5. 返回模拟的license信息（无限制使用）

#### All-in-One版本（api_foradar_amd）：
1. **明确设置** `LicenseVerifySwitch=true`
2. `internal.LicenseVerifySwitch = "true"`
3. `strconv.ParseBool("true")` 返回 `true, nil`
4. 触发 `licenseVerifySwitch == true` 条件，**启用license验证**
5. 需要真实的license文件才能正常使用

### 部署目标区分

#### SaaS版本：
```yaml
dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/api:latest  # 推送到Harbor作为SaaS服务
    ids:
      - api_foradar_amd_sass  # 使用无license验证的构建
```

#### All-in-One版本：
```yaml
hooks:
  post:
    - cmd: \cp -rf {{ dir .Path}}/api  /home/<USER>/builds/foradar/docker-all-in-one/foscan/api/
    # 复制到本地目录，用于打包all-in-one部署包
```

### 正式发布版本

```yaml
# 正式发布版本 - 也是SaaS无验证版本
ldflags:
  - -X 'api/internal.LicenseVerifySwitch=false'  # 明确设置为false
```

### 总结

这个设计通过**同一次CI/CD构建产生两个不同的二进制文件**：

1. **SaaS版本**：
   - `LicenseVerifySwitch` 为空或false
   - 无license验证，直接可用
   - 推送到Harbor作为容器服务

2. **All-in-One版本**：
   - `LicenseVerifySwitch=true`
   - 需要license验证
   - 复制到本地目录用于客户部署

**巧妙之处**：
- 同一套代码，通过编译时变量注入实现不同行为
- SaaS版本利用 `strconv.ParseBool("")` 的错误特性绕过验证
- All-in-One版本强制要求license验证，保护商业利益

## 问题4：`.goreleaser_test.yaml` 和 `.goreleaser.yaml` 的区别

### 用途区别

#### `.goreleaser_test.yaml` - 开发测试版本
```yaml
release:
  disable: true  # 禁用发布到GitLab Release
  draft: true    # 草稿模式
```

- **用于开发测试环境**
- **不会创建正式的GitLab Release**
- **构建多个版本**（SaaS + All-in-One + foeye）
- **手动触发**

#### `.goreleaser.yaml` - 正式发布版本
```yaml
# 没有disable: true，会创建正式发布
gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org
```

- **用于正式发布**
- **会创建GitLab Release和changelog**
- **只构建SaaS版本**
- **推送tag时自动触发**

### 构建内容区别

#### `.goreleaser_test.yaml` - 构建4个版本
```yaml
builds:
  - id: api_foradar_amd_sass      # SaaS版本（无license验证）
  - id: api_foradar_amd           # All-in-One版本（有license验证）
  - id: api_foradar_arm           # ARM版本
  - id: api_foeye_amd             # foeye版本
```

#### `.goreleaser.yaml` - 只构建1个版本
```yaml
builds:
  - id: api                       # 只有SaaS版本（无license验证）
    flags:
      - -tags=foradar
    ldflags:
      - -X 'api/internal.LicenseVerifySwitch=false'
```

### 镜像推送区别

#### 测试版本推送到开发环境
```yaml
dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/develop/api:latest  # 开发环境
```

#### 正式版本推送到生产环境
```yaml
dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/api  # 生产环境
```

## 问题5：每个goreleaser文件的env是否代指构建不同的二进制程序

**是的！每个`builds`条目都会构建一个独立的二进制程序。**

### 不同的构建配置产生不同的二进制

#### 构建1：SaaS版本二进制
```yaml
- id: api_foradar_amd_sass
  env:
    - CGO_ENABLED=0
  ldflags:
    - -X 'main.Version={{ .Tag }}'
    # 没有LicenseVerifySwitch设置
  flags:
    - -tags=foradar
```

**产生的二进制特点：**
- 文件名：`api`
- License验证：**禁用**（变量为空）
- 用途：SaaS服务

#### 构建2：All-in-One版本二进制
```yaml
- id: api_foradar_amd
  env:
    - CGO_ENABLED=0
  ldflags:
    - -X 'api/internal.LicenseVerifySwitch=true'  # 启用license验证
    - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
  flags:
    - -tags=foradar
```

**产生的二进制特点：**
- 文件名：`api`
- License验证：**启用**
- 用途：客户本地部署

#### 构建3：ARM版本二进制
```yaml
- id: api_foradar_arm
  env:
    - CGO_ENABLED=1
    - CC=aarch64-linux-gnu-gcc  # ARM交叉编译工具链
    - CXX=aarch64-linux-gnu-g++
    - AR=aarch64-linux-gnu-ar
  goarch:
    - arm64  # ARM64架构
```

#### 构建4：foeye版本二进制
```yaml
- id: api_foeye_amd
  flags:
    - -tags=foeye  # 使用foeye构建标签
  ldflags:
    - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
    - -X 'api/internal.SHA265={{ .Env.PRIVATE_FOEYE_LICENSE_SHA265 }}'
```

**产生的二进制特点：**
- 使用不同的许可证密钥
- 使用 `foeye` 构建标签（不同的许可证验证逻辑）

## 问题6：SaaS版本镜像仓库推送区别

### 有test和无test的主要区别

**对于SaaS版本，有test和无test的区别：**

1. **镜像推送地址不同**：
   - 有test：`harbor.fofa.info/fobase/foscan/develop/api:latest`（开发环境）
   - 无test：`harbor.fofa.info/fobase/foscan/api`（正式环境）

2. **License验证实现方式略有不同**：
   - 有test：不设置`LicenseVerifySwitch`（利用空字符串解析错误）
   - 无test：明确设置`LicenseVerifySwitch=false`

3. **最终效果完全一样**：都是无license验证的SaaS版本

4. **其他区别**：
   - 有test：构建多个版本（SaaS + All-in-One + ARM + foeye）
   - 无test：只构建SaaS版本

**所以主要就是推送的镜像仓库不一样，SaaS功能本身没有区别。**

## 总结

### 核心设计思想

1. **一套代码，多种部署**：通过编译时变量注入实现不同的功能行为
2. **灵活的构建策略**：使用goreleaser配置文件管理不同环境的构建需求
3. **商业模式支持**：SaaS版本无限制，All-in-One版本需要license验证
4. **环境隔离**：开发和生产环境使用不同的镜像仓库

### 构建流程总览

```
代码提交 → CI/CD触发 → goreleaser构建 → 产生多个二进制 → 推送到不同目标
                                    ├── SaaS版本 → Harbor镜像仓库
                                    ├── All-in-One版本 → 本地目录
                                    ├── ARM版本 → 本地目录
                                    └── foeye版本 → 本地目录
```

这种设计既满足了SaaS服务的快速部署需求，又保护了商业版本的license控制，是一个非常优雅的解决方案。
