package common

import (
	"errors"
	"fmt"
	"go-micro.dev/v4/selector"

	"go-micro.dev/v4"
	"go-micro.dev/v4/registry"
)

func CurrentNode(srv micro.Service) (*registry.Node, error) {
	serviceUUID := fmt.Sprintf("%s-%s", srv.Server().Options().Name, srv.Server().Options().Id)

	services, _ := srv.Client().Options().Registry.GetService(srv.Server().Options().Name)

	for _, service := range services {
		if service.Version != srv.Server().Options().Version {
			continue
		}
		for _, node := range service.Nodes {
			if node.Id == serviceUUID {
				return node, nil
			}
		}
	}
	return nil, errors.New("can't find the current node")
}

func GetServiceNode(srv micro.Service, serviceID, serviceName, serviceVersion string) (*registry.Node, error) {
	serviceUUID := fmt.Sprintf("%s-%s", serviceName, serviceID)

	services, _ := srv.Client().Options().Registry.GetService(serviceName)

	for _, service := range services {
		if service.Version != serviceVersion {
			continue
		}
		for _, node := range service.Nodes {
			if node.Id == serviceUUID {
				return node, nil
			}
		}

	}
	return nil, errors.New("can't find the current node")
}

func GetServicesByName(srv micro.Service, serviceName string) ([]*registry.Service, error) {
	return srv.Client().Options().Registry.GetService(serviceName)
}

func GetServiceNodesByName(srv micro.Service, serviceName string) ([]*registry.Node, error) {
	services, err := GetServicesByName(srv, serviceName)
	if err != nil {
		return nil, err
	}

	nodes := make([]*registry.Node, 0, len(services))
	for _, service := range services {
		nodes = append(nodes, service.Nodes...)
	}
	return nodes, nil
}

func SelectServiceNode(srv micro.Service, serviceName string) (*registry.Node, error) {
	next, err := srv.Client().Options().Selector.Select(serviceName)
	if err != nil {
		return nil, err
	}
	return next()
}

// SelectStrategyByNodeID 只筛选指定UUID的节点
func SelectStrategyByNodeID(nodeID string) selector.Strategy {
	return func(services []*registry.Service) selector.Next {
		nodes := make([]*registry.Node, 0, len(services))

		for _, service := range services {
			nodes = append(nodes, service.Nodes...)
		}

		return func() (*registry.Node, error) {
			if len(nodes) == 0 {
				return nil, selector.ErrNoneAvailable
			}

			for _, node := range nodes {
				if node.Id == nodeID {
					return node, nil
				}
			}
			return nil, selector.ErrNoneAvailable
		}
	}
}

func NodeSelectOption(nodeID string) selector.SelectOption {
	return selector.WithStrategy(SelectStrategyByNodeID(nodeID))
}
