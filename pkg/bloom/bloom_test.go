package bloom

import (
	"fmt"
	"sync"
	"sync/atomic"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBloomFilter(t *testing.T) {
	wg := sync.WaitGroup{}
	store := NewBloomStore()
	var sum int32 = 0
	for i := 0; i < 20; i++ {
		prefix := fmt.Sprintf("10.10.%d.", i)
		wg.Add(1)
		go testMethod(store, 100, prefix, &sum, &wg)
	}
	wg.Wait()

	assert.EqualValues(t, 100, sum)
}

func testMethod(store *Store, limit int, prefix string, sum *int32, wg *sync.WaitGroup) {
	for i := 0; i < 255; i++ {
		ip := fmt.Sprintf("%s%d", prefix, i)
		if store.Push("task-id", ip) > uint(limit) {
			break
		}
		atomic.AddInt32(sum, 1)
	}
	wg.Done()
}

func TestBloomFilter2(t *testing.T) {
	store := NewBloomStore()
	store.Init("key", []string{"**********", "**********"})
	assert.EqualValues(t, store.Count("key"), 2)
	assert.EqualValues(t, store.Has("key", "**********"), true)
	assert.EqualValues(t, store.Has("key", "**********00"), false)
	assert.EqualValues(t, store.Count("key"), 3)
	store.Push("key", "**********")
	assert.EqualValues(t, store.Count("key"), 3)
	store.Destroy("key")
}
