package bloom

import (
	"sync"

	bf "github.com/bits-and-blooms/bloom/v3"
	cmap "github.com/orcaman/concurrent-map/v2"
)

type filterCounter struct {
	count uint
	bf    *bf.BloomFilter
}

func (fc *filterCounter) Test(key []byte) bool {
	return fc.bf.Test(key)
}

func (fc *filterCounter) Add(key []byte) {
	if fc.bf.Test(key) {
		return
	}
	fc.bf.Add(key)
	fc.count++
}

func (fc *filterCounter) Push(key []byte) uint {
	if !fc.bf.Test(key) {
		fc.bf.Add(key)
		fc.count++
	}
	return fc.count
}

type Store struct {
	bloomMap cmap.ConcurrentMap[*filterCounter]
	rwLock   sync.RWMutex
}

func NewBloomStore() *Store {
	return &Store{
		bloomMap: cmap.New[*filterCounter](),
		rwLock:   sync.RWMutex{},
	}
}

func (bs *Store) Init(key string, items []string) {
	filter := bs.get(key)
	for _, item := range items {
		filter.Add(bs.data(item))
	}
}

func (bs *Store) Has(key, data string) bool {
	filter := bs.get(key)
	bytes := bs.data(data)
	if !filter.Test(bytes) {
		filter.Add(bytes)
		return false
	}
	return true
}

func (bs *Store) Push(key, data string) uint {
	bs.rwLock.Lock()
	defer bs.rwLock.Unlock()
	filter := bs.get(key)
	bytes := bs.data(data)
	if !filter.Test(bytes) {
		return filter.Push(bytes)
	}
	return filter.count
}

func (bs *Store) Count(key string) uint {
	filter := bs.get(key)
	return filter.count
}

func (bs *Store) get(taskID string) *filterCounter {
	if !bs.bloomMap.Has(taskID) {
		bs.bloomMap.Set(taskID, &filterCounter{0, bf.NewWithEstimates(500000, 0.000001)})
	}

	i, _ := bs.bloomMap.Get(taskID)
	return i
}

func (bs *Store) data(data string) []byte {
	return []byte(data)
}

func (bs *Store) Destroy(taskID string) {
	bs.bloomMap.Remove(taskID)
}
