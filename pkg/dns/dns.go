package dns

import (
	"context"
	"net"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/yl2chen/cidranger"
	"go-micro.dev/v4/logger"
)

type resolution struct {
	maxWorkers int
	wg         sync.WaitGroup
	isIPv6     bool
	ranger     cidranger.Ranger
}

type IpDomainRelation struct {
	ip     string
	domain string
}

func (r *resolution) Resolution(domains []string, timeout time.Duration) ([]string, []string, map[string][]string, error) {
	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建输入、输出通道
	// 并发数信号量通道
	semaphore := make(chan struct{}, r.maxWorkers)
	outputChan := make(chan IpDomainRelation)
	failedChan := make(chan string)

	var (
		ips           []string
		relations     = make(map[string][]string)
		failedDomains []string
		receiverWg    sync.WaitGroup // 用于管理接收goroutine
	)

	receiverWg.Add(1)
	// 启动协程从输出通道接收结果
	go func() {
		defer receiverWg.Done()
		for outputChan != nil || failedChan != nil {
			select {
			case result, ok := <-outputChan:
				if !ok {
					outputChan = nil
					continue
				}
				ips = append(ips, result.ip)
				relations[result.ip] = lo.Uniq(append(relations[result.ip], result.domain))
			case f, ok := <-failedChan:
				if !ok {
					failedChan = nil
					continue
				}
				failedDomains = append(failedDomains, f)
			}
		}
	}()

	for i, domain := range domains {
		// 检查是否超时，如果超时则停止发送新任务
		select {
		case <-ctx.Done():
			logger.Infof("DNS resolution timeout reached, stopping new tasks. Processed %d/%d domains", i, len(domains))
			goto waitForCompletion
		default:
		}

		semaphore <- struct{}{}
		r.wg.Add(1)
		go func(domain string) {
			defer func() {
				<-semaphore
				r.wg.Done()
			}()
			// Step 2: 解析域名ip
			// 创建一个超时的 context (使用更短的超时时间)
			resolveCtx, cancel := context.WithTimeout(ctx, time.Second*5)
			defer cancel()

			// 创建一个 Resolver
			resolver := net.Resolver{}

			// 使用 Resolver 的 LookupIPAddr 方法替代 net.LookupIP
			ipAddrs, err := resolver.LookupIPAddr(resolveCtx, domain)
			if err != nil {
				// 检查是否是因为超时取消导致的错误
				if resolveCtx.Err() != nil {
					logger.Debugf("Domain resolution cancelled due to timeout: %s", domain)
				} else {
					failedChan <- domain
				}
				return
			}

			// 从 IPAddr 中提取出 IP
			var ips []net.IP
			for _, ipAddr := range ipAddrs {
				ips = append(ips, ipAddr.IP)
			}

			// Step 3: 使用传入的策略过滤解析后的IP
			var filteredIPs []net.IP
			if r.isIPv6 {
				filteredIPs = filterIPv6(ips)
			} else {
				filteredIPs = filterIPv4(ips)
			}

			for _, ip := range filteredIPs {
				// Step 4: 组装域名ip关系
				contains, err := r.ranger.Contains(ip)

				if err != nil || contains {
					logger.Warnf("ranger.Contains error or filter %s", ip.String())
					continue
				}

				outputChan <- IpDomainRelation{
					ip:     ip.String(),
					domain: domain,
				}
			}
		}(domain)
	}

waitForCompletion:
	// 等待所有已启动的任务完成
	r.wg.Wait()
	// 关闭输出通道
	close(outputChan)
	close(failedChan)

	// 等待接收器完成
	receiverWg.Wait()

	// 检查是否因超时而提前结束
	if ctx.Err() != nil {
		logger.Infof("DNS resolution completed with timeout. Collected %d IPs, %d failed domains, %d relations",
			len(ips), len(failedDomains), len(relations))
	}

	return lo.Uniq(ips), lo.Uniq(failedDomains), relations, nil
}

// filterIPv4 只保留IPv4地址
func filterIPv4(ips []net.IP) []net.IP {
	var filteredIPs []net.IP
	for _, ip := range ips {
		if ip.To4() != nil {
			filteredIPs = append(filteredIPs, ip)
		}
	}
	return filteredIPs
}

// filterIPv6 只保留IPv6地址
func filterIPv6(ips []net.IP) []net.IP {
	var filteredIPs []net.IP
	for _, ip := range ips {
		if ip.To4() == nil {
			filteredIPs = append(filteredIPs, ip)
		}
	}
	return filteredIPs
}

type Resolution interface {
	Resolution([]string, time.Duration) (ips []string, failedDomains []string, relations map[string][]string, err error)
}

func NewResolution(maxWorkers int, isIPv6 bool, ranger cidranger.Ranger) Resolution {
	return &resolution{
		maxWorkers: maxWorkers,
		wg:         sync.WaitGroup{},
		isIPv6:     isIPv6,
		ranger:     ranger,
	}
}
