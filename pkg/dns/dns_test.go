package dns

import (
	"reflect"
	"sort"
	"sync"
	"testing"
	"time"

	"github.com/yl2chen/cidranger"
)

func TestNewResolution(t *testing.T) {
	type args struct {
		maxWorkers int
		isIPv6     bool
		ranger     cidranger.Ranger
	}
	tests := []struct {
		name string
		args args
		want Resolution
	}{
		{
			name: "pass one",
			args: args{
				maxWorkers: 1,
				isIPv6:     false,
				ranger:     cidranger.NewPCTrieRanger(),
			},
			want: NewResolution(1, false, cidranger.NewPCTrieRanger()),
		},
		{
			name: "pass two",
			args: args{
				maxWorkers: 1,
				isIPv6:     true,
				ranger:     cidranger.NewPCTrieRanger(),
			},
			want: NewResolution(1, true, cidranger.NewPCTrieRanger()),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewResolution(tt.args.maxWorkers, tt.args.isIPv6, cidranger.NewPCTrieRanger()); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("NewResolution() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_resolution_resolution(t *testing.T) {
	type fields struct {
		maxWorkers int
		wg         sync.WaitGroup
		isIPv6     bool
	}
	type args struct {
		domains []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		want1   []string
		want2   map[string][]string
		wantErr bool
	}{
		{
			name: "pass ipv4",
			args: args{
				domains: []string{"download.fofa.info"},
			},
			fields: fields{
				maxWorkers: 10000,
				wg:         sync.WaitGroup{},
				isIPv6:     false,
			},
			want: []string{
				"************",
				"************",
				"*************",
				"*************",
			},
			want1: []string{},
			want2: map[string][]string{
				"*************": {"download.fofa.info"},
				"*************": {"download.fofa.info"},
				"************":  {"download.fofa.info"},
				"************":  {"download.fofa.info"},
			},
			wantErr: false,
		},
		{
			name: "pass ipv6",
			args: args{
				domains: []string{"download.fofa.info"},
			},
			fields: fields{
				maxWorkers: 10000,
				wg:         sync.WaitGroup{},
				isIPv6:     true,
			},
			want: []string{
				"************",
				"************",
				"*************",
				"*************",
			},
			want1: []string{},
			want2: map[string][]string{
				"*************": {"download.fofa.info"},
				"*************": {"download.fofa.info"},
				"************":  {"download.fofa.info"},
				"************":  {"download.fofa.info"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := NewResolution(tt.fields.maxWorkers, tt.fields.isIPv6, cidranger.NewPCTrieRanger())

			if !tt.fields.isIPv6 {
				got, got1, got2, err := r.Resolution(tt.args.domains, 10*time.Second)
				if (err != nil) != tt.wantErr {
					t.Errorf("resolution() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				sort.Strings(tt.want)
				for _, g := range got {
					i := sort.SearchStrings(tt.want, g)
					if i < len(tt.want) && tt.want[i] == g {
						continue
					}
					t.Errorf("resolution() got = %v, want %v", got, tt.want)
				}

				sort.Strings(tt.want1)
				for _, g := range got1 {
					i := sort.SearchStrings(tt.want1, g)
					if i < len(tt.want1) && tt.want1[i] == g {
						continue
					}
					t.Errorf("resolution() got1 = %v, want %v", got1, tt.want1)
				}

				if len(got2) != len(tt.want2) {
					t.Errorf("resolution() got2 = %v, want %v", got2, tt.want2)
				}
			}

			if tt.fields.isIPv6 {
				got, got1, got2, err := r.Resolution(tt.args.domains, 10*time.Second)
				if (err != nil) != tt.wantErr {
					t.Errorf("resolution() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				if len(got) != 0 || len(got1) != 0 || len(got2) != 0 {
					t.Errorf("resolution() error ipv6")
				}
			}
		})
	}
}
