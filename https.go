package probe

import (
	"errors"
	"git.gobies.org/longzhuan/go_common/honeypot"
	"regexp"
	"strings"
)

var regHttps = regexp.MustCompile(`^HTTP`)
var regUnknownHttps = regexp.MustCompile(`UNKNOWN 400 Bad Request\r\n`)

func HTTPS(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "https"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	respBytes, header, body, connectionState, err := getHTTP(p, addr, "/", "", true)
	info.ConnectionState = connectionState
	info.Banner = respBytes
	if err != nil {
		return
	}

	// 判断协议，如下情况导致返回是false，但是https协议是对的，跳出就漏判了
	//curl https://*************
	//curl: (51) SSL: no alternative certificate subject name matches target host name '*************'
	if !regHttps.Match(respBytes) && !regUnknownHttps.Match(respBytes) {
		if len(p.GetConnectionState().PeerCertificates) > 0 {
			// 带证书的就是https的逻辑是不对的
			info.Protocol = getProtocolName(respBytes)

			info.Success = true
			info.ConnectionState = p.GetConnectionState()
			return
		}
		err = errors.New("not https")
		return
	}

	info.ConnectionState = p.GetConnectionState()
	// 有header的时候才重新赋值
	if len(header) > 0 {
		info.Banner = header
	}
	hs := string(header)
	bs := string(body)
	//https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status/418
	ctx := newHttpFilterContext(&addr, &info, body, &hs, respBytes)
	if strings.Contains(hs, "418 I'm a teapot") {
		info.Protocol = "htcpcp"
		//} else if isBeacon(hs) {
		//	return Beacon(addr, hs, info.Protocol, info.ConnectionState)
		//} else if strings.Contains(addr, "8688") && strings.Contains(hs, "400 Bad Request") && strings.Contains(hs, "Server: Apache") && strings.Contains(hs, "Connection: close") {
		//	return OpenremoteCtrl(addr, info, opts...)
		//} else if strings.Contains(hs, "X-ClickHouse-Summary") {
		//	return ClickHouseHttp(addr, info, opts...)
		//} else if strings.Contains(hs, "400 Bad Request") && strings.Contains(string(respBytes), "is for clickhouse-client program") {
		//	info.Banner = respBytes
		//	return ClickHouse(addr, info, opts...)
		//} else if isKubernetes(bs, hs) {
		//	return Kubernetes(addr, info, opts...)
		//} else if isPrometheus(bs, hs) {
		//	return Prometheus(addr, info, opts...)
		//} else if isVmware(bs, hs) {
		//	if vmInfo, err1 := getVmwareVersion(addr, info.Protocol, opts...); err1 == nil {
		//		info.Banner = append(info.Banner, vmInfo...)
		//	}
		//} else if isWinRm(addr, bs, hs) {
		//	return WinRm(addr, info, opts...)
	} else {
		httpFiltersLock.RLock()
		defer httpFiltersLock.RUnlock()
		for _, filter := range httpFilters {
			if filter.Https() && filter.Check(ctx, opts...) {
				return filter.Serve(ctx, opts...)
			}
		}
	}

	info.IsHoneypot, info.HoneypotName = honeypot.IsHttpHoneypot("", hs, bs)

	info.Success = true
	return
}

func getProtocolName(b []byte) (proto string) {
	proto = "https"

	if regFtp.Match(b) {
		proto = "sftp"
	} else if CheckRedis(b) {
		proto = "redis-ssl"
	}

	return
}

func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:       "https",
		RefererURL: "https://www.w3.org/Protocols/rfc2616/rfc2616.html",
		DefaultPorts: []int{311, 443, 444, 1311, 1443, 2053, 2080, 2083, 2087, 2096, 2376, 2443,
			3443, 4064, 4430, 4433, 4443, 5000, 5001, 5443, 5986, 6443,
			7000, 7005, 7071, 7443, 7777, 8000, 8010, 8080, 8081, 8082, 8088, 8139, 8834, 8443, 8880, 8889,
			9000, 9001, 9002, 9100, 9443, 9446, 9999, 10000, 10250, 16993},
		Handle: HTTPS,
		Weight: 10,
		PClass: "B",
	})
}
