package structs

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewJsonObj(t *testing.T) {
	obj, err := NewJsonObj("")
	assert.NotNil(t, err)
	assert.Nil(t, obj)

	obj, err = NewJsonObj(`{"a":"b", "banner":""}`)
	assert.Nil(t, err)
	assert.Equal(t, "b", obj.GetString("a"))

	// fastjson不支持
	//obj, err = NewJsonObj(`{"a":{"b":"c"}, "banner":""}`)
	//assert.Nil(t, err)
	//assert.Equal(t, "c", string(obj.GetStringBytes("a.b")))

	// 验证签名
	obj, err = NewJsonObj(`{"body":"<html></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "1465825743341333019", obj.<PERSON><PERSON>())
	assert.Equal(t, "-1255511321700480643", obj.<PERSON>Hash())
	assert.Equal(t, "098f80179f580a6d28145ad2cc205a79", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><known></known></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "7956195940639300741", obj.NHash())
	assert.Equal(t, "-7444481993511630114", obj.FHash())
	assert.Equal(t, "abcb0ba9cefefbb3ad25f955e5ed2e05", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><known><black></black></known></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "4074701324998390642", obj.NHash())
	assert.Equal(t, "3772361052347832008", obj.FHash())
	assert.Equal(t, "f8f64cfbbbbac1db3b5939c27425675f", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><unknown></unknown></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-2864212857490850327", obj.NHash())
	assert.Equal(t, "-6551419606606946569", obj.FHash())
	assert.Equal(t, "b5569dd36789b7be89cad28bc3c8a495", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><unknown><myapp/></unknown></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "5893961949806089791", obj.NHash())
	assert.Equal(t, "8908312994244279890", obj.FHash())
	assert.Equal(t, "97cf192300b888da473be27d5d84abe1", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><myapp></myapp></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "5890507554379951485", obj.NHash())
	assert.Equal(t, "1443961722667255273", obj.FHash())
	assert.Equal(t, "31fd60f56a1dd3da17a586816e2df77f", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><myapp><children></children></myapp></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-5998512438099429752", obj.NHash())
	assert.Equal(t, "282534951678390883", obj.FHash())
	assert.Equal(t, "ede4a241dec55c7f96620edc64eb94ad", obj.EHash())

	// _index 格式测试
	obj, err = NewJsonObj(`{"_type":"subdomain", "_source":{"body":"<html><head></head><body><myapp><children></children></myapp></body></html>"}}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-5998512438099429752", obj.NHash())
	assert.Equal(t, "282534951678390883", obj.FHash())
	assert.Equal(t, "ede4a241dec55c7f96620edc64eb94ad", obj.EHash())
}
