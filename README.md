[![pipeline status](https://git.gobies.org/shared-platform/foscan/checkurl/badges/feature/grpc/pipeline.svg)](https://git.gobies.org/shared-platform/foscan/checkurl/-/commits/feature/grpc)
[![coverage report](https://git.gobies.org/shared-platform/foscan/checkurl/badges/feature/grpc/coverage.svg)](https://git.gobies.org/shared-platform/foscan/checkurl/-/commits/feature/grpc)
[![Latest Release](https://git.gobies.org/shared-platform/foscan/checkurl/-/badges/release.svg)](https://git.gobies.org/shared-platform/foscan/checkurl/-/releases)

checkurl
======
从kafka取url，检查是否需要被爬取，需要大量查询redis和es

### 202303
* 操作ES可以根据开关进行关闭
* Redis的连接池跟配置文件里面的[worker] num保持一致。
* 根域名黑名单增加缓存，不每次都读Redis（这样通过DNS下发的数据，可以减少很多Redis请求）。
