[![pipeline status](https://git.gobies.org/shared-platform/foscan/api/badges/main/pipeline.svg)](https://git.gobies.org/shared-platform/foscan/api/-/commits/main)
[![coverage report](https://git.gobies.org/shared-platform/foscan/api/badges/main/coverage.svg)](https://git.gobies.org/shared-platform/foscan/api/-/commits/main)
[![Latest Release](https://git.gobies.org/shared-platform/foscan/api/-/badges/release.svg)](https://git.gobies.org/shared-platform/foscan/api/-/releases)
# api

foscan api gateway

## Getting started
```shell
go mod tidy
cd cmd/gateway
go run -tags foradar main.go
go run -tags foeye main.go
go run main.go 
```


### build

##### 最新稳定版本均是main分支或者master分支。portscan只有一个分支feature_plugin；checkurl只有一个分支feature/grpc

```shell
license控制版本
make build foeye
make build foradar

非license控制
make build foeye LicenseVerifySwitch=false
make build foradar LicenseVerifySwitch=false


docker-all-in-one

只有api项目feat/version分支的ci流程中有手动触发docker-all-in-one的job

CI发布过程：
1、其余项目比如rawgrab提交后会自动触发Developer_test (api项目本身不会自动触发)
2、替换完成后，再到api项目手动执行docker-all-in-one

手动发布过程：
具体目录：211机器
foardar: /home/<USER>/builds/foradar/docker-all-in-one/
foscan amd版本
foscan_arm arm版本

统计目录查看Makefile执行命令即可

foeye:
CI流程只会自动更新foeye所需foscan程序并不会自动生成tar包，需要手动生成发送给foeye团队。
目录：/home/<USER>/builds/foeye/foscan

执行tar命令
tar -zcvf foscan.release.tar.gz foscan
```

## 规则引擎与加密配置

### Sutra 规则引擎架构

#### 1. 规则加载流程
```
[满献提供加密规则] → [rules.json] → [gosutra项目] → [data_analysis服务] → [规则引擎初始化]
```

#### 2. 规则文件结构
- **源文件**: 满献提供的加密 sutra 规则文件
- **目标文件**: `rules.json` (重命名后)
- **存储位置**: `/home/<USER>/builds/gosutra/resources/rules.json`
- **规则数量**: 40000+ 条产品识别规则

#### 3. 加密与解密机制

##### RSA 公钥配置
```go
// data_analysis 服务启动时设置 RSA 公钥
logger.Info("[SUTRA] RSA 公钥设置成功")
```

##### 规则解密过程
1. **加载加密规则**: 从 `rules.json` 读取加密的规则数据
2. **RSA 解密**: 使用内置的 RSA 私钥解密规则内容
3. **规则解析**: 解析解密后的规则为可执行的匹配规则
4. **内存加载**: 将规则加载到内存中供匹配使用

##### 错误处理
```go
// 解密失败的规则会被跳过
error line , skip current line: ��Name�apilayer-Caddy�Rule�...
```

#### 4. 规则引擎初始化

##### 初始化流程
```go
// 1. 初始化 Sutra 规则引擎
logger.Info("[SUTRA] 开始初始化 Sutra 规则引擎...")

// 2. 设置 RSA 公钥
logger.Info("[SUTRA] RSA 公钥设置成功")

// 3. 加载内置加密规则
logger.Info("[SUTRA] 开始加载内置加密规则...")

// 4. 统计加载结果
logger.Info("[SUTRA] 内置规则加载完成 - 新增规则: 40697, 新增产品: 40697")
logger.Info("[SUTRA] 当前总计 - 规则数量: 40697, 产品数量: 40697")

// 5. 完成初始化
logger.Info("[SUTRA] Sutra 规则引擎初始化完成 - 总耗时: 10.505294297s")
```

##### 外部规则加载
```go
// data_analysis 还会尝试从 API 加载外部规则
logger.Info("[HANDLER] 开始初始化 DataAnalysis，准备加载外部规则...")
rules, err := GetRules(conf.ApiHost)
logger.Info("[HANDLER] 成功从 API 获取到 0 条规则")
```

### 规则更新流程

#### 1. 规则文件更新步骤
1. **获取新规则**: 满献提供加密的 sutra 规则文件
2. **文件重命名**: 改文件名为 `rules.json`
3. **上传覆盖**: 上传到 211 服务器覆盖目录文件 `/home/<USER>/builds/gosutra/resources/rules.json`
4. **重新构建**: 重新构建 `data_analysis` 项目（重要！）
5. **CI 发布**: 去 main 分支打 tag，触发 CICD 自动部署

#### 2. 部署目标
- **foradar SaaS**: 镜像仓库的 develop 和正式环境都会更新
- **foradar 本地化**: 需要去 api 仓库手动点击 `all_in_one` 部署按钮

#### 3. 开发流程
```
feat/xxx 分支测试 → 提交合并请求 → 合并到 master → master 打 tag → 自动部署
```

#### 4. 重要注意事项
⚠️ **更新规则文件必须重新构建 data_analysis 项目**
- `data_analysis` 服务会从 `gosutra` 项目的 `rules.json` 文件中读取规则
- 规则在服务启动时加载到内存中
- 不重新构建服务，新规则不会生效

### 数据处理流程

#### 规则匹配流程
1. **规则加载**: `data_analysis` 从 `gosutra` 加载加密规则并解密
2. **数据接收**: 接收来自 `rawgrab` 和 `crawler` 的数据
3. **规则匹配**: 使用 Sutra 引擎对数据进行规则匹配
4. **产品识别**: 识别具体的产品、版本、组件等信息
5. **结果输出**: 将匹配结果发送给 `quick_store` 存储

## 系统架构与数据流转

### 服务架构图
```
[API Gateway] → [Dispatcher] → [Port Scanner] → [RawGrab] → [Data Analysis] → [Quick Store]
                                     ↓              ↓
                              [Check URL] ← [Crawler]
```

### 核心服务说明

#### 1. API Gateway (api)
- **功能**: 接收外部请求，提供 HTTP API 接口
- **端口**: 通常为 8080
- **职责**:
  - 接收扫描任务请求
  - 调用 Dispatcher 服务分发任务
  - 返回任务状态和结果

#### 2. Dispatcher (dispatcher)
- **功能**: 任务调度和分发中心
- **职责**:
  - 接收来自 API Gateway 的任务
  - 将任务分发给 Port Scanner
  - 管理任务状态和生命周期

#### 3. Port Scanner (portscan)
- **功能**: 端口扫描服务
- **职责**:
  - 执行端口扫描
  - 发现开放端口和服务
  - 将结果发送给 RawGrab 或 Dispatcher

#### 4. RawGrab (rawgrab)
- **功能**: 协议识别和数据抓取
- **职责**:
  - 对开放端口进行协议识别
  - 抓取服务 banner 信息
  - 发送数据给 Data Analysis 进行规则匹配
  - 对 HTTP/HTTPS 服务发送给 Check URL

#### 5. Data Analysis (data_analysis)
- **功能**: 规则引擎和产品识别
- **职责**:
  - 加载 Sutra 规则引擎（40000+ 规则）
  - 对 RawGrab 和 Crawler 的数据进行规则匹配
  - 识别具体的产品、版本、组件等
  - 将匹配结果发送给 Quick Store

#### 6. Check URL (checkurl)
- **功能**: URL 检查和预处理
- **职责**:
  - 检查 HTTP/HTTPS 服务的可访问性
  - 发送有效的 URL 给 Crawler

#### 7. Crawler (crawler)
- **功能**: 网页爬取和内容分析
- **职责**:
  - 爬取网页内容
  - 提取网页信息（标题、内容等）
  - 发送爬取结果给 Data Analysis

#### 8. Quick Store (quickstore)
- **功能**: 数据存储服务
- **职责**:
  - 接收最终的分析结果
  - 存储到 Elasticsearch
  - 提供数据查询接口

### 数据流转详细流程

#### 主要数据流
1. **API Gateway** → **Dispatcher**: 任务创建和分发
2. **Dispatcher** → **Port Scanner**: 端口扫描任务
3. **Port Scanner** → **RawGrab**: 开放端口信息
4. **RawGrab** → **Data Analysis**: 协议识别结果
5. **RawGrab** → **Check URL**: HTTP/HTTPS 服务信息
6. **Check URL** → **Crawler**: 有效的 URL
7. **Crawler** → **Data Analysis**: 网页内容信息
8. **Data Analysis** → **Quick Store**: 最终分析结果

#### 消息队列 Topic 配置
- **Port Scanner** → **RawGrab**: `net.baimaohui.srv.foscan.raw_grab`
- **RawGrab** → **Data Analysis**: `net.baimaohui.srv.foscan.data_analysis`
- **RawGrab** → **Check URL**: `net.baimaohui.srv.foscan.check_url`
- **Check URL** → **Crawler**: `net.baimaohui.srv.foscan.crawler`
- **Crawler** → **Data Analysis**: `net.baimaohui.srv.foscan.data_analysis`
- **Data Analysis** → **Quick Store**: `net.baimaohui.srv.foscan.quick_store`

#### 关键数据结构
```go
// RawGrab 事件
type RawGrabEvent struct {
    TaskInfo     *TaskInfo
    JobId        string
    Origin       string
    Ip           string
    Port         uint32
    BaseProtocol string
    BindProtocol string
}

// Data Analysis 事件
type DataAnalysisEvent struct {
    TaskInfo *TaskInfo
    JobId    string
    Origin   string
    Normal   *Normal  // 包含协议、banner、产品信息等
}

// Quick Store 事件
type QuickStoreEvent struct {
    TaskInfo *TaskInfo
    JobId    string
    Origin   string
    Data     interface{}  // Normal 或 FullSiteCrawler
}
```

### 故障排查指南

#### 1. 数据流中断排查
- 检查各服务的日志输出
- 确认消息队列连接状态
- 验证 Topic 订阅是否正确

#### 2. 常见问题

##### 数据流转问题
- **Data Analysis 收不到消息**: 检查 RawGrab 是否正常发送消息
- **分布式部署问题**: 确保所有服务实例版本一致
- **消息队列连接异常**: 检查 broker 连接状态和 topic 配置

##### 规则引擎问题
- **规则匹配失败**: 确认 Sutra 规则引擎是否正常加载
- **规则解密失败**: 检查 RSA 密钥配置和规则文件完整性
- **规则文件缺失**: 确认 `rules.json` 文件是否存在且可读
- **规则加载超时**: 40000+ 规则加载通常需要 10+ 秒

##### 加密相关问题
- **RSA 公钥设置失败**: 检查密钥文件和权限
- **规则解密错误**: 查看日志中的 "error line, skip current line" 信息
- **规则文件损坏**: 重新获取并上传 rules.json 文件

#### 3. 调试技巧

##### 日志排查
```bash
# 查看 data_analysis 规则加载日志
docker logs foscan-data_analysis-1 | grep -E "(SUTRA|规则|rule)"

# 查看 rawgrab 消息发送日志
docker logs foscan-rawgrab-1 | grep -E "(Publish|DataAnalysis)"

# 查看服务启动和连接状态
docker logs foscan-data_analysis-1 | grep -E "(Starting|Connected|Registry)"
```

##### 规则引擎排查
```bash
# 检查规则加载统计
docker logs foscan-data_analysis-1 | grep "规则数量"

# 检查解密失败的规则
docker logs foscan-data_analysis-1 | grep "error line"

# 检查 RSA 密钥状态
docker logs foscan-data_analysis-1 | grep "RSA"
```

##### 性能监控
- 规则引擎初始化时间（正常 10-15 秒）
- 内存使用情况（40000+ 规则需要较大内存）
- 消息处理延迟和吞吐量

### 打包：
- 1.所有的developer_test作业会更新211（/home/<USER>/builds/foradar或者foeye目前）｜会更新212docker images ｜会更新https://117.50.62.154/harbor/projects/12/repositories 例如：fobase/foscan/develop/portscan（开发）
- 2.所有的release作业会更新211（/home/<USER>/builds/foradar或者foeye目前）｜会更新212docker images ｜会更新https://117.50.62.154/harbor/projects/12/repositories 例如：fobase/foscan/portscan（正式）
- 3.foradar本地化需要去api项目任何一个流水线执行docker-all-in-one_develop（开发） 或者docker-all-in-one_release作业后会更新https://117.50.62.154/harbor/projects/12/repositories 例如：fobase/foscan/all-in-one_arm（正式）


### 代码提交
- 我的 data_analysis和rawgrab 里面的go.mod每次部署前需要改回去。（重点）
- 其余的我每个模块里面main.go下边的config.yaml不能提交上去

### 注意
- 如果不同分支自动打包部署到212，可能服务异常，比如api main分支。其他项目是feat分支；
- docker-all-in-one_arm nmap 未更新到最新版本7.9






```shell
tar -zcvf foscan.release.tar.gz --exclude=foscan/releases/*/*.log* --exclude=foscan/releases/*/*.out --exclude=foscan/releases/api/data/id.key --exclude=foscan/releases/api/data/license.txt --exclude=foscan/releases/api/data/id_rsa.pub --exclude=foscan/releases/api/logs --exclude=foscan/releases/*/*.rpm  --exclude=foscan/releases/*/db  foscan
```


