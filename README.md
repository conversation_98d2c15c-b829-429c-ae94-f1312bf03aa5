[![pipeline status](https://git.gobies.org/shared-platform/foscan/data_analysis/badges/main/pipeline.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/commits/main)
[![coverage report](https://git.gobies.org/shared-platform/foscan/data_analysis/badges/main/coverage.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/commits/main)
[![Latest Release](https://git.gobies.org/shared-platform/foscan/data_analysis/-/badges/release.svg)](https://git.gobies.org/shared-platform/foscan/data_analysis/-/releases)


# data_analysis

data_analysis

## Getting started



########规则更新相关内容#########
1、规则文件路径：sutra/gosutra/resources/rules.json
2、规则文件格式：msgpack
3、规则文件内容：
   每个规则是一个 msgpack 编码的字节数组，解码后是一个 map[string]interface{} 类型的对象。
   每个规则对象包含以下字段：
   - product: 产品名称
   - rule: 规则表达式
   - type: 规则类型（例如 "regex"）
   - version: 规则版本
   - description: 规则描述
4、规则文件更新：
   当规则文件有更新时，需要重新构建 data_analysis 镜像。
   可以通过以下步骤更新规则文件：
   1. 更新 rules.json 文件
   2. 重新构建镜像
   3. 重启 foscan 服务



# 如果更新规则文件的话，需要更新这个程序，打包cicd


1. unit_test 阶段
   操作：只做测试，不构建镜像
   输出：生成测试覆盖率报告 detail.html
2. developer_test 阶段
   操作：使用 .goreleaser_test.yaml 配置构建
   镜像推送到：harbor.fofa.info/fobase/foscan/develop/data_analysis
3. release 阶段
   操作：使用 .goreleaser.yaml 配置构建（仅在打 tag 时触发）
   镜像推送到：harbor.fofa.info/fobase/foscan/data_analysis
   dockers:
- image_templates:
    - harbor.fofa.info/fobase/foscan/data_analysis
      ids:
    - data_analysis
      总结
      开发环境镜像：harbor.fofa.info/fobase/foscan/develop/data_analysis (每次推送代码)
      生产环境镜像：harbor.fofa.info/fobase/foscan/data_analysis (仅打 tag 时)
      两个镜像都推送到同一个 Harbor 仓库 harbor.fofa.info，但通过路径区分开发和生产环境。