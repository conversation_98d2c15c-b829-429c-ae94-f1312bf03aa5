portscan
======
端口扫描

## 下发任务
下发任务到redis，key为`scan`, db为`conf.toml`中的配置；下发任务到kafka，topic为`scan`
消息格式示例：
``` json
{ "rate":"1000", "hosts":"0.0.0.0/0", "ports":"80,443" }
```

## 版本
### *******
* 重试3次实现修改
* 增加端口扫描发送状态5之后，不再发送2的状态
* hostinfos类型修改
* 增加IP上报端口限制功能

## 规范
### 状态信息
- 开始扫描时间
  * redis hash键：scan_task里面的begin_scan_time
- 正在扫描IP信息
  * 信息内容：正在扫描：************（IP动态变化）
  * 信息存放：Redis普通键
  * foeye：foeye_current_scan_info
  * 三防：current_scan_info
- 结束任务处理
  * 删除停止状态标记
  * 调用任务结束worker
  * 下发域名爬虫任务
- 停止扫描
  * 信息存放：Redis普通键 stop_scan_flag
  * 前端点击停止的时候写入
  * 后端读取该键值，如果为1则停止任务，并且不再下发其他任务
  * 后端写入：redis hash键：scan_task里面的state；停止中为3，停止成功为4
- 资产达到限制
  * 在资产达到限制后，会杀掉masscan，同时还会置Redis普通键 stop_scan_flag为2

## 使用
### 编辑配置文件
``` toml
[log] # 日志文件
output = "/var/log/portscan/log.log"
err_output = "/var/log/portscan/err.log"

[masscan]
dir = "/masscan" # masscan的数据目录，主要用于存暂停文件

[consumer.kafka]
brokers = [ "***********:9092" ] # 消费者的队列(接收扫描任务)若为kafka，需要kafka的broker地址

[producer.kafka]
brokers = [ "***********:9092" ] # 生产者的队列(下发识别任务)若为kafka,需要kafka的broker地址

[pprof] # 用于内存，cpu检测，调试
enable = true
port = 8888

```
### docker
#### 运行
``` sh
docker run -d --rm --name portscan \
  --restart always \
  --log-opt max-size=10m \ # 只存最近10m的标准输出
  -v $PWD/conf.toml:/etc/portscan/conf.toml \ # 挂载配置文件
  -v $PWD/log:/var/log/portscan \ # 挂载日志目录
  -v $PWD/masscan:/masscan \ # 挂载masscan数据目录
  docker.fofa.so/library/portscan
```

#### 停止
``` sh
docker stop -t 30 portscan # 多给点停止时间保证masscan优雅关闭
```

### 直接使用二进制
#### 运行
``` sh
nohup ./portscan -c conf.toml &> /dev/null &
```

### 替换masscan程序
去tools目录下，更换masscan的二进制程序（找foeye的李满献要最新的程序）


## example
[容器结合脚本](./example/shell)
