#!/bin/sh
cd /fofa/portscan/
./c stop

while true
do
  count=`ps -ef | grep portscan | grep -v "grep" | wc -l`;

  if [ "$count" -eq 1 ]; then
    echo "please wait, the process is still alive";
    sleep 5;
  else
    echo "process stop already, now restart it";
    sudo chown fofa.fofa /fofa/portscan/portscan
    ./c start
    sleep 5;
    count=`ps -ef | grep portscan | grep -v "grep" | wc -l`;

    if [ "$count" -eq 0 ]; then
      echo  -e "\033[31m alerm; please check the process \033[0m";
    else
      echo "please wait, the process is working now";
    fi
    break;
  fi
done