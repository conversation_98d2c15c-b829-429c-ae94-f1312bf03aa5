- hosts: portscan
  gather_facts: F
  tasks:
  - name: upload program
    sudo: yes
    #copy: src=/opt/grab dest=/opt/
    copy:
      src: '{{ item.src }}'
      dest: '{{ item.dest }}'
    with_items:
    - { src: 'portscan', dest: '/fofa/portscan/', mode: 755}
    - { src: 'conf.toml', dest: '/fofa/portscan/', mode: 600}
    register: upload
  - name: show upload program
    debug: var=upload.stdout verbosity=0

  - name: restart
    script: portscanupdate.sh
    register: restart
  - name: show restart
    debug: var=restart.stdout verbosity=0