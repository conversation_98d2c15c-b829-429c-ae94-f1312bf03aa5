package probe

import "testing"

func TestTLS(t *testing.T) {
	NewTcpMocker(2443).
		SetResponse([]byte("\x16\x03\x03\x00\x2a\x02\x00\x00\x26\x03\x03\xae\x5e\xc4\x88\xc4\x01\x1a\x99\x62\x87\x76\x6e\xa8\x31\x71\x4e\xf8\x49\x1c\xdc\xca\xe2\xe7\x41\x15\x5b\xf6\xf5\x6b\xad\x35\xb5\x00\x00\x35\x00\x16\x03\x03\x05\x4d\x0b\x00\x05\x49\x00\x05\x46\x00\x05\x43\x30\x82\x05\x3f\x30\x82\x04\x27\xa0\x03\x02\x01\x02\x02\x12\x03\x7e\x66\x47\xb5\x98\x37\xc4\x58\x06\x66\x43\xc2\xab\x14\xc3\x47\x95\x30\x0d\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x0b\x05\x00\x30\x32\x31\x0b\x30\x09\x06\x03\x55\x04\x06\x13\x02\x55\x53\x31\x16\x30\x14\x06\x03\x55\x04\x0a\x13\x0d\x4c\x65\x74\x27\x73\x20\x45\x6e\x63\x72\x79\x70\x74\x31\x0b\x30\x09\x06\x03\x55\x04\x03\x13\x02\x52\x33\x30\x1e\x17\x0d\x32\x32\x30\x37\x31\x39\x31\x30\x32\x32\x32\x37\x5a\x17\x0d\x32\x32\x31\x30\x31\x37\x31\x30\x32\x32\x32\x36\x5a\x30\x28\x31\x26\x30\x24\x06\x03\x55\x04\x03\x13\x1d\x36\x66\x33\x38\x30\x36\x62\x63\x63\x64\x32\x36\x2e\x73\x6e\x2e\x6d\x79\x6e\x65\x74\x6e\x61\x6d\x65\x2e\x6e\x65\x74\x30\x82\x01\x22\x30\x0d\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x01\x05\x00\x03\x82\x01\x0f\x00\x30\x82\x01\x0a\x02\x82\x01\x01\x00\xc4\x7b\x11\x17\xb5\xdb\x74\x29\xca\x1b\x0f\x24\x2c\xcd\xbe\x25\x8a\x04\x54\x92\xe9\x68\xd6\xbf\x7d\x48\x15\x4a\xc1\x89\xd4\x23\xf7\x5d\x00\x0f\xc2\x0a\x99\x23\xb1\x82\x41\x44\xe4\xc6\xaa\x68\x37\xb8\x9e\xc5\x24\xf5\x0f\x2d\x45\x6a\x6d\xf8\x7a\xa4\x1b\xb0\xb0\xa2\x63\xda\xbe\x8e\xcf\x24\x6d\x2e\x9c\x07\x6e\xd0\x1f\x2b\x24\x76\xf4\xf3\xee\xa6\xcc\x39\xd2\x74\x2a\x1c\xed\x66\x0a\x26\xaa\x9a\x81\x6d\x1c\x33\xe9\x84\xf5\x0a\x07\xbf\xf1\x71\x37\x07\x58\xcf\x32\xbf\xda\x24\xb4\x15\xe5\xda\xfe\xf1\x89\x4a\xb2\x85\xd8\xf6\xf5\xa6\x61\xb5\x25\x43\x1e\xb0\x85\x0c\x37\x88\xab\x12\x75\xf4\xed\xe0\xe1\xb8\x8a\x34\x7b\x89\x0a\x4b\xb8\x56\x71\x7d\xf4\x2c\xc8\xc0\x64\xb6\x47\xc2\x1b\x86\x0a\xfa\xb0\x0f\x1a\x58\x41\xd2\x45\xaf\x63\x1f\xaa\x09\xbf\x45\x7c\x13\x1e\xf6\xbd\x39\x05\x3b\x9a\xb9\x93\x97\x8c\x77\x94\x06\xc5\x8e\x36\x04\x13\x69\x0d\x30\x3a\x71\xdc\xab\xf5\x52\x30\x7a\xf7\x6d\x10\xf5\x63\xfb\x39\xc4\x94\x3a\xa0\xf2\xfe\xc6\xff\xb8\x2f\xc3\xcd\x01\x3d\x57\xcc\xf8\x3b\xf3\xc9\x22\xd5\x2b\xeb\xf9\x5b\x7a\xf1\xf6\x51\xc7\x02\x03\x01\x00\x01\xa3\x82\x02\x57\x30\x82\x02\x53\x30\x0e\x06\x03\x55\x1d\x0f\x01\x01\xff\x04\x04\x03\x02\x05\xa0\x30\x1d\x06\x03\x55\x1d\x25\x04\x16\x30\x14\x06\x08\x2b\x06\x01\x05\x05\x07\x03\x01\x06\x08\x2b\x06\x01\x05\x05\x07\x03\x02\x30\x0c\x06\x03\x55\x1d\x13\x01\x01\xff\x04\x02\x30\x00\x30\x1d\x06\x03\x55\x1d\x0e\x04\x16\x04\x14\x4d\x82\x37\x5a\xee\x22\x10\x9f\x21\xfa\x68\x52\x9e\xc6\xf3\xa0\xdd\x9c\x67\xa7\x30\x1f\x06\x03\x55\x1d\x23\x04\x18\x30\x16\x80\x14\x14\x2e\xb3\x17\xb7\x58\x56\xcb\xae\x50\x09\x40\xe6\x1f\xaf\x9d\x8b\x14\xc2\xc6\x30\x55\x06\x08\x2b\x06\x01\x05\x05\x07\x01\x01\x04\x49\x30\x47\x30\x21\x06\x08\x2b\x06\x01\x05\x05\x07\x30\x01\x86\x15\x68\x74\x74\x70\x3a\x2f\x2f\x72\x33\x2e\x6f\x2e\x6c\x65\x6e\x63\x72\x2e\x6f\x72\x67\x30\x22\x06\x08\x2b\x06\x01\x05\x05\x07\x30\x02\x86\x16\x68\x74\x74\x70\x3a\x2f\x2f\x72\x33\x2e\x69\x2e\x6c\x65\x6e\x63\x72\x2e\x6f\x72\x67\x2f\x30\x28\x06\x03\x55\x1d\x11\x04\x21\x30\x1f\x82\x1d\x36\x66\x33\x38\x30\x36\x62\x63\x63\x64\x32\x36\x2e\x73\x6e\x2e\x6d\x79\x6e\x65\x74\x6e\x61\x6d\x65\x2e\x6e\x65\x74\x30\x4c\x06\x03\x55\x1d\x20\x04\x45\x30\x43\x30\x08\x06\x06\x67\x81\x0c\x01\x02\x01\x30\x37\x06\x0b\x2b\x06\x01\x04\x01\x82\xdf\x13\x01\x01\x01\x30\x28\x30\x26\x06\x08\x2b\x06\x01\x05\x05\x07\x02\x01\x16\x1a\x68\x74\x74\x70\x3a\x2f\x2f\x63\x70\x73\x2e\x6c\x65\x74\x73\x65\x6e\x63\x72\x79\x70\x74\x2e\x6f\x72\x67\x30\x82\x01\x03\x06\x0a\x2b\x06\x01\x04\x01\xd6\x79\x02\x04\x02\x04\x81\xf4\x04\x81\xf1\x00\xef\x00\x76\x00\xdf\xa5\x5e\xab\x68\x82\x4f\x1f\x6c\xad\xee\xb8\x5f\x4e\x3e\x5a\xea\xcd\xa2\x12\xa4\x6a\x5e\x8e\x3b\x12\xc0\x20\x44\x5c\x2a\x73\x00\x00\x01\x82\x16\x31\xed\xd8\x00\x00\x04\x03\x00\x47\x30\x45\x02\x21\x00\xf2\x42\xc4\x52\x4c\xd6\x85\x4d\x92\xcb\x92\x60\x4b\xdf\xe1\x4a\xaf\x26\xc8\x13\x78\x89\x39\x89\x21\x78\x90\x9f\xd0\xa8\x13\x75\x02\x20\x77\xae\xfc\x9c\x35\x76\x53\xf0\xe6\xfc\xe7\x45\x35\xd2\x56\x1a\x9c\x95\x5a\x9a\xe2\xe5\x0d\x9e\x68\xda\x9f\x8e\xe4\x32\x68\xc8\x00\x75\x00\x46\xa5\x55\xeb\x75\xfa\x91\x20\x30\xb5\xa2\x89\x69\xf4\xf3\x7d\x11\x2c\x41\x74\xbe\xfd\x49\xb8\x85\xab\xf2\xfc\x70\xfe\x6d\x47\x00\x00\x01\x82\x16\x31\xee\x16\x00\x00\x04\x03\x00\x46\x30\x44\x02\x20\x0f\x3a\xc5\x4c\x61\x6b\xfa\x34\x1f\xd5\x37\xbe\xbb\xcc\xec\x5b\xf9\xba\xe4\x9c\x88\x00\xea\x45\x6d\x33\x4e\x99\x4d\x0c\xf8\xea\x02\x20\x44\xbe\x97\x5f\xc1\x81\x72\xd2\x41\x5c\x41\xf8\x0f\x0a\x0c\xd7\xbf\x67\x6f\x6d\x95\x7d\x3a\xf7\x2f\x73\x50\x59\xe9\x8e\xbc\x29\x30\x0d\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x0b\x05\x00\x03\x82\x01\x01\x00\x0e\x84\x3b\x55\xed\x3f\x15\x43\xbc\x6a\x6b\xb9\x3a\x1e\x3a\x7b\x87\xec\x57\xe4\xe7\x7c\x85\x1e\x4a\x0b\x67\x0c\xa6\x44\x6a\x8c\x73\x8b\xce\xe1\xc7\x16\x1a\x95\xd7\xd7\x09\x2c\x3e\x2e\x7b\x4d\x3a\x84\xb5\x7c\x93\x81\x59\x9e\x35\x5a\x42\x80\x83\xc7\x7a\xed\xf3\x27\xc4\x9f\x1f\x01\x8b\x30\xee\xf7\x61\x4a\xf5\xbf\x8b\x54\xe5\x79\x57\x1d\x49\x53\xd4\x34\xcc\x2b\xe1\x89\xdf\x20\xc8\xbc\x93\x10\xb3\xf1\x87\x46\x96\xf1\xef\x36\x0c\x27\xf3\x64\x04\xaa\x30\xad\xbe\xa7\xd4\xa3\x53\x1a\xd5\xae\x9b\xf5\x9b\x33\xbf\xfa\xec\x47\xf5\xa2\xcb\x69\xbb\xf9\x1e\x93\x98\xad\x64\xd3\x5b\xb2\xfd\x80\x87\x5b\xda\x41\x47\x33\xeb\x8a\x4b\xcd\x7a\xef\x21\x15\x1b\x7a\x59\xa2\x7e\x5f\x0a\x40\x6d\xb3\xc0\x96\xcd\xd4\x46\xea\xd5\x2a\x9f\x8b\xe8\xac\x89\xf8\x9a\x09\xb4\x08\x24\x1c\x85\x65\x0d\x30\x14\x16\x35\x46\x46\x87\xbb\x30\x60\x1f\x85\x3d\x3d\xe6\xd2\xa2\xc6\xc7\x90\x94\x7d\x00\x92\x85\xe9\x71\x14\xd2\x30\xf9\x2c\x7b\x88\xc8\x78\x0f\x30\x0e\xa0\xea\x2e\xbf\x65\x89\x49\x8f\x1c\xed\xf7\xa1\xa4\x73\x69\x54\x7b\xb3\x88\x34\x6b\x87\x3d\x7c\x16\x03\x03\x00\x04\x0e\x00\x00\x00")).
		SetProtocol("tls").
		Run(TLS).
		Test(t)
}
