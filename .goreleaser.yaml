# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/gateway/
    id: api
    flags:
      - -tags=foradar
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
    ldflags:
      - -X 'api/internal.LicenseVerifySwitch=false'
      - -X 'api/internal.Version={{ .Tag }}'
      - -X 'api/internal.BuildAt={{ .Date }}'
      - -X 'api/internal.Private={{ .Env.PRIVATE_FOEYE_LICENSE_KEY }}'
      - -X 'api/internal.SHA265={{ .Env.PRIVATE_FOEYE_LICENSE_SHA265 }}'
    goos:
      - linux
    goarch:
      - amd64
      - arm64
nfpms:
  - id: api
    maintainer: foscan
    license: MIT
    description: |-
        Foscan's api service.
    formats:
      - rpm
    bindir: ./foscan/api/
    contents:
      - src: script/systemd/foscan_api.service
        dst: /usr/lib/systemd/system/foscan_api.service

      - src: script/logrotate.d/foscan
        dst: /etc/logrotate.d/foscan

      - dst: /foscan/api/data
        type: dir

      - src: config.yaml
        dst: /foscan/api/config.yaml
        type: "config|noreplace"
  - rpm:
      group: Unspecified
      summary: Foscan Api Package
      compression: gzip

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/api
    ids:
      - api
    extra_files:
      - config.yaml
      - script/logrotate.d/foscan
archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    files:
      - src: config.yaml
        dst: config.yaml

checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'

gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org

# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
