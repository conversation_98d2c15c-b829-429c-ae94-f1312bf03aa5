# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: rawgrab
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp trusted_cert_list.txt {{ dir .Path}}/trusted_cert_list.txt
    goos:
      - linux
    goarch:
      - amd64
      - arm64
nfpms:
  - id: rawgrab
    maintainer: foscan
    license: MIT
    description: |-
        Foscan's rawgrab service.
    formats:
      - rpm
    bindir: ./foscan/rawgrab/
    contents:
      - src: script/systemd/foscan_rawgrab.service
        dst: /usr/lib/systemd/system/foscan_rawgrab.service

      - src: trusted_cert_list.txt
        dst: /foscan/rawgrab/trusted_cert_list.txt

      - src: script/logrotate.d/foscan
        dst: /etc/logrotate.d/foscan

      - src: config.yaml
        dst: /foscan/rawgrab/config.yaml
        type: "config|noreplace"
  - rpm:
      group: Unspecified
      summary: Foscan rawgrab Package
      compression: gzip

release:
  draft: true

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/rawgrab
    ids:
      - rawgrab
    extra_files:
      - config.yaml
      - trusted_cert_list.txt
      - script/logrotate.d/foscan
archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    files:
      - src: config.yaml
        dst: config.yaml

checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'

gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org

# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
