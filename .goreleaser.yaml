# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...

builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: quickstore_amd
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - \cp -rf {{ dir .Path}}/quickstore  /home/<USER>/builds/foradar/docker-all-in-one/foscan/store/
        - \cp -rf {{ dir .Path}}/quickstore  /home/<USER>/builds/foeye/foscan/releases/store/
    goos:
      - linux
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: quickstore_arm
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - \cp -rf {{ dir .Path}}/quickstore  /home/<USER>/builds/foradar/docker-all-in-one/foscan_arm/store/
    goos:
      - linux
    goarch:
      - arm64

dockers:
  - image_templates:
      - "harbor.fofa.info/fobase/foscan/develop/quickstore:latest"
    ids:
      - quickstore_amd
    extra_files:
      - config.yaml
      - script/logrotate.d/foscan

archives:
  - format: tar.gz
    # name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- .Version }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    files:
      - src: config.yaml
        dst: config.yaml

changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'

gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org
