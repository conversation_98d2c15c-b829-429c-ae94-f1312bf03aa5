# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
builds:
  - env:
      - CGO_ENABLED=0
    dir: cmd/grpc/
    id: data_analysis
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
        - cp GeoLite2-City.mmdb {{ dir .Path}}/GeoLite2-City.mmdb
        - cp GeoLite2-ASN.mmdb {{ dir .Path}}/GeoLite2-ASN.mmdb
    goos:
      - linux
    goarch:
      - amd64
      - arm64
nfpms:
  - id: data_analysis
    maintainer: foscan
    license: MIT
    description: |-
        Foscan's data_analysis service.
    formats:
      - rpm
    bindir: ./foscan/data_analysis/
    contents:
      - src: script/systemd/foscan_data_analysis.service
        dst: /usr/lib/systemd/system/foscan_data_analysis.service

      - src: script/logrotate.d/foscan
        dst: /etc/logrotate.d/foscan

      - src: GeoLite2-City.mmdb
        dst: /foscan/data_analysis/GeoLite2-City.mmdb

      - src: GeoLite2-ASN.mmdb
        dst: /foscan/data_analysis/GeoLite2-ASN.mmdb

      - src: config.yaml
        dst: /foscan/data_analysis/config.yaml
        type: "config|noreplace"
  - rpm:
      group: Unspecified
      summary: Foscan data_analysis Package
      compression: gzip

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/data_analysis
    ids:
      - data_analysis
    extra_files:
      - config.yaml
      - GeoLite2-City.mmdb
      - GeoLite2-ASN.mmdb
      - script/logrotate.d/foscan
archives:
  - format: tar.gz
    # name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- .Version }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    files:
      - src: config.yaml
        dst: config.yaml

checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'

gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org

# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
