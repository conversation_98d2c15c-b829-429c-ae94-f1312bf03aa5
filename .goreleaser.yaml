# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod tidy
    # you may remove this if you don't need go generate
    - go generate ./...
builds:
  - env:
      - CGO_ENABLED=1
    id: dispatcher
    ldflags:
      - -X 'main.Version={{ .Tag }}'
      - -X 'main.BuildAt={{ .Date }}'
    hooks:
      post:
        - cp config.yaml {{ dir .Path}}/config.yaml
    goos:
      - linux
    goarch:
      - amd64

#  - env:
#      - CGO_ENABLED=0
#    id: dispatcher_license
#    binary: dispatcher_license
#    ldflags:
#      - -X 'main.Mode=foeye'
#    hooks:
#      post:
#        - cp config.yaml {{ dir .Path}}/config.yaml
#    goos:
#      - linux
#    goarch:
#      - amd64
#      - arm64

nfpms:
  - id: dispatcher
    maintainer: foscan
    license: MIT
    description: |-
        Foscan's dispatcher service.
    formats:
      - rpm
    bindir: ./foscan/dispatcher/
    contents:
      - src: script/systemd/foscan_dispatcher.service
        dst: /usr/lib/systemd/system/foscan_dispatcher.service

      - src: script/logrotate.d/foscan
        dst: /etc/logrotate.d/foscan

      - src: config.yaml
        dst: /foscan/dispatcher/config.yaml
        type: "config|noreplace"
  - rpm:
      group: Unspecified
      summary: Foscan dispatcher Package
      compression: gzip

dockers:
  - image_templates:
      - harbor.fofa.info/fobase/foscan/dispatcher
    ids:
      - dispatcher
    extra_files:
      - config.yaml
      - script/logrotate.d/foscan
#  - image_templates:
#      - harbor.fofa.info/fobase/foscan/dispatcher_license
#    ids:
#      - dispatcher_license
#    extra_files:
#      - config.yaml
#      - script/logrotate.d/foscan
archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- .Version }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    files:
      - src: config.yaml
        dst: config.yaml

checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'

gitlab_urls:
  api: https://git.gobies.org/api/v4/
  download: https://git.gobies.org

# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
