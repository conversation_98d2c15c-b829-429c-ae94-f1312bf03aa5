[HTTP]
HttpHoneypotStr = [
    ["glastopf","b_<h2>Blog Comments</h2>","b_Please post your comments for the blog"],
    ["web","b_CouchDB/","h_www.drupal.org"],
    ["web","h_couchdb","h_camera"],
    ["web","h_Server: DVRDVS-Webs","h_Drupal "],
    ["web","h_webvpnLang","h_Drupal "],
    ["web","h_Bitrix Site Manager","h_Drupal"],
    ["web","h_Camera ","h_PMS","h_MacOSX"],
    ["web","h_Apache,Tomcat,Jboss"],
    ["web","b_Server: A10WS","b_Server: cisco-IOS"],
    ["web","b_<title>GPON Home Gateway</title>","b_<title>NETSurveillance WEB</title>"],
    ["web","b_action=\"\"","b_VPN系统已经调试完成"],
    ["web","b_action=\"\"","b_src=\"/static/x.js\""],
    ["HFish","b_new WebAssembly['Module']","b_static/x.js\"></script>"],
    ["HFish", "b_<DT><A HREF=\"https://hfish.io/#/"],
    ["amun", "b_<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\"><html><head><title>It works!</title></head><html><body><h1>It works!</h1><br><EMAIL><br><EMAIL></body></html>"],
    ["opencanary", "b_<div id=app></div><script type=text/javascript src=/static/js/manifest.7b96bedea8b11a528f3f.js></script>"],
    ["weblogic", "h_Oracle WebLogic Server on JRockit Virtual Edition Module Dependencies"],
    ["戍将", "b_登录戍将攻击诱捕平台"],
    ["Struts", "b_Powered by <img src=\"struts.svg\" style=\"height: 3em;\"/>", "b_Powered by <img src=\"apache.png\" style=\"height: 3em;\"/><br/>"],
    ["Conpot", "b_Overview - Siemens, SIMATIC, S7-200", "b_<h2>Technodrome</h2>", "b_<td style=\"width:150px;\"><b>System uptime:</b></td>"],
    ["web", "b_AMPPS Admin Login Panel", "b_Fortinet FortiMail Login Panel"],
]
HttpHoneyCount = [
    ["web","b_Server: ","b_<title>","10"],
    ["web","b_Server: ","100"]
]

[Elastic]
ESHoneyCount = [["web","Server: ","<title>","10"]]

[UtilProtocol]
UtilHoneypotStr = [
    ["glastopf","<h2>Blog Comments</h2>","Please post your comments for the blog"],
    ["web","CouchDB/","www.drupal.org"],
    ["web","couchdb","camera"],
    ["web","Server: DVRDVS-Webs","Drupal "],
    ["web","webvpnLang","Drupal "],
    ["web","Bitrix Site Manager","Drupal"],
    ["web","Camera ","PMS","MacOSX"],
    ["web","Apache,Tomcat,Jboss"],
    ["web","Server: A10WS","Server: cisco-IOS"],
    ["web","<title>GPON Home Gateway</title>","<title>NETSurveillance WEB</title>"],
    ["web","action=\"\"","VPN系统已经调试完成"],
    ["web","action=\"\"","src=\"/static/x.js\""],
    ["HFish","new WebAssembly['Module']","static/x.js\"></script>"],
    ["HFish", "<DT><A HREF=\"https://hfish.io/#/"],
    ["amun", "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\"><html><head><title>It works!</title></head><html><body><h1>It works!</h1><br><EMAIL><br><EMAIL></body></html>"],
    ["opencanary", "<div id=app></div><script type=text/javascript src=/static/js/manifest.7b96bedea8b11a528f3f.js></script>"],
    ["weblogic", "Oracle WebLogic Server on JRockit Virtual Edition Module Dependencies"],
    ["戍将", "登录戍将攻击诱捕平台"],
    ["Struts", "Powered by <img src=\"struts.svg\" style=\"height: 3em;\"/>", "Powered by <img src=\"apache.png\" style=\"height: 3em;\"/><br/>"],
    ["Conpot", "Overview - Siemens, SIMATIC, S7-200", "<h2>Technodrome</h2>", "<td style=\"width:150px;\"><b>System uptime:</b></td>"],
    ["web", "AMPPS Admin Login Panel", "Fortinet FortiMail Login Panel"],
]
UtilHoneyCount = [
    ["web","Server: ","<title>","10"],
    ["web","Server: ","100"]
]

[WebHoneypot]
[WebHoneypot.wemo]
[WebHoneypot.elastic]
[WebHoneypot.http]
[WebHoneypot.hadoop-ipc]
[WebHoneypot.iota-rpc]
[WebHoneypot."hbase_master(http)"]
[WebHoneypot."hbase_region_server(http)"]
[WebHoneypot.monero]
[WebHoneypot.etcd]