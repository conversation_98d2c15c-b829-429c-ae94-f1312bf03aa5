@startuml
'https://plantuml.com/activity-diagram-beta


start
note
{
    "bandwidth":"100",
    "blacklist":"cidr1,cidr2,cidrn",
    "crawler_all_url":false,
    "crawler_specific_url":"url1|ur2|urln",
    "crawler_url_black_key":"",
    "deep_get_mac":false,
    "deep_get_os":false,
    "gateway_mac":"false",
    "grab_concurrent":10,

    "ip_list":"cidr1,cidr2"",
    "ip_list_filename":"",
    "is_ipv6":false,
    "max_asset_num":0,
    "ping_scan":false,
    "ports":"p1,p2",
    "port_group":[
        {
            "ports": "p1,p2,pn",
            "ip_list": ["cidr1","cidr2","cidrn"],
        }
    ],
    "protocol_update_cycle":0,
    "repeat_times":0,
    "resume_filename":"",
    "send_eth":"",
    "task_id":"",
    "task_type":"common|quick",
    "treck_scan":false,
    "unknown_protocol_in_db":false
}

end note
if (ParamValidatePass) then (false)
stop
endif

if (HasDomain) then (true)
  :DomainParse();
  if (ParsedIPType==ScanIPType) then (yes)
    if (ParsedIPInIPBlacklist) then (no)
        if (IPFilterStrategyPass) then (true)
          :RecordIPDomainRelations;
          note
           Map {
              "ip1": ["domain1", "domain2"],
              "ip2": ["domain2", "domain3"]
           }
          end note
          :AddParsedIPToTaskInfo.IpList;
        endif
    endif
  endif
endif
:SendIPDomainRelationsToCheckURL;
:SendToDispatcher;
stop

@enduml
