package doc

import (
	"crypto/tls"
	"fmt"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"net/http"
	"testing"
)

func TestRequest(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, "http://************:80", nil)
	assert.NoError(t, err)

	c := http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	response, err := c.<PERSON>(req)
	assert.NoError(t, err)
	defer response.Body.Close()
	b, err := ioutil.ReadAll(response.Body)
	assert.NoError(t, err)
	fmt.Println("body:", string(b))
}
