@startuml
'https://plantuml.com/activity-diagram-beta

start
note
IN {
        TaskInfo task_info = 1;
        string job_id = 2;
        string ip = 3;
        uint32 port = 4;
        string protocol = 5;
        string base_protocol = 6;
    }
end note
if (ParamValidatePass) then (false)
stop
endif
:URLTargetWrap;
note
OUT:
    http
        80 -> http://ip
        other -> http://ip:port
    https
        443 -> https://ip
        other -> https://ip:port
end note
:GetRequestTargetURLHeaderHost;
if (TargetIPInIPDomainRelations) then (true)
   note
    IPDomainRelations {
         "ip1": ["domain1", "domain2"],
         "ip2": ["domain2", "domain3"]
    }
   end note

  :SetHost;
  :SetDomain;
  :SetSubDomain;
endif
  :SendToCrawler;
  note
    IN {
        TaskInfo task_info = 1;
        string ip = 1;
        uint32 port = 2;
        string protocol = 3;
        string base_protocol = 4;
        string host = 5;
        string domain = 6;
        string subdomain = 7;
        string url = 8;
    }
  end note
stop

@enduml
