@startuml
'https://plantuml.com/activity-diagram-beta

start
note
versionExtractRule = {
  "rule_id":{
    "reg_string": "BarracudaHTTP ([\\d\\.]+)",
    "index": "[1]",
    "is_regexp": true,
  }
}
end note
:ruleTags = sutra.Match(asset.attribute);
if (len(ruleTags) > 0) then (true)
  : for _,ruleTag := range ruleTags;
  if (ruleTagHaveVersionExtractRule) then (yes)
    :DoVersionExtract;
    :VersionFormattingProcess;
    if (formattedVersionNotEmpty) then (yes)
      :writeVersionToRuleTags;
    endif
endif
endif
stop

@enduml




