@startuml
'https://plantuml.com/activity-diagram-beta

start
note
IN {
    "task_info": {
    string task_id = 1;
    bool is_ipv6 = 2; // 是否是ipv6扫描
    int32 task_type = 3; // 任务类型
    bool unknown_protocol_in_db = 7; // 未知协议是否入库
    bool is_crawler_all_url = 8; // 是否开启全站爬虫（调用goby爬虫）
    string crawler_specific_url = 9; // 爬取指定URL
    string crawler_url_black_key = 10; // URL黑名单
},
    string job_id = 2;
    string origin = 3;
    string ip = 4;
    uint32 port = 5;
    string base_protocol = 6;
}
end note
if (ParamValidatePass) then (false)
stop
endif

:GrabManager.Grab(ip, port, basePro, host);
if (grabResultStatusIsFalse && customGrabSwitchIsTrue) then (true)
  :run customGrab;
endif

if (grabResultProtocolIsUnknown && task_info.unknown_protocol_in_db==false) then (true)
stop
endif

:RemoveBracketsInIP;

if (grabResultProtocolIsHTTPOrHTTPS) then (true)
    :SendToDispatcherCheckURL;
    note
    IN {
        TaskInfo task_info = 1;
        string job_id = 2;
        string ip = 3;
        uint32 port = 4;
        string protocol = 5;
        string base_protocol = 6;
    }
    end note
endif
:EscapeHTML;
:SendToDataAnalysis;
note
IN {
        TaskInfo task_info = 1;
        string ip = 1;
        uint32 port = 2;
        string protocol = 3;
        string base_protocol = 4;
        bool is_ipv6 = 5;
        string banner = 6;
        uint32 banner_len = 7;
        string timestamp = 8;
        // website's string cert
        optional string cert = 9;
        // the ip's hostname array
        repeated string hostnames = 10;
        optional CertObject certs = 11;
        repeated string appserver = 12;
        // website dom object
        optional google.protobuf.Any dom = 13;
        optional string domain = 14;
        // website icon object
        optional google.protobuf.Any favicon = 15;
        // website's dom fid by EHash calculate
        optional string fid = 16;
        optional string header = 17;
        optional string host = 18;
        optional string body = 19;
        optional string title = 20;
        repeated string version = 21;
        // 是否是垃圾网站
        optional bool is_fraud = 22;
        // 垃圾网站名称
        optional string fraud_name = 23;
        // 是否是蜜罐
        optional bool is_honeypot = 24;
        //蜜罐名称
        optional string honeypot_name = 25;
        optional string server = 26;
        // 子域名
        optional string subdomain = 27;
        // 是否是域名
        optional bool is_domain = 28;
        // 网站编码
        optional string charset = 29;

        // response http code
        uint32 status_code = 30;
        string utf8html = 31;
        string sub_body = 32;
        // 开发语言
        repeated string language = 33;
        // 中间件
        repeated string middleware = 34;
        // 操作系统
        repeated string os = 35;
        // 硬件产品型号
        repeated string modal = 36;
        string location_url = 37;
        string ipcnet = 38;
        repeated string product = 39;
        repeated RuleTag rule_tags = 40;
        string mac = 41;
        string netbios_name = 42;
    }
end note
stop

@enduml
