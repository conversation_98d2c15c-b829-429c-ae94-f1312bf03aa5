@startuml
'https://plantuml.com/activity-diagram-beta

start
note
 IN {
        TaskInfo task_info = 1;
        string ip = 1;
        uint32 port = 2;
        string protocol = 3;
        string base_protocol = 4;
        string host = 5;
        string domain = 6;
        string subdomain = 7;
        string url = 8;
    }
end note
if (ParamValidatePass) then (false)
stop
endif
:SetRequestInsecureSkipVerify;
:SetRequestTimeout;

if (task_info.crawler_specific_url contain 'delete') then (true)
stop
endif

:RequestTargetURLWithLocation;
if (RequestTargetURLWithLocationSuccess) then (true)
  :GetTitle;
  :GetUtf8html;
  :GetCharset;
  :GetBodyLen;
  :GetPort;
  :GetLocUrl;
  :GetHeader;
  :GetHeaderLen;
  :GetStatusCode;
  :GetServer;
  :GetServerLen;
  :GetIsFraud;
  :GetFraudName;
  :GetIsHoneypot;
  :GetHoneypotName;
  :GetBodyICP;
  :GetCert;
  :GetCerts;
  :GetIcon;
  note
  favicon {
    "url":       "",
    "phash":     "",
    "hash":      "",
    "phash_bit": "",
    "p":         {},
    "base64":    ""
  }
  end note
  :GetDom;
  note
    dom {
      "ehash": "",
      "hash": "",
      "nhash": "",
      "p": {},
      "shash_bit": "",
      "sim_hash": "",
      "tag_count": "",
      "tag_len": ""
    }
    end note
endif
:SendToDataAnalysis;
note
IN {
        TaskInfo task_info = 1;
        string ip = 1;
        uint32 port = 2;
        string protocol = 3;
        string base_protocol = 4;
        bool is_ipv6 = 5;
        string banner = 6;
        uint32 banner_len = 7;
        string timestamp = 8;
        // website's string cert
        optional string cert = 9;
        // the ip's hostname array
        repeated string hostnames = 10;
        optional CertObject certs = 11;
        repeated string appserver = 12;
        // website dom object
        optional google.protobuf.Any dom = 13;
        optional string domain = 14;
        // website icon object
        optional google.protobuf.Any favicon = 15;
        // website's dom fid by EHash calculate
        optional string fid = 16;
        optional string header = 17;
        optional string host = 18;
        optional string body = 19;
        optional string title = 20;
        repeated string version = 21;
        // 是否是垃圾网站
        optional bool is_fraud = 22;
        // 垃圾网站名称
        optional string fraud_name = 23;
        // 是否是蜜罐
        optional bool is_honeypot = 24;
        //蜜罐名称
        optional string honeypot_name = 25;
        optional string server = 26;
        // 子域名
        optional string subdomain = 27;
        // 是否是域名
        optional bool is_domain = 28;
        // 网站编码
        optional string charset = 29;

        // response http code
        uint32 status_code = 30;
        string utf8html = 31;
        string sub_body = 32;
        // 开发语言
        repeated string language = 33;
        // 中间件
        repeated string middleware = 34;
        // 操作系统
        repeated string os = 35;
        // 硬件产品型号
        repeated string modal = 36;
        string location_url = 37;
        string ipcnet = 38;
        repeated string product = 39;
        repeated RuleTag rule_tags = 40;
        string mac = 41;
        string netbios_name = 42;
    }
end note
stop
@enduml
