[supervisord]
nodaemon=true

[group:foscan]
programs=api,dispatcher,portscan,rawgrab,checkurl,crawler,data_analysis,quickstore

[program:api]
user=root
directory=/foscan/api
command=/foscan/api/api
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/api/std.log
stderr_logfile=/foscan/api/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:dispatcher]
user=root
directory=/foscan/dispatcher
command=/foscan/dispatcher/dispatcher
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/dispatcher/std.log
stderr_logfile=/foscan/dispatcher/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:portscan]
user=root
directory=/foscan/portscan
command=/foscan/portscan/portscan
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/portscan/std.log
stderr_logfile=/foscan/portscan/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:rawgrab]
user=root
directory=/foscan/rawgrab
command=/foscan/rawgrab/rawgrab
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/rawgrab/std.log
stderr_logfile=/foscan/rawgrab/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true


[program:checkurl]
user=root
directory=/foscan/checkurl
command=/foscan/checkurl/checkurl
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/checkurl/std.log
stderr_logfile=/foscan/checkurl/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:crawler]
user=root
directory=/foscan/crawler
command=/foscan/crawler/crawler
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/crawler/std.log
stderr_logfile=/foscan/crawler/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:data_analysis]
user=root
directory=/foscan/data_analysis
command=/foscan/data_analysis/data_analysis
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/data_analysis/std.log
stderr_logfile=/foscan/data_analysis/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true

[program:quickstore]
user=root
directory=/foscan/store
command=/foscan/store/quickstore
autostart=true
autorestart=true
startsecs=10
stdout_logfile=/foscan/store/std.log
stderr_logfile=/foscan/store/std.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile_backups=0
stderr_logfile_backups=0
stopsignal=QUIT
stopasgroup=true
