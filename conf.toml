[log]
output = "stdout"
err_output = "stderr"

[redis]
addr = "127.0.0.1:6379"
db = 0
prefix = "mk:"
#password = ""
black_count = 1000
stop_task_key = "stop_scan_flag"
exec_ip_key = "current_scan_info"
task_progress_key = "portscan_scan_task"

[limit_redis]
enable = false # 是否启用限制功能
addr = "127.0.0.1:6379"
db = 15
#password = ""
prefix = "limit:"

[consumer.sidekiq]
addr = "127.0.0.1:6379"
password = ""
db = 0
#db = 1
#portscan_queue = "go_scan_service_worker"
portscan_queue = "scan_service_worker"
grab_queue = "grab_worker"
ping_queue = "assets_insert"
checkurl_queue = "check_url"
foeye = false

#[consumer.redis]
#addr = ""
#password = ""
#db = 0

#[consumer.kafka]
#brokers = ["***********:9092"]

#[producer.redis]
#addr = ""
#password = ""
#db = 0

#[producer.kafka]
#brokers = [ "127.0.0.1:9092" ]
#topic = "grab"
#ping_topic = "data_analysis" # ping存活扫描的topic

[masscan]
dir = "./tools/"
source_port = 58914
blackip_list = ""

[nmap]
dir = "./tools/"
source_port = 58924
blackip_list = ""

[worker]
batch_size = 10
same_ip_max_count = 100 # 同IP最大接收端口数，-1表示没限制
send_eth = "" # 发送网卡名称
reserve_inner_ip = true # 是否保留内网IP

[pprof]
enable = false
port = 8888

[rpc]
enable = false
addr = "127.0.0.1:65432"

