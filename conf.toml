[worker]
num = 1
add_link_host = false
disable_host_checked = false
alexa_domain_path = "./top-1m.csv"
alexa_num = 5

## 支持sidekiq的配置，仅限支持foeye
#[sidekiq]
#addr = "************:6379"
#password = ""
#db = 5
#checkurl_queue = "check_url"
#processurl_queue = "process_url"
#foeye = true

[consumer.kafka]
brokers = ["127.0.0.1:9092"]
partition = 0
topic = "checkurl"

[producer.crawler.kafka]
brokers = ["127.0.0.1:9092"]
topic = "crawler"

[elasticsearch]
url = "http://127.0.0.1:9200"
# index = "fofapro"
index_subdomain = "fofapro_subdomain"
type_subdomain = "subdomain"
es_version = 6
disable_records_info = false # 是否禁用记录信息

[redis]
addr = "127.0.0.1:6379"
db = 5
prefix = "fofa:"
checked_prefix = "f:" # checked host的前缀
checked_key_expire = "720h" # checked host的自动超时时间（默认30天，不能写30d）
#password = ""

[log]
output = "stdout"#"/var/log/checkurl/log.log"
err_output = "stdout"


[pprof]
enable = false
port = 8888
