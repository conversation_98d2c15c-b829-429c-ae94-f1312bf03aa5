# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with 'go test -c'
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# don't commit the service binary to vcs
dispatcher

.idea
dist/
detail.html
*.log
*.db
Dockerfile_all_in_one
Dockerfile_all-in-one_arm
*.png
.DS_Store
