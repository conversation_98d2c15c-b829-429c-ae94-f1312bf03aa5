package flow

import (
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/queue"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"log"
	"testing"
)

func consumerInitRedisMocks() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestNewConsumer(t *testing.T) {
	var consumerCfg config.ConsumerConfig
	var sidekiqCfg config.SidekiqConfig
	//var rdsCfg config.Redis
	//var kafkaCfg config.KafkaConsumer
	var consumer queue.SimpleConsumer

	handleError(nil)

	// invalid
	//rdsCfg.Addr = "127.0.0.1:16379"
	//rdsCfg.Db = 10
	//consumerCfg.Sidekiq = nil
	sidekiqCfg.Addr = "127.0.0.1:16379"
	sidekiqCfg.Db = 10
	sidekiqCfg.Hostname = "TestHostname"
	consumerCfg.Sidekiq = &sidekiqCfg
	consumerCfg.Redis = nil
	consumerCfg.Kafka = nil
	consumer = NewConsumer(consumerCfg)
	assert.NotNil(t, consumer)

	//kafkaCfg.Brokers = []string{"127.0.0.1:19092"}
	//consumerCfg.Sidekiq = nil
	//consumerCfg.Redis = nil
	//consumerCfg.Kafka = &kafkaCfg
	//consumer = NewConsumer(consumerCfg)
	//assert.Nil(t, consumer)

	mockRds := consumerInitRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	// valid
	sidekiqCfg.Addr = mockRds.Addr()
	sidekiqCfg.Db = 10
	sidekiqCfg.Hostname = "TestHostname"
	consumerCfg.Sidekiq = &sidekiqCfg
	consumerCfg.Redis = nil
	consumerCfg.Kafka = nil
	consumer = NewConsumer(consumerCfg)
	assert.NotNil(t, consumer)

	//rdsCfg.Addr = mockRds.Addr()
	//rdsCfg.Db = 10
	//consumerCfg.Sidekiq = nil
	//consumerCfg.Redis = &rdsCfg
	//consumerCfg.Kafka = nil
	//consumer = NewConsumer(consumerCfg)
	//assert.NotNil(t, consumer)

	//kafkaCfg.Brokers = []string{"***********:9092"}
	//consumerCfg.Sidekiq = nil
	//consumerCfg.Redis = nil
	//consumerCfg.Kafka = &kafkaCfg
	//consumer = NewConsumer(consumerCfg)
	//assert.NotNil(t, consumer)

	//NewConsumer(consumerCfg)
	//NewSidekiqConsumer(nil)
	//NewKafkaConsumer(nil)
}
