package flow

import (
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/sidekiq"
	"log"
)

func handleError(err error) {
	log.Println("+++ERROR:", err)
}

func NewConsumer(conf config.ConsumerConfig) queue.SimpleConsumer {
	if conf.Sidekiq != nil {
		return NewSidekiqConsumer(conf.Sidekiq)
	}
	//if conf.Kafka != nil {
	//	return NewKafkaConsumer(conf.Kafka)
	//}
	log.Fatal("please give a consumer")
	return nil
}

func NewSidekiqConsumer(conf *config.SidekiqConfig) queue.SimpleConsumer {
	if conf == nil {
		log.Fatal("sidekiq config is nil")
		return nil
	}

	sidekiq.Init(conf.Addr, conf.Password, conf.Db, conf.Hostname)
	return sidekiq.NewConsumer(conf.PortScanQueue)
}

//func NewKafkaConsumer(conf *config.KafkaConsumer) queue.SimpleConsumer {
//	if conf == nil {
//		log.Fatal("redis config is nil")
//		return nil
//	}
//
//	sc, err := kafkaq.NewCommonSimpleConsumer("portscan", []string{"scan"}, conf.Brokers, true, handleError)
//	if err != nil {
//		log.Fatal(err)
//	}
//	return sc
//}
