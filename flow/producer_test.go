package flow

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewProducer(t *testing.T) {
	prod := NewKafkaProducer(nil)
	assert.Nil(t, prod)

	//var cfg config.ProducerConfig

	//NewProducer(cfg)
	//NewKafkaProducer(nil)

	//mockRds := consumerInitRedisMocks()
	//log.Println("  mock redis addr: ", mockRds.Addr())
	//
	//var redisCfg config.Redis
	//redisCfg.Addr = mockRds.Addr()
	//cfg.Redis = &redisCfg
	//cfg.Kafka = nil
	//prodRedis := NewProducer(cfg)
	//assert.Nil(t, prodRedis)
	//
	//var kafkaCfg config.KafkaProducer
	//kafkaCfg.Brokers = []string{"127.0.0.1:19092"}
	//cfg.Redis = nil
	//cfg.Kafka = &kafkaCfg
	//prodKafka := NewProducer(cfg)
	//assert.Nil(t, prodKafka)
	//
	//redisCfg.Addr = "10.10.10.199:6379"
	//cfg.Redis = &redisCfg
	//cfg.Kafka = nil
	//prodRedisDup := NewProducer(cfg)
	//assert.NotNil(t, prodRedisDup)
	//
	//kafkaCfg.Brokers = []string{"10.10.10.82:9092"}
	//cfg.Redis = nil
	//cfg.Kafka = &kafkaCfg
	//prodKafkaDup := NewProducer(cfg)
	//assert.NotNil(t, prodKafkaDup)
}
