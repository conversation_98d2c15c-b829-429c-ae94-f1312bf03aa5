package main

import (
	"checkurl/internal/mqqueue"
	"checkurl/internal/mqqueue/kafkamq"
	"log"
)

func NewCrawlerProducer(conf mqqueue.SimplProducerConfig) mqqueue.SimpleProducer {
	if conf.Kafka != nil {
		topics := conf.Kafka.Topic
		log.Println("connect to crawler producer topic:", topics, "addr:", conf.Kafka.Brokers)
		sp, err := kafkamq.NewCommonSimpleProducer(topics, conf.Kafka.Brokers, true, handleError)
		if err != nil {
			log.Fatal(err)
		}
		return sp
	}
	log.Fatal("please enable a crawler producer")
	return nil
}
