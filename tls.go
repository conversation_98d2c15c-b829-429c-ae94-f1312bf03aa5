package probe

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/binary"
)

func TLS(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "tls"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	b := TLSDo(p)
	certs := parseTLSCertificate(b)
	if certs != nil {
		info.Success = true
		info.ConnectionState = &tls.ConnectionState{
			PeerCertificates: certs,
		}
	}

	return
}

func TLSDo(p *probe) []byte {
	// send TLS 1.2 Client Hello. (packet from `sslscan --tls10 x.x.x.x:443`, and change the `version` part)
	p.MustWriteTCP([]byte{
		0x16,       // Content Type: Handshake
		0x03, 0x01, // TLS 1.0
		0x03, 0x53, // Length: 851
		// Handshake Protocol:
		0x01,             // Handshake Type: Client Hello
		0x00, 0x03, 0x4f, // Length
		0x03, 0x03, // Version: TLS 1.2。  从1.0改成用1.2版本，这个版本常用些
		// Random :
		0x61, 0xdd, 0x54, 0xed, 0x77, 0x88, 0x77, 0x8b,
		0x8e, 0x23, 0x9a, 0x29, 0xa2, 0xde, 0xbc, 0xc7,
		0xb7, 0x89, 0xdf, 0x65, 0xd1, 0x34, 0x02, 0x04,
		0xec, 0xdf, 0x24, 0xa2, 0x04, 0x8b, 0x21, 0x40,

		0x00, // Session ID Length

		// Cipher Suites:
		0x02, 0xb0, // Length
		0x00, 0x00, 0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04,
		0x00, 0x05, 0x00, 0x06, 0x00, 0x07, 0x00, 0x08,
		0x00, 0x09, 0x00, 0x0a, 0x00, 0x0b, 0x00, 0x0c,
		0x00, 0x0d, 0x00, 0x0e, 0x00, 0x0f, 0x00, 0x10,
		0x00, 0x11, 0x00, 0x12, 0x00, 0x13, 0x00, 0x14,
		0x00, 0x15, 0x00, 0x16, 0x00, 0x17, 0x00, 0x18,
		0x00, 0x19, 0x00, 0x1a, 0x00, 0x1b, 0x00, 0x1e,
		0x00, 0x1f, 0x00, 0x20, 0x00, 0x21, 0x00, 0x22,
		0x00, 0x23, 0x00, 0x24, 0x00, 0x25, 0x00, 0x26,
		0x00, 0x27, 0x00, 0x28, 0x00, 0x29, 0x00, 0x2a,
		0x00, 0x2b, 0x00, 0x2c, 0x00, 0x2d, 0x00, 0x2e,
		0x00, 0x2f, 0x00, 0x30, 0x00, 0x31, 0x00, 0x32,
		0x00, 0x33, 0x00, 0x34, 0x00, 0x35, 0x00, 0x36,
		0x00, 0x37, 0x00, 0x38, 0x00, 0x39, 0x00, 0x3a,
		0x00, 0x3b, 0x00, 0x3c, 0x00, 0x3d, 0x00, 0x3e,
		0x00, 0x3f, 0x00, 0x40, 0x00, 0x41, 0x00, 0x42,
		0x00, 0x43, 0x00, 0x44, 0x00, 0x45, 0x00, 0x46,
		0x00, 0x67, 0x00, 0x68, 0x00, 0x69, 0x00, 0x6a,
		0x00, 0x6b, 0x00, 0x6c, 0x00, 0x6d, 0x00, 0x84,
		0x00, 0x85, 0x00, 0x86, 0x00, 0x87, 0x00, 0x88,
		0x00, 0x89, 0x00, 0x8a, 0x00, 0x8b, 0x00, 0x8c,
		0x00, 0x8d, 0x00, 0x8e, 0x00, 0x8f, 0x00, 0x90,
		0x00, 0x91, 0x00, 0x92, 0x00, 0x93, 0x00, 0x94,
		0x00, 0x95, 0x00, 0x96, 0x00, 0x97, 0x00, 0x98,
		0x00, 0x99, 0x00, 0x9a, 0x00, 0x9b, 0x00, 0x9c,
		0x00, 0x9d, 0x00, 0x9e, 0x00, 0x9f, 0x00, 0xa0,
		0x00, 0xa1, 0x00, 0xa2, 0x00, 0xa3, 0x00, 0xa4,
		0x00, 0xa5, 0x00, 0xa6, 0x00, 0xa7, 0x00, 0xa8,
		0x00, 0xa9, 0x00, 0xaa, 0x00, 0xab, 0x00, 0xac,
		0x00, 0xad, 0x00, 0xae, 0x00, 0xaf, 0x00, 0xb0,
		0x00, 0xb1, 0x00, 0xb2, 0x00, 0xb3, 0x00, 0xb4,
		0x00, 0xb5, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0xb8,
		0x00, 0xb9, 0x00, 0xba, 0x00, 0xbb, 0x00, 0xbc,
		0x00, 0xbd, 0x00, 0xbe, 0x00, 0xbf, 0x00, 0xc0,
		0x00, 0xc1, 0x00, 0xc2, 0x00, 0xc3, 0x00, 0xc4,
		0x00, 0xc5, 0x00, 0xc6, 0x00, 0xc7, 0x13, 0x01,
		0x13, 0x02, 0x13, 0x03, 0x13, 0x04, 0x13, 0x05,
		0xc0, 0x01, 0xc0, 0x02, 0xc0, 0x03, 0xc0, 0x04,
		0xc0, 0x05, 0xc0, 0x06, 0xc0, 0x07, 0xc0, 0x08,
		0xc0, 0x09, 0xc0, 0x0a, 0xc0, 0x0b, 0xc0, 0x0c,
		0xc0, 0x0d, 0xc0, 0x0e, 0xc0, 0x0f, 0xc0, 0x10,
		0xc0, 0x11, 0xc0, 0x12, 0xc0, 0x13, 0xc0, 0x14,
		0xc0, 0x15, 0xc0, 0x16, 0xc0, 0x17, 0xc0, 0x18,
		0xc0, 0x19, 0xc0, 0x1a, 0xc0, 0x1b, 0xc0, 0x1c,
		0xc0, 0x1d, 0xc0, 0x1e, 0xc0, 0x1f, 0xc0, 0x20,
		0xc0, 0x21, 0xc0, 0x22, 0xc0, 0x23, 0xc0, 0x24,
		0xc0, 0x25, 0xc0, 0x26, 0xc0, 0x27, 0xc0, 0x28,
		0xc0, 0x29, 0xc0, 0x2a, 0xc0, 0x2b, 0xc0, 0x2c,
		0xc0, 0x2d, 0xc0, 0x2e, 0xc0, 0x2f, 0xc0, 0x30,
		0xc0, 0x31, 0xc0, 0x32, 0xc0, 0x33, 0xc0, 0x34,
		0xc0, 0x35, 0xc0, 0x36, 0xc0, 0x37, 0xc0, 0x38,
		0xc0, 0x39, 0xc0, 0x3a, 0xc0, 0x3b, 0xc0, 0x3c,
		0xc0, 0x3d, 0xc0, 0x3e, 0xc0, 0x3f, 0xc0, 0x40,
		0xc0, 0x41, 0xc0, 0x42, 0xc0, 0x43, 0xc0, 0x44,
		0xc0, 0x45, 0xc0, 0x46, 0xc0, 0x47, 0xc0, 0x48,
		0xc0, 0x49, 0xc0, 0x4a, 0xc0, 0x4b, 0xc0, 0x4c,
		0xc0, 0x4d, 0xc0, 0x4e, 0xc0, 0x4f, 0xc0, 0x50,
		0xc0, 0x51, 0xc0, 0x52, 0xc0, 0x53, 0xc0, 0x54,
		0xc0, 0x55, 0xc0, 0x56, 0xc0, 0x57, 0xc0, 0x58,
		0xc0, 0x59, 0xc0, 0x5a, 0xc0, 0x5b, 0xc0, 0x5c,
		0xc0, 0x5d, 0xc0, 0x5e, 0xc0, 0x5f, 0xc0, 0x60,
		0xc0, 0x61, 0xc0, 0x62, 0xc0, 0x63, 0xc0, 0x64,
		0xc0, 0x65, 0xc0, 0x66, 0xc0, 0x67, 0xc0, 0x68,
		0xc0, 0x69, 0xc0, 0x6a, 0xc0, 0x6b, 0xc0, 0x6c,
		0xc0, 0x6d, 0xc0, 0x6e, 0xc0, 0x6f, 0xc0, 0x70,
		0xc0, 0x71, 0xc0, 0x72, 0xc0, 0x73, 0xc0, 0x74,
		0xc0, 0x75, 0xc0, 0x76, 0xc0, 0x77, 0xc0, 0x78,
		0xc0, 0x79, 0xc0, 0x7a, 0xc0, 0x7b, 0xc0, 0x7c,
		0xc0, 0x7d, 0xc0, 0x7e, 0xc0, 0x7f, 0xc0, 0x80,
		0xc0, 0x81, 0xc0, 0x82, 0xc0, 0x83, 0xc0, 0x84,
		0xc0, 0x85, 0xc0, 0x86, 0xc0, 0x87, 0xc0, 0x88,
		0xc0, 0x89, 0xc0, 0x8a, 0xc0, 0x8b, 0xc0, 0x8c,
		0xc0, 0x8d, 0xc0, 0x8e, 0xc0, 0x8f, 0xc0, 0x90,
		0xc0, 0x91, 0xc0, 0x92, 0xc0, 0x93, 0xc0, 0x94,
		0xc0, 0x95, 0xc0, 0x96, 0xc0, 0x97, 0xc0, 0x98,
		0xc0, 0x99, 0xc0, 0x9a, 0xc0, 0x9b, 0xc0, 0x9c,
		0xc0, 0x9d, 0xc0, 0x9e, 0xc0, 0x9f, 0xc0, 0xa0,
		0xc0, 0xa1, 0xc0, 0xa2, 0xc0, 0xa3, 0xc0, 0xa4,
		0xc0, 0xa5, 0xc0, 0xa6, 0xc0, 0xa7, 0xc0, 0xa8,
		0xc0, 0xa9, 0xc0, 0xaa, 0xc0, 0xab, 0xc0, 0xac,
		0xc0, 0xad, 0xc0, 0xae, 0xc0, 0xaf, 0xc0, 0xb0,
		0xc0, 0xb1, 0xc0, 0xb2, 0xc0, 0xb3, 0xc0, 0xb4,
		0xc0, 0xb5, 0xc1, 0x00, 0xc1, 0x01, 0xc1, 0x02,
		0xcc, 0xa8, 0xcc, 0xa9, 0xcc, 0xaa, 0xcc, 0xab,
		0xcc, 0xac, 0xcc, 0xad, 0xcc, 0xae, 0xd0, 0x01,
		0xd0, 0x02, 0xd0, 0x03, 0xd0, 0x05,

		// Compression Methods:
		0x01, // Length
		0x00,

		// Extensions:
		0x00, 0x76, // Extensions Length
		// Extension server_name: "testsslsh.com"
		0x00, 0x00, 0x00, 0x12, 0x00, 0x10,
		0x00, 0x00, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x73,
		0x73, 0x6c, 0x73, 0x68, 0x2e, 0x63, 0x6f, 0x6d,

		// Extension ec_point_formats
		0x00, 0x0b, 0x00, 0x04, 0x03, 0x00, 0x01, 0x02,
		// Extension session_ticket
		0x00, 0x23, 0x00, 0x00,
		// Extension signature_algorithms
		0x00, 0x0d, 0x00, 0x30,
		0x00, 0x2e, 0x08, 0x04, 0x08, 0x05, 0x08, 0x06,
		0x08, 0x07, 0x08, 0x08, 0x08, 0x09, 0x08, 0x0a,
		0x08, 0x0b, 0x06, 0x01, 0x06, 0x02, 0x06, 0x03,
		0x05, 0x01, 0x05, 0x02, 0x05, 0x03, 0x04, 0x01,
		0x04, 0x02, 0x04, 0x03, 0x03, 0x01, 0x03, 0x02,
		0x03, 0x03, 0x02, 0x01, 0x02, 0x02, 0x02, 0x03,
		// Extension supported_groups
		0x00, 0x0a, 0x00, 0x1c, 0x00, 0x1a, 0x00, 0x17,
		0x00, 0x19, 0x00, 0x1c, 0x00, 0x1b, 0x00, 0x18,
		0x00, 0x1a, 0x00, 0x16, 0x00, 0x0e, 0x00, 0x0d,
		0x00, 0x0b, 0x00, 0x0c, 0x00, 0x09, 0x00, 0x0a,
	})

	b := p.MustReadTCP(8192)
	// 再尝试读一次。 这是考虑到有些tls端口第一个包只响应Server Hello，第二个包才响应Certificate。 如 106.107.134.24:9000
	var (
		err1 error
		b2   []byte
	)
	defer recoverError(&err1)
	b2, err1 = p.ReadTCP(8192)
	if err1 == nil {
		b = append(b, b2...)
	}
	return b
}

func startTLSByProbe(p *probe, startReq string) ([]*x509.Certificate, string) {
	var err error
	defer recoverError(&err)
	var (
		startTls []byte
	)

	// 请求
	p.MustWriteTCPString(startReq)
	startTls = p.MustRead(1024)

	b := TLSDo(p)
	certs := parseTLSCertificate(b)
	return certs, string(startTls)
}

func startTLSByAddr(addr, startReq string, isFirstRead bool, opts ...interface{}) ([]*x509.Certificate, string) {
	var err error
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)
	var (
		startTls []byte
	)

	if isFirstRead {
		_, err = p.ReadTCP(1024)
		if err != nil {
			return nil, ""
		}
	}

	// 请求
	p.MustWriteTCPString(startReq)
	startTls = p.MustRead(1024)

	b := TLSDo(p)
	certs := parseTLSCertificate(b)
	return certs, string(startTls)
}

func parseTLSCertificate(b []byte) []*x509.Certificate {
	offset := 0
	for offset < len(b) {
		length := int(binary.BigEndian.Uint16(b[3:5]))
		if len(b) < length+5 {
			return nil
		}

		if len(b) < 5 || b[offset] != 0x16 { // check if NOT handshake protocol
			offset += 5 + length
			continue
		}

		offset2 := offset + 5
		for offset2-(offset+5) < length {
			if len(b) < offset2+4 {
				return nil
			}
			handshakeType := b[offset2]
			handshakeLength := int(uint32(b[offset2+1])<<16 | uint32(b[offset2+2])<<8 | uint32(b[offset2+3]))
			if handshakeType != 0xb { //  check if NOT Certificate
				offset2 += handshakeLength + 4
			} else {
				if len(b) < offset2+7 {
					return nil
				}
				certsLen := uint32(b[offset2+4])<<16 | uint32(b[offset2+5])<<8 | uint32(b[offset2+6])
				if len(b) < offset2+7+int(certsLen) {
					return nil
				}

				// code from crypto/tls/handshake_messages.go certificateMsg.unmarshal()
				numCerts := 0
				d := b[offset2+7:]
				for certsLen > 0 {
					if len(d) < 4 {
						return nil
					}
					certLen := uint32(d[0])<<16 | uint32(d[1])<<8 | uint32(d[2])
					if uint32(len(d)) < 3+certLen {
						return nil
					}
					d = d[3+certLen:]
					certsLen -= 3 + certLen
					numCerts++
				}

				certificates := make([][]byte, numCerts)
				d = b[offset2+7:]
				for i := 0; i < numCerts; i++ {
					certLen := uint32(d[0])<<16 | uint32(d[1])<<8 | uint32(d[2])
					certificates[i] = d[3 : 3+certLen]
					d = d[3+certLen:]
				}

				certs := make([]*x509.Certificate, len(certificates))
				for i, asn1Data := range certificates {
					certObj, err := x509.ParseCertificate(asn1Data)
					if err != nil {
						//  errors.New("tls: failed to parse certificate from server: " + err.Error())
						return nil
					}
					certs[i] = certObj
				}

				if len(certs) > 0 {
					return certs
				} else {
					return nil
				}
			}
		}

		offset += 5 + length
	}

	return nil
}

/*func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:         "tls",
		RefererURL:   "https://datatracker.ietf.org/doc/html/rfc5246",
		DefaultPorts: []int{2443, 8443, 2376},
		Handle:       TLS,
	})
}
*/
