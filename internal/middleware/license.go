package middleware

import (
	"api/internal"
	"api/internal/license"
	"github.com/gin-gonic/gin"
	"net/http"
)

func License(l *license.License) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		l.Verify()

		if !l.Info.IsValid {
			ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"status_code": http.StatusBadRequest,
				"message":     internal.LicenseError,
			})
			return
		}

		ctx.Set(internal.LicenseKey, l.Info)
		ctx.Next()
	}
}
