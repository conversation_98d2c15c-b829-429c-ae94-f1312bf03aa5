package repository

import (
	"bytes"
	"encoding/json"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/store"
)

func (repo *repository) AddRule(r model.Rule) error {
	var buf bytes.Buffer

	err := json.NewEncoder(&buf).Encode(r)

	if err != nil {
		return errors.Wrapf(err, "repository.AddRule encode protocol failed %v", r)
	}

	return repo.ruleStore.Write(&store.Record{
		Key:   r.RuleID,
		Value: buf.Bytes(),
	})
}

func (repo *repository) GetRule(ruleID string) ([]*store.Record, error) {
	return repo.ruleStore.Read(ruleID)
}

func (repo *repository) UpdateRule(r model.Rule) error {
	var buf bytes.Buffer

	err := json.NewEncoder(&buf).Encode(r)

	if err != nil {
		return errors.Wrapf(err, "repository.UpdateRule encode protocol failed %v", r)
	}

	return repo.ruleStore.Write(&store.Record{
		Key:   r.RuleID,
		Value: buf.Bytes(),
	})
}

func (repo *repository) DeleteRule(ruleID string) error {
	return repo.ruleStore.Delete(ruleID)
}

func (repo *repository) AllRules() ([]model.Rule, error) {
	keys, err := repo.ruleStore.List()

	if err != nil {
		return []model.Rule{}, err
	}

	var rules []model.Rule

	for _, key := range keys {
		records, err := repo.ruleStore.Read(key)
		if err != nil {
			logger.Warnf("repository.AllRules Read error key: %s", key)
			continue
		}

		for _, record := range records {
			var rule model.Rule
			err = json.Unmarshal(record.Value, &rule)

			if err != nil {
				logger.Warnf("repository.AllRules json.Unmarshal record to Rule error record: %s", string(record.Value))
				continue
			}

			rules = append(rules, rule)
		}
	}

	return rules, nil
}
