package repository

import (
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"go-micro.dev/v4/store"
)

type repository struct {
	ruleStore     store.Store
	protocolStore store.Store
}

type Repository interface {
	AllProtocols() ([]model.Protocol, error)
	AddProtocols(protocols []model.Protocol) error
	UpdateProtocol(p model.Protocol) error
	DeleteProtocol(ids []string, isAll bool) error

	AllRules() ([]model.Rule, error)
	AddRule(r model.Rule) error
	GetRule(ruleID string) ([]*store.Record, error)
	UpdateRule(r model.Rule) error
	DeleteRule(ruleID string) error
}

func NewRepository(ruleStore store.Store, protocolStore store.Store) Repository {
	return &repository{
		ruleStore:     ruleStore,
		protocolStore: protocolStore,
	}
}
