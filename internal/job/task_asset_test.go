package job

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/model"
	"github.com/elastic/go-elasticsearch/v6"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockElasticsearchClient 模拟 Elasticsearch 客户端
type MockElasticsearchClient struct {
	mock.Mock
	BulkRequests []string // 存储所有的 bulk 请求
}

func (m *MockElasticsearchClient) Bulk(body io.Reader, o ...func(*elasticsearch.BulkRequest)) (*elasticsearch.Response, error) {
	// 读取请求体
	bodyBytes, _ := io.ReadAll(body)
	m.BulkRequests = append(m.BulkRequests, string(bodyBytes))
	
	// 模拟成功响应
	response := &elasticsearch.Response{
		StatusCode: 200,
		Body:       io.NopCloser(strings.NewReader(`{"took":1,"errors":false,"items":[]}`)),
	}
	return response, nil
}

// 实现其他必要的方法（简化版）
func (m *MockElasticsearchClient) Info(o ...func(*elasticsearch.InfoRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Search(o ...func(*elasticsearch.SearchRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Index(index string, body io.Reader, o ...func(*elasticsearch.IndexRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Get(index, id string, o ...func(*elasticsearch.GetRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Delete(index, id string, o ...func(*elasticsearch.DeleteRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Update(index, id string, body io.Reader, o ...func(*elasticsearch.UpdateRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Create(index, id string, body io.Reader, o ...func(*elasticsearch.CreateRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Exists(index, id string, o ...func(*elasticsearch.ExistsRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) DeleteByQuery(index []string, body io.Reader, o ...func(*elasticsearch.DeleteByQueryRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) UpdateByQuery(index []string, o ...func(*elasticsearch.UpdateByQueryRequest)) (*elasticsearch.Response, error) {
	return nil, nil
}

func (m *MockElasticsearchClient) Cat() *elasticsearch.Cat {
	return nil
}

func (m *MockElasticsearchClient) Cluster() *elasticsearch.Cluster {
	return nil
}

func (m *MockElasticsearchClient) Indices() *elasticsearch.Indices {
	return nil
}

func (m *MockElasticsearchClient) Ingest() *elasticsearch.Ingest {
	return nil
}

func (m *MockElasticsearchClient) Nodes() *elasticsearch.Nodes {
	return nil
}

func (m *MockElasticsearchClient) Remote() *elasticsearch.Remote {
	return nil
}

func (m *MockElasticsearchClient) Snapshot() *elasticsearch.Snapshot {
	return nil
}

func (m *MockElasticsearchClient) Tasks() *elasticsearch.Tasks {
	return nil
}

func (m *MockElasticsearchClient) CCR() *elasticsearch.CCR {
	return nil
}

func (m *MockElasticsearchClient) ILM() *elasticsearch.ILM {
	return nil
}

func (m *MockElasticsearchClient) License() *elasticsearch.License {
	return nil
}

func (m *MockElasticsearchClient) Migration() *elasticsearch.Migration {
	return nil
}

func (m *MockElasticsearchClient) ML() *elasticsearch.ML {
	return nil
}

func (m *MockElasticsearchClient) Monitoring() *elasticsearch.Monitoring {
	return nil
}

func (m *MockElasticsearchClient) Rollup() *elasticsearch.Rollup {
	return nil
}

func (m *MockElasticsearchClient) Security() *elasticsearch.Security {
	return nil
}

func (m *MockElasticsearchClient) SQL() *elasticsearch.SQL {
	return nil
}

func (m *MockElasticsearchClient) SSL() *elasticsearch.SSL {
	return nil
}

func (m *MockElasticsearchClient) Watcher() *elasticsearch.Watcher {
	return nil
}

func (m *MockElasticsearchClient) XPack() *elasticsearch.XPack {
	return nil
}

func (m *MockElasticsearchClient) Graph() *elasticsearch.Graph {
	return nil
}

// 创建测试用的 rule_tags 数据
func createSubdomainRuleTags() []*rpcx.RuleTag {
	return []*rpcx.RuleTag{
		{
			CnCategory:       "服务",
			CnCompany:        "其他",
			CnParentCategory: "支撑系统",
			CnProduct:        "uvicorn",
			Level:            "3",
			RuleId:           "912062",
			Softhard:         "2",
		},
		{
			CnCategory:       "视频监控",
			CnCompany:        "IQinVision, Inc.",
			CnParentCategory: "物联网设备",
			CnProduct:        "IQinVision-IQeye3",
			RuleId:           "692542",
		},
		{
			CnCompany:        "International Business Machines Corporation",
			CnProduct:        "IBM-Storwize-V7000",
			Level:            "1",
			RuleId:           "782292",
			Softhard:         "1",
		},
		{
			CnCategory:       "机器学习",
			CnCompany:        "杭州未来速度科技有限公司",
			CnParentCategory: "支撑系统",
			CnProduct:        "未来速度-Xinference",
			Level:            "5",
			RuleId:           "923482",
			Softhard:         "2",
		},
	}
}

func createServiceRuleTags() []*rpcx.RuleTag {
	return []*rpcx.RuleTag{
		{
			CnCategory:       "服务",
			CnCompany:        "其他",
			CnParentCategory: "支撑系统",
			CnProduct:        "uvicorn",
			Level:            "3",
			RuleId:           "912062",
			Softhard:         "2",
		},
		{
			CnCategory:       "视频监控",
			CnCompany:        "IQinVision, Inc.",
			CnParentCategory: "物联网设备",
			CnProduct:        "IQinVision-IQeye3",
			RuleId:           "692542",
		},
	}
}

func TestTaskAssetRuleTagsAggregation(t *testing.T) {
	// 创建模拟的 Elasticsearch 客户端
	mockES := &MockElasticsearchClient{}
	
	// 创建配置
	conf := &model.Config{
		Elastic: model.Elastic{
			TaskAssetIndex: "fofaee_task_assets",
			TaskAssetType:  "ips",
		},
	}

	t.Run("测试 subdomain 数据处理", func(t *testing.T) {
		// 创建 subdomain 事件
		subdomainEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "25045",
			},
			JobId:  "job1",
			Origin: "crawler", // 表示来自 subdomain
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       "************",
					Port:     8000,
					Protocol: "http",
					RuleTags: createSubdomainRuleTags(),
				},
			},
		}

		// 创建 job 并执行
		j := NewTask(conf, mockES, subdomainEvent)
		err := j.Normal()
		
		assert.NoError(t, err)
		assert.Len(t, mockES.BulkRequests, 1)
		
		// 验证请求内容包含所有 4 个 rule_tags
		request := mockES.BulkRequests[0]
		assert.Contains(t, request, "912062") // uvicorn
		assert.Contains(t, request, "692542") // IQinVision-IQeye3
		assert.Contains(t, request, "782292") // IBM-Storwize-V7000
		assert.Contains(t, request, "923482") // 未来速度-Xinference
		
		t.Logf("Subdomain request: %s", request)
	})

	t.Run("测试 service 数据处理", func(t *testing.T) {
		// 重置 mock
		mockES.BulkRequests = []string{}
		
		// 创建 service 事件
		serviceEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "25045",
			},
			JobId:  "job2",
			Origin: "rawgrab", // 表示来自 service
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       "************",
					Port:     8000,
					Protocol: "http",
					RuleTags: createServiceRuleTags(),
				},
			},
		}

		// 创建 job 并执行
		j := NewTask(conf, mockES, serviceEvent)
		err := j.Normal()
		
		assert.NoError(t, err)
		assert.Len(t, mockES.BulkRequests, 1)
		
		// 验证请求内容包含 2 个 rule_tags
		request := mockES.BulkRequests[0]
		assert.Contains(t, request, "912062") // uvicorn
		assert.Contains(t, request, "692542") // IQinVision-IQeye3
		assert.NotContains(t, request, "782292") // 不应该包含这个
		assert.NotContains(t, request, "923482") // 不应该包含这个
		
		t.Logf("Service request: %s", request)
	})

	t.Run("验证 Elasticsearch 脚本逻辑", func(t *testing.T) {
		// 重置 mock
		mockES.BulkRequests = []string{}
		
		// 创建 subdomain 事件
		subdomainEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "25045",
			},
			JobId:  "job1",
			Origin: "crawler",
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       "************",
					Port:     8000,
					Protocol: "http",
					RuleTags: createSubdomainRuleTags(),
				},
			},
		}

		j := NewTask(conf, mockES, subdomainEvent)
		err := j.Normal()
		assert.NoError(t, err)
		
		// 解析请求内容
		request := mockES.BulkRequests[0]
		lines := strings.Split(request, "\n")
		
		// 找到包含脚本的行
		var scriptData map[string]interface{}
		for _, line := range lines {
			if strings.Contains(line, "script") {
				err := json.Unmarshal([]byte(line), &scriptData)
				assert.NoError(t, err)
				break
			}
		}
		
		// 验证脚本包含正确的聚合逻辑
		script := scriptData["script"].(map[string]interface{})
		inline := script["inline"].(string)
		
		// 验证脚本包含 rule_tags 处理逻辑
		assert.Contains(t, inline, "ctx._source.rules.removeIf(item -> item.rule_id == rule_tag.rule_id)")
		assert.Contains(t, inline, "ctx._source.rules.add(rule_tag)")
		
		// 验证 upsert 包含初始的 rule_tags
		upsert := scriptData["upsert"].(map[string]interface{})
		rules := upsert["rules"].([]interface{})
		assert.Len(t, rules, 4) // subdomain 应该有 4 个 rule_tags
		
		t.Logf("Script inline: %s", inline)
		t.Logf("Upsert rules count: %d", len(rules))
	})
}
