package job

import (
	"testing"
	"time"

	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/model"
	"github.com/elastic/go-elasticsearch/v6"
	"github.com/stretchr/testify/assert"
)

// 创建测试用的 rule_tags 数据
func createSubdomainRuleTags() []*rpcx.RuleTag {
	return []*rpcx.RuleTag{
		{
			CnCategory:       "服务",
			CnCompany:        "其他",
			CnParentCategory: "支撑系统",
			CnProduct:        "uvicorn",
			Level:            "3",
			RuleId:           "912062",
			Softhard:         "2",
		},
		{
			CnCategory:       "视频监控",
			CnCompany:        "IQinVision, Inc.",
			CnParentCategory: "物联网设备",
			CnProduct:        "IQinVision-IQeye3",
			RuleId:           "692542",
		},
		{
			CnCompany:        "International Business Machines Corporation",
			CnProduct:        "IBM-Storwize-V7000",
			Level:            "1",
			RuleId:           "782292",
			Softhard:         "1",
		},
		{
			CnCategory:       "机器学习",
			CnCompany:        "杭州未来速度科技有限公司",
			CnParentCategory: "支撑系统",
			CnProduct:        "未来速度-Xinference",
			Level:            "5",
			RuleId:           "923482",
			Softhard:         "2",
		},
	}
}

func createServiceRuleTags() []*rpcx.RuleTag {
	return []*rpcx.RuleTag{
		{
			CnCategory:       "服务",
			CnCompany:        "其他",
			CnParentCategory: "支撑系统",
			CnProduct:        "uvicorn",
			Level:            "3",
			RuleId:           "912062",
			Softhard:         "2",
		},
		{
			CnCategory:       "视频监控",
			CnCompany:        "IQinVision, Inc.",
			CnParentCategory: "物联网设备",
			CnProduct:        "IQinVision-IQeye3",
			RuleId:           "692542",
		},
	}
}

func TestTaskAssetRuleTagsAggregation(t *testing.T) {
	// 先删除可能存在的测试数据
	t.Run("清理测试数据", func(t *testing.T) {
		cfg := elasticsearch.Config{
			Addresses: []string{
				"http://************:9200",
			},
		}
		client, err := elasticsearch.NewClient(cfg)
		if err != nil {
			t.Skipf("无法连接到 Elasticsearch: %v", err)
		}

		// 删除可能存在的测试文档
		taskId := "25045"
		ip := "************"
		docId := taskId + "_" + ip

		// 尝试删除文档（如果存在）
		client.Delete("fofaee_task_assets", docId)
		t.Logf("已尝试删除文档: %s", docId)
	})
}

func TestTaskAssetRuleTagsAggregationClean(t *testing.T) {
	// 创建真实的 Elasticsearch 客户端，连接到指定地址
	cfg := elasticsearch.Config{
		Addresses: []string{
			"http://************:9200",
		},
	}
	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		t.Skipf("无法连接到 Elasticsearch: %v", err)
	}
	
	// 创建配置，使用真实的索引名称
	conf := &model.Config{
		Elastic: model.Elastic{
			TaskAssetIndex:   "fofaee_task_assets", // 使用真实索引
			TaskAssetType:    "ips",
			SubdomainIndex:   "fofaee_subdomain",
			SubdomainType:    "subdomain",
			ServiceIndex:     "fofaee_service",
			ServiceType:      "service",
		},
	}

	taskId := "25045"  // 使用真实的 task_id
	ip := "************"

	t.Run("测试 subdomain 数据入库", func(t *testing.T) {
		// 创建 subdomain 事件
		url := "http://************:8000"
		subdomainEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: taskId,
			},
			JobId:  "job1",
			Origin: "crawler", // 表示来自 subdomain
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       ip,
					Port:     8000,
					Protocol: "http",
					RuleTags: createSubdomainRuleTags(),
					Url:      &url,
				},
			},
		}

		// 创建 job 并执行
		j := NewTask(conf, client, subdomainEvent)
		err := j.Normal()
		
		assert.NoError(t, err)
		t.Logf("Subdomain 数据入库成功，包含 %d 个 rule_tags", len(createSubdomainRuleTags()))
		
		// 等待一下让 ES 处理
		time.Sleep(2 * time.Second)
	})

	t.Run("测试 service 数据入库", func(t *testing.T) {
		// 创建 service 事件
		serviceEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: taskId, // 使用相同的 task_id，测试聚合
			},
			JobId:  "job2",
			Origin: "rawgrab", // 表示来自 service
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       ip,
					Port:     8000,
					Protocol: "http",
					RuleTags: createServiceRuleTags(),
				},
			},
		}

		// 创建 job 并执行
		j := NewTask(conf, client, serviceEvent)
		err := j.Normal()
		
		assert.NoError(t, err)
		t.Logf("Service 数据入库成功，包含 %d 个 rule_tags", len(createServiceRuleTags()))
		
		// 等待一下让 ES 处理
		time.Sleep(2 * time.Second)
	})

	t.Run("验证最终聚合结果", func(t *testing.T) {
		// 查询 Elasticsearch 验证最终结果
		t.Logf("查询 Elasticsearch 索引 %s，文档 ID: %s_%s", conf.Elastic.TaskAssetIndex, taskId, ip)
		t.Logf("查询命令: curl -X GET \"************:9200/%s/ips/%s_%s?pretty\"", conf.Elastic.TaskAssetIndex, taskId, ip)
		t.Logf("预期结果：rules 字段应该包含 4 个不重复的 rule_tags")
		t.Logf("- 912062 (uvicorn)")
		t.Logf("- 692542 (IQinVision-IQeye3)")
		t.Logf("- 782292 (IBM-Storwize-V7000)")
		t.Logf("- 923482 (未来速度-Xinference)")

		// 如果当前逻辑有问题，可能只会看到 2 个 rule_tags：
		t.Logf("如果有 bug，可能只会看到:")
		t.Logf("- 912062 (uvicorn)")
		t.Logf("- 692542 (IQinVision-IQeye3)")
	})
}

// 测试多个端口的情况
func TestTaskAssetMultiplePortsRuleTagsAggregation(t *testing.T) {
	// 创建真实的 Elasticsearch 客户端，连接到指定地址
	cfg := elasticsearch.Config{
		Addresses: []string{
			"http://************:9200",
		},
	}
	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		t.Skipf("无法连接到 Elasticsearch: %v", err)
	}
	
	// 创建配置
	conf := &model.Config{
		Elastic: model.Elastic{
			TaskAssetIndex:   "test_fofaee_task_assets",
			TaskAssetType:    "ips",
			SubdomainIndex:   "test_fofaee_subdomain",
			SubdomainType:    "subdomain",
			ServiceIndex:     "test_fofaee_service",
			ServiceType:      "service",
		},
	}

	taskId := "test_multiport_25045"
	ip := "************"

	// 测试多个端口的情况
	ports := []uint32{8000, 8080, 8089, 8888}
	
	for i, port := range ports {
		t.Run("测试端口 8000 subdomain 数据", func(t *testing.T) {
			if port != 8000 {
				t.Skip("只测试 8000 端口")
			}
			
			subdomainEvent := &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: taskId,
				},
				JobId:  "subdomain_job_8000",
				Origin: "crawler",
				Data: &rpcx.QuickStoreEvent_Normal{
					Normal: &rpcx.Normal{
						Ip:       ip,
						Port:     port,
						Protocol: "http",
						RuleTags: createSubdomainRuleTags(),
					},
				},
			}

			j := NewTask(conf, client, subdomainEvent)
			err := j.Normal()
			assert.NoError(t, err)
			
			time.Sleep(1 * time.Second)
		})
		
		t.Run("测试端口 8000 service 数据", func(t *testing.T) {
			if port != 8000 {
				t.Skip("只测试 8000 端口")
			}
			
			serviceEvent := &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: taskId,
				},
				JobId:  "service_job_8000",
				Origin: "rawgrab",
				Data: &rpcx.QuickStoreEvent_Normal{
					Normal: &rpcx.Normal{
						Ip:       ip,
						Port:     port,
						Protocol: "http",
						RuleTags: createServiceRuleTags(),
					},
				},
			}

			j := NewTask(conf, client, serviceEvent)
			err := j.Normal()
			assert.NoError(t, err)
			
			time.Sleep(1 * time.Second)
		})
		
		// 只处理第一个端口，避免测试过于复杂
		if i == 0 {
			break
		}
	}

	t.Run("验证聚合结果", func(t *testing.T) {
		t.Logf("请手动查询 Elasticsearch 索引 %s，文档 ID: %s_%s", conf.Elastic.TaskAssetIndex, taskId, ip)
		t.Logf("预期结果：")
		t.Logf("- rules 字段应该包含 4 个不重复的 rule_tags (来自 subdomain 和 service 的聚合)")
		t.Logf("- 如果有问题，可能只会看到 2 个 rule_tags")
	})
}

// 测试真正的聚合逻辑 - 使用新的文档ID
func TestRuleTagsAggregationLogic(t *testing.T) {
	// 创建 Elasticsearch 客户端
	cfg := elasticsearch.Config{
		Addresses: []string{
			"http://************:9200",
		},
	}
	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		t.Skipf("无法连接到 Elasticsearch: %v", err)
	}

	// 创建配置
	conf := &model.Config{
		Elastic: model.Elastic{
			TaskAssetIndex:   "fofaee_task_assets",
			TaskAssetType:    "ips",
			SubdomainIndex:   "fofaee_subdomain",
			SubdomainType:    "subdomain",
			ServiceIndex:     "fofaee_service",
			ServiceType:      "service",
		},
	}

	// 使用一个新的 task_id 来确保文档不存在（必须是纯数字）
	taskId := "99999"
	ip := "************"
	docId := taskId + "_" + ip

	// 先删除可能存在的文档
	client.Delete(conf.Elastic.TaskAssetIndex, docId)
	t.Logf("已删除可能存在的文档: %s", docId)

	t.Run("第一步：只插入 subdomain 数据", func(t *testing.T) {
		url := "http://************:8000"
		subdomainEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: taskId,
			},
			JobId:  "subdomain_job",
			Origin: "crawler",
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       ip,
					Port:     8000,
					Protocol: "http",
					RuleTags: createSubdomainRuleTags(), // 4 个 rule_tags
					Url:      &url,
				},
			},
		}

		j := NewTask(conf, client, subdomainEvent)
		err := j.Normal()
		assert.NoError(t, err)

		t.Logf("Subdomain 数据入库完成，应该有 4 个 rule_tags")
		time.Sleep(2 * time.Second) // 等待 ES 处理
	})

	t.Run("第二步：插入 service 数据进行聚合", func(t *testing.T) {
		serviceEvent := &rpcx.QuickStoreEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: taskId,
			},
			JobId:  "service_job",
			Origin: "rawgrab",
			Data: &rpcx.QuickStoreEvent_Normal{
				Normal: &rpcx.Normal{
					Ip:       ip,
					Port:     8000,
					Protocol: "http",
					RuleTags: createServiceRuleTags(), // 2 个 rule_tags（与 subdomain 有重复）
				},
			},
		}

		j := NewTask(conf, client, serviceEvent)
		err := j.Normal()
		assert.NoError(t, err)

		t.Logf("Service 数据入库完成，应该与 subdomain 数据聚合")
		time.Sleep(2 * time.Second) // 等待 ES 处理
	})

	t.Run("第三步：验证聚合结果", func(t *testing.T) {
		t.Logf("查询文档: curl -X GET \"************:9200/%s/ips/%s?pretty\"", conf.Elastic.TaskAssetIndex, docId)
		t.Logf("预期结果分析:")
		t.Logf("- Subdomain 有 4 个 rule_tags: 912062, 692542, 782292, 923482")
		t.Logf("- Service 有 2 个 rule_tags: 912062, 692542")
		t.Logf("- 如果是真正聚合：应该有 4 个不重复的 rule_tags")
		t.Logf("- 如果是覆盖：可能只有 2 个 rule_tags（service 的）")
		t.Logf("- 当前逻辑是 removeIf + add，相同 rule_id 会被覆盖，但不同的会保留")
	})
}
