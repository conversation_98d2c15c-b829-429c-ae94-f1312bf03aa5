package job

import (
	"encoding/json"
	"fmt"
	"go-micro.dev/v4/logger"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
)

func (j *job) getIndexAndType() (index, documentType string) {
	if strings.Contains(j.msg.Origin, "crawler") {
		return j.conf.Elastic.SubdomainIndex, j.conf.Elastic.SubdomainType
	} else {
		return j.conf.Elastic.ServiceIndex, j.conf.Elastic.ServiceType
	}
}

func (j *job) wrapServiceOrSubdomainElasticRequestBody() {
	req := j.msg.GetNormal()
	index, documentType := j.getIndexAndType()

	if index == "" {
		logger.Error("the elastic config index and document type not config")
		return
	}

	if req.Cert != nil && *req.Cert != "" {
		j.source["cert"] = req.Cert
	}

	if req.Certs != nil {
		j.source["certs"] = req.Certs
	}

	if req.IsHoneypot != nil {
		j.source["is_honeypot"] = req.IsHoneypot
	}

	if req.HoneypotName != nil {
		j.source["honeypot_name"] = req.HoneypotName
	}

	if strings.Contains(j.msg.Origin, "crawler") {
		// subdomain 没有time 字段
		delete(j.source, "time")
		delete(j.source, "base_protocol")

		j.source["url"] = *req.Url
		j.source["body"] = ""
		j.source["title"] = ""
		j.source["header"] = ""
		j.source["host"] = ""
		j.source["fraud_name"] = ""
		j.source["isdomain"] = false
		j.source["is_fraud"] = false
		j.source["is_honeypot"] = false

		if req.LocationUrl != "" {
			j.source["location_url"] = req.LocationUrl
		}

		if req.Utf8Html != "" {
			j.source["body"] = req.Utf8Html
		}

		if req.SubBody != "" {
			j.source["sub_body"] = req.SubBody
			j.source["subbody"] = ""
		}

		if req.Title != nil {
			j.source["title"] = *req.Title
		}

		if req.Fid != nil && *req.Fid != "" {
			j.source["fid"] = req.Fid
		}

		if req.Header != nil {
			j.source["header"] = req.Header
		} else {
			j.source["header"] = req.Banner
		}

		if req.IsDomain != nil {
			j.source["isdomain"] = *req.IsDomain
		}

		if req.JsInfo != nil {
			j.source["js_info"] = req.JsInfo
		}

		if req.Domain != nil {
			j.source["domain"] = req.Domain
		}

		if req.Subdomain != nil {
			j.source["subdomain"] = req.Subdomain
		}

		if req.Language != nil {
			j.source["language"] = req.Language
		}

		if req.Middleware != nil {
			j.source["middleware"] = req.Middleware
		}

		if req.Charset != nil {
			j.source["charset"] = req.Charset
		}

		if req.LocationUrl != "" {
			j.source["location_url"] = req.LocationUrl
		}

		if req.StatusCode != 0 {
			j.source["status_code"] = req.StatusCode
		}

		if req.Host != nil {
			// TODO should use real host.
			//j.source["host"] = *req.Host
			//j.source["host"] = *req.Url
			j.source["host"] = strings.TrimPrefix(*req.Url, "http://")
		}

		if req.LocationUrl != "" {
			j.source["LocUrl"] = req.LocationUrl
		}

		if req.Dom != nil {
			dom := new(structpb.Struct)
			err := req.Dom.UnmarshalTo(dom)
			if err != nil {
				logger.Warn("dom from protobuf ant to struct failed: ", err)
			}
			j.source["dom"] = dom
		}

		if req.Favicon != nil {
			favicon := new(structpb.Struct)
			err := req.Favicon.UnmarshalTo(favicon)

			if err != nil {
				logger.Warn("favicon from protobuf ant to struct failed: ", err)
			}

			j.source["favicon"] = favicon
		}

		if req.IsFraud != nil {
			j.source["is_fraud"] = req.IsFraud
		}

		if req.FraudName != nil {
			j.source["fraud_name"] = req.FraudName
		}

	} else {
		j.source["banner"] = req.Banner
		j.source["ban_len"] = req.BannerLen

		if req.Jarm != nil {
			j.source["jarm"] = req.Jarm
		}

		if len(req.Hostnames) > 0 {
			j.source["hostnames"] = req.Hostnames
		}
	}

	bodyMap := make(map[string]interface{})

	bodyMap["doc"] = j.source
	bodyMap["doc_as_upsert"] = true

	body, _ := json.Marshal(bodyMap)

	meta := []byte(fmt.Sprintf(`{"update":{"_id" : "%s","_index":"%s", "_type":"%s"}}%s`, j.serviceOrSubdomainDocumentID, index, documentType, "\n"))
	body = append(body, "\n"...)

	j.buf.Grow(len(meta) + len(body))
	j.buf.Write(meta)
	j.buf.Write(body)
}
