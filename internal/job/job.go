package job

import (
	"bytes"
	"context"
	"data_analysis/internal/sutra"
	"data_analysis/internal/worker"
	"encoding/json"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go-micro.dev/v4/logger"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
)

type job struct {
	msg    *rpcx.DataAnalysisEvent
	source map[string]interface{}
	sutra  sutra.Sutra
}

type Job interface {
	Run(ctx context.Context) (*rpcx.DataAnalysisEvent, error)
}

func NewJob(msg *rpcx.DataAnalysisEvent, sutra sutra.Sutra) Job {
	return &job{
		msg:    msg,
		source: make(map[string]interface{}),
		sutra:  sutra,
	}
}

func (j *job) Run(ctx context.Context) (*rpcx.DataAnalysisEvent, error) {
	source := make(map[string]interface{})
	worker.SetAdditionInfo(source, j.msg.Normal.Ip)

	metaData := sutra.MatchData{}

	// 添加调试日志
	logger.Infof("[DEBUG] Origin: %s", j.msg.Origin)
	logger.Infof("[DEBUG] Contains grab: %v", strings.Contains(j.msg.Origin, "grab"))

	if strings.Contains(j.msg.Origin, "grab") {
		metaData.Protocol = j.msg.Normal.Protocol
		metaData.Banner = j.msg.Normal.Banner
		logger.Infof("[DEBUG] GRAB分支 - Protocol: %s, Banner长度: %d", metaData.Protocol, len(metaData.Banner))
	} else {
		logger.Infof("[DEBUG] 非GRAB分支 - 设置其他字段")
		if j.msg.Normal.Title != nil {
			metaData.Title = *j.msg.Normal.Title
		}

		metaData.Body = j.msg.Normal.Utf8Html
		if metaData.Body == "" && j.msg.Normal.Body != nil {
			metaData.Body = *j.msg.Normal.Body
		}

		if j.msg.Normal.Header != nil {
			metaData.Header = *j.msg.Normal.Header
		}

		if j.msg.Normal.Fid != nil {
			metaData.Fid = *j.msg.Normal.Fid
		}

		if j.msg.Normal.Favicon != nil {
			favicon := new(structpb.Struct)
			err := j.msg.Normal.Favicon.UnmarshalTo(favicon)

			if err != nil {
				return nil, errors.Wrap(err, j.msg.Normal.Favicon.String())
			}
			m := favicon.Fields
			metaData.IconHash = m["hash"].String()
		}
	}

	if j.msg.Normal.Cert != nil {
		metaData.Cert = *j.msg.Normal.Cert
	}

	if j.msg.Normal.SubBody != "" {
		metaData.SubBody = j.msg.Normal.SubBody
	}

	if j.msg.Normal.Server != nil {
		metaData.Server = *j.msg.Normal.Server
		logger.Infof("[DEBUG] Server字段: %s", metaData.Server)
	}

	if j.msg.Normal.Domain != nil {
		metaData.Domain = *j.msg.Normal.Domain
	}

	buf := &bytes.Buffer{}

	if err := json.NewEncoder(buf).Encode(metaData); err != nil {
		return nil, err
	}

	// 添加关键调试日志
	logger.Infof("[DEBUG] 传递给规则引擎的完整数据: %s", buf.String())
	logger.Infof("[DEBUG] TaskID: %s, IP: %s", j.msg.TaskInfo.TaskId, j.msg.Normal.Ip)

	products, err := j.sutra.Match(ctx, buf.String())

	if err != nil {
		logger.Warnf("sutra.Match error param:%s; task_id:%s", buf.String(), j.msg.TaskInfo.TaskId)
	}

	if len(products) == 0 {
		logger.Warnf("sutra.Match not found param:%s; task_id:%s", buf.String(), j.msg.TaskInfo.TaskId)
	}

	if len(products) > 0 {
		var (
			product  []string
			ruleTags []*rpcx.RuleTag
		)

		for _, p := range products {
			product = append(product, p.Name)
			ruleTags = append(ruleTags, &rpcx.RuleTag{
				CnCategory:       p.Category,
				CnParentCategory: p.ParentCategory,
				CnCompany:        p.Company,
				CnProduct:        p.Name,
				Level:            p.Level,
				RuleId:           p.RuleId,
				Softhard:         p.SoftHard,
			})
		}

		j.msg.Normal.Product = lo.Uniq(product)
		j.msg.Normal.RuleTags = ruleTags
	}

	if v, ok := source["ipcnet"]; ok {
		j.msg.Normal.Ipcnet = v.(string)
	}

	b, err := json.Marshal(source["geoip"])
	if err != nil {
		logger.Warnf("json geoip to byte failed:%v", err)
	} else {
		j.msg.Normal.Geoip = new(rpcx.Geoip)
		err = json.Unmarshal(b, j.msg.Normal.Geoip)
		if err != nil {
			logger.Warnf("json geoIP  Unmarshal to grpc Geoip error:%v", err)
			logger.Info("geoip: ", string(b))
		}
	}

	b, err = json.Marshal(source["asn"])
	if err != nil {
		logger.Warnf("json asn to byte error:%v", err)
	} else {
		j.msg.Normal.Asn = new(rpcx.ASN)
		err = json.Unmarshal(b, j.msg.Normal.Asn)
		if err != nil {
			logger.Warnf("json asn Unmarshal to grpc ASN error:%v", err)
		}
	}

	return j.msg, nil
}
