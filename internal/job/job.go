package job

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/rawgrab"
	tag "git.gobies.org/goby/bmhtags"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	mm "git.gobies.org/shared-platform/foscan/pkg/model"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"rawgrab/internal/model"
	"strconv"
	"strings"
	"time"
)

type job struct {
	conf      *model.Config
	grab      rawgrab.GrabScanner
	protocols []mm.Protocol
}

func (j *job) Grab(msg *pb.RawGrabEvent) (*pb.DataAnalysisEvent, error) {
	if j.protocols != nil {
		logger.Infof("CustomGrab is start %v", msg)
		e, err := j.CustomGrab(msg)

		if err == nil {
			logger.Infof("CustomGrab result is %v", e)
			return e, nil
		}

		logger.Infof("CustomGrab not has result ip:%s port:%d base_protocol:%s warn: %v", msg.Ip, msg.Port, msg.BaseProtocol, err)
	}

	var result *rawgrab.GrabResult

	result = j.grab.Grab(context.TODO(), msg.Ip, strconv.Itoa(int(msg.Port)), msg.BaseProtocol, msg.BindProtocol, "")

	if result != nil && result.Status != "success" && msg.BindProtocol != "" {
		result = j.grab.Grab(context.TODO(), msg.Ip, strconv.Itoa(int(msg.Port)), msg.BaseProtocol, "", "")
		logger.Info("bind protocol grab result: ", result)
	}

	if result != nil && result.Status != "success" {
		result.Protocol = "unknown"
		logger.Warn("the grab result not success result:", result)
	}

	// 去除ip中的中括号
	if result != nil && strings.Contains(result.IP, "[") {
		result.IP = strings.Trim(result.IP, "[")
		result.IP = strings.Trim(result.IP, "]")
	}

	// 无效结果和未知协议入库判断
	if result.Protocol == "unknown" && !msg.TaskInfo.UnknownProtocolInDb {
		logger.Warnf("UnknownProtocolInDb is false jump; request: %v; result: %v", msg, result)
		return nil, errors.New("UnknownProtocolInDb is false jump this data.")
	}

	var buf bytes.Buffer
	enc := json.NewEncoder(&buf)
	enc.SetEscapeHTML(false)
	err := enc.Encode(result)

	if err != nil {
		return nil, err
	}

	int64Port, err := strconv.ParseInt(result.Port, 10, 32)

	if err != nil {
		return nil, err
	}

	normal := &pb.Normal{
		Ip:           result.IP,
		Port:         uint32(int64Port),
		Protocol:     result.Protocol,
		BaseProtocol: result.BaseProtocol,
		IsIpv6:       result.IsIpv6,
		Banner:       result.Banner,
		BannerLen:    uint32(len(result.Banner)),
		Timestamp:    result.Time,
		Cert:         &result.Cert,
		Hostnames:    result.Hostname,
		IsHoneypot:   &result.IsHoneypot,
		Domain:       &result.Domain,
		Subdomain:    &result.Subdomain,
		Server:       &result.Server,
		Host:         &result.Host,
	}

	if result.Jarm != nil {
		normal.Jarm = &pb.Jarm{
			Hash:  result.Jarm.Hash,
			Group: result.Jarm.Group,
		}
	}

	if result.Certs != nil {
		var certs pb.CertObject

		marshal, _ := json.Marshal(result.Certs)

		if err != nil {
			logger.Warnf("the certs marshal error: %v certs: %v", err, result.Certs)
		} else {
			err = json.Unmarshal(marshal, &certs)
			if err != nil {
				logger.Warnf("the certs unmarshal error: %v certs: %v", err, result.Certs)
			} else {
				normal.Certs = &certs
			}
		}
	}

	m := tag.Extract(result.Protocol, result.Banner)

	if m != nil {
		if v, ok := m["modal"]; ok {
			normal.Modal = v.([]string)
		}

		if v, ok := m["os"]; ok {
			normal.Os = v.([]string)
		}
		if v, ok := m["middleware"]; ok {
			normal.Middleware = v.([]string)
		}
		if v, ok := m["appserver"]; ok {
			normal.Appserver = v.([]string)
		}
		if v, ok := m["language"]; ok {
			normal.Language = v.([]string)
		}

		if v, ok := m["version"]; ok {
			normal.Version = v.([]string)
		}
	}

	normal.Mac, normal.NetbiosName = fetchNetBiosInfo(result.Protocol, result.Banner)

	return &pb.DataAnalysisEvent{
		TaskInfo: msg.TaskInfo,
		JobId:    msg.JobId,
		Origin:   constant.ServiceRawGrab,
		Normal:   normal,
	}, nil
}

func (j *job) CustomGrab(msg *pb.RawGrabEvent) (*pb.DataAnalysisEvent, error) {
	protocolType := "0"

	if msg.BaseProtocol == "udp" {
		protocolType = "1"
	}

	logger.Info("protocolType:", protocolType)
	for _, protocol := range j.protocols {
		b := bytes.Buffer{}
		_ = json.NewEncoder(&b).Encode(protocol)
		logger.Info("protocol:", b.String())

		ports := strings.Split(protocol.BindPort, ",")

		if protocol.ProtocolType != protocolType {
			continue
		}

		for _, port := range ports {
			parseInt, err := strconv.ParseInt(port, 10, 32)
			if err != nil {
				logger.Warn("custom protocol strconv.ParseInt failed: ", protocol)
				continue
			}

			if msg.Port != uint32(parseInt) {
				continue
			}

			banner := Detect(msg.Ip, uint(msg.Port), protocol)

			logger.Info("detect result:", banner)
			if banner != nil {
				return &pb.DataAnalysisEvent{
					TaskInfo: msg.TaskInfo,
					JobId:    msg.JobId,
					Origin:   constant.ServiceRawGrab,
					Normal: &pb.Normal{
						Ip:           msg.Ip,
						Port:         msg.Port,
						Protocol:     protocol.Name,
						BaseProtocol: msg.BaseProtocol,
						IsIpv6:       msg.TaskInfo.IsIpv6,
						Banner:       string(banner),
						BannerLen:    uint32(len(string(banner))),
						Timestamp:    fmt.Sprintf("%d", time.Now().Unix()),
					},
				}, nil
			}
		}
	}

	return nil, errors.New("not found custom protocol")
}

type Job interface {
	Grab(*pb.RawGrabEvent) (*pb.DataAnalysisEvent, error)
	CustomGrab(*pb.RawGrabEvent) (*pb.DataAnalysisEvent, error)
}

func NewJob(conf *model.Config, grab rawgrab.GrabScanner, protocols []mm.Protocol) Job {
	return &job{
		conf:      conf,
		grab:      grab,
		protocols: protocols,
	}
}
