package job

import (
	"context"
	"crawler/internal/config"
	"crawler/internal/http"
	"crawler/internal/parse"
	"crawler/internal/req"
	"crawler/internal/util"
	"crawler/internal/worker"
	"encoding/json"
	gcrawler "git.gobies.org/goby/crawler"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/pkg/errors"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
)

type job struct {
	ip   string
	url  string
	conf config.CrawlerConf
	msg  *rpcx.CrawlerEvent
}

func (j *job) Normal() (*rpcx.DataAnalysisEvent, error) {
	task := &parse.CrawlerTask{
		Url:       j.msg.Url,
		Domain:    j.msg.Domain,
		Subdomain: j.msg.Subdomain,
		Host:      j.msg.Host,
	}

	// 执行请求
	url := j.msg.Url
	h := req.Header{
		"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
		"Accept-Charset":  "GBK,utf-8;q=0.7,*;q=0.3",
		"Accept-Language": "zh-CN,zh;q=0.8",
		"User-Agent":      "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36",
		"Host":            "",
	}
	if j.msg.Host != "" {
		h["Host"] = j.msg.Host
	}

	resp, _, err := http.RequestWithLocationWithHeader(j.msg.Url, 1, http.RTHost, h)

	if err != nil {
		return nil, errors.Wrapf(err, "error url:%s", url)
	}

	// 判断http跳转到https的情况
	tmpResp, err := worker.ProcHttpsReq(url, resp, task)
	if err != nil {
		return nil, err
	} else if tmpResp != nil {
		resp = tmpResp
	}

	isDomain := false
	if !strings.Contains(task.Url, j.msg.Ip) {
		isDomain = true
	}
	// 这个地方必须用修改后的Host，否则端口判断不对
	info := http.GetHttpInfo(resp, task.Host, j.msg.Ip, isDomain)
	info["domain"] = task.Domain
	// 兼容老版本没有host字断的情况
	tmpHost := ""
	if len(task.Host) == 0 {
		tmpHost = task.Url
	} else {
		tmpHost = task.Host
	}
	info["host"] = util.Url2Hostinfo(tmpHost)
	info["url"] = util.ProcUrl(task.Url)
	info["subdomain"] = task.Subdomain
	info["ip"] = j.msg.Ip
	info["sub_body"] = ""
	if isDomain {
		info["is_domain"] = true
	}

	if strings.HasPrefix(task.Url, "https://") {
		info["protocol"] = "https"
	} else {
		info["protocol"] = "http"
	}

	//// 获取cname信息
	//fw.procGetCName(task.Url, info)

	// murmur3 算法性能很低，后续需要优化 如：https://4ndwc1l61aiv.xvnaqs.com/
	refers := url
	bodyStr, _ := info["utf8html"].(string)
	bodyLen := len(bodyStr)

	worker.ProcGetDom(refers, bodyStr, info)

	// 获取icon
	if resp.Request() != nil && resp.Request().URL != nil && len(resp.Request().URL.String()) > 0 {
		refers = resp.Request().URL.String()
	}
	// 没有内容且屏蔽请求默认icon地址的不再请求
	if worker.IsNeedReqIcon(bodyLen, false) {
		disableDefaultReq := false
		if resp.Response().StatusCode >= 300 {
			disableDefaultReq = true
		}
		logger.Infof("get icon in  host:", task.Url, "status_code:", resp.Response().StatusCode, "disable_default:", disableDefaultReq)
		worker.ProcGetIcon(refers, info, disableDefaultReq)
	}

	// 获取js-info
	jsInfo, isSensitive := worker.GetBodyJsInfo(bodyStr, refers)
	if jsInfo != nil && jsInfo.JsNum != 0 {
		info["js_info"] = jsInfo

		// 如果识别到了垃圾数据，不重复判断
		if info["is_fraud"] == nil {
			tmpFraud, tmpFraudName := http.CheckJsFraud(jsInfo)
			if tmpFraud {
				info["is_fraud"] = true
				info["fraud_name"] = tmpFraudName
			}
		}
	}

	if isSensitive {
		info["is_sensitive"] = true
	}

	if len(j.msg.TaskInfo.CrawlerSpecificUrl) > 0 {
		urls := strings.Split(j.msg.TaskInfo.CrawlerSpecificUrl, "|")
		for _, dUrl := range urls {
			rRrl := url + dUrl
			logger.Info("crawler specific url:", rRrl)
			resp, _, err := http.RequestWithLocation(rRrl, 1, http.RTHost)
			if err != nil {
				continue
			}
			htmls, err := http.GetDecodeHtml(info["host"].(string), resp, true)
			if err != nil {
				continue
			}
			html := string(htmls[0:util.Min(1024*1024, len(htmls))])
			info["sub_body"] = info["sub_body"].(string) + html
			info["utf8html"] = info["utf8html"].(string) + html
		}
	}

	marshal, err := json.Marshal(info)
	if err != nil {
		return nil, err
	}

	logger.Infof("crawler result info json string %s", string(marshal))
	normal := new(rpcx.Normal)

	// 单独处理LocUrl
	if l, ok := info["LocUrl"]; ok {
		normal.LocationUrl = l.(string)
	}

	err = json.Unmarshal(marshal, normal)
	if err != nil {
		return nil, err
	}

	if info["dom"] != nil {
		m, _ := structpb.NewStruct(info["dom"].(map[string]interface{}))
		domAny, _ := anypb.New(m)
		normal.Dom = domAny
	}

	if info["favicon"] != nil {
		m, _ := structpb.NewStruct(info["favicon"].(map[string]interface{}))
		faviconAny, _ := anypb.New(m)
		normal.Favicon = faviconAny
	}

	normal.Ip = j.msg.Ip
	normal.Port = j.msg.Port
	normal.Protocol = j.msg.Protocol
	normal.BaseProtocol = j.msg.BaseProtocol
	normal.Host = &j.msg.Host
	normal.Url = &j.msg.Url

	return &rpcx.DataAnalysisEvent{
		TaskInfo: j.msg.TaskInfo,
		JobId:    j.msg.JobId,
		Origin:   constant.ServiceCrawler,
		Normal:   normal,
	}, nil
}

func (j *job) SiteURL(srv micro.Service) error {
	logger.Info("Received Crawler.SiteURL request: ", j.msg)

	opt = ParseConfig(j.conf)

	opt.InitURL = j.msg.Url
	logger.Info("Crawler.SiteURL option: ", opt)
	gc := gcrawler.New(opt)

	if err := gc.Start(); err != nil {
		logger.Errorf("crawler start failed %v", err)
		return err
	}

	for _, u := range gc.GetURLs() {
		all := new(rpcx.FullSiteCrawler)
		all.Url = u.URL
		all.Type = u.Type
		all.Params = u.Params
		all.Data = u.Data
		all.Method = u.Method
		all.PostDataType = u.PostDataType
		all.Hash = u.Hash

		newValue, err := structpb.NewStruct(u.ExtraHeaders)
		if err != nil {
			logger.Warnf("ExtraHeaders transfer error %v", err)
		} else {
			a, _ := anypb.New(newValue)
			all.ExtraHeaders = a
		}

		all.ContentType = u.ContentType
		all.Referer = u.Referer
		all.State = uint32(u.State)
		all.StatusCode = uint32(u.StatusCode)

		if u.HostInfo == "" {
			all.HostInfo = u.GetFixedUrl().HostInfo
		} else {
			all.HostInfo = u.HostInfo
		}

		all.Path = u.Path
		all.Dir = u.Dir
		all.File = u.File
		all.Ip = j.msg.Ip

		e := &rpcx.QuickStoreEvent{
			TaskInfo: j.msg.TaskInfo,
			JobId:    j.msg.JobId,
			Origin:   constant.ServiceCrawler,
			Data: &rpcx.QuickStoreEvent_FullSiteCrawler{
				FullSiteCrawler: all,
			},
		}

		err = srv.Client().Publish(context.Background(), client.NewMessage(constant.ServiceQuickStore, e))

		if err != nil {
			logger.Errorf("Crawler send FullSiteCrawler error: %v event: %v", err, e)
		} else {
			logger.Info("Crawler send FullSiteCrawler success. task_id: %s url: %s", j.msg.TaskInfo.TaskId, j.msg.Url)
		}
	}

	logger.Infof("FullSiteCrawler end. task_id: %s url: %s", j.msg.TaskInfo.TaskId, j.msg.Url)
	return nil
}

type Job interface {
	Normal() (*rpcx.DataAnalysisEvent, error)
	SiteURL(srv micro.Service) error
}

func NewJob(msg *rpcx.CrawlerEvent, conf config.CrawlerConf) Job {
	return &job{
		msg:  msg,
		conf: conf,
	}
}
