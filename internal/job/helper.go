package job

import (
	"encoding/hex"
	"regexp"
)

var regNBMacAddr = regexp.MustCompile(`MAC:([a-zA-Z0-9:]*)`)
var regNBname = regexp.MustCompile(`Hostname:(\S*)<\d+>`)

func fetchNetBiosInfo(protocol, banner string) (nbMac, nbName string) {
	if protocol != "netbios" {
		return
	}

	arrMac := regNBMacAddr.FindStringSubmatch(banner)
	if len(arrMac) > 1 {
		nbMac = arrMac[1]
	}

	arrNbName := regNBname.FindStringSubmatch(banner)
	if len(arrNbName) > 1 {
		nbName = arrNbName[1]
	}

	return
}

// MustDecode 十六进制字符串数据解码
func MustDecode(str string) ([]byte, error) {
	// 3b97d7b01105467f2342e1b3edee0dd9
	src := []byte(str)
	dst := make([]byte, hex.DecodedLen(len(src)))

	_, err := hex.Decode(dst, src)
	if err != nil {
		return nil, err
	}

	return dst, nil
}

// MustEncodeToString 编码字符串到十六进制.
func MustEncodeToString(data []byte) string {
	return hex.EncodeToString(data)
}

// DecodeString
func DecodeString(s string) ([]byte, error) {
	return hex.DecodeString(s)
}

func DecodeStringForHuman(s string) (string, error) {
	decode, err := hex.DecodeString(s)
	if err != nil {
		return "", err
	}
	return string(decode), nil
}
