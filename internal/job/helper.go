package job

import (
	"github.com/hashicorp/go-version"
	"math"
)

func versionFromStringToNumber(s string) (float64, error) {
	v, err := version.NewVersion(s)
	if err != nil {
		return 0, err
	}

	p := v.Segments()

	var versionNum float64
	for i, n := range p {
		versionNum += float64(n) * math.Pow(10, float64(-i))
	}

	prerelease := v.Prerelease()
	if prerelease != "" {
		versionNum -= math.Pow(float64(prerelease[0]), -1)
	}

	return versionNum, nil
}
