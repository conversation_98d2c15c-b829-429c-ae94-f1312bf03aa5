package job

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/elastic/go-elasticsearch/v6/esapi"
	"go-micro.dev/v4/logger"
	"strconv"
	"strings"
	"time"
)

func (j *job) wrapTaskAsset() error {
	var b bytes.Buffer
	b.WriteString(j.msg.TaskInfo.TaskId)
	b.WriteString("_")
	b.WriteString(j.source["ip"].(string))

	documentID := b.String()

	uPort := j.source["port"].(uint32)
	sPort := strconv.Itoa(int(uPort))
	j.source["port_string"] = sPort

	j.source["task_id"] = j.msg.TaskInfo.TaskId

	req := j.msg.GetNormal()

	var title, host, url, banner, charset, honeypotName, fraudName, domain string

	var (
		isHoneypot = false
		isFraud    = false
	)
	banner = req.Banner
	if banner == "" && req.Header != nil {
		banner = *req.Header
	}

	if req.Domain != nil {
		domain = *req.Domain
	}

	if req.IsHoneypot != nil {
		isHoneypot = *req.IsHoneypot
	}

	if req.IsFraud != nil {
		isFraud = *req.IsFraud
	}

	if req.HoneypotName != nil {
		honeypotName = *req.HoneypotName
	}

	if req.FraudName != nil {
		fraudName = *req.FraudName
	}

	if req.Charset != nil {
		charset = *req.Charset
	}

	portListItem := map[string]interface{}{
		"protocol":      j.source["protocol"],
		"port":          j.source["port"],
		"banner":        banner,
		"certs":         j.source["certs"],
		"is_honeypot":   isHoneypot,   // 是否是蜜罐
		"honeypot_name": honeypotName, // 蜜罐名称
	}

	if req.Title != nil {
		title = *req.Title
	}

	if req.Host != nil {
		host = strings.TrimPrefix(*req.Host, "http://")
	}

	if req.Url != nil {
		url = *req.Url
	}

	hostListItem := map[string]interface{}{
		"port":   j.source["port"],
		"header": banner,
		"title":  title,
		"host":   url,
	}

	titleListItem := map[string]interface{}{
		"port":          j.source["port"],
		"title":         title,
		"host":          url,
		"is_honeypot":   isHoneypot,   // 是否是蜜罐
		"honeypot_name": honeypotName, // 蜜罐名称
		"charset":       charset,      // 网站编码
		"is_fraud":      isFraud,      // 是否是垃圾网站
		"fraud_name":    fraudName,    // 垃圾网站名称
	}

	m := map[string]interface{}{
		"script": map[string]interface{}{
			"inline": `
		if (params.data.protocol.contains("http")) {
			if (ctx._source.title_list == null || ctx._source.title_list.size() == 0) { ctx._source.title_list = new ArrayList() }
			if (params.title_list_item.title != null && params.title_list_item.title.length() > 0) {
				ctx._source.title_list.removeIf(item -> item.port == params.data.port); // remove existed port in title_list
				ctx._source.title_list.add(params.title_list_item);
			}

			if (ctx._source.host_list == null || ctx._source.host_list.size() == 0) { ctx._source.host_list = new ArrayList() }
			if (params.data.isdomain != null && params.data.isdomain) {
				ctx._source.host_list.removeIf(item -> item.port == params.data.port);
				ctx._source.host_list.add(params.host_list_item);
				ctx._source.host = params.title_list_item.host;
			}
		}

		if (ctx._source.port_list == null || ctx._source.port_list.size() == 0) { ctx._source.port_list = new ArrayList() }
		ctx._source.port_list.removeIf(item -> item.port == params.data.port); // remove existed port in port_list
		ctx._source.port_list.add(params.port_list_item);

		if (ctx._source.rules == null || ctx._source.rules.size() == 0) { ctx._source.rules= new ArrayList() }
		if (params.data.rule_tags == null || params.data.rule_tags.size() == 0) { params.data.rule_tags = new ArrayList() }

		// 更强健的聚合逻辑：确保不会丢失任何 rule_tags
		params.data.rule_tags.forEach((new_rule_tag) -> {
			boolean found = false;
			for (int i = 0; i < ctx._source.rules.size(); i++) {
				if (ctx._source.rules.get(i).rule_id == new_rule_tag.rule_id) {
					// 找到相同 rule_id，更新现有记录
					ctx._source.rules.set(i, new_rule_tag);
					found = true;
					break;
				}
			}
			if (!found) {
				// 没找到相同 rule_id，添加新记录
				ctx._source.rules.add(new_rule_tag);
			}
		});

		if (ctx._source.ports == null || ctx._source.ports.size() == 0) { ctx._source.ports= new ArrayList() }
		ctx._source.ports.add(params.data.port_string);
		ctx._source.ports = ctx._source.ports.stream().distinct().sorted().collect(Collectors.toList());
		ctx._source.port_size = ctx._source.ports.length;

		if (ctx._source.protocols == null || ctx._source.protocols.size() == 0) { ctx._source.protocols= new ArrayList() }
		ctx._source.protocols.add(params.data.protocol);
		ctx._source.protocols = ctx._source.protocols.stream().filter(x -> !x.isEmpty()).distinct().sorted().collect(Collectors.toList());

		if (params.data.mac !=null && params.data.mac.length() > 0) {ctx._source.mac = params.data.mac;}
		if (params.data.netbios_name !=null && params.data.netbios_name.length() > 0) {ctx._source.netbios_name = params.data.netbios_name;}

		if (ctx._source.hosts == null || ctx._source.hosts.size() == 0) { ctx._source.hosts= new ArrayList() }
		if (params.data.domain !=null && params.domain.length() > 0) {
			ctx._source.hosts.add(params.title_list_item.host);
			ctx._source.hosts = ctx._source.hosts.stream().distinct().sorted().collect(Collectors.toList());
		}

		ctx._source.lastupdatetime = params.lastupdatetime
`,
			"params": map[string]interface{}{
				"data":            j.source,
				"domain":          domain,
				"host_list_item":  hostListItem,
				"port_list_item":  portListItem,
				"title_list_item": titleListItem,
				"lastupdatetime":  time.Now().Format(TimeFormat),
			},
			"lang": "painless",
		},
	}

	upsert := map[string]interface{}{
		"ip":      j.source["ip"],
		"task_id": j.source["task_id"],
		"is_ipv6": j.source["is_ipv6"],
		"state":   1,
		"ports": []string{
			j.source["port_string"].(string),
		},
		"rules": j.source["rule_tags"],
		"protocols": []string{
			j.source["protocol"].(string),
		},
		"port_list": []map[string]interface{}{
			portListItem,
		},
		"port_size":      1,
		"createtime":     j.source["lastupdatetime"],
		"lastupdatetime": j.source["lastupdatetime"],
	}

	if host != "" {
		upsert["hosts"] = []string{host}
	}

	if strings.Contains(j.source["protocol"].(string), "http") {
		if req.IsDomain != nil && *req.IsDomain {
			upsert["host_list"] = []map[string]interface{}{
				hostListItem,
			}
		} else {
			if len(title) > 0 {
				upsert["title_list"] = []map[string]interface{}{
					titleListItem,
				}
			}
		}
	}

	m["upsert"] = upsert
	data, err := json.Marshal(m)

	if err != nil {
		fmt.Println("json.Marshal error", err, err.Error())
		return err
	}

	meta := []byte(fmt.Sprintf(`{"update":{"_id" : "%s","_index":"%s", "_type":"%s", "retry_on_conflict": 10}}%s`, documentID, j.conf.Elastic.TaskAssetIndex, j.conf.Elastic.TaskAssetType, "\n"))
	data = append(data, "\n"...)

	j.buf.Grow(len(meta) + len(data))
	j.buf.Write(meta)
	j.buf.Write(data)

	bulk, err := j.elastic.Bulk(bytes.NewReader(j.buf.Bytes()))

	if err != nil {
		return err
	}

	if bulk.IsError() {
		return errors.New(bulk.String())
	}

	defer bulk.Body.Close()

	logger.Info("elastic normal result:", bulk.String())
	return nil
}

// taskAssetPing
// if port==0 insert
// if port!=0 insert and port_list have value
// finally the port!=0 should override port==0.
func (j *job) taskAssetPing() error {
	var b bytes.Buffer
	b.WriteString(j.msg.TaskInfo.TaskId)
	b.WriteString("_")
	b.WriteString(j.source["ip"].(string))

	uPort := j.msg.GetNormal().Port
	sPort := strconv.Itoa(int(uPort))

	portListItem := map[string]interface{}{
		"protocol": "",
		"port":     uPort,
		"banner":   "",
		"certs":    nil,
	}

	index := "fofaee_task_assets"
	documentType := "ips"
	documentID := b.String()

	m := map[string]interface{}{
		"script": map[string]interface{}{
			"inline": `
			ctx._source.lastupdatetime = params.lastupdatetime;

			if (params.u_port != 0) {
				if (ctx._source.port_list == null || ctx._source.port_list.size() == 0) { ctx._source.port_list = new ArrayList() }
		    	ctx._source.port_list.removeIf(item -> item.port == params.u_port);
		    	ctx._source.port_list.add(params.port_list_item);

				if (ctx._source.ports == null || ctx._source.ports.size() == 0) { ctx._source.ports= new ArrayList() }
				ctx._source.ports.add(params.s_port);
				ctx._source.ports = ctx._source.ports.stream().distinct().sorted().collect(Collectors.toList());
				ctx._source.port_size = ctx._source.ports.length;
			}
`,
			"params": map[string]interface{}{
				"u_port":         uPort,
				"s_port":         sPort,
				"port_list_item": portListItem,
				"lastupdatetime": time.Now().Format(TimeFormat),
			},
			"lang": "painless",
		},
	}

	upsert := map[string]interface{}{
		"ip":             j.source["ip"],
		"task_id":        j.msg.TaskInfo.TaskId,
		"is_ipv6":        j.source["is_ipv6"],
		"state":          1,
		"ports":          []string{},
		"protocols":      []string{},
		"port_list":      []map[string]interface{}{},
		"port_size":      0,
		"createtime":     j.source["lastupdatetime"],
		"lastupdatetime": j.source["lastupdatetime"],
	}

	if uPort != 0 {
		upsert["ports"] = []string{sPort}
		upsert["port_size"] = 1
		upsert["port_list"] = []map[string]interface{}{
			portListItem,
		}
	}

	m["upsert"] = upsert
	data, err := json.Marshal(m)

	if err != nil {
		return err
	}

	retryOnConflict := 3
	updateRequest := esapi.UpdateRequest{
		Index:           index,
		DocumentType:    documentType,
		DocumentID:      documentID,
		Body:            bytes.NewReader(data),
		RetryOnConflict: &retryOnConflict,
	}

	do, err := updateRequest.Do(context.Background(), j.elastic)

	if err != nil {
		return err
	}

	if do.IsError() {
		return errors.New(do.String())
	}

	defer do.Body.Close()

	return nil
}
