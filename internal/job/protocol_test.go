package job

import (
	"git.gobies.org/shared-platform/foscan/pkg/model"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
)

type DetectSuite struct {
	suite.Suite
}

func TestDetectSuite(t *testing.T) {
	s := &DetectSuite{}

	suite.Run(t, s)
}

func (s *DetectSuite) TestDetect() {
	<PERSON><PERSON>("TestDetect", s.T(), func() {
		<PERSON><PERSON>("pass", func() {
			banner := Detect("10.10.11.74", 80, model.Protocol{
				Name:         "自定义协议",
				BindPort:     "80,443,",
				ProtocolType: "tcp",
				Timeout:      10,
				Judge: []model.RawGrabRuleJudge{
					{
						Logic: []model.RawGrabRuleJudgeLogic{
							{
								Value:     "html",
								Content:   "contains",
								LogicType: "banner",
							},
						},
						SendContent: "b8e3b1e1df2b000c29ecb26a08004500002c305e0000ff06618b0a0a0b990a0a0a36e62c00509ab23a380000000060020400aedc0000020405b4",
					},
				},
			})

			So(banner, ShouldNotBeEmpty)
		})
	})
}

func (s *DetectSuite) Test_logicsProcess() {
	<PERSON><PERSON>("Test_logicsProcess", s.T(), func() {
		<PERSON><PERSON>("contains pass", func() {
			ok := logicsProcess("HTTP/1.1 400 Bad Request\nServer: openresty\nDate: Fri, 28 Jul 2023 06:38:34 GMT\nContent-Type: text/html\nContent-Length: 154\nConnection: close\n\n<html>\n<head><title>400 Bad Request</title></head>\n<body>\n<center><h1>400 Bad Request</h1></center>\n<hr><center>openresty</center>\n</body>\n</html>", model.RawGrabRuleJudge{
				SendContent: "b8e3b1e1df2b000c29ecb26a08004500002c305e0000ff06618b0a0a0b990a0a0a36e62c00509ab23a380000000060020400aedc0000020405b4",
				Logic: []model.RawGrabRuleJudgeLogic{
					{
						Value:     "html",
						Content:   "contains",
						LogicType: "banner",
					},
				},
			})

			So(ok, ShouldEqual, true)
		})

		Convey("equal false", func() {
			ok := logicsProcess("HTTP/1.1 400 Bad Request\nServer: openresty\nDate: Fri, 28 Jul 2023 06:38:34 GMT\nContent-Type: text/html\nContent-Length: 154\nConnection: close\n\n<html>\n<head><title>400 Bad Request</title></head>\n<body>\n<center><h1>400 Bad Request</h1></center>\n<hr><center>openresty</center>\n</body>\n</html>", model.RawGrabRuleJudge{
				SendContent: "b8e3b1e1df2b000c29ecb26a08004500002c305e0000ff06618b0a0a0b990a0a0a36e62c00509ab23a380000000060020400aedc0000020405b4",
				Logic: []model.RawGrabRuleJudgeLogic{
					{
						Value:     "false",
						Content:   "equal",
						LogicType: "banner",
					},
				},
			})

			So(ok, ShouldEqual, false)
		})

		Convey("regular false", func() {
			ok := logicsProcess("HTTP/1.1 400 Bad Request\nServer: openresty\nDate: Fri, 28 Jul 2023 06:38:34 GMT\nContent-Type: text/html\nContent-Length: 154\nConnection: close\n\n<html>\n<head><title>400 Bad Request</title></head>\n<body>\n<center><h1>400 Bad Request</h1></center>\n<hr><center>openresty</center>\n</body>\n</html>", model.RawGrabRuleJudge{
				SendContent: "b8e3b1e1df2b000c29ecb26a08004500002c305e0000ff06618b0a0a0b990a0a0a36e62c00509ab23a380000000060020400aedc0000020405b4",
				Logic: []model.RawGrabRuleJudgeLogic{
					{
						Value:     "regular",
						Content:   "regular",
						LogicType: "banner",
					},
				},
			})

			So(ok, ShouldEqual, false)
		})

		Convey("char_length equal pass", func() {
			ok := logicsProcess("HTTP/1.1 400 Bad Request\nServer: openresty\nDate: Fri, 28 Jul 2023 06:38:34 GMT\nContent-Type: text/html\nContent-Length: 154\nConnection: close\n\n<html>\n<head><title>400 Bad Request</title></head>\n<body>\n<center><h1>400 Bad Request</h1></center>\n<hr><center>openresty</center>\n</body>\n</html>", model.RawGrabRuleJudge{
				SendContent: "b8e3b1e1df2b000c29ecb26a08004500002c305e0000ff06618b0a0a0b990a0a0a36e62c00509ab23a380000000060020400aedc0000020405b4",
				Logic: []model.RawGrabRuleJudgeLogic{
					{
						Value:     "288",
						Content:   "equal",
						LogicType: "char_length",
						Child: []model.RawGrabRuleJudgeChildLogic{
							{
								Value:    "H",
								Content:  "equal",
								Position: "1",
							},
						},
					},
				},
			})

			So(ok, ShouldEqual, true)
		})
	})
}
