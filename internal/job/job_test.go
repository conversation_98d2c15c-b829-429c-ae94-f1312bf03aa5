package job

import (
	"context"
	"data_analysis/internal/sutra"
	"data_analysis/internal/worker"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/structpb"
	"reflect"
	"testing"
)

type JobSuit struct {
	suite.Suite
	job job
}

func TestJobSuit(t *testing.T) {
	s := &JobSuit{}
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)
	s.job = job{
		msg:    &rpcx.DataAnalysisEvent{},
		sutra:  newSutra,
		source: make(map[string]interface{}),
	}
	suite.Run(t, s)
}

func (s *JobSuit) BeforeTest(suiteName, testName string) {
	titel := "国家网络安全产品漏洞库"
	fid := "49f6cecc58c9e370cad8b11fd5df12eb"
	body := `<!DOCTYPE html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="favicon.ico"><title>国家网络安全产品漏洞库</title><link href="css/chunk-088cf292.731831c2.css" rel="prefetch"><link href="css/chunk-123bdb31.21ffd106.css" rel="prefetch"><link href="css/chunk-16d55c5e.59c92408.css" rel="prefetch"><link href="css/chunk-1dba4ef7.943db444.css" rel="prefetch"><link href="css/chunk-25eb5aa9.7ea1f751.css" rel="prefetch"><link href="css/chunk-2df9c786.76c564c6.css" rel="prefetch"><link href="css/chunk-313e83b6.a6a83466.css" rel="prefetch"><link href="css/chunk-3660d352.8a767779.css" rel="prefetch"><link href="css/chunk-3f001010.90d93d8b.css" rel="prefetch"><link href="css/chunk-45475516.85d6b08c.css" rel="prefetch"><link href="css/chunk-45d51394.eae736f1.css" rel="prefetch"><link href="css/chunk-4659fea8.e3d9c62a.css" rel="prefetch"><link href="css/chunk-49eb7a5a.8ddb9085.css" rel="prefetch"><link href="css/chunk-4b5e677e.3470ef39.css" rel="prefetch"><link href="css/chunk-4ee4ad52.30194845.css" rel="prefetch"><link href="css/chunk-52bcca98.d09fc0eb.css" rel="prefetch"><link href="css/chunk-5b96149e.ef3e06cd.css" rel="prefetch"><link href="css/chunk-6d825caf.33baec5c.css" rel="prefetch"><link href="css/chunk-70d2ea68.f9a5f3be.css" rel="prefetch"><link href="css/chunk-7591bc1a.6265090d.css" rel="prefetch"><link href="css/chunk-75b325fc.e6f3f848.css" rel="prefetch"><link href="css/chunk-76f2fd76.c519e0cd.css" rel="prefetch"><link href="css/chunk-77c28cfc.6e9f37fc.css" rel="prefetch"><link href="css/chunk-7c53da18.1bf447c1.css" rel="prefetch"><link href="css/chunk-7dabca25.e72c66e2.css" rel="prefetch"><link href="css/chunk-9f1f4362.952e3c30.css" rel="prefetch"><link href="css/chunk-aa999304.777a9dc2.css" rel="prefetch"><link href="css/chunk-ab4b0c5c.08fd454d.css" rel="prefetch"><link href="css/chunk-ab4c39e6.60a89db3.css" rel="prefetch"><link href="css/chunk-ab617120.f08dbe5d.css" rel="prefetch"><link href="css/chunk-ad585242.3d971621.css" rel="prefetch"><link href="css/chunk-b069b272.a029ff80.css" rel="prefetch"><link href="css/chunk-bb965082.696f0d85.css" rel="prefetch"><link href="css/chunk-c1be8c0e.67238952.css" rel="prefetch"><link href="js/chunk-088cf292.36fc9067.js" rel="prefetch"><link href="js/chunk-123bdb31.47aa8c8c.js" rel="prefetch"><link href="js/chunk-16d55c5e.b6a12c51.js" rel="prefetch"><link href="js/chunk-1dba4ef7.b095f53f.js" rel="prefetch"><link href="js/chunk-25eb5aa9.ce65c761.js" rel="prefetch"><link href="js/chunk-2df9c786.9224f7a3.js" rel="prefetch"><link href="js/chunk-313e83b6.d7c0fa68.js" rel="prefetch"><link href="js/chunk-3660d352.8fe876d4.js" rel="prefetch"><link href="js/chunk-3f001010.691d80da.js" rel="prefetch"><link href="js/chunk-43f42b03.cefc5cdf.js" rel="prefetch"><link href="js/chunk-45475516.37084db6.js" rel="prefetch"><link href="js/chunk-45d51394.1f8e0624.js" rel="prefetch"><link href="js/chunk-4659fea8.90fcd0a9.js" rel="prefetch"><link href="js/chunk-49eb7a5a.b4f18984.js" rel="prefetch"><link href="js/chunk-4b5e677e.4e939ee5.js" rel="prefetch"><link href="js/chunk-4ee4ad52.52b1f5a7.js" rel="prefetch"><link href="js/chunk-52bcca98.50d748fc.js" rel="prefetch"><link href="js/chunk-5b96149e.1a913c53.js" rel="prefetch"><link href="js/chunk-6d825caf.90f460ab.js" rel="prefetch"><link href="js/chunk-70d2ea68.751cece1.js" rel="prefetch"><link href="js/chunk-7591bc1a.4f88efa1.js" rel="prefetch"><link href="js/chunk-75b325fc.c8d12c4e.js" rel="prefetch"><link href="js/chunk-76f2fd76.1813d075.js" rel="prefetch"><link href="js/chunk-77c28cfc.36c72e01.js" rel="prefetch"><link href="js/chunk-7c53da18.ea9e6f37.js" rel="prefetch"><link href="js/chunk-7dabca25.4287a737.js" rel="prefetch"><link href="js/chunk-9f1f4362.a2ce0f6f.js" rel="prefetch"><link href="js/chunk-aa999304.220abb62.js" rel="prefetch"><link href="js/chunk-ab4b0c5c.093a00f6.js" rel="prefetch"><link href="js/chunk-ab4c39e6.2cbdee37.js" rel="prefetch"><link href="js/chunk-ab617120.1c229a47.js" rel="prefetch"><link href="js/chunk-ad585242.af4f27e9.js" rel="prefetch"><link href="js/chunk-b069b272.8a3d67f2.js" rel="prefetch"><link href="js/chunk-bb965082.68671582.js" rel="prefetch"><link href="js/chunk-c1be8c0e.f66083e0.js" rel="prefetch"><link href="css/app.2571f4e2.css" rel="preload" as="style"><link href="css/chunk-vendors.8140bef9.css" rel="preload" as="style"><link href="js/app.83178e04.js" rel="preload" as="script"><link href="js/chunk-vendors.eb704f02.js" rel="preload" as="script"><link href="css/chunk-vendors.8140bef9.css" rel="stylesheet"><link href="css/app.2571f4e2.css" rel="stylesheet"></head><body><noscript><strong>We're sorry but vuls-library doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script src="js/chunk-vendors.eb704f02.js"></script><script src="js/app.83178e04.js"></script></body></html>`
	header := "HTTP/1.1 200 OK Connection: close Content-Length: 4984 Accept-Ranges: bytes Content-Type: text/html Date: Fri, 08 Sep 2023 03:11:12 GMT Etag: \"613734cf-1378\" Last-Modified: Tue, 07 Sep 2021 09:45:51 GMT Server: nginx/1.10.2"

	favicon := map[string]interface{}{
		"url":       "http://************:2088/favicon.ico",
		"phash":     -4311858170287136300,
		"hash":      1361843888,
		"phash_bit": "1100010000101001001100111101011011011110001010110110000111010100",
		"base64":    "AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACsbigArG4oVaxuKKGsbiierG4on6xuKJ",
	}

	m, _ := structpb.NewStruct(favicon)
	faviconAny, _ := anypb.New(m)

	s.job.msg = &rpcx.DataAnalysisEvent{
		TaskInfo: &rpcx.TaskInfo{
			TaskId: "task_id",
			IsIpv6: false,
		},
		Normal: &rpcx.Normal{
			Ip:       "************",
			Port:     2088,
			Protocol: "http",
			Title:    &titel,
			Body:     &body,
			Header:   &header,
			Fid:      &fid,
			Favicon:  faviconAny,
		},
	}
}

func TestNewJob(t *testing.T) {
	type args struct {
		msg   *rpcx.DataAnalysisEvent
		sutra sutra.Sutra
	}

	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)
	tests := []struct {
		name string
		args args
		want Job
	}{
		{
			name: "pass",
			args: args{
				msg:   &rpcx.DataAnalysisEvent{},
				sutra: newSutra,
			},
			want: &job{
				msg:    &rpcx.DataAnalysisEvent{},
				source: make(map[string]interface{}),
				sutra:  newSutra,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewJob(tt.args.msg, tt.args.sutra); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewJob() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestTeradataRuleMatching1 测试 Teradata 规则匹配 - 通过 server 字段
func TestTeradataRuleMatching1(t *testing.T) {
	t.Logf("=== 开始执行测试用例1: 通过 server 字段匹配 ===")

	// 初始化 Sutra 规则引擎
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)

	// 加载地理位置数据库
	err = worker.LoadGeoDb("../../GeoLite2-City.mmdb", "../../GeoLite2-ASN.mmdb", "", "")
	assert.NoError(t, err)

	// 构造测试数据 - 基于你的 ES 索引数据
	server := "teradata-viewpoint"
	banner := "HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=UTF-8\r\nContent-Length: 1234\r\nServer: teradata-viewpoint\r\n\r\n"

	testJob := &job{
		msg: &rpcx.DataAnalysisEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "test_teradata_task",
				IsIpv6: false,
			},
			JobId:  "test_teradata_job",
			Origin: "test_origin", // 不包含 "grab"，测试修复后的逻辑
			Normal: &rpcx.Normal{
				Ip:       "**************",
				Port:     80,
				Protocol: "http",
				Server:   &server,
				Banner:   banner,
			},
		},
		sutra:  newSutra,
		source: make(map[string]interface{}),
	}

	// 执行规则匹配
	result, err := testJob.Run(context.Background())
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果
	t.Logf("【测试用例1结果】匹配结果 - Products: %v", result.Normal.Product)
	t.Logf("【测试用例1结果】匹配结果 - RuleTags: %v", result.Normal.RuleTags)

	// 如果规则匹配成功，应该有产品信息
	if len(result.Normal.Product) > 0 {
		t.Logf("【测试用例1结果】成功匹配到产品: %v", result.Normal.Product)
		assert.Greater(t, len(result.Normal.RuleTags), 0)
	} else {
		t.Logf("【测试用例1结果】未匹配到任何产品")
	}

	t.Logf("=== 测试用例1执行完成 ===\n")
}

// TestTeradataRuleMatching2 测试 Teradata 规则匹配 - 通过 banner 字段
func TestTeradataRuleMatching2(t *testing.T) {
	t.Logf("=== 开始执行测试用例2: 通过 banner 字段匹配 ===")

	// 初始化 Sutra 规则引擎
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)

	// 加载地理位置数据库
	err = worker.LoadGeoDb("../../GeoLite2-City.mmdb", "../../GeoLite2-ASN.mmdb", "", "")
	assert.NoError(t, err)

	// 构造测试数据 - 只有 banner 包含 teradata-viewpoint
	banner := "teradata-viewpoint HTTP Server v1.0"

	testJob := &job{
		msg: &rpcx.DataAnalysisEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "test_teradata_banner_task",
				IsIpv6: false,
			},
			JobId:  "test_teradata_banner_job",
			Origin: "rawgrab", // 包含 "grab"，测试修复后的逻辑
			Normal: &rpcx.Normal{
				Ip:       "**************",
				Port:     80,
				Protocol: "http",
				Banner:   banner,
				// 注意：这里不设置 Server 字段
			},
		},
		sutra:  newSutra,
		source: make(map[string]interface{}),
	}

	// 执行规则匹配
	result, err := testJob.Run(context.Background())
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果
	t.Logf("【测试用例2结果】Banner匹配结果 - Products: %v", result.Normal.Product)
	t.Logf("【测试用例2结果】Banner匹配结果 - RuleTags: %v", result.Normal.RuleTags)

	// 如果规则匹配成功，应该有产品信息
	if len(result.Normal.Product) > 0 {
		t.Logf("【测试用例2结果】通过Banner成功匹配到产品: %v", result.Normal.Product)
		assert.Greater(t, len(result.Normal.RuleTags), 0)
	} else {
		t.Logf("【测试用例2结果】通过Banner未匹配到任何产品")
	}

	t.Logf("=== 测试用例2执行完成 ===\n")
}

// TestTeradataRuleMatching3 测试完整的 ES 索引数据匹配
func TestTeradataRuleMatching3(t *testing.T) {
	t.Logf("=== 开始执行测试用例3: 完整 ES 索引数据匹配 ===")

	// 初始化 Sutra 规则引擎
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)

	// 加载地理位置数据库
	err = worker.LoadGeoDb("../../GeoLite2-City.mmdb", "../../GeoLite2-ASN.mmdb", "", "")
	assert.NoError(t, err)

	// 基于你提供的完整 ES 索引数据构造测试
	server := "Teradata-viewpoint"
	banner := "HTTP/1.1 200 OK\r\nDate: Wed, 30 Jul 2025 15:04:05 GMT\r\nServer: teradata-viewpoint\r\nContent-Type: text/html\r\nContent-Length: 2048\r\nConnection: close\r\n\r\n"
	protocol := "http"

	testJob := &job{
		msg: &rpcx.DataAnalysisEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "test_full_es_data",
				IsIpv6: false,
			},
			JobId:  "test_full_es_job",
			Origin: "crawler", // 测试不同的 Origin
			Normal: &rpcx.Normal{
				Ip:       "**************",
				Port:     80,
				Protocol: protocol,
				Server:   &server,
				Banner:   banner,
			},
		},
		sutra:  newSutra,
		source: make(map[string]interface{}),
	}

	// 执行规则匹配
	result, err := testJob.Run(context.Background())
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 详细验证
	t.Logf("【测试用例3结果】完整数据匹配结果:")
	t.Logf("【测试用例3结果】  - IP: %s", result.Normal.Ip)
	t.Logf("【测试用例3结果】  - Port: %d", result.Normal.Port)
	t.Logf("【测试用例3结果】  - Protocol: %s", result.Normal.Protocol)
	t.Logf("【测试用例3结果】  - Server: %v", result.Normal.Server)
	t.Logf("【测试用例3结果】  - Products: %v", result.Normal.Product)
	t.Logf("【测试用例3结果】  - RuleTags: %v", result.Normal.RuleTags)

	// 验证关键字段是否正确设置
	assert.Equal(t, "**************", result.Normal.Ip)
	assert.Equal(t, uint32(80), result.Normal.Port)
	assert.Equal(t, "http", result.Normal.Protocol)
	assert.NotNil(t, result.Normal.Server)
	assert.Equal(t, "teradata-viewpoint", *result.Normal.Server)

	t.Logf("=== 测试用例3执行完成 ===\n")
}

// TestServerOnlyMatching 测试只有 server 字段的匹配问题
func TestServerOnlyMatching(t *testing.T) {
	t.Logf("=== 开始测试只有 server 字段的匹配问题 ===")

	// 初始化 Sutra 规则引擎
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)

	// 测试1：只有 server 字段（会导致 "neither subdomain nor service type" 错误）
	testData1 := `{"server":"teradata-viewpoint"}`
	t.Logf("测试数据1: %s", testData1)

	products1, err1 := newSutra.Match(context.Background(), testData1)
	t.Logf("测试1结果 - error: %v, products: %v", err1, products1)

	// 测试2：添加 header 字段使其成为 subdomain 类型
	testData2 := `{"server":"teradata-viewpoint","header":"HTTP/1.1 200 OK"}`
	t.Logf("测试数据2: %s", testData2)

	products2, err2 := newSutra.Match(context.Background(), testData2)
	t.Logf("测试2结果 - error: %v, products: %v", err2, products2)

	// 测试3：添加 banner 字段使其成为 service 类型
	testData3 := `{"server":"teradata-viewpoint","banner":"HTTP/1.1 200 OK\r\nServer: teradata-viewpoint"}`
	t.Logf("测试数据3: %s", testData3)

	products3, err3 := newSutra.Match(context.Background(), testData3)
	t.Logf("测试3结果 - error: %v, products: %v", err3, products3)

	t.Logf("=== 只有 server 字段的匹配问题测试完成 ===")
}

func (s *JobSuit) Test_job_Run() {
	Convey("Test_job_Run", s.T(), func() {
		Convey("pass", func() {
			err := worker.LoadGeoDb("../../GeoLite2-City.mmdb", "../../GeoLite2-ASN.mmdb", "", "")
			So(err, ShouldBeNil)
			defer gomonkey.ApplyMethodReturn(s.job.sutra, "Match", nil, nil).Reset()
			_, err = s.job.Run(context.Background())
			So(err, ShouldBeNil)
		})

		Convey("products", func() {
			err := worker.LoadGeoDb("../../GeoLite2-City.mmdb", "../../GeoLite2-ASN.mmdb", "", "")
			So(err, ShouldBeNil)
			defer gomonkey.ApplyMethodReturn(s.job.sutra, "Match", []*structs.Product{
				{
					Category:       "Service",
					ParentCategory: "服务",
					Name:           "NGINX",
					RuleId:         "209",
					SoftHard:       "2",
					Company:        "Nginx",
				},
			}, nil).Reset()
			_, err = s.job.Run(context.Background())
			So(err, ShouldBeNil)
		})
	})
}
