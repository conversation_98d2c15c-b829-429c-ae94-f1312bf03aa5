package job

import (
	"crawler/internal/config"
	"crawler/internal/http"
	"crawler/internal/req"
	"crawler/internal/worker"
	"errors"
	"git.gobies.org/goby/crawler"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"reflect"
	"testing"
)

type JobSuit struct {
	suite.Suite
	job job
}

func TestCrawlerSuit(t *testing.T) {
	s := &JobSuit{}
	s.job = job{
		msg: &rpcx.CrawlerEvent{
			TaskInfo: &rpcx.TaskInfo{
				TaskId: "1",
			},
			Url:   "https://************",
			JobId: "job_id",
		},
		conf: config.CrawlerConf{
			MaxCrawlLinks:        1,
			MaxCrawlLinksPerHost: 1,
			MaxLinks:             1,
			Type:                 "ajax",
		},
	}

	suite.Run(t, s)
}

func TestNewJob(t *testing.T) {
	type args struct {
		msg  *rpcx.CrawlerEvent
		conf config.CrawlerConf
	}
	tests := []struct {
		name string
		args args
		want Job
	}{
		{
			name: "pass",
			args: args{
				msg:  &rpcx.CrawlerEvent{},
				conf: config.CrawlerConf{},
			},
			want: &job{
				msg:  &rpcx.CrawlerEvent{},
				conf: config.CrawlerConf{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewJob(tt.args.msg, tt.args.conf); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewJob() = %v, want %v", got, tt.want)
			}
		})
	}
}

func (s *JobSuit) Test_job_Normal() {
	Convey("Test_job_Normal", s.T(), func() {
		Convey("http.RequestWithLocationWithHeader error", func() {
			defer ApplyFuncReturn(http.RequestWithLocationWithHeader, nil, 0, errors.New("")).Reset()
			_, err := s.job.Normal()
			So(err, ShouldBeError)
		})

		Convey("worker.ProcHttpsReq error", func() {
			defer ApplyFuncReturn(http.RequestWithLocationWithHeader, nil, 0, nil).Reset()
			defer ApplyFuncReturn(worker.ProcHttpsReq, nil, errors.New("")).Reset()
			_, err := s.job.Normal()
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(http.RequestWithLocationWithHeader, &req.Resp{}, 0, nil).Reset()
			defer ApplyFuncReturn(worker.ProcHttpsReq, nil, nil).Reset()
			defer ApplyFuncReturn(http.GetHttpInfo, make(map[string]interface{})).Reset()
			defer ApplyFuncReturn(worker.ProcGetDom).Reset()
			defer ApplyFuncReturn(worker.IsNeedReqIcon, false).Reset()
			_, err := s.job.Normal()
			So(err, ShouldBeNil)
		})
	})
}

func (s *JobSuit) Test_job_SiteURL() {
	Convey("Test_job_SiteURL", s.T(), func() {
		Convey("Crawler.Start error", func() {
			defer ApplyMethodReturn(&crawler.Crawler{}, "Start", errors.New("")).Reset()
			err := s.job.SiteURL(micro.NewService())
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(&crawler.Crawler{}, "Start", nil).Reset()
			defer ApplyMethodReturn(&crawler.Crawler{}, "GetURLs", []*crawler.URL{
				{
					URL:      "",
					HostInfo: "HostInfo",
				},
			}).Reset()
			defer ApplyMethodReturn(micro.NewService().Client(), "Publish", nil).Reset()
			err := s.job.SiteURL(micro.NewService())
			So(err, ShouldBeNil)
		})

	})
}
