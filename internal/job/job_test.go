package job

import (
	"encoding/json"
	"errors"
	"git.gobies.org/fofa-backend/rawgrab/grab"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/rawgrab"
	tag "git.gobies.org/goby/bmhtags"
	mm "git.gobies.org/shared-platform/foscan/pkg/model"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"rawgrab/internal/model"
	"strconv"
	"testing"
)

type JobSuit struct {
	suite.Suite
	job *job
}

func TestJobSuit(t *testing.T) {
	s := &JobSuit{}

	suite.Run(t, s)
}

func (s *JobSuit) BeforeTest(suiteName, testName string) {
	conf := &model.Config{}
	grabOptions := make([]func(plugin *grab.GrabPlugin), 0)

	// 初始化服务器ip
	grabOptions = append(grabOptions, grab.WithInitRegServerIp(conf.ServerIp))

	// 初始化信任证书
	if conf.TrustCertFile != "" {
		grabOptions = append(grabOptions, grab.WithInitTrustCerts(conf.TrustCertFile))
	}

	optGraber := make([]func(scanner rawgrab.GrabScanner), 0, 3)

	if conf.TraverseAllProtocol {
		optGraber = append(optGraber, grab.WithAllRetry())
	}

	//加载特定产品的重试协议
	optGraber = append(optGraber, grab.WithProductType("fofa/foeye"))

	defaultPlugin := grab.NewGrabPlugin(grabOptions...)
	g := defaultPlugin.NewGrab(optGraber...)
	s.job = &job{
		conf: conf,
		grab: g,
		protocols: []mm.Protocol{
			{
				Name:         "tcp",
				ProtocolType: "0",
				BindPort:     "80,443",
				Judge: []mm.RawGrabRuleJudge{
					{
						Logic: []mm.RawGrabRuleJudgeLogic{
							{
								Value: "1",
							},
						},
					},
				},
			},
			{
				Name:         "udp",
				ProtocolType: "1",
				BindPort:     "80,443",
				Judge: []mm.RawGrabRuleJudge{
					{
						Logic: []mm.RawGrabRuleJudgeLogic{
							{
								Value: "1",
							},
						},
					},
				},
			},
		},
	}
}

func (s *JobSuit) TestNewJob() {
	Convey("TestNewJob", s.T(), func() {
		Convey("pass", func() {
			r := NewJob(s.job.conf, s.job.grab, s.job.protocols)
			So(r, ShouldResemble, s.job)
		})
	})
}

func (s *JobSuit) Test_job_CustomGrab() {
	Convey("Test_job_CustomGrab", s.T(), func() {
		Convey("pass", func() {
			defer ApplyFuncReturn(Detect, []byte("banner")).Reset()
			_, err := s.job.CustomGrab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         80,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("error", func() {
			defer ApplyFuncReturn(Detect, nil).Reset()
			port := uint32(80)
			_, err := s.job.CustomGrab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         port,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeError)
		})
	})
}

func (s *JobSuit) Test_job_Grab() {
	Convey("Test_job_Grab", s.T(), func() {
		Convey("CustomGrab result", func() {

			defer ApplyMethodReturn(s.job, "CustomGrab", nil, nil).Reset()
			port := uint32(80)
			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         port,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("pass UnknownProtocolInDb false", func() {
			defer ApplyMethodReturn(s.job.grab, "Grab", &rawgrab.GrabResult{
				IP:   "***********",
				Port: "80",
			}).Reset()
			port := uint32(80)
			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         port,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeError)
		})

		Convey("enc.Encode error", func() {
			defer ApplyMethodReturn(s.job.grab, "Grab", &rawgrab.GrabResult{
				IP:     "***********",
				Port:   "80",
				Status: "success",
			}).Reset()

			defer ApplyMethodReturn(&json.Encoder{}, "Encode", errors.New("")).Reset()
			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         80,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeError)
		})

		Convey("strconv.ParseInt error", func() {
			defer ApplyMethodReturn(s.job.grab, "Grab", &rawgrab.GrabResult{
				IP:     "***********",
				Port:   "80",
				Status: "success",
			}).Reset()

			defer ApplyFuncReturn(strconv.ParseInt, int64(0), errors.New("")).Reset()
			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         80,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			certs := make(map[string]interface{})
			certs["aa"] = "bb"
			defer ApplyMethodReturn(s.job.grab, "Grab", &rawgrab.GrabResult{
				IP:       "***********",
				Port:     "80",
				Protocol: "http",
				Status:   "success",
				Jarm: &rawgrab.JarmInfo{
					Group: "",
					Hash:  "hash",
				},
				Certs:  certs,
				Banner: "HTTP/1.1 301 Moved Permanently Server: nginx Date: Wed, 26 Jul 2023 13:07:32 GMT Content-Type: text/html Content-Length: 178 Connection: keep-alive Location: https://***********/",
			}).Reset()

			m := make(map[string]interface{})
			m["modal"] = []string{"modal"}
			m["middleware"] = []string{"middleware"}
			m["appserver"] = []string{"appserver"}
			m["os"] = []string{"os"}
			m["language"] = []string{"language"}
			m["version"] = []string{"version"}

			defer ApplyFuncReturn(tag.Extract, m).Reset()
			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "***********",
				Port:         80,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("pass ipv6", func() {
			certs := make(map[string]interface{})
			certs["aa"] = "bb"
			defer ApplyMethodReturn(s.job.grab, "Grab", &rawgrab.GrabResult{
				IP:       "[2402:93c0:0:1f::1f]",
				Port:     "80",
				Protocol: "http",
				Status:   "success",
				Jarm: &rawgrab.JarmInfo{
					Group: "",
					Hash:  "hash",
				},
				Certs:  certs,
				Banner: "HTTP/1.1 301 Moved Permanently Server: nginx Date: Wed, 26 Jul 2023 13:07:32 GMT Content-Type: text/html Content-Length: 178 Connection: keep-alive Location: https://***********/",
			}).Reset()

			_, err := s.job.Grab(&pb.RawGrabEvent{
				Ip:           "2402:93c0:0:1f::1f",
				Port:         80,
				BaseProtocol: "tcp",
				TaskInfo: &pb.TaskInfo{
					IsIpv6: false,
				},
			})
			So(err, ShouldBeNil)
		})
	})
}
