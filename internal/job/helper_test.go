package job

import (
	"reflect"
	"testing"
)

func TestDecodeString(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			args: args{
				s: "48656c6c6f20476f7068657221",
			},
			want:    []byte("Hello Gopher!"),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := DecodeString(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DecodeString() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDecodeStringForHuman(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			args: args{
				s: "48656c6c6f20476f7068657221",
			},
			want:    "Hello Gopher!",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := DecodeStringForHuman(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeStringForHuman() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("DecodeStringForHuman() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMustDecode(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			args: args{
				str: "48656c6c6f20476f7068657221",
			},
			want:    []byte("Hello Gopher!"),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := MustDecode(tt.args.str)
			if (err != nil) != tt.wantErr {
				t.Errorf("MustDecode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MustDecode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMustEncodeToString(t *testing.T) {
	type args struct {
		data []byte
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				data: []byte("Hello Gopher!"),
			},
			want: "48656c6c6f20476f7068657221",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MustEncodeToString(tt.args.data); got != tt.want {
				t.Errorf("MustEncodeToString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fetchNetBiosInfo(t *testing.T) {
	type args struct {
		protocol string
		banner   string
	}
	tests := []struct {
		name       string
		args       args
		wantNbMac  string
		wantNbName string
	}{
		{
			args: args{
				protocol: "netbios",
				banner:   "NetBIOS Response\nMAC:00:0c:29:b8:3c:ee\nHostname:WIN-2F602TBUNGU<0>\nWORKGROUP<0>\nWIN-2F602TBUNGU<20>",
			},
			wantNbMac:  "00:0c:29:b8:3c:ee",
			wantNbName: "WIN-2F602TBUNGU",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNbMac, gotNbName := fetchNetBiosInfo(tt.args.protocol, tt.args.banner)
			if gotNbMac != tt.wantNbMac {
				t.Errorf("fetchNetBiosInfo() gotNbMac = %v, want %v", gotNbMac, tt.wantNbMac)
			}
			if gotNbName != tt.wantNbName {
				t.Errorf("fetchNetBiosInfo() gotNbName = %v, want %v", gotNbName, tt.wantNbName)
			}
		})
	}
}
