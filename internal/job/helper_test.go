package job

import "testing"

func Test_versionFromStringToNumber(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    float64
		wantErr bool
	}{
		{
			name: "invalid 1",
			args: args{
				s: "a",
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "invalid 2",
			args: args{
				s: "v1.1.1.1.1.2.3.24.4.5.5.6.7.2.1.1",
			},
			want:    1.111125445567211,
			wantErr: false,
		},
		{
			name: "invalid 3",
			args: args{
				s: "v1.1.1.1.1.2.3.24.4.5.5.6.7.2.1.1-alpha",
			},
			want:    1.1008161672166956,
			wantErr: false,
		},
		{
			name: "invalid 4",
			args: args{
				s: "-alpha",
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := versionFromStringToNumber(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("versionFromStringToNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("versionFromStringToNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}
