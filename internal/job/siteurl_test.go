package job

import (
	"crawler/internal/config"
	gcrawler "git.gobies.org/goby/crawler"
	"github.com/stretchr/testify/assert"
	"regexp"
	"testing"
)

func TestParseConfig(t *testing.T) {
	type args struct {
		conf config.CrawlerConf
	}
	tests := []struct {
		name string
		args args
		want *gcrawler.Options
	}{
		{
			name: "",
			args: args{
				conf: config.CrawlerConf{
					MaxCrawlLinks:        1,
					MaxCrawlLinksPerHost: 1,
					MaxLinks:             1,
					Type:                 "ajax",
				},
			},
			want: &gcrawler.Options{
				MaxCrawlLinks:        1,
				MaxCrawlLinksPerHost: 1,
				MaxLinks:             1,
				Type:                 gcrawler.CrawlerTypeAJAX,
				ExcludeUrlFilters: []*regexp.Regexp{
					regexp.MustCompile("https://bam.nr-data.net/"),
					regexp.MustCompile("https://hm.baidu.com/hm.gif"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ParseConfig(tt.args.conf)
			assert.Equal(t, tt.want.Type, got.Type)
			assert.Equal(t, tt.want.MaxCrawlLinks, got.MaxCrawlLinks)
			assert.Equal(t, tt.want.MaxLinks, got.MaxLinks)
			assert.Equal(t, tt.want.MaxCrawlLinksPerHost, got.MaxCrawlLinksPerHost)
			assert.Equal(t, tt.want.ExcludeUrlFilters, got.ExcludeUrlFilters)
			t.Log(got.ExcludeUrlFilters)
		})
	}
}
