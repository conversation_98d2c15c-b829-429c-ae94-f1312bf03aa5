package job

import (
	"crawler/internal/config"
	gcrawler "git.gobies.org/goby/crawler"
	"go-micro.dev/v4/logger"
	"regexp"
	"strings"
	"sync"
	"time"
)

var (
	once sync.Once
	opt  *gcrawler.Options
)

func ParseConfig(conf config.CrawlerConf) *gcrawler.Options {
	once.Do(func() {
		opt = gcrawler.NewOptions("")
		opt.Debug = conf.Debug
		opt.UniqParams = conf.UniqParams
		opt.LoadTimeout = conf.LoadTimeout * time.Second
		opt.MaxLinks = conf.MaxLinks
		opt.MaxCrawlLinks = conf.MaxCrawlLinks
		opt.MaxCrawlLinksPerHost = conf.MaxCrawlLinksPerHost
		opt.AutoScanDirsFromURL = conf.AutoScanDirsFromURL
		opt.ScanIndexPageWithDirList = conf.ScanIndexPageWithDirList
		if conf.ExcludeUrlFilters != "" {
			for _, excUrl := range strings.Split(conf.ExcludeUrlFilters, ",") {
				opt.ExcludeUrlFilters = append(opt.ExcludeUrlFilters, regexp.MustCompile(excUrl))
			}
		}

		if conf.Type == "html" {
			opt.Type = gcrawler.CrawlerTypeHTML
		} else if conf.Type == "ajax" {
			opt.Type = gcrawler.CrawlerTypeAJAX
		}

		switch conf.Scope {
		case 0:
			opt.SetScope(gcrawler.CrawlerScopeHost)

		case 1:
			opt.SetScope(gcrawler.CrawlerScopeSubDir)

		case 2:
			opt.SetScope(gcrawler.CrawlerScopeIP)

		case 3:
			opt.SetScope(gcrawler.CrawlerScopeDomain)

		case 4:
			opt.SetScope(gcrawler.CrawlerScopeURL)
		}

		logger.Infof("goby crawler option %v", opt)
	})

	return opt
}
