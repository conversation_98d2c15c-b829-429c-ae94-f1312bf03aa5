package job

import (
	"bytes"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"go-micro.dev/v4/logger"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// Logic type related constant definition.
const (
	LogicTypeForBanner     = "banner"
	LogicTypeForCharLength = "char_length"
)

// Content related constant definition.
const (
	ContentForContains = "contains"
	ContentForEqual    = "equal"
	ContentForRegular  = "regular"
)

// Content related constant definition.
const (
	LogicMarkForAnd = "&&"
	LogicMarkForOr  = "||"
)

const (
	DataPacketReceiveMaxNumber = 16
)

// Detect singleJudgesProcess 单个自定义协议设别判断.
func Detect(ip string, port uint, rule model.Protocol) []byte {
	network := "tcp"

	if rule.ProtocolType == "1" {
		network = "udp"
	}

	conn, err := net.Dial(network, fmt.Sprintf("%s:%d", ip, port))
	if err != nil {
		logger.Warn("custom protocol net.Dial failed: ", err)
		return nil
	}
	defer conn.Close()

	bannerBuffer := bytes.Buffer{}
	defer bannerBuffer.Reset()

	// 通过一个迭代就单个协议下的所有验证规则是否通过
	for _, judge := range rule.Judge {
		if len(judge.Logic) == 0 {
			continue
		}

		// Will send content.
		send, err := MustDecode(judge.SendContent)
		if err != nil {
			logger.Errorf("Hex encode with must decode has error, err: %s, reason: %s",
				err,
				"Check whether the packet content is in hexadecimal format",
			)
			continue
		}

		_, err = conn.Write(send)
		if err != nil {
			logger.Warn("custom protocol conn.Write failed: ", err)
			continue
		}

		var read int
		buffer := bytes.Buffer{}
		for i := 0; i < DataPacketReceiveMaxNumber; i++ {
			err = conn.SetReadDeadline(time.Now().Add(time.Duration(rule.Timeout) * time.Second))
			if err != nil {
				logger.Warn("custom protocol conn.SetReadDeadline failed: ", err)
				continue
			}

			r := make([]byte, 1024)
			read, err = conn.Read(r)
			if err != nil {
				logger.Warn("custom protocol conn.Read failed: ", err)
				continue
			}
			data := r[:read]
			buffer.Write(data)
			bannerBuffer.Write(data)

			if i == DataPacketReceiveMaxNumber {
				break
			}
		}

		encode := MustEncodeToString(buffer.Bytes())
		human, err := DecodeStringForHuman(encode)
		if err != nil {
			logger.Warn("custom protocol DecodeStringForHuman failed: ", err)
			continue
		}

		// 单条发包内容的判断规则是否验证通过.
		if !logicsProcess(human, judge) {
			return nil
		}
	}
	return bannerBuffer.Bytes()
}

// logicsProcess Custom protocol logics process.
func logicsProcess(human string, judge model.RawGrabRuleJudge) bool {
	// If banner information is empty, will quit.
	success := false
	if human == "" {
		return false
	}

	// Logic process result slice.
	processes := make([]bool, 0)
	processesStr := make([]string, 0)

	// Logic mark string, value will round in && or ||
	var logicMark string

	// Response banner information judge the logic process.
	for idx, logic := range judge.Logic {
		// Assign logic mark string.
		if idx == 1 {
			logicMark = logic.NextRelation
		}

		// TODO: More then logic extras of needs.
		switch logic.LogicType {
		case LogicTypeForBanner:

			bannerSuccess := false

			switch logic.Content {
			case ContentForContains:
				bannerSuccess = strings.Contains(human, logic.Value)

				logger.Infof("LogicTypeForBanner with ContentForContains calc result: %t", bannerSuccess)
			case ContentForEqual:
				// True.
				compare := strings.Compare(human, logic.Value)
				if compare == 0 {
					bannerSuccess = true
				}

				logger.Infof("LogicTypeForBanner with ContentForEqual calc result: %t", bannerSuccess)
			case ContentForRegular:
				// Regular.
				matched, err := regexp.MatchString(logic.Value, human)
				if err != nil {
					bannerSuccess = false
				} else {
					bannerSuccess = matched
				}

				logger.Infof("LogicTypeForBanner with ContentForRegular calc result: %t, regular: %s",
					bannerSuccess, logic.Value)
			}

			processes = append(processes, bannerSuccess)
		case LogicTypeForCharLength:
			if logic.Content == ContentForEqual {
				equalSuccess := false

				val, err := strconv.Atoi(logic.Value)
				if err != nil {
					logger.Info("value not is number")
					continue
				}

				// Human banner context related variables.
				hc := []rune(human)
				hl := len(hc)

				if hl == val {
					// Because logic type is char length exists child logic judge, so...
					if len(logic.Child) != 0 {
						last := logic.Child[0]
						if last.Content != ContentForEqual {
							continue
						}

						position, err := strconv.Atoi(last.Position)
						if err != nil {
							continue
						}

						if !(position > 0 && position < hl) {
							continue
						}

						if string(hc[position-1]) == last.Value {
							equalSuccess = true
						}
					}
				}

				logger.Infof("LogicTypeForCharLength calc, result:%s, logic: %s, banner.len: %d", equalSuccess, logic, hl)

				processes = append(processes, equalSuccess)
			}
		}
	}

	// Base logic mark string, processing specific result.
	for _, process := range processes {
		if process {
			processesStr = append(processesStr, "true")
		} else {
			processesStr = append(processesStr, "false")
		}
	}

	r := strings.Join(processesStr, ",")
	if logicMark == "" {
		if strings.Contains(r, "true") {
			success = true
		}
	} else {
		switch logicMark {
		case LogicMarkForAnd:
			if !strings.Contains(r, "false") {
				success = true
			}
		case LogicMarkForOr:
			if strings.Contains(r, "true") {
				success = true
			}
		}
	}

	logger.Infof("Logic process result, human: %s, judge: %+v, logic_mark: %s, processes: %+v, result %+v",
		human,
		judge,
		logicMark,
		processes,
		success,
	)

	return success
}
