package url

import (
	"fmt"
	"github.com/goware/urlx"
	"github.com/imroc/domain"
	"github.com/pkg/errors"
	"net"
	"net/url"
	"strings"
)

// 省略http:// 不省略https://
// 省略默认端口
type HostInfo struct {
	Url       string
	Host      string
	OnlyHost  string
	Domain    string
	Subdomain string
}

func procUrl(scheme, url, port string) string {
	newUrl := url

	// http协议的80端口去掉
	if scheme == "http" && port == "80" {
		newUrl = strings.ReplaceAll(url, ":80", "")
		newUrl = strings.ReplaceAll(newUrl, "http://", "")
	}

	// https协议的443端口去掉
	if scheme == "https" && port == "443" {
		newUrl = strings.ReplaceAll(url, ":443", "")
	}

	// http的网站去掉http://
	if !strings.Contains(url, "https://") {
		newUrl = strings.ReplaceAll(newUrl, "http://", "")
	}

	return newUrl
}

func GetHostInfo1(targetURL string, isIPv6 bool) (h []*HostInfo, err error) {
	// 解析 URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return nil, err
	}

	hostname := parsedURL.Hostname()

	ipAddrs, err := net.LookupIP(hostname)
	if err != nil {
		return nil, err
	}

	for _, ip := range ipAddrs {
		fmt.Println(ip.String())
		if ipv4 := ip.To4(); ipv4 != nil {
			fmt.Println("IPv4:", ipv4)
		} else if ipv6 := ip.To16(); ipv6 != nil {
			fmt.Println("IPv6:", ipv6)
		}
	}

	return nil, nil
}

func GetHostInfo(url string) (hostinfo *HostInfo, err error) {
	// 根域名
	u, err := domain.Parse(url)
	if err != nil {
		err = errors.Wrap(err, "域名解析失败")
		return
	}
	hostinfo = new(HostInfo)
	if u.Domain != "" && u.PublicSuffix != "" {
		hostinfo.Domain = u.Domain + "." + u.PublicSuffix
	}

	hostinfo.Subdomain = u.Subdomain

	// Host,OnlyHost
	uu, err := urlx.Parse(url)
	if err != nil {
		err = errors.Wrap(err, "url解析失败")
		return
	}
	port := uu.Port()
	hostname := uu.Hostname()
	hostinfo.OnlyHost = hostname
	hostinfo.Url = procUrl(uu.Scheme, url, port)

	chgHostname := hostname
	if strings.Contains(hostname, ":") {
		chgHostname = "[" + hostname + "]"
	}

	host := ""
	if uu.Scheme == "https" {
		host = "https://" + chgHostname
		if port != "" && port != "443" {
			host += ":" + port
		}
		hostinfo.Host = host
		return
	}
	if port != "" && port != "80" {
		host = chgHostname + ":" + port
	} else {
		host = chgHostname
	}
	hostinfo.Host = host
	return
}
