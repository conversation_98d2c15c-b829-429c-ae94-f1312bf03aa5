package url

import (
	"errors"
	"github.com/goware/urlx"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestProcUrl(t *testing.T) {
	var origUrl, newUrl, scheme, port string

	// ----------- 1: http IP
	scheme = "http"
	origUrl = "127.0.0.1"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "127.0.0.1", newUrl)

	origUrl = "127.0.0.1:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "127.0.0.1", newUrl)

	origUrl = "127.0.0.1:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "127.0.0.1:808", newUrl)

	origUrl = "***********:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "***********:443", newUrl)

	// 带http://的
	origUrl = "http://***********"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "***********", newUrl)

	origUrl = "http://***********:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "***********", newUrl)

	origUrl = "http://***********:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "***********:808", newUrl)

	origUrl = "http://***********:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "***********:443", newUrl)

	// ----------- 2: http域名
	scheme = "http"
	origUrl = "www.baidu.com"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "www.baidu.com:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "www.baidu.com:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com:808", newUrl)

	origUrl = "www.baidu.com:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com:443", newUrl)

	// 带http://的
	origUrl = "http://www.baidu.com"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "http://www.baidu.com:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "http://www.baidu.com:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com:808", newUrl)

	origUrl = "http://www.baidu.com:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "www.baidu.com:443", newUrl)

	// ----------- 3: https ip
	scheme = "https"
	origUrl = "https://127.0.0.1"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://127.0.0.1", newUrl)

	origUrl = "https://127.0.0.1:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://127.0.0.1:80", newUrl)

	origUrl = "https://127.0.0.1:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://127.0.0.1:808", newUrl)

	origUrl = "https://***********:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://***********", newUrl)

	// 带http://的
	origUrl = "https://***********"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://***********", newUrl)

	origUrl = "https://***********:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://***********:80", newUrl)

	origUrl = "https://***********:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://***********:808", newUrl)

	origUrl = "https://***********:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://***********", newUrl)

	// ----------- 4: https域名
	scheme = "https"
	origUrl = "https://www.baidu.com"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com", newUrl)

	origUrl = "https://www.baidu.com:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com:80", newUrl)

	origUrl = "https://www.baidu.com:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com:808", newUrl)

	origUrl = "https://www.baidu.com:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com", newUrl)

	// 带http://的
	origUrl = "https://www.baidu.com:80"
	port = "80"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com:80", newUrl)

	origUrl = "https://www.baidu.com:808"
	port = "808"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com:808", newUrl)

	origUrl = "https://www.baidu.com:443"
	port = "443"
	newUrl = procUrl(scheme, origUrl, port)
	assert.Equal(t, "https://www.baidu.com", newUrl)
}

func TestGetHostInfo(t *testing.T) {
	subTest := []struct {
		url         string
		expHostInfo HostInfo
		expErr      error
	}{
		{
			url: "https://feedback.hoobill.com",
			expHostInfo: HostInfo{
				Url:       "https://feedback.hoobill.com",
				Host:      "https://feedback.hoobill.com",
				OnlyHost:  "feedback.hoobill.com",
				Domain:    "hoobill.com",
				Subdomain: "feedback",

				// feedback.hoobill.com:80 => https
				// https://feedback.hoobill.com => https
			},
			expErr: nil,
		},

		{
			url: "https://***********:995",
			expHostInfo: HostInfo{
				Url:       "https://***********:995",
				Host:      "https://***********:995",
				OnlyHost:  "***********",
				Domain:    "",
				Subdomain: "",
			},
			expErr: nil,
		},
		{
			url:    "",
			expErr: errors.New("域名解析失败"),
		},
	}

	for _, subT := range subTest {
		info, err := GetHostInfo(subT.url)
		if subT.expErr == nil {
			assert.Equal(t, subT.expErr, err)
		} else {
			assert.NotEqual(t, nil, err)
			continue
		}

		assert.Equal(t, subT.expHostInfo.Url, info.Url)
		assert.Equal(t, subT.expHostInfo.Host, info.Host)
		assert.Equal(t, subT.expHostInfo.OnlyHost, info.OnlyHost)
		assert.Equal(t, subT.expHostInfo.Domain, info.Domain)
		assert.Equal(t, subT.expHostInfo.Subdomain, info.Subdomain)
	}
}

func TestGetHostInfo2(t *testing.T) {
	url := "http://uatxyd2_1.cmschina.com.cn"
	u, err := urlx.Parse(url)
	assert.Nil(t, err)
	assert.Equal(t, "uatxyd2_1.cmschina.com.cn", u.Host)
	//hostInfo, err := GetHostInfo(url)
	//fmt.Print(hostInfo, err)
}
