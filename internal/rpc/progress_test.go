package rpc

import (
	"github.com/stretchr/testify/assert"
	"log"
	"net/http"
	"net/rpc"
	"testing"
	"time"
)

type PortscanRPC struct {
	cbTaskProgress RpcPortscanCallback
}

// 任务进度回调
type RpcPortscanCallback func(req TaskProgressReq) error

func (psr *PortscanRPC) TaskProgress(req *TaskProgressReq, resp *RPCResp) error {
	return nil
}

func RpcPortscanProgress(req TaskProgressReq) error {
	return nil
}

func NewRpcServer(addr string) {
	newPortscanRpcServer(RpcPortscanProgress)
	rpc.HandleHTTP()

	log.Println("start rpc server on:", addr)
	err := http.ListenAndServe(addr, nil)
	if err != nil {
		log.Fatal("[ERROR] start rpc server failed:", err)
	}

	return
}

func newPortscanRpcServer(rpcb RpcPortscanCallback) {
	psRpc := new(PortscanRPC)
	psErr := rpc.Register(psRpc)
	if psErr != nil {
		log.Fatal("[ERROR] portscan rpc register failed", psErr)
	}

	psRpc.cbTaskProgress = rpcb
	return
}

func TestRunRpcClient(t *testing.T) {
	addr := "127.0.0.1:56521"

	go NewRpcServer(addr)

	// 稍微暂停一下
	time.Sleep(300 * time.Millisecond)

	rpcCli := RunRpcClient(addr)
	assert.NotNil(t, rpcCli)

	rpcCli.SendProgress("100", "127.0.0.1", "2", "10", "0.00", "12:33", "")

	var req TaskProgressReq
	rpcCli.retryNotFinishReq(req)

	err := rpcCli.Reconnect()
	assert.Nil(t, err)
}

func TestRunRpcClientErr(t *testing.T) {
	addr := "127.0.0.1:66521"

	rpcCli := RunRpcClient(addr)
	assert.NotNil(t, rpcCli)

	err := rpcCli.Reconnect()
	assert.NotNil(t, err)
}
