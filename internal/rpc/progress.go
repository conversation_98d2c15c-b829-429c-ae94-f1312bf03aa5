package rpc

import (
	"log"
	"net/rpc"
	"strings"
	"sync"
	"time"
)

type TaskProgressReq struct {
	TaskId        string
	IP            string
	State         string
	HostCompleted string
	Progress      string
	RemainTime    string
	ErrMsg        string
}

type RPCResp struct {
	ProcState bool
	Msg       string
}

type RPCClient struct {
	client     *rpc.Client
	mu         sync.Mutex
	serverAddr string // rpc服务器地址
}

func RunRpcClient(addr string) *RPCClient {
	cli, err := rpc.DialHTTP("tcp", addr)
	if err != nil {
		log.Println("[FATAL] connect rpc:", err)
	}

	log.Println("connect rpc server:", addr, "success")
	return &RPCClient{
		client:     cli,
		serverAddr: addr,
	}
}

/*
state取值：
*/
func (rc *RPCClient) SendProgress(tid, ip, state, hostCompleted, progress, remainTime, errMsg string) {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	req := TaskProgressReq{
		tid,
		ip,
		state,
		hostCompleted,
		progress,
		remainTime,
		errMsg,
	}
	var resp RPCResp

	err := rc.client.Call("PortscanRPC.TaskProgress", &req, &resp)
	if err != nil {
		log.Println("RPC-cli send-progress call error:", err)

		// 完成回调需要重试
		if state > "2" {
			go rc.retryNotFinishReq(req)
		}

		if strings.Contains(err.Error(), "connection is shut down") {
			rc.Reconnect()
		}
	}

	log.Printf("  rpc req tid:%s ip:%s state:%s hostcompleted:%s progress:%s remain:%s errmsg:%s  resp:%t  respmsg:%s\n",
		req.TaskId, req.IP, req.State, req.HostCompleted, req.Progress, req.RemainTime, req.ErrMsg, resp.ProcState, resp.Msg)
}

func (rc *RPCClient) Reconnect() error {
	cli, err := rpc.DialHTTP("tcp", rc.serverAddr)
	if err != nil {
		log.Println("[ERROR] reconnect rpc:", err)
		return err
	}

	log.Println("reconnect rpc server:", rc.serverAddr, "success")
	rc.client = cli

	return nil
}

// 将未完成的结束任务重试后发送
func (rc *RPCClient) retryNotFinishReq(req TaskProgressReq) {
	valid := false
	// 最长5分钟
	for i := 0; i < 300; i++ {
		err := rc.Reconnect()
		if err == nil {
			valid = true
			break
		}

		time.Sleep(1 * time.Second)
	}

	if valid {
		rc.SendProgress(req.TaskId, req.IP, req.State, req.HostCompleted, req.Progress, "", req.ErrMsg)
	}
}
