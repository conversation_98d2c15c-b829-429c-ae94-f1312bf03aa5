package http

import (
	"strings"
)

// 反转字符串
func reverseStr(s string) string {
	r := []rune(s)
	for i, j := 0, len(r)-1; i < len(r)/2; i, j = i+1, j-1 {
		r[i], r[j] = r[j], r[i]
	}

	return string(r)
}

// 将子域名拆分
func SubdomainSplitScan(subdomain, domain, publicSuffix string, isHttps bool, m map[string]bool) {
	if strings.Count(subdomain, ".") <= 0 {
		return
	}

	var commHost string
	// 子域名全部拆分
	if strings.Contains(subdomain, ".") {
		subdomain = subdomain[strings.Index(subdomain, ".")+1:]
		revSubdomain := reverseStr(subdomain)
		for _, vv := range strings.Split(revSubdomain, ".") {
			commHost = reverseStr(vv) + "." + commHost
			newHost := commHost + domain + "." + publicSuffix
			if isHttps {
				m["https://"+newHost] = true
			} else {
				m[newHost] = true
			}

		}
	}
}

func SubdomainSplit(subdomain, domain, publicSuffix, refer string, m map[string]bool) {
	if strings.Count(subdomain, ".") <= 0 {
		return
	}
	subSptMap := make(map[string]bool)
	if strings.HasPrefix(refer, "https://") {
		SubdomainSplitScan(subdomain, domain, publicSuffix, true, subSptMap)
	} else {
		SubdomainSplitScan(subdomain, domain, publicSuffix, false, subSptMap)
	}

	if len(subSptMap) > 0 {
		for kk, _ := range subSptMap {
			m[kk] = true
		}
	}
}
