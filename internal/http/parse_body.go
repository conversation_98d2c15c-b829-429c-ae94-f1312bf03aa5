package http

import (
	"crawler/internal/util"
	"github.com/imroc/domain"
	"log"
	"regexp"
	"strings"
)

var regUrlCommon = regexp.MustCompile(`(http[s]?:\/\/.*?)[\&\,\% \/\'\"\>\<]`)
var regUrlSrc = regexp.MustCompile(`src=['"]*\/\/([^\/'";\)]*)`)
var regUrlHref = regexp.MustCompile(`href=['"]*\/\/([^\/'";\)]*)`)
var regCert = regexp.MustCompile(`\b([a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5})\b`)

// 正则里面必须要有分组
var regIllegalIP = regexp.MustCompile(`(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])`)

func ScanBody(html, cert, refer string, m map[string]bool) {
	var lenCommon, lenSrc, lenHref, lenCert = 0, 0, 0, 0
	var resultCommon, resultSrc, resultHref, resultCert [][]string
	tmpDomains := make(map[string]bool)
	selfPortCnt := 0

	if html != "" {
		resultCommon = regUrlCommon.FindAllStringSubmatch(html, -1)
		lenCommon = len(resultCommon)
		resultSrc = regUrlSrc.FindAllStringSubmatch(html, -1)
		lenSrc = len(resultSrc)
		resultHref = regUrlHref.FindAllStringSubmatch(html, -1)
		lenHref = len(resultHref)
	}

	if cert != "" {
		resultCert = regCert.FindAllStringSubmatch(cert, -1)
		lenCert = len(resultCert)
	}

	allLen := lenCommon + lenSrc + lenHref + lenCert
	if allLen <= 0 {
		return
	}

	result := make([][]string, allLen)
	if lenCommon > 0 {
		result = append(result, resultCommon...)
	}
	if lenSrc > 0 {
		result = append(result, resultSrc...)
	}
	if lenHref > 0 {
		result = append(result, resultHref...)
	}
	if lenCert > 0 {
		result = append(result, resultCert...)
	}

	// 先对数组进行去重
	mapResult := make(map[string]int)
	for _, val := range result {
		if len(val) <= 1 {
			continue
		}

		chkVal := val[1]
		if _, ok := mapResult[chkVal]; !ok {
			mapResult[chkVal] = 1
		}
	}

	// 获取有效的host/ip
	for match, _ := range mapResult {
		url := match
		url = strings.Replace(url, " ", "", -1)

		// 不包含.字符的无效
		if !strings.Contains(url, ".") {
			continue
		}

		if url == "http://www." || url == "https://ssl." {
			continue
		}

		if url[0] == '.' {
			continue
		}

		u, err := domain.Parse(url)
		if err != nil {
			log.Println(" domain parse failed", url)
			continue
		}
		// 没有获取到域名的无效
		if u.Domain == "" || u.Subdomain == "ssl" {
			log.Printf("  domain invalid refer=%s, newurl=%s, u.domain=%s, u.subdomain=%s,\n",
				refer, url, u.Domain, u.Subdomain)
			continue
		}

		urlDomain := u.Domain + "." + u.PublicSuffix
		// 添加根域名列表
		if _, ok := tmpDomains[urlDomain]; !ok {
			tmpDomains[urlDomain] = true
		}

		host := util.Url2Hostinfo(url)
		port, portErr := util.PortOfUrl(url)
		if portErr != nil {
			log.Printf("scan-body get port error refer:%s  url:%v\n", refer, url)
		} else {
			if port != 80 && port != 443 {
				selfPortCnt += 1
			}
		}

		//if regIllegalIP.MatchString(host) {
		//	log.Printf("host is invalid ip  refer=%s, host=%s,\n", refer, host)
		//	continue
		//}

		m[host] = true

		// 根域名添加www域名后下发
		if strings.Count(host, ".") <= 1 {
			m["www."+host] = true
		}

		// 子域名切分后下发
		SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, url, m)
	} // for map end

	if selfPortCnt > 50 {
		log.Printf("scan-body too much port cnt:%d, refer:%s  hosts:%v\n",
			selfPortCnt, refer, m)
		clearMap(m)
	} else {
		if len(tmpDomains) > 30 {
			log.Printf("scan-body too much rootdomain cnt:%d, refer:%s  hosts:%v\n",
				len(tmpDomains), refer, m)
			clearMap(m)
		}
	}

	log.Printf("scan-body refer:%s, orig:%d, valid:%d, domains:%d, selfPort:%d\n",
		refer, len(mapResult), len(m), len(tmpDomains), selfPortCnt)
}

func clearMap(m map[string]bool) {
	for k := range m {
		delete(m, k)
	}
}
