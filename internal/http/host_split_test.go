package http

import (
	"github.com/imroc/domain"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSubdomainSplit(t *testing.T) {
	var (
		refer string
		m     = make(map[string]bool)
	)
	refer = "safe.priv.uc.360.cn"
	u, err := domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, refer, m)
	assert.Equal(t, len(m), 2)
	assert.Equal(t, m["priv.uc.360.cn"], true)
	assert.Equal(t, m["uc.360.cn"], true)

	//https
	m = make(map[string]bool)
	refer = "https://safe.priv.uc.360.cn"
	u, err = domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, refer, m)
	assert.Equal(t, len(m), 2)
	assert.Equal(t, m["https://priv.uc.360.cn"], true)
	assert.Equal(t, m["https://uc.360.cn"], true)

	m = make(map[string]bool)
	refer = "http://safe.priv.uc.360.cn"
	u, err = domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, refer, m)
	assert.Equal(t, len(m), 2)
	assert.Equal(t, m["priv.uc.360.cn"], true)
	assert.Equal(t, m["uc.360.cn"], true)

	//测试等于空
	subdomain, domains, publicSuffix := "", "", ""
	m = make(map[string]bool)
	refer = "http://safe.priv.uc.360.cn"
	u, err = domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(subdomain, domains, publicSuffix, refer, m)
	assert.Equal(t, len(m), 0)

	//测试等于空
	m = make(map[string]bool)
	refer = ""
	u, err = domain.Parse("http://safe.priv.uc.360.cn")
	assert.Nil(t, err)
	SubdomainSplit(subdomain, domains, publicSuffix, refer, m)
	assert.Equal(t, len(m), 0)

	//www.hbjswm.gov.cn
	m = make(map[string]bool)
	refer = "www.hbjswm.gov.cn"
	u, err = domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, refer, m)
	assert.Equal(t, len(m), 0)

	m = make(map[string]bool)
	refer = "http://priv.uc.360.cn"
	u, err = domain.Parse(refer)
	assert.Nil(t, err)
	SubdomainSplit(u.Subdomain, u.Domain, u.PublicSuffix, refer, m)
	assert.Equal(t, len(m), 1)
	assert.Equal(t, m["uc.360.cn"], true)
}
