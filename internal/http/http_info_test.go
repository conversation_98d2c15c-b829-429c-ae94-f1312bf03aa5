package http

import (
	"bytes"
	"compress/gzip"
	"crawler/internal/util"
	"fmt"
	"github.com/stretchr/testify/assert"
	"net/url"
	"testing"
)

func TestGetTitle(t *testing.T) {
	fmt.Println("----------------test get title-------------------------")
	var bodys string

	// title test"
	bodys = "<title>Comelit 1456S - Index</title><script type=\"text/javascript\" src=\"jquery.min.js\"></script><script type=\"text/javascript\" src=\"jquery.inlineedit.js\"></script><script type=\"text/javascript\">function confirm_reboot() {input_box=confirm(\"Do you really want to reboot?\");if (input_box==true) {window.open ('reboot.html','_self',false)}}</script><script type=\"text/javascript\">function ask_for_confirm(msg, destPage) {input_box=confirm(msg);if (input_box==true) {window.open (destPage,'_self',false)}}function ask_for_confirm_popup(msg, destPage, doRefresh, callback) {input_box=confirm(msg);if (input_box==true) {post_page(destPage, doRefresh, callback);}}</script><script type=\"text/javascript\">function post_page(surl, doRefresh, callback) {	$.post(surl, function(data) {		if (data && data.length > 0) {			if (data.indexOf(\"LOGIN_IS_REQUIRED\") === -1)				alert(data);			if (doRefresh === undefined || doRefresh === null || doRefresh === true) {				window.location.reload(true); 			}		}		else if (doRefresh && doRefresh===true) {			window.location.reload(true);		}		if (typeof callback !== 'undefined' && callback !== null) {			callback();		}	});}</script><script type=\"text/javascript\">function printDiv(divID) {	var divElements = document.getElementById(divID).innerHTML;	var oldPage     = document.body.innerHTML;   var header      = '<table summary=\"Header - Logo\" border=\"0\" width=\"100%\"><tr><td width=\"15%\"><p align=\"center\"><a href=\"http://www.comelitgroup.com\"><img border=\"0\" height=\"20\" src=\"comelit-logo.png\" alt=\"Comelit Logo\"></a></td><td width=\"70%\"><p align=\"center\">&nbsp;<font size=\"5\" color=\"#ffffff\">Comelit 1456S</font></p></td><td width=\"15%\"><p align=\"center\"><a href=\"http://www.comelitgroup.com\"><img border=\"0\" height=\"20\" src=\"comelit-logo.png\" alt=\"Comelit Logo\"></a></td></tr></table>';	document.body.innerHTML = 		\"<html><head><title>Comelit 1456B</title>		</head>"
	assert.Equal(t, "Comelit 1456S - Index", GetBodyTitle(bodys))

	bodys = "<META HTTP-EQUIV=\"Content-Type\" CONTENT=\"text/html; charset=iso-8859-1\"> <!-- Style Sheet link and Meta data     -->\n<TITLE>NETGEAR GS110TP</TITLE> <!-- Netgear Page Title  -->"
	assert.Equal(t, "NETGEAR GS110TP", GetBodyTitle(bodys))

	bodys = "<head>"
	assert.Equal(t, "", GetBodyTitle(bodys))

	bodys = "<head>\n<title>401 Unauthorized</title>\n</head>"
	assert.Equal(t, "401 Unauthorized", GetBodyTitle(bodys))

	bodys = "<meta http-equiv=\"expires\" content=\"0\">\n<title>index</title> \n<script>"
	assert.Equal(t, "index", GetBodyTitle(bodys))

	bodys = "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n<title>D-LINK SYSTEMS, INC. | WIRELESS ROUTER | HOME</title>		\n<script"
	assert.Equal(t, "D-LINK SYSTEMS, INC. | WIRELESS ROUTER | HOME", GetBodyTitle(bodys))

	bodys = "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<title>网站访问报错</title>\n<style type=\"text/css\">"
	assert.Equal(t, "网站访问报错", GetBodyTitle(bodys))

	bodys = "<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_16.png?v=4398\" sizes=\"16x16\"/>\r\n<title>Diskstation216&nbsp;-&nbsp;Synology&nbsp;DiskStation</title>\r\n<link "
	assert.Equal(t, "Diskstation216&nbsp;-&nbsp;Synology&nbsp;DiskStation", GetBodyTitle(bodys))

	bodys = "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\">\n<html>\n  <head>\n    <LINK REL=stylesheet HREF=\"/base/style.css\" TYPE=\"text/css\">\n<META http-equiv=\"Pragma\" content=\"no-cache\">\n<META HTTP-EQUIV=\"Content-Type\" CONTENT=\"text/html; charset=iso-8859-1\"> <!-- Style Sheet link and Meta data     -->\n    <TITLE>NETGEAR GS724T</TITLE> <!-- Netgear Page Title  -->\n\n    <script  src=\"/base/js/tabs_Layer2.js\" type=\"text/javascript\"></script>\n    <script language=\"javascript\">\n      var a1, a2, a3, a4, a5, a6, a7, a8;\n      a1 = new Image(130,29);\n      a1.src = \"/base/images/tab_Login_off.gif\";\n    </script>\n    <table class='tableStyle'>\n                          <tr>\n                            <td colspan='2' class='subSectionTabTopLeft spacer80Percent font12BoldBlue'>Login</td>\n                            <td class='subSectionTabTopRight spacer20Percent'><a href='javascript: void(0);' onClick=\"newWindow('/base/help/help.html#userlogin','userlogin','448','336','resizable=yes')\"><img src='/base/images/help_icon.gif' width='12' height='12' title='Click for help'/></a></td></tr><tr><td colspan='3' class='subSectionTabTopShadow'>&nbsp;</td>\n                          </tr>\n                        </table>\n  </body>\n</html>"
	assert.Equal(t, "NETGEAR GS724T", GetBodyTitle(bodys))

	bodys = "<html><head></head><body><title ID=titletext>建设中</title></body></html>"
	assert.Equal(t, "建设中", GetBodyTitle(bodys))

	bodys = "<html><head></head><body><title data-no-trans=\"\">11STREET : Best Way to K-Shopping</title></body></html>"
	assert.Equal(t, "11STREET : Best Way to K-Shopping", GetBodyTitle(bodys))

	bodys = "<html><head><title>SPC4300 - DISTRISUD MARSSA</title><meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"><meta name=\"google\" value=\"notranslate\"><link href=\"style2.css\" type=\"text/css\" rel=\"stylesheet\"><link rel=\"icon\" type=\"image/vnd.microsoft.icon\" href=\"favicon.ico\"></head><body topmargin=\"0\" leftmargin=\"0\" bgcolor=#ffffff><table border=0 width=782 cellspacing=0 cellpadding=0 align=center><tr><td width=1 bgcolor=#cfcfcf><div style=width:1px;><spacer type=black></spacer></div></td><td width=100%><table border=0 width=100% cellspacing=0 cellpadding=0 align=center><tr><td colspan=3></td></tr><tr><td colspan=3 width=1 bgcolor=#203090><div style=width:1px;><spacer type=black></spacer></div></td></tr><tr><td colspan=3 width=1 bgcolor=#ffffff><div style=width:1px;><spacer type=black></spacer></div></td></tr><html><head><title>SPC4300 </title><meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\"><meta name=\"google\" value=\"notranslate\"><link href=\"style2.css\" type=\"text/css\" rel=\"stylesheet\"><link rel=\"icon\" type=\"image/vnd.microsoft.icon\" href=\"favicon.ico\"></head><body topmargin=\"0\" leftmargin=\"0\" bgcolor=#ffffff><table border=0 width=782 cellspacing=0 cellpadding=0 align=center><tr><td width=1 bgcolor=#cfcfcf><div style=width:1px;><spacer type=black></spacer></div></td><td width=100%><table border=0 width=100% cellspacing=0 cellpadding=0 align=center><tr><td colspan=3><img src = \"logo_v.png\" WIDTH=\"200\";HEIGHT= \"38\" style=\"padding-top: 20px; padding-bottom: 20px; padding-left: 20px \" </td></tr><tr><td colspan=3 width=1 bgcolor=#203090><div style=width:1px;><spacer type=black></spacer></div></td></tr><tr><td colspan=3 width=1 bgcolor=#ffffff><div style=width:1px;><spacer type=black></spacer></div></td></tr><tr height = 1 bgcolor = #3b6492><td align=center colspan = 3></td></tr></table></td></tr></table><table border=0 width=780 cellspacing=0 cellpadding=0 align=center><tr height= 20><td align=center class=innerheading colspan=4><TABLE WIDTH=\"100%\" HEIGHT=\"450\"><TR VALIGN=\"center\" ALIGN=\"center\"><TD><TABLE CLASS=login HEIGHT=120 WIDTH=300 style = \"padding-bottom:10px; padding-top:10px\"><FORM METHOD=\"post\" ACTION=\"login.htm?action=login&language=253\"><TR ALIGN=\"center\"><TD COLSPAN=5 HEIGHT= 10 width = 300><P CLASS=heading4>SPC4300</P></TD></TR><TR ALIGN=\"left\"><TD  style=\"width:5%;\"></TD><TD style=\"width:25%;\"CLASS=heading2>ID Utilisateur:</TD><TD style=\"width:5%;\"></TD><TD style=\"width:60%;\"><INPUT class = \"field\" TYPE=\"text\" NAME=\"userid\" style=\"width:100%\"></TD><TD style=\"width:5%;\"></TD></TR><TR ALIGN=\"left\"><TD></TD><TD CLASS=heading2>Mot de passe:</TD><TD></TD><TD><INPUT TYPE=\"password\" class =\"field\" NAME=\"password\" style=\"width:100%\"></TD><TD></TD></TR><TR ALIGN=\"left\"><TD COLSPAN=4 ALIGN=right><INPUT class = \"submit\" TYPE=\"submit\" VALUE=\"Connexion\"></TD><TD WIDTH=10></TD></TR></FORM></TABLE><P CLASS=heading2></P></TD></TR></TABLE></TD><tr height=8><td colspan=4>&nbsp;</td></tr>     <tr><td style=\"border-top-color:navy; border-top-width:1px; border-top-style:solid;\" colspan=\"4\"></td></tr><tr><td colspan=4 class=footer><table border=0 cellspacing=0 cellpadding=0 style=\"width:780px; text-align:left;\"><tr height=5><td colspan=3><div style=height:5px;><spacer type=black></spacer></div></td>                  </tr></table></td></tr><tr><td class=footer valign=left\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#169Vanderbilt 2015 </td><td class=footer align=right><FORM METHOD=\"post\" ACTION=\"login.htm\"><SELECT NAME=\"language\" onchange=\"submit()\"><OPTION VALUE=\"0\">English</OPTION><OPTION VALUE=\"2\">French</OPTION><OPTION VALUE=\"15\">German</OPTION><OPTION VALUE=\"253\" SELECTED>Langue Utilisateur</OPTION></SELECT> </FORM>        							</td><td width=\"4%\"></td></tr><tr height=8><td colspan=3><div style=height:5px;><spacer type=black></spacer></div></td></tr><tr height=1><td colspan=3 bgcolor=#cfcfcf><div style=width:780px;><spacer type=black></spacer></div></td></tr>	</table><td width=1 bgcolor=#cfcfcf><div style=width:1px;><spacer type=black></spacer></div></td></body></html>"
	assert.Equal(t, "SPC4300 - DISTRISUD MARSSA", GetBodyTitle(bodys))

	fmt.Println("-----------------------------------------")
}

func TestPortOfUrl(t *testing.T) {
	var surl string
	var err error
	var port int

	// http
	surl = "fofa.so"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "fofa.so:88"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 88, port)
	assert.Nil(t, err)

	surl = "http://fofa.so"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "http://fofa.so:80"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "http://fofa.so:81"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 81, port)
	assert.Nil(t, err)

	// https
	surl = "https://fofa.so"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.Nil(t, err)

	surl = "https://www.fofa.so:443"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.Nil(t, err)

	surl = "https://fofa.so:88"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 88, port)
	assert.Nil(t, err)

	surl = "https://fofa.so:80"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "https://fofa.so:81"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 81, port)
	assert.Nil(t, err)

	// inner-ip http
	surl = "**********"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "**********:88"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 88, port)
	assert.Nil(t, err)

	surl = "http://**********"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "http://**********:80"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "http://**********:81"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 81, port)
	assert.Nil(t, err)

	// inner-ip https
	surl = "https://**********"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.Nil(t, err)

	surl = "https://**********:443"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.Nil(t, err)

	surl = "https://**********:88"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 88, port)
	assert.Nil(t, err)

	surl = "https://**********:80"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.Nil(t, err)

	surl = "https://**********:81"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 81, port)
	assert.Nil(t, err)

	// abnormal
	surl = "https://**********#abcd"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.NotNil(t, err)

	surl = "http://**********#abcd"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.NotNil(t, err)

	surl = "http://w-tokyosaito.jp#main"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 80, port)
	assert.NotNil(t, err)

	surl = "https://w-tokyosaito.jp#main"
	port, err = util.PortOfUrl(surl)
	assert.Equal(t, 443, port)
	assert.NotNil(t, err)
}

func TestGzipDecode(t *testing.T) {
	var src, dest []byte
	var retSt error

	src = nil
	retSt = nil
	dest, retSt = GzipDecode(src)
	assert.NotNil(t, retSt)
	assert.Equal(t, 0, len(dest))

	src = []byte("")
	retSt = nil
	dest, retSt = GzipDecode(src)
	assert.NotNil(t, retSt)
	assert.Equal(t, 0, len(dest))

	src = []byte("0")
	retSt = nil
	dest, retSt = GzipDecode(src)
	assert.NotNil(t, retSt)
	assert.Equal(t, 0, len(dest))

	src = []byte("a")
	retSt = nil
	dest, retSt = GzipDecode(src)
	assert.NotNil(t, retSt)
	assert.Equal(t, 0, len(dest))

	src = []byte("abc")
	retSt = nil
	dest, retSt = GzipDecode(src)
	assert.NotNil(t, retSt)
	assert.Equal(t, 0, len(dest))

	retSt = nil
	var b bytes.Buffer
	w := gzip.NewWriter(&b)
	defer w.Close()
	w.Write([]byte("abcd"))
	w.Flush()
	fmt.Println("gzip size:", len(b.Bytes()))
	dest, retSt = GzipDecode(b.Bytes())
	//assert.Nil(t, retSt)
	assert.Equal(t, 4, len(dest))
}

func TestEnsureRune(t *testing.T) {
	var src []byte
	var retStr string

	src = []byte("")
	retStr = EnsureRune(src)
	assert.Equal(t, retStr, "")

	src = []byte("a")
	retStr = EnsureRune(src)
	assert.Equal(t, "a", retStr)
}

func Test_GetIconUrl(t *testing.T) {
	surl := "https://fofa.so/abcd/"
	uu, err := url.Parse(surl)
	// url解析失败后不继续处理
	if err != nil {
		return
	}

	var retUrl, utf8Html, urlFilename string
	var state bool

	utf8Html = "<link rel=\"shortcut icon\" href=\"../favicon.ico\" type=\"image/x-icon\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/../favicon.ico", retUrl)
	urlFilename, _ = GetUrlFilename(retUrl)
	assert.Equal(t, ".ico", urlFilename)

	utf8Html = "<link rel=\"shortcut icon\" href=\"../favicon.png\" type=\"image/x-icon\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/../favicon.png", retUrl)
	urlFilename, _ = GetUrlFilename(retUrl)
	assert.Equal(t, ".png", urlFilename)

	utf8Html = "<link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"/favicon.ico\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/favicon.ico", retUrl)

	utf8Html = "<link id=\"prtgfavicon\" rel=\"shortcut icon\" type=\"image/ico\" href=\"/favicon.ico\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/favicon.ico", retUrl)

	utf8Html = "<link href=\"themes/wk/images/favicon.ico\" rel=\"icon\" type=\"image/x-icon\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/themes/wk/images/favicon.ico", retUrl)

	utf8Html = "<link href=\"themes/wk/images/favicon.ico\" rel=\"shortcut icon\" type=\"image/x-icon\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/themes/wk/images/favicon.ico", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" href=\"/images/watchguard/favicon.ico\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/images/watchguard/favicon.ico", retUrl)

	utf8Html = "<link href=\"/favicon/favicon.ico?v=B2FT-ZOZJ1bO2lzIwse-Vg\" rel=\"shortcut icon\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/favicon/favicon.ico?v=B2FT-ZOZJ1bO2lzIwse-Vg", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"https://kemahasiswaan.ikipsiliwangi.ac.id/wp-content/themes/ikipsiliwangi_prodi_by_riftom/favicon.ico\" /> "
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://kemahasiswaan.ikipsiliwangi.ac.id/wp-content/themes/ikipsiliwangi_prodi_by_riftom/favicon.ico", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"http://kemahasiswaan.ikipsiliwangi.ac.id/wp-content/themes/ikipsiliwangi_prodi_by_riftom/favicon.ico\" /> "
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "http://kemahasiswaan.ikipsiliwangi.ac.id/wp-content/themes/ikipsiliwangi_prodi_by_riftom/favicon.ico", retUrl)

	utf8Html = "<link rel=\"stylesheet\" href=\"/resources/favicon.ico\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, false, state)
	//assert.Equal(t, "/resources/favicon.ico", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" href=\"images/favicon.ico\" type=\"image/x-icon\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/images/favicon.ico", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" href=\"favicon.ico\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/favicon.ico", retUrl)

	utf8Html = "<link href=\"/templates/captivashop/favicon.ico\" rel=\"shortcut icon\" type='image/vnd.microsoft.icon' />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/templates/captivashop/favicon.ico", retUrl)

	utf8Html = "<LINK REL=\"ICON\" HREF=\"/images/favicon.ico?ver=17.5.9.577\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/images/favicon.ico?ver=17.5.9.577", retUrl)

	utf8Html = "<link href=\"/favicon.ico?v=4\" rel=\"shortcut icon\"/>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/favicon.ico?v=4", retUrl)

	utf8Html = "<link href=\"image/top_logo.gif\" type=\"image/gif\" rel=\"icon\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/image/top_logo.gif", retUrl)

	utf8Html = "<link href=\"/image/top_logo.gif\" type=\"image/gif\" rel=\"icon\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/image/top_logo.gif", retUrl)

	utf8Html = "<link rel=icon href=./static/images/FaviconZyxel.png>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/static/images/FaviconZyxel.png", retUrl)

	utf8Html = "<link rel=icon href=/static/images/FaviconZyxel.png>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/static/images/FaviconZyxel.png", retUrl)

	utf8Html = "<link type=image/x-icon href=/static/images/favicon.ico rel=icon>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/static/images/favicon.ico", retUrl)

	utf8Html = "<link rel=\"icon\" href=\"/core/img/favicon.ico\">\n"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/core/img/favicon.ico", retUrl)

	utf8Html = "<head><meta charset=\"utf-8\"/><base href=\"/\"/><meta content=\"width=device-width,initial-scale=1\" name=\"viewport\"/><meta content=\"#081C42\" media=\"(prefers-color-scheme: light)\" name=\"theme-color\"/><meta content=\"#081C42\" media=\"(prefers-color-scheme: dark)\" name=\"theme-color\"/><meta content=\"MinIO Console\" name=\"description\"/><link href=\"./styles/root-styles.css\" rel=\"stylesheet\"/><link href=\"./apple-icon-180x180.png\" rel=\"apple-touch-icon\" sizes=\"180x180\"/><link href=\"./favicon-32x32.png\" rel=\"icon\" sizes=\"32x32\" type=\"image/png\"/><link href=\"./favicon-96x96.png\" rel=\"icon\" sizes=\"96x96\" type=\"image/png\"/><link href=\"./favicon-16x16.png\" rel=\"icon\" sizes=\"16x16\" type=\"image/png\"/><link href=\"./manifest.json\" rel=\"manifest\"/><link color=\"#3a4e54\" href=\"./safari-pinned-tab.svg\" rel=\"mask-icon\"/>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/favicon-16x16.png", retUrl)

	utf8Html = "<LINK REL=\"ICON\" HREF=\"/images/favicon.ico?ver=10.06 build 6533\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/images/favicon.ico?ver=10.06", retUrl)

	utf8Html = "<meta charset=\"utf-8\">\\n<title>LOGIN - *************:8083 - Hestia Control Panel</title>\\n<link rel=\"alternate icon\" href=\"/images/favicon.png\" type=\"image/png\">\\n<link rel=\"icon\" href=\"/images/logo.svg\" type=\"image/svg+xml\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/images/favicon.png", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"../actigis2010/favicon.ico\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/../actigis2010/favicon.ico", retUrl)

	utf8Html = "<html><head><meta charSet=\"utf-8\"/><link rel=\"icon\" href=\"/favicon.ico\"/><link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/apple-touch-icon.png\"/><link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\"/><link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\"/><link rel=\"manifest\" href=\"/site.webmanifest\"/><link rel=\"mask-icon\" href=\"/safari-pinned-tab.svg\" color=\"#5bbad5\"/><meta name=\"msapplication-TileColor\" content=\"#da532c\"/><meta name=\"theme-color\" content=\"#2f2f2f\" media=\"(prefers-color-scheme: dark)\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/favicon-16x16.png", retUrl)

	utf8Html = "<meta name=\"keywords\" content=\"多任务处理,网页应用程序,个人云端\" />\n\n<link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/>\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_32.png?v=4398\" sizes=\"32x32\"/>\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_16.png?v=4398\" sizes=\"16x16\"/>\n<title>faust-nas&nbsp;-&nbsp;Synology&nbsp;DiskStation</title>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/webman/resources/images/icon_dsm_16.png?v=4398", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" href=\"data:image/x-icon;base64,AAABAAEAICAAAAEAIADSAgAAFgAAAIlQTkcNChoKAAAADUlIRFIAAAAgAAAAIAgGAAAAc3p69AAAAplJREFUWIXt1j2IHGUYB/DfOzdnjIKFkECIVWIKvUFsIkRExa9KJCLaWAgWJx4DilZWgpDDiI0wiViIoGATP1CCEDYHSeCwUBBkgiiKURQJFiLo4d0eOxYzC8nsO9m9XcXC+8MW+3z+9/l6l2383xH+iSBpElyTdoda26xsDqp/h0CVZ3vwKm7tMBngAs7h7eRYebG6hMtMBHbMBX89vfARHprQ5U8cwdFQlIOZCVR5di1+w/wWXT/EY6EoN5NZCODuKZLDwzgSMCuBe2fwfX6QZwtpWzqfBBtLC3txF/ZhxKbBGx0EfsTJS77vwmGjlZrD4mUzUOXZjVjGI65cnTXchB8iupdDUb7QinsQZ7GzZftdQj2JVZ49iC/w6JjksIo7OnS9tiA5Vn6GtyK2+1MY5NkhfGDygVrBAxH5WkPuMjR7/3UsUFLl2Q68s4XkA3ws3v9zoSjX28Kr5wL1xrTxa6ou+f6OZGvqPg9v1wZeaUjcELE/DVfNhWFSvy/enOIZ9eq1sTokEMNLWI79oirP8g6fXpVnh7GEvY1sV/OJ4f0UhyKKk6EoX4x5pEkgXv6L6OM99YqNw/c4kXSwG5nkIfpLCynuiahW1GWeJHkfT4aiXO9atz1XcD6I6yLyHu6bIPk6Hg9FeYZ63y9EjBarPDvQ8VJ1nd9V3D4m+RncForyxFCQ4hSeahlej88Hefauurdwaufr5z/F/ZHAX6nL+mZE18e36IWiHLkFocqzW9QXcNz1+wUHxJ/f10JRPjvGP4pk/vj5L3F8AtufdD+/p6dJDknzX+05fDLGtife/766t9MRgFCUffWTudwE3AqBlVCUf0xLYGTQqzzbhydwJ3Y34g318J1tmX+DPBTlz9MS2MY2/nP8DTGaqeTDf30rAAAAAElFTkSuQmCC\" type=\"image/x-icon\" />"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "data:image/x-icon;base64,AAABAAEAICAAAAEAIADSAgAAFgAAAIlQTkcNChoKAAAADUlIRFIAAAAgAAAAIAgGAAAAc3p69AAAAplJREFUWIXt1j2IHGUYB/DfOzdnjIKFkECIVWIKvUFsIkRExa9KJCLaWAgWJx4DilZWgpDDiI0wiViIoGATP1CCEDYHSeCwUBBkgiiKURQJFiLo4d0eOxYzC8nsO9m9XcXC+8MW+3z+9/l6l2383xH+iSBpElyTdoda26xsDqp/h0CVZ3vwKm7tMBngAs7h7eRYebG6hMtMBHbMBX89vfARHprQ5U8cwdFQlIOZCVR5di1+w/wWXT/EY6EoN5NZCODuKZLDwzgSMCuBe2fwfX6QZwtpWzqfBBtLC3txF/ZhxKbBGx0EfsTJS77vwmGjlZrD4mUzUOXZjVjGI65cnTXchB8iupdDUb7QinsQZ7GzZftdQj2JVZ49iC/w6JjksIo7OnS9tiA5Vn6GtyK2+1MY5NkhfGDygVrBAxH5WkPuMjR7/3UsUFLl2Q68s4XkA3ws3v9zoSjX28Kr5wL1xrTxa6ou+f6OZGvqPg9v1wZeaUjcELE/DVfNhWFSvy/enOIZ9eq1sTokEMNLWI79oirP8g6fXpVnh7GEvY1sV/OJ4f0UhyKKk6EoX4x5pEkgXv6L6OM99YqNw/c4kXSwG5nkIfpLCynuiahW1GWeJHkfT4aiXO9atz1XcD6I6yLyHu6bIPk6Hg9FeYZ63y9EjBarPDvQ8VJ1nd9V3D4m+RncForyxFCQ4hSeahlej88Hefauurdwaufr5z/F/ZHAX6nL+mZE18e36IWiHLkFocqzW9QXcNz1+wUHxJ/f10JRPjvGP4pk/vj5L3F8AtufdD+/p6dJDknzX+05fDLGtife/766t9MRgFCUffWTudwE3AqBlVCUf0xLYGTQqzzbhydwJ3Y34g318J1tmX+DPBTlz9MS2MY2/nP8DTGaqeTDf30rAAAAAElFTkSuQmCC", retUrl)

	utf8Html = "<link rel=\"Shortcut Icon\" href=\"http://res.zohi.tv/t/site/10001/9c5ab5ceab7f7925e10de4a9ba9b7b47/assets/images/common/favicon1.ico\" type=\"image/x-icon\"> "
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "http://res.zohi.tv/t/site/10001/9c5ab5ceab7f7925e10de4a9ba9b7b47/assets/images/common/favicon1.ico", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" type=image/x-icon href=static/img/favicon.ico>"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://fofa.so/abcd/static/img/favicon.ico", retUrl)

	utf8Html = "<LINK rel=\"shortcut icon\" href=\"\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, false, state)
	assert.Equal(t, "", retUrl)

	utf8Html = "<link rel=\"shortcut icon\" href=\"//static.ipingxing.com/root/img/index/icon-efd68c66b0604b0eabcaa29a80007f2f4786a691.png\" type=\"image/x-icon\"/> "
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "https://static.ipingxing.com/root/img/index/icon-efd68c66b0604b0eabcaa29a80007f2f4786a691.png", retUrl)

	// http的特殊地址
	surl = "http://fofa.so/abcd/"
	uu, err = url.Parse(surl)
	// url解析失败后不继续处理
	if err != nil {
		return
	}

	utf8Html = "<link rel=\"shortcut icon\" href=\"//static.ipingxing.com/root/img/index/icon-efd68c66b0604b0eabcaa29a80007f2f4786a691.png\" type=\"image/x-icon\"/> "
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, true, state)
	assert.Equal(t, "http://static.ipingxing.com/root/img/index/icon-efd68c66b0604b0eabcaa29a80007f2f4786a691.png", retUrl)

	// 不应该匹配的
	// https: //www.163.com
	utf8Html = "<link rel=\"apple-touch-icon-precomposed\" href=\"//static.ws.126.net/www/logo/logo-ipad-icon.png\" >"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, false, state)

	// 包含black信息
	utf8Html = "<link id=\"icon-link\" rel=\"icon\" type=\"image/x-icon\" href=\"/assets/images/blank.ico\">"
	retUrl, state = GetIconUrl(utf8Html, uu)
	assert.Equal(t, false, state)
}

func TestGetHttpInfo(t *testing.T) {
	urls := "https://fofa.info"
	resp, _, err := RequestWithLocation(urls, 1, RTHost)
	assert.Nil(t, err)
	info := GetHttpInfo(resp, urls, "************", true)
	assert.Equal(t, 443, info["port"].(int))
	assert.Equal(t, false, info["is_ipv6"].(bool))
	assert.Equal(t, 200, info["status_code"].(int))
}
