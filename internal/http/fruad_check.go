package http

import (
	"git.gobies.org/longzhuan/go_common/rabbish"
	"strings"
)

type JsInfo struct {
	Md5    []string `json:"md5"`  //jsMd5
	JsLen  []int    `json:"len"`  //js body长度
	JsName []string `json:"name"` //js文件名
	JsNum  int      `json:"num"`  //js总数
}

func NeedCheckFraud(hostinfo, body string) bool {
	need := true

	// 政府/教育的先不判断
	if strings.Contains(hostinfo, "edu.cn") || strings.Contains(hostinfo, "gov.cn") {
		need = false
	}

	return need
}

func CheckCtxFraud(body string) (bool, string) {
	fraud := false
	fraudName := ""

	dragCnt := strings.Count(body, "draggable")
	idCnt := strings.Count(body, " id=")
	if strings.Contains(body, "src=\"/js/gezdg.script") && dragCnt > 50 {
		fraud = true
		fraudName = "gezdg"
	}

	if strings.Contains(body, "src=\"/tj/tj.js") && idCnt >= 200 {
		fraud = true
		fraudName = "tj.js"
	}

	return fraud, fraudName
}

func CheckJsFraud(jsInfo *JsInfo) (bool, string) {
	fraud := false
	fraudName := ""

	if jsInfo == nil || jsInfo.JsNum < 1 {
		return fraud, fraudName
	}

	for _, v := range jsInfo.Md5 {
		for _, vv := range rabbish.GetRabbishMd5s() {
			if len(vv) < 2 {
				continue
			}

			if v == vv[1] {
				fraud = true
				fraudName = vv[0]
			}
		}
	}

	return fraud, fraudName
}
