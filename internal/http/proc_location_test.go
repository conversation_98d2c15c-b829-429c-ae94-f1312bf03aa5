package http

import (
	"github.com/stretchr/testify/assert"
	"net/url"
	"testing"
)

func TestNeedFollowLocation(t *testing.T) {
	var html string
	var resultSt bool

	// false
	html = ""
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "a"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "abcabcabc"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "abcabcabc1"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	// true < 400
	html = "abcabcabc12"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	for i := 0; i < 400; i++ {
		html += "a"
	}
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html += "a"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// 不包含body
	html += "a"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body></body <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body>aaaa</body <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// contain </body>
	html = "</body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "a</body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "abbcd</body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	//  完整<body></body>
	html = "<body></body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/><body></body> "
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body>a</body> <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" />\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body a <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" /></body>\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body> a <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" /></body>\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<body > a <link rel=\"apple-touch-icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" /></body>\r\n<link rel=\"mask-icon\" href=\"webman/safari_pin_icon.svg\" color=\"#0086E5\" />\r\n<link rel=\"shortcut icon\" href=\"webman/favicon.ico?v=4399\" />\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_96.png?v=4398\" sizes=\"96x96\"/>\r\n<link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_64.png?v=4398\" sizes=\"64x64\"/><link rel=\"shortcut icon\" href=\"webman/resources/images/icon_dsm_48.png?v=4398\" sizes=\"48x48\"/>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\n<html lang=\"ko\">\n<head>\n   <meta http-equiv=\"Content-Type\" content=\"text/html; charset=euc-kr\">\n    <meta http-equiv=\"Refresh\" CONTENT=\"0.5;URL=https://login.11st.co.kr/login/Login.tmall?returnURL=http%3A%2F%2F220.103.233.43%2FIndex.tmall\">\n    <title>???????? No.1, 11???? - 11????</title>\n    <script type=\"text/javascript\">location.replace('https://login.11st.co.kr/login/Login.tmall?returnURL=http%3A%2F%2F220.103.233.43%2FIndex.tmall');</script>\n</head>\n<body>?α??? ???????? ?̵??մϴ?.\n<a href=\"https://login.11st.co.kr/login/Login.tmall?returnURL=http%3A%2F%2F220.103.233.43%2FIndex.tmall\"></a></body>\n</html>\n"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	html = "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\">\n<html lang=\"en\">\n<head>\n<title>Setup</title>\n<meta http-equiv=\"x-ua-compatible\" content=\"IE=EmulateIE8\" >\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n<META http-equiv=\"Pragma\" CONTENT=\"no-cache\">\n<META HTTP-EQUIV=\"Cache-Control\" CONTENT=\"no-cache\">	\n<meta HTTP-EQUIV=\"Expires\" CONTENT=\"Mon, 06 Jan 1990 00:00:01 GMT\"> \n<script language=\"javascript\" type=\"text/javascript\" src=\"lang.js\"></script>\n<script language=\"javascript\" type=\"text/javascript\" src=\"linux.js\"></script>\n<script language=\"javascript\" type=\"text/javascript\">\nfunction loadnext() \n{\n    window.location.href = \"Docsis_system.asp\";\n}\n\n</script>\n</head>\n<body bgcolor=\"#ffffff\" text=\"#666666\" onLoad=\"loadnext()\"> \n<form name=\"login\" method=\"POST\" action=\"\">\n<div ID=\"maintext\" STYLE=\"position:absolute;left:50px;top:40px;font-family:Arial,Helvetica\">\n<h4>Checking JavaScript Support </h4>\n<p>To provide an enhanced user interface, this Router uses JavaScript extensively.\n<P>If this page is not quickly replaced, your Browser does NOT support JavaScript.\n<P>Please enable JavaScript in your Browser, or use a different Browser. </p>\n</div>\n\n<div ID=\"NStext\" STYLE=\"position:absolute;left:50px;top:220px;font-family:Arial,Helvetica;\">\nNetscape 4.x Browsers are NOT supported.\n<p>Please use a later browser, such as IE 5.5 or later, or the current version of Mozilla or Netscape.</p>\n</div>\n</form>\n</body>\n</html>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	html = "<html>\n<head>\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n    <title></title>\n</head>\n<body>\n<script language=\"javascript\">\n    var sign = \"?\";\n    if (\"returnUrl=http%3A%2F%2F117.159.16.67%2Fos%2Fhtml%2Findex.init.do\" != 'null') {\n        sign = \"&\";\n    }\n    var elnScreen = screen.width + \"*\" + screen.height;\n    top.location.href = \"/login/login.init.do?returnUrl=http%3A%2F%2F117.159.16.67%2Fos%2Fhtml%2Findex.init.do\" + sign + \"elnScreen=\" + elnScreen + \"elnScreen\";\n    //location.href=\"/login/login.init.do?returnUrl=http%3A%2F%2F117.159.16.67%2Fos%2Fhtml%2Findex.init.do\"\n</script>\n</body>\n</html>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	var preStr, tmpStr string

	for i := 0; i < 400; i++ {
		preStr += "a"
	}

	//  body里面250个字符
	tmpStr = ""
	for i := 0; i < 249; i++ {
		tmpStr += "a"
	}
	html = "<body>" + tmpStr + "</body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// body里面内容刚好等于250
	tmpStr = ""
	for i := 0; i < 235; i++ {
		tmpStr += "a"
	}
	html = preStr + "<body " + tmpStr + " </body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// body里面内容刚好超过250
	tmpStr = ""
	for i := 0; i < 236; i++ {
		tmpStr += "a"
	}
	html = preStr + "<body " + tmpStr + " </body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	// body内容远远超过250
	tmpStr = ""
	for i := 0; i < 500; i++ {
		tmpStr += "a"
	}
	html = preStr + "<body " + tmpStr + " </body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	//  BODY里面250个字符
	tmpStr = ""
	for i := 0; i < 249; i++ {
		tmpStr += "a"
	}
	html = "<BODY>" + tmpStr + "</BODY>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// BODY里面内容刚好等于250
	tmpStr = ""
	for i := 0; i < 235; i++ {
		tmpStr += "a"
	}
	html = preStr + "<BODY " + tmpStr + " </BODY>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// BODY里面内容刚好超过250
	tmpStr = ""
	for i := 0; i < 236; i++ {
		tmpStr += "a"
	}
	html = preStr + "<BODY " + tmpStr + " </BODY>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	// BODY内容远远超过250
	tmpStr = ""
	for i := 0; i < 500; i++ {
		tmpStr += "a"
	}
	html = preStr + "<BODY " + tmpStr + " </BODY>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	// 其他内容有很长，但没超过2000
	preStr = ""
	for i := 0; i < 1950; i++ {
		preStr += "a"
	}

	html = preStr + "<body ></body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// 其他内容有很长，刚好2000
	preStr = ""
	for i := 0; i < 1985; i++ {
		preStr += "a"
	}
	html = preStr + "<body ></body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, true, resultSt)

	// 其他内容有很长，刚好超过2000
	preStr = ""
	for i := 0; i < 1986; i++ {
		preStr += "a"
	}
	html = preStr + "<body ></body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	preStr = ""
	for i := 0; i < 2000; i++ {
		preStr += "a"
	}
	html = preStr + "<body ></body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)

	preStr = ""
	for i := 0; i < 1000; i++ {
		preStr += "a"
	}
	for i := 0; i < 235; i++ {
		tmpStr += "a"
	}
	html = preStr + "<body >" + tmpStr + "</body>"
	resultSt = NeedFollowLocation(html)
	assert.Equal(t, false, resultSt)
}

func TestGetHtmlTopLocation(t *testing.T) {
	var reqUrl, htmls, newUrl, newHost string
	var rErr error
	var pUrl *url.URL
	var fetchSt bool

	// 单独ip
	reqUrl = "http://************"
	pUrl, rErr = url.Parse(reqUrl)
	// url解析失败后不继续处理
	if rErr != nil {
		t.Errorf("url parse failed!")
	}

	// ..
	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"../logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)

	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"/../logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/../logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/../logon/logon.htm", newUrl)

	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"../../logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)

	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"../../../logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)

	// .
	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"./logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)

	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"././logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)

	htmls = "<script language=javascript>var logonInfo = new Array(\"TL-AC100\");</script><HTML><HEAD><SCRIPT language=\"javascript\">if (window.top!=window.self) {window.top.location=\"./././logon/logon.htm\"}"
	fetchSt, newUrl, newHost = GetHtmlLocation(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
	assert.Equal(t, "************", newHost)
	fetchSt, newUrl = HtmlLocationNeedProc(RegSelfLoc, reqUrl, pUrl, htmls)
	assert.Equal(t, true, fetchSt)
	assert.Equal(t, "http://************/logon/logon.htm", newUrl)
}
