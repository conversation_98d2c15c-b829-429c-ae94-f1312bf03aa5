package http

import (
	"bytes"
	"compress/gzip"
	"crawler/internal/cert"
	"crawler/internal/encoding"
	"crawler/internal/req"
	"crawler/internal/trust_cert"
	"crawler/internal/util"
	"encoding/json"
	tag "git.gobies.org/goby/bmhtags"
	"git.gobies.org/longzhuan/go_common/honeypot"
	"git.gobies.org/longzhuan/go_common/rabbish"
	"github.com/miekg/dns"
	"github.com/pkg/errors"
	"html"
	"io"
	"log"
	"math"
	"net/http"
	"net/url"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"
)

var regTitle = regexp.MustCompile(`(?i)<title[^<>]*?>([^<>]*)<\/title>`)
var regEncodeTitle = regexp.MustCompile(`(?i)<title[^<>]*?>([&#;0-9\_]*)<\/title>`)
var icpRegex = regexp.MustCompile(`[京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀](ICP[备证]|[A-Z]\d\-)\d{6,8}(号)?(-[1-9])?`)

var regexAfterLink = regexp.MustCompile(`(?i)<link\s*[^<>]*?\s*rel=["']?shortcut icon["']?\s{1,}[^<>]*?\s*href=["']?([^"'<> ]*)["']?`)
var regexAfterLinkBak00 = regexp.MustCompile(`(?i)<link\s*[^<>]*?\s*rel=["']?alternate icon["']?\s{1,}[^<>]*?\s*href=["']?([^"'<> ]*)["']?`)
var regexAfterLinkIcon00 = regexp.MustCompile(`(?i)<link\s*[^<>]*?\s*rel=["']?icon["']?\s{1,}[^<>]*?\s*href=["']?([^"'<> ]*)["']?`)
var regexBeforeLink = regexp.MustCompile(`(?i)<link\s*[^<>]*?\s{1,}href=["']?([^"'<> ]*)["']?\s*\s*[^<>]*?\s{1,}rel=["']?shortcut icon["']?`)
var regexBeforeLink01 = regexp.MustCompile(`(?i)<link\s*[^<>]*?\s{1,}href=["']?([^"'<> ]*)["']?\s*\s*[^<>]*?\s{1,}rel=["']?icon["']?`)
var regexIconLinkMultis []*regexp.Regexp
var regexCheckFraud = regexp.MustCompile(`\<[a-zA-Z0-9]*\s*id=`)
var DnsCli dns.Client

func init() {
	req.SetFlags(req.LrespHead)
	req.EnableCookie(false)
	trans, _ := req.Client().Transport.(*http.Transport)
	trans.DisableKeepAlives = true
	trans.DisableCompression = true

	katrans, _ := req.KAClient().Transport.(*http.Transport)
	katrans.DisableKeepAlives = false
	katrans.DisableCompression = true

	//查询dns客户端
	DnsCli = dns.Client{
		Timeout: 5 * time.Second,
	}

	regexIconLinkMultis = append(regexIconLinkMultis, regexp.MustCompile(`(\d{1,})[x*]\d{1,}\.`))
	regexIconLinkMultis = append(regexIconLinkMultis, regexp.MustCompile(`\_(\d{1,})\.`))
	log.Println("regex icon get-min-links  len:", len(regexIconLinkMultis))
}

func GetBodyTitle(html string) string {
	title := ""

	result := regTitle.FindStringSubmatch(html)
	if len(result) >= 2 {
		title = result[1]
		title = strings.Replace(title, "\r\n", "", -1)
	}

	return title
}

func GetBodyICP(html string) string {
	icp := ""

	result := icpRegex.FindStringSubmatch(html)
	if len(result) >= 2 {
		icp = result[0]
		icp = strings.Replace(icp, "\r\n", "", -1)
	}

	return icp
}

// type(0:header/1:server)
func GetHeaderInfo(resp *req.Resp, host, charset string, typ int) string {
	var tmpStr string
	if typ == 0 {
		tmpStr = encoding.DumpHead(resp)
	} else if typ == 1 {
		header := resp.Response().Header
		tmpStr = header.Get("Server")
	}

	return tmpStr
}

func CheckIpv6(ip string) bool {
	split := strings.Split(ip, ":")
	if len(split) > 1 {
		return true
	}
	return false
}

func GetHttpInfo(resp *req.Resp, host, ip string, isDomain bool) (info map[string]interface{}) {
	info = make(map[string]interface{})

	// get charset
	charset := encoding.GetAllCharset(resp.Response().Header.Get("Content-Type"), resp.String())
	info["charset"] = encoding.GetRealCharset(resp.Response().Header.Get("Content-Type"), resp.String())

	// port
	port, err := util.PortOfUrl(host)
	if err != nil {
		log.Println("[ERROR] get http port:", err)
		return
	}
	info["port"] = port

	// location
	tmpLoc := resp.Response().Header.Get("Location")
	if len(tmpLoc) > 4 {
		tmpLoc = util.Url2Hostinfo(tmpLoc)
		if len(tmpLoc) > 4 {
			info["LocUrl"] = tmpLoc
		}
		info["LocUrl"] = tmpLoc
	}

	// header
	head := GetHeaderInfo(resp, host, charset, 0)
	info["header"] = head
	info["header_len"] = len(head)

	// status code
	info["status_code"] = resp.Response().StatusCode

	// ipv6
	info["is_ipv6"] = CheckIpv6(ip)

	// tag
	header := resp.Response().Header
	extractTag := func(name string) {
		if name != "" {
			tag.ExtractTo(info, name, header.Get(name))
		}
	}
	for _, name := range []string{"Server", "Set-Cookie", "X-Powered-By"} {
		extractTag(name)
	}

	// server，需要转换为utf8
	server := GetHeaderInfo(resp, host, charset, 1)
	if server != "" {
		info["server"] = server
		info["server_len"] = len(server)
	} else {
		info["server"] = ""
	}

	// html
	htmls, err := GetDecodeHtml(host, resp, true)
	if err == nil {
		htmls = string(htmls[0:util.Min(1024*1024, len(htmls))])
	} else {
		log.Println("[ERROR] bad encoding  host:", host, "errdesc:", err)

		htmls = string(htmls[0:util.Min(1024*1024, len(htmls))])
		htmls = EnsureRune([]byte(htmls))
	}
	info["utf8html"] = htmls
	info["body_len"] = len(htmls)

	if len(info["utf8html"].(string)) <= 0 {
		log.Println("    html is empty  host:", host, "status_code:", resp.Response().StatusCode)
	}

	// title
	titles := GetBodyTitle(htmls)
	info["title"] = titles
	info["title_len"] = len(titles)

	// title count 只有超过一个的才记录字断
	lowHtml := strings.ToLower(htmls)
	countTitle := strings.Count(lowHtml, "<title")
	if countTitle > 1 {
		info["title_count"] = countTitle
	}

	// icp备案号
	icp := GetBodyICP(htmls)
	if len(icp) > 0 {
		info["icp"] = icp
	}

	// 蜜罐识别
	//isHoneypot, honeypotName := honeypot.IsHttpHoneypot(server, htmls)
	isHoneypot, honeypotName := honeypot.IsHttpHoneypot(server, head, htmls)
	if isHoneypot {
		info["is_honeypot"] = isHoneypot
		info["honeypot_name"] = honeypotName
	}

	// 垃圾数据识别
	if NeedCheckFraud(host, htmls) {
		isFraud, fraudName := rabbish.IsHttpRabbish(server, head, htmls)
		if isFraud {
			info["is_fraud"] = isFraud
			info["fraud_name"] = fraudName
		}

		if !isFraud {
			// 根据行为判断
			isFraud, fraudName = CheckCtxFraud(htmls)
			if isFraud {
				info["is_fraud"] = isFraud
				info["fraud_name"] = fraudName
			}
		}
	}

	//cert
	cs := resp.Response().TLS
	if cs == nil {
		return
	}
	lenCert := len(cs.PeerCertificates)
	if lenCert > 0 {
		firstCert := cs.PeerCertificates[0]
		sCert := string(cert.EncodeX509ToText(firstCert, 50))
		info["cert"] = sCert

		tmpCerts := trust_cert.GetCertsObj(lenCert, sCert, firstCert, cs.PeerCertificates, host, isDomain)
		b, err := json.Marshal(tmpCerts)
		if err != nil {
			log.Println("[ERROR] get http info cert to json failed:", err)
			return
		}
		var mpCerts map[string]interface{}
		err = json.Unmarshal(b, &mpCerts)
		if err != nil {
			log.Println("[ERROR] get http info certs_json to map failed:", err)
			return
		}
		info["certs"] = mpCerts
	}

	return
}

func GetDecodeHtml(refer string, resp *req.Resp, isUtf8 bool) (string, error) {
	var utf8Html, ctxType, rawbody string
	var err error

	headers := encoding.DumpHead(resp)
	ctxType = resp.Response().Header.Get("Content-Type")
	// 判断gzip
	if strings.Contains(headers, "Content-Encoding: gzip") {
		log.Printf("%s content is gzip", refer)

		decodeHtml, decErr := GzipDecode(resp.Bytes())
		if decErr == nil {
			rawbody = string(decodeHtml)
		} else {
			return "", decErr
		}
	} else {
		rawbody = resp.String()
	}

	if isUtf8 {
		utf8Html, err = encoding.UTF8(ctxType, rawbody)
		if err != nil {
			return utf8Html, err
		}

		if regEncodeTitle.MatchString(utf8Html) {
			log.Println("title is escaped", refer)

			utf8Html = html.UnescapeString(utf8Html)
		}
	} else {
		utf8Html = rawbody
	}

	return utf8Html, nil
}

func GzipDecode(in []byte) ([]byte, error) {
	if in == nil || len(in) == 0 {
		return []byte(""), errors.New("input byte is empty")
	}

	reader, err := gzip.NewReader(bytes.NewReader(in))
	if err != nil {
		var out []byte
		return out, err
	}
	defer reader.Close()

	return io.ReadAll(reader)
}

func EnsureRune(p []byte) string {
	var buf bytes.Buffer
	for len(p) > 0 {
		r, size := utf8.DecodeRune(p)
		if r == utf8.RuneError {
			buf.WriteString(".")
		} else if unicode.IsPrint(r) || unicode.IsSpace(r) {
			buf.WriteRune(r)
		} else {
			writeDot(&buf, size)
		}
		p = p[size:]
	}
	return buf.String()
}

func writeDot(buf *bytes.Buffer, num int) {
	for i := 0; i < num; i++ {
		buf.WriteString(".")
	}
}

func GetIconUrl(htmls string, purl *url.URL) (string, bool) {
	// 长度太小的没有ico
	if len(htmls) < 12 {
		return "", false
	}

	// 先使用带shortcut icon的获取
	res := regexAfterLink.FindAllStringSubmatch(htmls, -1)
	if len(res) <= 0 {
		res = regexBeforeLink.FindAllStringSubmatch(htmls, -1)
	}
	// 备用icon
	if len(res) <= 0 {
		res = regexAfterLinkBak00.FindAllStringSubmatch(htmls, -1)
	}
	// 再使用只带icon的获取
	if len(res) <= 0 {
		res = regexAfterLinkIcon00.FindAllStringSubmatch(htmls, -1)
	}
	if len(res) <= 0 {
		res = regexBeforeLink01.FindAllStringSubmatch(htmls, -1)
	}

	regValLen := len(res)
	//fmt.Println("    get-icon-url addr info  sum_len:", regValLen)
	if regValLen < 1 {
		return "", false
	}

	tmpUrl := ""
	minLoc := -1
	// 如果地址有多条的情况，使用最小那个
	if regValLen > 1 {
		loopProcCnt := -1 // 如果一开始就加，必须从-1开始
		tmpSortVal := math.MaxInt32

		for i := range res {
			loopProcCnt++
			tmpUrl = res[i][1]

			var tmpIconFilename [][]string
			for _, v := range regexIconLinkMultis {
				tmpIconFilename = v.FindAllStringSubmatch(tmpUrl, -1)
				//fmt.Println("    get-icon-url multi icon addr  loc:", loopProcCnt, "value_len:", len(tmpIconFilename), "detail:", tmpIconFilename)
				if len(tmpIconFilename) > 0 {
					break
				}
			}

			if len(tmpIconFilename) <= 0 {
				continue
			}

			tmpNum, err := strconv.Atoi(tmpIconFilename[0][1])
			//fmt.Println("    get-icon-url multi icon num info", tmpNum, tmpSortVal, "loc:", loopProcCnt)
			if err != nil {
				continue
			}

			if tmpNum < tmpSortVal {
				minLoc = loopProcCnt
				tmpSortVal = tmpNum
			}
		} // for loop end
	} // if len > 1 end

	//fmt.Println("    get-icon-url min data location:", minLoc)

	// 获取数据
	if minLoc >= 0 && minLoc < regValLen {
		tmpUrl = res[minLoc][1]
	} else {
		tmpUrl = res[0][1]
		//fmt.Println("    get-icon-url return first icon addr", tmpUrl)
	}

	// 包含空信息的icon不要
	loTmpUrl := strings.ToLower(tmpUrl)
	if strings.Contains(loTmpUrl, "blank.") {
		tmpUrl = ""
	}

	// 最小长度a.ico
	if len(tmpUrl) < 4 {
		return "", false
	}

	// 绝对路径直接返回
	if strings.Contains(tmpUrl, "http://") || strings.Contains(tmpUrl, "https://") {
		return tmpUrl, true
	}

	// 另外一种绝对路径
	if len(tmpUrl) > 2 && tmpUrl[:2] == "//" {
		return purl.Scheme + ":" + tmpUrl, true
	}

	// base64
	if strings.Contains(tmpUrl, ";base64") {
		return tmpUrl, true
	}

	// 相对路径合并
	if string(tmpUrl[0]) == "/" {
		// 绝对路径形式
		iconUrl := purl.Scheme + "://" + purl.Host + tmpUrl
		return iconUrl, true
	}

	procSt := true
	_, iconUrl := GetRelativeFullUrl(purl, tmpUrl)
	if !strings.Contains(iconUrl, purl.Host) {
		procSt = false
	}

	return iconUrl, procSt
}

func GetUrlFilename(surl string) (string, bool) {
	var ext string
	// 获取文件名
	iconUP, err := url.Parse(surl)
	if err != nil {
		return "", false
	}
	if tmpExt := filepath.Ext(iconUP.Path); len(tmpExt) > 0 {
		ext = tmpExt
	}

	return ext, len(ext) > 0
}
