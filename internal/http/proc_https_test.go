package http

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNeedProcHttps(t *testing.T) {
	var html string
	var result bool

	html = ""
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)

	html = "400 The plain HTTP request was sent to HTTPS port</title>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "400 The plain HTTP request was sent to HTTPS port"
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)

	html = "<title>400 The plain HTTP request was sent to HTTPS port</title>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "<title  aaasf>400 The plain HTTP request was sent to HTTPS port</title>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "<body><title  aaasf>400 The plain HTTP request was sent to HTTPS port</title>asfasf</body>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "<body><a asfa>The plain HTTP request was sent to HTTPS port</a></body>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = ">The plain HTTP request was sent to HTTPS port<"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = ">the plain HTTP request was sent to HTTPS port<"
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)

	html = "Client sent an HTTP request to an HTTPS server"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "<body>Client sent an HTTP request to an HTTPS server</body>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "client sent an HTTP request to an HTTPS server"
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)

	html = "This server uses SSL for security. Please use HTTPS to connect."
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "This server uses SSL for security. Please use HTTPS to connect"
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)

	html = "<html>This server uses SSL for security. Please use HTTPS to connect.</html>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "This combination of host and port requires TLS."
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "<body>This combination of host and port requires TLS.</body>"
	result = NeedProcHttps(html)
	assert.Equal(t, true, result)

	html = "This combination of host and port requires TLS"
	result = NeedProcHttps(html)
	assert.Equal(t, false, result)
}

func TestProcHttpsUrl(t *testing.T) {
	var origUrl, newUrl string

	origUrl = "a.c"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "a.c", newUrl)

	// 127.0.0.1
	origUrl = "http://127.0.0.1:80"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:80", newUrl)

	origUrl = "http://127.0.0.1:443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1", newUrl)

	origUrl = "http://127.0.0.1:4430"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:4430", newUrl)

	origUrl = "http://127.0.0.1:4438"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:4438", newUrl)

	origUrl = "http://127.0.0.1:44380"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:44380", newUrl)

	origUrl = "http://127.0.0.1:3443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:3443", newUrl)

	// **********
	origUrl = "http://**********:80"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:80", newUrl)

	origUrl = "http://**********:8080"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:8080", newUrl)

	origUrl = "http://**********:22"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:22", newUrl)

	origUrl = "http://**********:443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********", newUrl)

	origUrl = "http://127.0.0.1:4430"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:4430", newUrl)

	origUrl = "http://**********:4438"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:4438", newUrl)

	origUrl = "http://**********:44380"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:44380", newUrl)

	origUrl = "http://**********:3443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://**********:3443", newUrl)

	// 192.168.0.10
	origUrl = "http://192.168.0.10:80"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:80", newUrl)

	origUrl = "http://192.168.0.10:8080"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:8080", newUrl)

	origUrl = "http://192.168.0.10:22"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:22", newUrl)

	origUrl = "http://192.168.0.10:443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10", newUrl)

	origUrl = "http://192.168.0.10:4430"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:4430", newUrl)

	origUrl = "http://192.168.0.10:4438"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:4438", newUrl)

	origUrl = "http://192.168.0.10:44380"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:44380", newUrl)

	origUrl = "http://192.168.0.10:3443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:3443", newUrl)

	// 115.2.0.22
	origUrl = "http://115.2.0.22:80"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22:80", newUrl)

	origUrl = "http://115.2.0.22:8080"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22:8080", newUrl)

	origUrl = "http://192.168.0.10:22"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:22", newUrl)

	origUrl = "http://115.2.0.22:443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22", newUrl)

	origUrl = "http://115.2.0.22:4430"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22:4430", newUrl)

	origUrl = "http://192.168.0.10:4438"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://192.168.0.10:4438", newUrl)

	origUrl = "http://115.2.0.22:44380"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22:44380", newUrl)

	origUrl = "http://115.2.0.22:3443"
	newUrl = GetProcHttpsUrl(origUrl)
	assert.Equal(t, "https://115.2.0.22:3443", newUrl)
}
