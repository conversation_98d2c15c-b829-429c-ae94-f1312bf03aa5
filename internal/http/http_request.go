package http

import (
	"crawler/internal/req"
	"crawler/internal/util"
	"errors"
	"log"
	"net/url"
	"regexp"
)

var maxRedirect = 5

type RequestType int32

const (
	RTHost RequestType = 0
	RTIcon RequestType = 1
	RTJS   RequestType = 2
)

var ReqHeader = req.Header{
	"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
	"Accept-Charset":  "GBK,utf-8;q=0.7,*;q=0.3",
	"Accept-Language": "zh-CN,zh;q=0.8",
	"User-Agent":      "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36",
}
var RegMetaRefresh = regexp.MustCompile(`(?i)<META\s*HTTP-EQUIV=['"]*?REFRESH['"]*?\s*CONTENT=['"]*?[\d\.]*;\s*URL=['"]*([^'"<>]*)['"]*\s*`)
var RegLoctHref = regexp.MustCompile(`(?i)location\.href\s*=\s*["'](.*?)["']`)
var RegSelfLoc = regexp.MustCompile(`(?i)\S+\.location\s*=\s*["'](.*?)["']`)
var RegLocReplace = regexp.MustCompile(`(?i)location\.replace\(["']([^()]*?)["']`)
var RegWindowOpen = regexp.MustCompile(`(?i)window\.open\(["']([^()]*?)["']`)
var RegCommentRegex = regexp.MustCompile(`<!--.*-->`)

func RequestWithLocationWithHeader(urls string, followTimes int, reqTyp RequestType, header req.Header) (*req.Resp, int, error) {
	if len(urls) <= 0 {
		return nil, followTimes, errors.New("url is nil")
	}
	urls = util.UrlAddPrefix(urls)
	log.Printf("  request %d %s\n", followTimes, urls)

	resp, err := req.Get(urls, header)
	if err != nil {
		return nil, followTimes, err
	}

	// 最大跳转次数限制
	if followTimes > maxRedirect {
		return resp, followTimes, nil
	}

	if reqTyp == RTHost {
		// 判断跳转
		html, err := GetDecodeHtml(urls, resp, true)
		if err != nil {
			return resp, followTimes, nil
		}

		// 替换掉网页中的注释内容，只有请求首页才执行
		html = replaceCommentHtmls(urls, html)

		// 当长度比较小的时候才去提取页面里面的location
		if NeedFollowLocation(html) {
			u, err := url.Parse(urls)
			// url解析失败后不继续处理
			if err != nil {
				return resp, followTimes, nil
			}

			// location.href
			locHrefSt, locHrefUrl := HtmlLocationNeedProc(RegLoctHref, urls, u, html)
			if locHrefSt {
				followTimes += 1
				return RequestWithLocation(locHrefUrl, followTimes, reqTyp)
			}

			// self.location
			selfLocSt, selfLocUrl := HtmlLocationNeedProc(RegSelfLoc, urls, u, html)
			if selfLocSt {
				followTimes += 1
				return RequestWithLocation(selfLocUrl, followTimes, reqTyp)
			}

			// // top.location
			// topLocSt, topLocUrl := GetHtmlLocation(regTopLoc, urls, u, html)
			// if topLocSt && strings.Contains(topLocUrl, u.Host) {
			// 	followTimes += 1
			// 	return RequestWithLocation(topLocUrl, followTimes,reqTyp)
			// }

			// location.replace
			locRepSt, locRepUrl := HtmlLocationNeedProc(RegLocReplace, urls, u, html)
			if locRepSt {
				followTimes += 1
				return RequestWithLocation(locRepUrl, followTimes, reqTyp)
			}

			// window.open
			winOpenSt, winOpenUrl := HtmlLocationNeedProc(RegWindowOpen, urls, u, html)
			if winOpenSt {
				followTimes += 1
				return RequestWithLocation(winOpenUrl, followTimes, reqTyp)
			}

			// meta refresh
			metaSt, metaUrl := HtmlLocationNeedProc(RegMetaRefresh, urls, u, html)
			if metaSt {
				followTimes += 1
				return RequestWithLocation(metaUrl, followTimes, reqTyp)
			}
		}
	}

	return resp, followTimes, nil
}
func RequestWithLocation(urls string, followTimes int, reqTyp RequestType) (*req.Resp, int, error) {
	if len(urls) <= 0 {
		return nil, followTimes, errors.New("url is nil")
	}
	urls = util.UrlAddPrefix(urls)
	log.Printf("  request %d %s\n", followTimes, urls)

	resp, err := req.Get(urls, ReqHeader)
	if err != nil {
		return nil, followTimes, err
	}

	// 最大跳转次数限制
	if followTimes > maxRedirect {
		return resp, followTimes, nil
	}

	if reqTyp == RTHost {
		// 判断跳转
		html, err := GetDecodeHtml(urls, resp, true)
		if err != nil {
			return resp, followTimes, nil
		}

		// 替换掉网页中的注释内容，只有请求首页才执行
		html = replaceCommentHtmls(urls, html)

		// 当长度比较小的时候才去提取页面里面的location
		if NeedFollowLocation(html) {
			u, err := url.Parse(urls)
			// url解析失败后不继续处理
			if err != nil {
				return resp, followTimes, nil
			}

			// location.href
			locHrefSt, locHrefUrl := HtmlLocationNeedProc(RegLoctHref, urls, u, html)
			if locHrefSt {
				followTimes += 1
				return RequestWithLocation(locHrefUrl, followTimes, reqTyp)
			}

			// self.location
			selfLocSt, selfLocUrl := HtmlLocationNeedProc(RegSelfLoc, urls, u, html)
			if selfLocSt {
				followTimes += 1
				return RequestWithLocation(selfLocUrl, followTimes, reqTyp)
			}

			// // top.location
			// topLocSt, topLocUrl := GetHtmlLocation(regTopLoc, urls, u, html)
			// if topLocSt && strings.Contains(topLocUrl, u.Host) {
			// 	followTimes += 1
			// 	return RequestWithLocation(topLocUrl, followTimes,reqTyp)
			// }

			// location.replace
			locRepSt, locRepUrl := HtmlLocationNeedProc(RegLocReplace, urls, u, html)
			if locRepSt {
				followTimes += 1
				return RequestWithLocation(locRepUrl, followTimes, reqTyp)
			}

			// window.open
			winOpenSt, winOpenUrl := HtmlLocationNeedProc(RegWindowOpen, urls, u, html)
			if winOpenSt {
				followTimes += 1
				return RequestWithLocation(winOpenUrl, followTimes, reqTyp)
			}

			// meta refresh
			metaSt, metaUrl := HtmlLocationNeedProc(RegMetaRefresh, urls, u, html)
			if metaSt {
				followTimes += 1
				return RequestWithLocation(metaUrl, followTimes, reqTyp)
			}
		}
	}

	return resp, followTimes, nil
}

func replaceCommentHtmls(url, html string) string {
	replaced := html

	if RegCommentRegex.MatchString(html) {
		// 替换掉html注释掉的内容，可能会造成干扰。
		// 多行的注释暂时不处理
		log.Println("    replace html comment ctx  url:", url)
		replaced = RegCommentRegex.ReplaceAllString(html, "")
	}

	return replaced
}
