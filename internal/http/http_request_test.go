package http

import (
	"crawler/internal/req"
	"errors"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"testing"
)

type HttpRequestSuit struct {
	suite.Suite
}

func TestHttpRequestSuit(t *testing.T) {
	s := &HttpRequestSuit{}

	suite.Run(t, s)
}

//func TestGetMetaRefresh(t *testing.T) {
//
//	//----------------meta refresh start-----------------
//	reqUrl := "http://77.77.252.252:8080" // meta refresh 绝对路径
//	resps, locTimes, reqErr := RequestWithLocation(reqUrl, 1)
//	if reqErr != nil {
//		log.Panicln(errors.WithStack(reqErr))
//	} else {
//		// headers := dumpsHead(resps)
//		// fmt.Println(headers)
//
//		assert.Equal(t, 2, locTimes)
//
//		html, err := encoding.UTF8("utf-8", resps.String())
//		if err != nil {
//			log.Panicln(errors.WithStack(err))
//		} else {
//			assert.Contains(t, html, "Cisco EPC3928S EuroDocsis 3.0 2-PORT Voice Gateway")
//		}
//	}
//
//	fmt.Printf("\n\n.....................................\n")
//	reqUrl = "5.166.179.208" // meta refresh 绝对路径/长度<400
//	resps, locTimes, reqErr = RequestWithLocation(reqUrl, 1)
//	if reqErr != nil {
//		log.Panicln(errors.WithStack(reqErr))
//	} else {
//		assert.Equal(t, 2, locTimes)
//
//		html, err := encoding.UTF8("utf-8", resps.String())
//		if err != nil {
//			log.Panicln(errors.WithStack(err))
//		} else {
//			assert.Contains(t, html, "<title>Смена пароля</title>")
//			assert.Contains(t, html, "formChangePasswordFirstStart")
//		}
//	}
//
//	fmt.Printf("\n\n.....................................\n")
//	reqUrl = "203.177.163.249:8088" // meta refresh 绝对路径/跳转到不带端口/长度<400
//	resps, locTimes, reqErr = RequestWithLocation(reqUrl, 1)
//	if reqErr != nil {
//		log.Panicln(errors.WithStack(reqErr))
//	} else {
//		assert.Equal(t, 1, locTimes)
//
//		html, err := encoding.UTF8("utf-8", resps.String())
//		if err != nil {
//			log.Panicln(errors.WithStack(err))
//		} else {
//			assert.Contains(t, html, "<meta http-equiv='refresh' content='1; url=http://203.177.163.249/&arubalp=b406a800-7ec1-43b0-8dd2-c6e441dd89'>")
//		}
//	}
//
//	fmt.Printf("\n\n.....................................\n")
//	reqUrl = "49.48.22.198" // meta refresh 绝对路径/跳转到其他域名
//	resps, locTimes, reqErr = RequestWithLocation(reqUrl, 1)
//	if reqErr != nil {
//		log.Panicln(errors.WithStack(reqErr))
//	} else {
//		assert.Equal(t, 1, locTimes)
//
//		html, err := encoding.UTF8("utf-8", resps.String())
//		if err != nil {
//			log.Panicln(errors.WithStack(err))
//		} else {
//			assert.Contains(t, html, "<META HTTP-EQUIV=\"Refresh\" CONTENT=\"0;URL=http://pmb1.dyndns.org/cyber2a/\">")
//		}
//	}
//
//	fmt.Printf("\n\n.....................................\n")
//	reqUrl = "nas.mmprinting.nl" // meta refresh 绝对路径/跳转到本域名后又跳转到其他域名
//	resps, locTimes, reqErr = RequestWithLocation(reqUrl, 1)
//	if reqErr != nil {
//		log.Panicln(errors.WithStack(reqErr))
//	} else {
//		assert.Equal(t, 2, locTimes)
//
//		html, err := encoding.UTF8("utf-8", resps.String())
//		if err != nil {
//			log.Panicln(errors.WithStack(err))
//		} else {
//			assert.Contains(t, html, "<meta http-equiv=\"refresh\" content=\"0; url=http://mmprinting.quickconnect.to/photo/share/VYdDZH4S\" />")
//		}
//	}
//	//----------------meta refresh end-----------------
//}

func TestReplaceCommentHtmls(t *testing.T) {
	var replacedHtml, urls string

	// 有注释的
	html := `<html>
<head>
</head>
<!--<body onload="javascript: window.location = '/OAapp/WebObjects/OAapp.woa'"></body>-->
<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`

	replacedHtml = replaceCommentHtmls(urls, html)
	assert.Equal(t, `<html>
<head>
</head>

<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`, replacedHtml)

	// 正常
	html = `<html>
<head>
</head>
<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`

	replacedHtml = replaceCommentHtmls(urls, html)
	assert.Equal(t, `<html>
<head>
</head>
<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`, replacedHtml)

	// 有注释，但是多行
	html = `<html>
<head>
</head>
<!--<body onload="javascript: window.location = '/OAapp/WebObjects/OAapp.woa'"></body>
-->
<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`

	replacedHtml = replaceCommentHtmls(urls, html)
	assert.Equal(t, `<html>
<head>
</head>
<!--<body onload="javascript: window.location = '/OAapp/WebObjects/OAapp.woa'"></body>
-->
<body onload="javascript: window.location = '/OAapp/htpages/app/module/login/8.0Login.jsp'"></body>
</html>`, replacedHtml)
}

func (s *HttpRequestSuit) TestRequestWithLocationWithHeader() {
	Convey("TestRequestWithLocationWithHeader", s.T(), func() {
		Convey("urls empty", func() {
			_, _, err := RequestWithLocationWithHeader("", 1, RTHost, req.Header{})
			So(err, ShouldBeError)
		})

		Convey("req.Get error", func() {
			defer ApplyFuncReturn(req.Get, nil, errors.New("")).Reset()
			_, _, err := RequestWithLocationWithHeader("1", 1, RTHost, req.Header{})
			So(err, ShouldBeError)
		})

		Convey("GetDecodeHtml error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "", errors.New("")).Reset()
			_, _, err := RequestWithLocationWithHeader("1", 1, RTHost, req.Header{})
			So(err, ShouldBeNil)
		})

		Convey("url.Parse error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", nil).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, true).Reset()
			_, _, err := RequestWithLocationWithHeader("1", 1, RTHost, req.Header{})
			So(err, ShouldBeNil)
		})

		Convey("location.href error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", nil).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, true).Reset()
			defer ApplyFuncReturn(HtmlLocationNeedProc, true, "").Reset()
			defer ApplyFuncReturn(RequestWithLocation, nil, 0, errors.New("")).Reset()
			_, _, err := RequestWithLocationWithHeader("1", 1, RTHost, req.Header{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", errors.New("")).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, false).Reset()
			_, _, err := RequestWithLocationWithHeader("1", 1, RTHost, req.Header{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *HttpRequestSuit) TestRequestWithLocation() {
	Convey("TestRequestWithLocation", s.T(), func() {
		Convey("urls empty", func() {
			_, _, err := RequestWithLocation("", 1, RTHost)
			So(err, ShouldBeError)
		})

		Convey("req.Get error", func() {
			defer ApplyFuncReturn(req.Get, nil, errors.New("")).Reset()
			_, _, err := RequestWithLocation("1", 1, RTHost)
			So(err, ShouldBeError)
		})

		Convey("GetDecodeHtml error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "", errors.New("")).Reset()
			_, _, err := RequestWithLocation("1", 1, RTHost)
			So(err, ShouldBeNil)
		})

		Convey("url.Parse error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", nil).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, true).Reset()
			_, _, err := RequestWithLocation("1", 1, RTHost)
			So(err, ShouldBeNil)
		})

		Convey("location.href error", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", nil).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, true).Reset()
			defer ApplyFuncReturn(HtmlLocationNeedProc, true, "").Reset()
			defer ApplyFuncReturn(RequestWithLocation, nil, 0, errors.New("")).Reset()
			_, _, err := RequestWithLocation("1", 1, RTHost)
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(req.Get, nil, nil).Reset()
			defer ApplyFuncReturn(GetDecodeHtml, "<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> ", errors.New("")).Reset()
			defer ApplyFuncReturn(NeedFollowLocation, false).Reset()
			_, _, err := RequestWithLocation("1", 1, RTHost)
			So(err, ShouldBeNil)
		})
	})
}
