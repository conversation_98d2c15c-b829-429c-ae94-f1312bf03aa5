package http

import (
	"crawler/internal/util"
	"strings"
)

func NeedProcHttps(html string) bool {
	procResult := false

	chkTitles := "400 The plain HTTP request was sent to HTTPS port</title>"
	chkBodys := ">The plain HTTP request was sent to HTTPS port<"
	chkCliBody := "Client sent an HTTP request to an HTTPS server"
	chkSslBody := "This server uses SSL for security. Please use HTTPS to connect."
	chkTlsBody := "This combination of host and port requires TLS."
	chkTls2Body := "This web server is running in SSL mode."
	if strings.Contains(html, chkTitles) || strings.Contains(html, chkBodys) ||
		strings.Contains(html, chkCliBody) || strings.Contains(html, chkSslBody) ||
		strings.Contains(html, chkTlsBody) || strings.Contains(html, chkTls2Body) {
		procResult = true
	}

	return procResult
}

func GetProcHttpsUrl(urlStr string) string {
	var tmpUrl string
	findStr := ":443"

	// 先替换为https
	tmpUrl = strings.Replace(urlStr, "http://", "https://", -1)

	// 替换默认的443端口
	port, portErr := util.PortOfUrl(urlStr)
	if portErr != nil {
		if len(urlStr) > 4 {
			tmpPort := urlStr[len(urlStr)-4:]
			if tmpPort == findStr {
				tmpUrl = strings.Replace(tmpUrl, findStr, "", -1)
			}
		}
	} else {
		if port == 443 {
			tmpUrl = strings.Replace(tmpUrl, findStr, "", -1)
		}
	}

	return tmpUrl
}
