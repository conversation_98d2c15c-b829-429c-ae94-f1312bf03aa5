package http

import (
	"crawler/internal/util"
	"log"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
)

var regExistsBody = regexp.MustCompile(`(?is)<body.*?</body>`) //作为单行处理
var regOnlyBodyScript = regexp.MustCompile(`(?is)<body>.*?<script.*?>[^<>]*<\/script>.*?<\/body>`)

func NeedFollowLocation(html string) bool {
	needProc := false

	// 内容过短的不处理
	// 长度小于400的直接提取
	// 长度大于400但是没有<body>内容的也需要处理
	bodyLen := len(html)
	if bodyLen > 10 {
		if bodyLen < 400 {
			needProc = true
		} else {
			if !strings.Contains(html, "</body>") && !strings.Contains(html, "</BODY>") {
				needProc = true
			} else {
				if bodyLen < 2000 {
					matchVal := regExistsBody.FindString(html)
					lenMatchVal := len(matchVal)

					// body内容很短的也算
					if lenMatchVal > 0 && lenMatchVal < 250 {
						needProc = true
					}

					// body全部都是script的也算
					if lenMatchVal < 1000 && regOnlyBodyScript.Match([]byte(matchVal)) {
						needProc = true
					}
				}
			}
		}
	}

	return needProc
}

// 合并完整相对路径
func GetRelativeFullUrl(purl *url.URL, newPath string) (bool, string) {
	if purl == nil {
		return false, ""
	}
	var newUrl string

	urlPath := purl.Path
	//这个其实是跳转到/ui/
	if urlPath == "/ui" {
		urlPath += "/"
	}

	// 如果icon是以./开头的，去掉
	if strings.HasPrefix(newPath, "./") {
		newPath = newPath[2:]
	}

	if len(urlPath) <= 1 {
		newUrl = purl.Scheme + "://" + purl.Host + "/" + newPath
	} else {
		tmpDir := filepath.Dir(urlPath)
		if string(tmpDir[len(tmpDir)-1]) != "/" {
			tmpDir += "/"
		}

		newUrl = purl.Scheme + "://" + purl.Host + tmpDir + newPath
	}

	return true, newUrl
}

func GetHtmlLocation(regs *regexp.Regexp, urls string, purl *url.URL, html string) (bool, string, string) {
	locUrl := ""
	locaUrlHost := ""
	procSt := false

	if regs.Match([]byte(html)) {
		results := regs.FindStringSubmatch(html)
		if len(results) < 2 {
			return procSt, urls, purl.Host
		}

		locHref := results[1]
		// 内容为空的不处理
		if len(locHref) <= 0 {
			return procSt, urls, purl.Host
		}

		// 包含这个的先不处理跳转
		if strings.Contains(locHref, "\\x") {
			return procSt, urls, purl.Host
		}

		// 先处理跳转地址不对的几种情况
		if strings.Contains(locHref, "https:\\\\") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "https:\\\\", "https://")
			log.Println("get html location contain https backslash  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}
		if strings.Contains(locHref, "http:\\\\") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "http:\\\\", "http://")
			log.Println("get html location contain http backslash  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}
		if strings.Contains(locHref, "https:\\/\\/") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "https:\\/\\/", "https://")
			log.Println("get html location contain https 1 backslash  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}
		if strings.Contains(locHref, "http:\\/\\/") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "http:\\/\\/", "http://")
			log.Println("get html location contain http 1 backslash  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}
		if strings.Contains(locHref, "\\u002f") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "\\u002f", "/")
			log.Println("get html location contain unicode /  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}
		if strings.Contains(locHref, "\\u0026") {
			oldHref := locHref
			locHref = strings.ReplaceAll(locHref, "\\u0026", "&")
			log.Println("get html location contain unicode &  refer:", urls, "  old:", oldHref, "  new:", locHref)
		}

		// 仅仅包含这个的不继续处理
		if locHref == "http://" || locHref == "https://" {
			return procSt, urls, purl.Host
		}

		procSt = true
		if strings.Contains(locHref, "http://") || strings.Contains(locHref, "https://") {
			locUrl = locHref
		} else {
			if string(locHref[0]) == "/" {
				// 绝对路径形式
				locUrl = purl.Scheme + "://" + purl.Host + locHref
			} else {
				//处理./这种目录，需要考虑可能会包含../的情况
				locHref = strings.Replace(locHref, "../", "", -1)
				locHref = strings.Replace(locHref, "./", "", -1)
				//locHref = strings.Replace(locHref, "spiderNotExistsDirooo", "../", -1)

				// 相对路径形式
				// if string(locHref[len(locHref)-1]) == "/" {
				// 	locUrl = url + locHref
				// } else {
				// 	locUrl = url + "/" + locHref
				// }

				_, locUrl = GetRelativeFullUrl(purl, locHref)
				if !strings.Contains(locUrl, purl.Host) {
					procSt = false
				}
			}
		}
	}

	// 判断跳转到同一个URL
	if locUrl == urls {
		procSt = false
	}

	// 获取跳转后的host
	if procSt {
		locaUrlHost = util.Url2Hostinfo(locUrl)
	}

	return procSt, locUrl, locaUrlHost
}

func HtmlLocationNeedProc(regs *regexp.Regexp, urls string, purl *url.URL, html string) (bool, string) {
	origHostinfo := util.Url2Hostinfo(urls)
	procSt := false
	locSt, locUrl, locHost := GetHtmlLocation(regs, urls, purl, html)
	if locSt && len(locUrl) > 0 && locHost == origHostinfo {
		procSt = true
	}

	// 这个情况需要替换为目录，不然请求不对
	if strings.Contains(locUrl, "\\") && !strings.Contains(locUrl, "\\/") {
		log.Println("html location contain backslash not dir", urls, locUrl)

		locUrl = strings.ReplaceAll(locUrl, "\\", "/")
	}

	if strings.Contains(locUrl, "\\") {
		log.Println("html location contain backslash", urls, locUrl)

		locUrl = strings.ReplaceAll(locUrl, "\\", "")
	}

	return procSt, locUrl
}
