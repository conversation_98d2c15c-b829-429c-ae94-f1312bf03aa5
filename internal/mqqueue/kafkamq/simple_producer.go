package kafkamq

import (
	"checkurl/pkg/errorhandle"
	"github.com/Shopify/sarama"
	"github.com/pkg/errors"
	"log"
	"os"
	"time"
)

func init() {
	sarama.Logger = log.New(os.<PERSON>dout, "[<PERSON><PERSON>] ", log.LstdFlags)
}

type SimpleProducer struct {
	sarama.AsyncProducer
	//*kafka.Writer
	errCh chan error
}

func NewSimpleProducer(addrs []string, config *sarama.Config) (*SimpleProducer, error) {
	producer, err := sarama.NewAsyncProducer(addrs, config)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	sp := &SimpleProducer{
		AsyncProducer: producer,
		errCh:         make(chan error),
	}

	return sp, nil
}

/*func NewSimpleProducer1(topic string, addrs []string) (*SimpleProducer, error) {

	w := kafka.NewWriter(kafka.WriterConfig{
		Brokers:          addrs,
		Topic:            topic,
		CompressionCodec: gzip.NewCompressionCodec(),
		Balancer:         &kafka.Hash{},
		Async:            true,
	})
	sp := &SimpleProducer{
		Writer: w,
		errCh:  make(chan error),
	}

	return sp, nil
}*/

func NewCommonSimpleProducer(topic string, brokers []string, enableCompression bool, errorHandler errorhandle.Handler) (*SimpleProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.NoResponse
	config.Producer.Partitioner = sarama.NewHashPartitioner
	config.Producer.Return.Errors = true
	config.Net.DialTimeout = 10 * time.Second

	if enableCompression {
		config.Producer.Compression = sarama.CompressionGZIP // gzip 压缩
		config.Producer.Flush.Bytes = 1024 * 1024            // 1MB 批量压缩
		config.Producer.MaxMessageBytes = 104850000          // 比broker的socket.request.max.bytes少一点
		config.Producer.Flush.Frequency = 1 * time.Minute
	}

	sp, err := NewSimpleProducer(brokers, config)
	//sp, err := NewSimpleProducer1(topic, brokers)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	go func() {
		for err := range sp.AsyncProducer.Errors() {
			sp.errCh <- err.Err
		}
	}()

	if errorHandler != nil {
		go func() {
			for err := range sp.Errors() {
				errorHandler(err)
			}
		}()
	}

	return sp, nil
}

func (sp *SimpleProducer) Close() error {
	return sp.AsyncProducer.Close()
}

func (sp *SimpleProducer) Publish(topic string, data []byte) {
	sp.Input() <- &sarama.ProducerMessage{Topic: topic, Value: sarama.ByteEncoder(data)}
	//sp.WriteMessages(context.Background(), kafka.Message{Value: data})
}

func (sp *SimpleProducer) Errors() <-chan error {
	return sp.errCh
}

func (sp *SimpleProducer) Retry(topic string, data []byte) {
	sp.Input() <- &sarama.ProducerMessage{Topic: topic, Value: sarama.ByteEncoder(data)}
}
