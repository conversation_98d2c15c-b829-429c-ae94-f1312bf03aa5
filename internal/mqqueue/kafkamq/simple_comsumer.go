package kafkamq

import (
	"checkurl/pkg/errorhandle"
	"github.com/Shopify/sarama"
	"github.com/bsm/sarama-cluster"
	"github.com/pkg/errors"
	"log"
	"time"
)

type SimpleConsumer struct {
	//*kafka.Reader
	*cluster.Consumer
	errCh chan error
	msgCh chan []byte
}

func (sc *SimpleConsumer) Messages() <-chan []byte {
	return sc.msgCh
}

func (sc *SimpleConsumer) Errors() <-chan error {
	return sc.errCh
}

func (sc *SimpleConsumer) Close() error {
	return sc.Consumer.Close()
}

func NewSimpleConsumer(name string, topics, brokers []string, config *cluster.Config) (*SimpleConsumer, error) {
	cg, err := cluster.NewConsumer(brokers, name, topics, config)
	if err != nil {
		return nil, errors.Wrap(err, "failed to crate simple consumer")
	}
	sc := &SimpleConsumer{
		Consumer: cg,
		msgCh:    make(chan []byte),
	}
	go func() {
		for msg := range sc.Consumer.Messages() {
			// sc.CommitUpto(msg)
			// fmt.Fprintf(os.Stdout, "fetch msg: %s/%d/%d\t%s\t%s\n", msg.Topic, msg.Partition, msg.Offset, msg.Key, msg.Value)
			sc.Consumer.MarkOffset(msg, "")
			sc.msgCh <- msg.Value
		}
	}()
	return sc, nil
}

/*func NewSimpleConsumer1(topic string, brokers []string, partition int) (*SimpleConsumer, error) {

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers:   brokers,
		Topic:     topic,
		Partition: partition,
		MinBytes:  10e3, // 10KB
		MaxBytes:  10e6, // 10MB
	})

	sc := &SimpleConsumer{
		Reader: r,
		errCh:  make(chan error),
		msgCh:  make(chan []byte),
	}
	log.Printf("Message at Partition: %s, Offset: %d, Topic: %s\n", r.Stats().Partition, r.Stats().Offset, r.Stats().Topic)
	go func() {
		for {
			m, err := r.ReadMessage(context.Background())
			if err != nil {
				log.Println(err)
				if err == io.EOF {
					break
				}
				continue
			}
			sc.msgCh <- m.Value
		}
	}()
	return sc, nil
}*/

func NewCommonSimpleConsumer(name string, topics, brokers []string, partition int, disableBuffer bool, errorHandler errorhandle.Handler) (*SimpleConsumer, error) {
	config := cluster.NewConfig()
	config.ClientID = name
	config.Group.Return.Notifications = true
	config.Group.PartitionStrategy = cluster.StrategyRoundRobin
	config.Group.Session.Timeout = 20 * time.Second
	config.Group.Heartbeat.Interval = 6 * time.Second
	config.Consumer.Return.Errors = true
	config.Version = sarama.V0_10_0_1
	if disableBuffer {
		config.ChannelBufferSize = 0
	}

	sc, err := NewSimpleConsumer(name, topics, brokers, config)
	//sc, err := NewSimpleConsumer1(name, brokers, partition)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// consume notifications
	go func() {
		for ntf := range sc.Consumer.Notifications() {
			log.Printf("Rebalanced: %+v\n", ntf)
		}
	}()
	if errorHandler != nil {
		go func() {
			for err := range sc.Errors() {
				errorHandler(err)
			}
		}()
	}
	return sc, nil
}
