package sidekiq

import (
	"checkurl/pkg/errorhandle"
	"strconv"

	workers "github.com/jrallison/go-workers"
)

type SimpleConsumer struct {
	errCh chan error
	msgCh chan []byte
}

func NewCommonSimpleConsumer(queue, addr, password string, db int, errorHandler errorhandle.Handler) *SimpleConsumer {
	sc := NewSimpleConsumer(queue, addr, password, db)
	if errorHandler != nil {
		go func() {
			for err := range sc.Errors() {
				errorHandler(err)
			}
		}()
	}
	return sc
}

func NewSimpleConsumer(queue, addr, password string, db int) *SimpleConsumer {
	m := map[string]string{
		// location of redis instance
		"server": addr,
		// instance of the database
		"database": strconv.Itoa(db),
		// unique process id for this instance of workers (for proper recovery of inprogress jobs on crash)
		"process": "1",
	}
	if password != "" {
		m["password"] = password
	}
	sp := &SimpleConsumer{
		errCh: make(chan error),
		msgCh: make(chan []byte),
	}
	workers.Configure(m)
	job := func(msg *workers.Msg) {
		data, err := msg.Bytes()
		if err != nil {
			sp.errCh <- err
			return
		}
		sp.msgCh <- data
	}
	// pull messages from "myqueue2" with concurrency of 20
	workers.Process(queue, job, 1)

	return sp
}

func (sc *SimpleConsumer) Messages() <-chan []byte {
	return sc.msgCh
}

func (sc *SimpleConsumer) Close() error {
	workers.Quit()
	return nil
}

func (sc *SimpleConsumer) Errors() <-chan error {
	return sc.errCh
}
