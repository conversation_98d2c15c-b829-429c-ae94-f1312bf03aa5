package sidekiq

//type SimpleProducer struct {
//class string
//errCh chan error
//}

//func NewSimpleProducer(addr, password string, db int, class string) (*SimpleProducer, error) {
//m := map[string]string{
//// location of redis instance
//"server": addr,
//// instance of the database
//"database": strconv.Itoa(db),
//// unique process id for this instance of workers (for proper recovery of inprogress jobs on crash)
//"process": "1",
//}
//if password != "" {
//m["password"] = password
//}
//sp := &SimpleProducer{
//class: class,
//errCh: make(chan error),
//}
//workers.Configure(m)
//return sp, nil
//}

//func (sp *SimpleProducer) Publish(mqqueue string, data []byte) {
//workers.EnqueueWithOptions(mqqueue, sp.class, workers.EnqueueOptions{Retry: true, RetryCount: 3})
//}

//func (sp *SimpleProducer) Close() error {
//}

//func (sp *SimpleProducer) Errors() <-chan error {
//return sp.errCh
//}
