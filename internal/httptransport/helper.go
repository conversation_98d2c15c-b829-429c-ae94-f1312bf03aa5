package httptransport

import (
	"api/internal"
	"encoding/base64"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"net"
	"strings"
)

func StringToList(rawString string) []string {
	lists := strings.Split(rawString, ",")
	if len(lists) == 1 && lists[0] == "" {
		return []string{}
	}
	return lists
}

func TaskTypeConvert(taskType string) (uint, error) {
	t, ok := constant.TaskType[taskType]

	if !ok {
		return 0, errors.New(internal.TaskTypeError)
	}

	return t, nil
}

func DecQBase64(encodeString string) (string, error) {
	var estr = strings.Replace(encodeString, "%2B", "+", -1)
	estr = strings.Replace(estr, " ", "+", -1)
	estr = strings.Replace(estr, "%252B", "+", -1)
	estr = strings.Replace(estr, "%20", "+", -1)
	estr = strings.Replace(estr, "%3D", "=", -1)
	if decodeBytes, err := base64.StdEncoding.DecodeString(estr); err != nil {
		return "", err
	} else {
		return strings.TrimSpace(string(decodeBytes)), nil
	}
}

func isValidIPv4(ip string) bool {
	parsedIP := net.ParseIP(ip)

	return parsedIP != nil && parsedIP.To4() != nil
}

func isValidIPv6(ip string) bool {
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() == nil
}

func isValidIPv4CIDR(cidr string) bool {
	_, ipNet, err := net.ParseCIDR(cidr)
	return err == nil && ipNet.IP.To4() != nil
}

func isValidIPv6CIDR(cidr string) bool {
	_, ipNet, err := net.ParseCIDR(cidr)
	return err == nil && ipNet.IP.To4() == nil
}
