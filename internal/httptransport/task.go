package httptransport

import (
	"api/internal"
	"api/internal/httptransport/request"
	response2 "api/internal/httptransport/response"
	"api/internal/response"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/errors"
	"go-micro.dev/v4/logger"
	"net/http"
)

func (h *handle) addTask(ctx *gin.Context) {
	var req request.AddTaskRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Warnf("Received handle addTask request:%v error:%v", req, err)
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	logger.Info("Received handle addTask request:", req)
	if req.IPList == "" &&
		req.IPListFileName == "" &&
		req.PortGroup == nil &&
		(req.HostInfos == nil || len(req.HostInfos) == 0) {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.NoScanTarget, nil)
		return
	}

	bandwidth, err := req.GetBandwidth()

	if err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	convert, err := TaskTypeConvert(req.TaskType)
	if err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	// port group 校验
	if req.PortGroup != nil {
		for _, group := range req.PortGroup {
			for _, l := range group.IPList {
				if !req.IsIPV6 && (!isValidIPv4(l) && !isValidIPv4CIDR(l)) {
					response.Return(ctx, http.StatusUnprocessableEntity, fmt.Sprintf("%s:%s", l, internal.InValidIPv4), nil)
					return
				}

				if req.IsIPV6 && (!isValidIPv6(l) && !isValidIPv6CIDR(l)) {
					response.Return(ctx, http.StatusUnprocessableEntity, fmt.Sprintf("%s:%s", l, internal.InValidIPv6), nil)
					return
				}
			}
		}
	}

	if req.IsIPV6 && convert != constant.TaskTypeCommon {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.ParamInValid, nil)
		return
	}

	//// 黑名单格式校验
	//if len(req.Blacklist) > 0 {
	//	excludes := strings.Split(req.Blacklist, ",")
	//	for _, e := range excludes {
	//		if !req.IsIPV6 && (!isValidIPv4(e) && !isValidIPv4CIDR(e)) {
	//			response.Return(ctx, http.StatusUnprocessableEntity, fmt.Sprintf("%s:%s", e, internal.ExcludeInValidIPv4), nil)
	//			return
	//		}
	//
	//		if req.IsIPV6 && (!isValidIPv6(e) && !isValidIPv6CIDR(e)) {
	//			response.Return(ctx, http.StatusUnprocessableEntity, fmt.Sprintf("%s:%s", e, internal.ExcludeInValidIPv6), nil)
	//			return
	//		}
	//	}
	//}

	// TODO 兼容旧内核foeye恢复已暂停任务后续应该去掉
	if len(req.ResumeFilename) > 0 {
		logger.Infof("from ResumeFilename resume paused task. task_id: %s bandwidth: %s", req.TaskID, req.Bandwidth)
		resp, err := h.srv.ResumeTask(ctx, &request.TaskResumeRequest{
			TaskID:    req.TaskID,
			Bandwidth: req.Bandwidth,
		})

		if err != nil {
			logger.Error("from taskAdd resume paused task error:", err.Error(), req)
			response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
			return
		}

		response.Return(ctx, http.StatusOK, "", resp)
		return
	}

	dtoReq := &request.CreateRequest{
		TaskID: req.TaskID,
		Ports:  &req.Ports,
		IP: &request.IPTarget{
			List: StringToList(req.IPList),
			File: req.IPListFileName,
			URL:  "",
		},
		HostInfos: req.HostInfos,
		PortScanner: &request.PortScanner{
			TaskType: convert,
			Options: request.PortScannerOptions{
				Rate:           bandwidth,
				Blacklist:      StringToList(req.Blacklist),
				PingScan:       req.PingScan,
				GatewayMac:     req.GatewayMac,
				DeepGetOS:      req.DeepGetOS,
				DeepGetMac:     req.DeepGetMac,
				IsIPV6:         req.IsIPV6,
				Retries:        req.RepeatTimes,
				TreckScan:      req.TreckScan,
				SendETH:        req.SendETH,
				ResumeFilename: req.ResumeFilename,
			},
		},
		Options: request.RequestOptions{
			MaxAssetNum:         req.MaxAssetNum,
			GrabConcurrent:      req.GrabConcurrent,
			ProtocolUpdateCycle: req.ProtocolUpdateCycle,
			UnknownProtocolInDB: req.UnknownProtocolInDB,
			CrawlerAllUrl:       req.CrawlerAllUrl,
			CrawlerUrlBlackKey:  req.CrawlerUrlBlackKey,
			CrawlerSpecificURL:  req.CrawlerSpecificURL,
			IPDomainRelations:   req.IPDomainRelations,
			Extra:               req.Extra,
		},
	}

	if len(req.PortGroup) != 0 {
		var pg []request.PortGroupItem

		for _, pgi := range req.PortGroup {
			pg = append(pg, request.PortGroupItem{
				IPList: pgi.IPList,
				Ports:  pgi.Ports,
			})
		}
		dtoReq.Options.PortGroup = pg
	}

	res, err := h.srv.AddTask(ctx, dtoReq)
	if err != nil {
		logger.Error("taskAdd task error:", err.Error(), req)
		e := errors.Parse(err.Error())

		if e != nil {
			response.Return(ctx, int(e.Code), e.Detail, nil)
			return
		}
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", res)
}

func (h *handle) deleteTask(ctx *gin.Context) {
	var req request.TaskIDRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	err := h.srv.DeleteTask(ctx, &req)
	if err != nil {
		logger.Error("deleteTask task error:", err.Error(), req)
		e := errors.Parse(err.Error())

		if e != nil {
			response.Return(ctx, int(e.Code), e.Detail, nil)
			return
		}
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", response2.DeleteTaskResponse{
		TaskID: req.TaskID,
	})
	return
}

func (h *handle) pauseTask(ctx *gin.Context) {
	var req request.TaskIDRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	resp, err := h.srv.PauseTask(ctx, &req)
	if err != nil {
		logger.Error("pauseTask task error:", err.Error(), req)
		e := errors.Parse(err.Error())

		if e != nil {
			response.Return(ctx, int(e.Code), e.Detail, nil)
			return
		}

		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", resp)
	return
}

func (h *handle) resumeTask(ctx *gin.Context) {
	var req request.TaskResumeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	resp, err := h.srv.ResumeTask(ctx, &req)
	if err != nil {
		logger.Error("resumeTask task error:", err.Error(), req)

		e := errors.Parse(err.Error())

		if e != nil {
			response.Return(ctx, int(e.Code), e.Detail, nil)
			return
		}

		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", resp)
	return
}

func (h *handle) getTask(ctx *gin.Context) {
	var req request.TaskIDRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	resp, err := h.srv.GetTask(ctx, &req)
	if err != nil {
		logger.Error("getTask task error:", err.Error(), req)
		e := errors.Parse(err.Error())

		if e != nil {
			response.Return(ctx, int(e.Code), e.Detail, nil)
			return
		}

		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", resp)
	return
}
