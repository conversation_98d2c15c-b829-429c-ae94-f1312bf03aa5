package httptransport

import (
	"api/internal"
	"api/internal/httptransport/request"
	response2 "api/internal/httptransport/response"
	"api/internal/response"
	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
	"net/http"
	"strconv"
)

func (h *handle) activatedLicense(ctx *gin.Context) {
	var req request.ActivatedRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	err := h.srv.ActivatedLicense(ctx, req.License)
	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}

func (h *handle) getIDKey(ctx *gin.Context) {
	idKey, err := h.srv.GetIDKey(ctx)
	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", idKey)
}

func (h *handle) getSystemInfo(ctx *gin.Context) {
	logger.Infof("Received getSystemInfo request.LicenseVerifySwitch=%s", internal.LicenseVerifySwitch)
	licenseVerifySwitch, err := strconv.ParseBool(internal.LicenseVerifySwitch)
	if err != nil || !licenseVerifySwitch {
		response.Return(ctx, http.StatusOK, "", response2.GetSystemInfoResponse{
			Version:          internal.Version,
			LicenseState:     true,
			AssetLimitNum:    100000,
			ProductLimitDate: "2025-01-01",
			UpgradeLimitDate: "2025-01-01",
			Modules:          response2.Modules{},
		})
		return
	}

	response.Return(ctx, http.StatusOK, "", h.srv.GetSystemInfo(ctx))
}
