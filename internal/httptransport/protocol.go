package httptransport

import (
	"api/internal"
	"api/internal/httptransport/request"
	"api/internal/response"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) allProtocols(ctx *gin.Context) {
	protocols, err := h.srv.AllProtocols(ctx)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", protocols)
}

func (h *handle) addProtocols(ctx *gin.Context) {
	var req []model.Protocol

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	err := h.srv.AddProtocols(ctx, req)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}

func (h *handle) updateProtocols(ctx *gin.Context) {
	var req request.UpdateProtocol

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	id := ctx.Param("id")

	if id == "" {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.RequestParamMiss, nil)
		return
	}

	err := h.srv.UpdateProtocol(ctx, model.Protocol{
		ID:           id,
		Name:         req.Name,
		ProtocolType: req.ProtocolType,
		BindPort:     req.BindPort,
		Timeout:      req.Timeout,
		Judge:        req.Judge,
	})

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}

func (h *handle) deleteProtocol(ctx *gin.Context) {
	var req request.DeleteProtocol

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	err := h.srv.DeleteProtocol(ctx, req)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}
