package httptransport

import (
	"api/internal"
	"api/internal/httptransport/request"
	"api/internal/response"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) allRules(ctx *gin.Context) {
	rules, err := h.srv.AllRules(ctx)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", rules)
}

func (h *handle) addRule(ctx *gin.Context) {
	var req request.AddRule

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.Translate(err), nil)
		return
	}

	err := h.srv.AddRule(ctx, req)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.<PERSON>rror(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}

func (h *handle) updateRule(ctx *gin.Context) {
	var req request.UpdateRule

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.Translate(err), nil)
		return
	}

	ruleID := ctx.Param("rule_id")

	if ruleID == "" {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.RequestParamMiss, nil)
		return
	}

	err := h.srv.UpdateRule(ctx, ruleID, req)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}

func (h *handle) deleteRule(ctx *gin.Context) {
	ruleID := ctx.Param("rule_id")

	if ruleID == "" {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.RequestParamMiss, nil)
		return
	}

	err := h.srv.DeleteRule(ctx, ruleID)

	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", nil)
}
