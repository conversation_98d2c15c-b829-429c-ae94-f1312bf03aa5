GET http://10.10.11.212:61234/api/v1/rules
Accept: application/json

###
POST http://10.10.11.212:61234/api/v1/rules
Content-Type: application/json

{
  "product": "elementUI",
  "rule": "body=\"js/chunk-elementUI\"",
  "rule_id": "100003",
  "level": 1,
  "category": "elementUI",
  "parent_category": "elementUI",
  "softhard": "1",
  "company": "1",
  "user_id": "1",
  "from": "foradr"
}

###

PUT http://127.0.0.1:61234/api/v1/rules/100003
Content-Type: application/json

{
  "product": "elementUI1",
  "rule": "body=\"js/chunk-elementUI\"",
  "rule_id": "100003",
  "level": 1,
  "category": "elementUI1",
  "parent_category": "elementUI",
  "softhard": "1",
  "company": "1",
  "user_id": "1",
  "from": "foradr"
}


###
DELETE http://10.10.11.212:61234/api/v1/rules/100003

