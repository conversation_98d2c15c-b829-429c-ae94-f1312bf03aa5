package request

import "git.gobies.org/shared-platform/foscan/pkg/model"

type AddProtocols struct {
	Protocols []model.Protocol `json:"protocols" binding:"required"`
}

type UpdateProtocol struct {
	// 名称
	// Required: true
	// Example: test_UDP
	Name string `json:"name" binding:"required"`
	// 协议类型
	// Required: true
	// Example: tcp
	ProtocolType string `json:"protocol_type" binding:"required"`
	// 绑定端口
	// Required: true
	// Example: 10,11,21,22
	BindPort string `json:"bind_port" binding:"required"`
	// 超时时间
	// Required: true
	// Example: 10
	Timeout uint `json:"timeout" binding:"required"`
	// 发包规则
	// Required: true
	Judge []model.RawGrabRuleJudge `json:"judge" binding:"required"`
}

type DeleteProtocol struct {
	All bool     `json:"all"`
	IDs []string `json:"ids"`
}
