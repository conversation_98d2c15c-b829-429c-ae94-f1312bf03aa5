DELETE http://127.0.0.1:61234/api/v1/protocols
Content-Type: application/json

{
  "ids": [
    "2",
    "2"
  ],
  "all": true
}

###
PUT http://127.0.0.1:61234/api/v1/protocols/1
Content-Type: application/json

{
  "id": "1",
  "name": "自定义协议1",
  "protocol_type": "协议类型",
  "bind_port": "1,2,3",
  "timeout": 10,
  "judge": [
    {
      "logic": [
        {
          "value": "",
          "content": "",
          "logic_type": "",
          "next_relation": "",
          "child": [
            {
              "position": "",
              "content": "",
              "value": ""
            }
          ]
        }
      ],
      "send_content": "1"
    }
  ]
}

###
POST http://127.0.0.1:61234/api/v1/protocols
Content-Type: application/json

[
  {
    "id": "1",
    "name": "自定义协议",
    "protocol_type": "协议类型",
    "bind_port": "1,2,3",
    "timeout": 10,
    "judge": [
      {
        "logic": [
          {
            "value": "",
            "content": "",
            "logic_type": "",
            "next_relation": "",
            "child": [
              {
                "position": "",
                "content": "",
                "value": ""
              }
            ]
          }
        ],
        "send_content": "1"
      }
    ]
  }
]

###
GET http://127.0.0.1:61234/api/v1/protocols
Accept: application/json

###
