package request

import (
	"api/internal/jsonx"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
)

// IPTarget IP目标
type IPTarget struct {
	// 扫描目标
	//
	// Example: ["**********", "**********/24"]
	List []string `json:"list,omitempty" binding:"omitempty,dive,ip|cidr"`

	// 扫描目标文件
	// 需要先调用文件上传接口，为该接口的返回值
	//
	// Required: false
	// Example: /tmp/upload/ip/ip-range-101.txt
	File string `json:"file,omitempty" binding:"omitempty"`

	// 扫描目标网络文件地址
	//
	// Example: http://www.xx.cc/ip-range-101.txt
	URL string `json:"url,omitempty" binding:"omitempty,url"`
}

// CreateOptions 任务下发配置选项
type PortScannerOptions struct {
	// 发包速率 默认值 100 packets/second
	//
	// Example: 100
	Rate uint `json:"rate"`

	// 排除扫描目标
	//
	// Example: ["**********", "**********/24"]
	Blacklist []string `json:"blacklist" binding:"omitempty,dive,ip|cidr"`

	// 是否启用ping识别资产
	//
	// Example: false
	PingScan bool `json:"ping_scan"`

	// 网关MAC地址
	//
	// Example: f4:6b:8c:63:b8:a8
	GatewayMac string `json:"gateway_mac" binding:"omitempty,mac"`

	// 扫描使用的网卡名称
	//
	// Example: eth0
	SendETH string `json:"send_eth,omitempty"`
	// 深度识别操作系统
	//
	// Example: false
	DeepGetOS bool `json:"deep_get_os"`

	// 深度获取MAC地址
	//
	// Example: false
	DeepGetMac bool `json:"deep_get_mac"`

	// 启用IPv6扫描
	//
	// Example: false
	IsIPV6 bool `json:"is_ipv6"`

	// 重复次数
	//
	// Example: 1
	Retries uint `json:"retries"`

	// 启用treck协议识别资产
	//
	// Example: false
	TreckScan bool `json:"treck_scan"`

	// 任务恢复信息
	ResumeFilename string `json:"resume_filename"`
}

// PortScanner 资产扫描配置项
type PortScanner struct {
	// 扫描任务类型, 0:msscan, 1:nmap
	//
	// Required: true
	// Example: 0
	TaskType uint `json:"task_type,omitempty" binding:"oneof=0 1"`

	// 扫描对象配置
	//
	// Required: true
	Options PortScannerOptions `json:"options,omitempty" binding:"required"`
}

// Vulnerability 漏洞扫描配置
type Vulnerability struct {
	// FofaUrl
	//
	// Required: false
	// Example: http://fofa.so
	FofaUrl string `json:"fofa_url,omitempty"`

	// ExploitName
	//
	// Required: false
	// Example: ["exp_json_encode.json"]
	ExploitName []string `json:"exploit_name,omitempty"`

	// ExploitFile
	//
	// Required: false
	// Example: ["exp_json_encode.json"]
	ExploitFile []string `json:"exploit_file,omitempty"`
	// Operation
	//
	// 可选值：s 和 e 。s为poc扫描，e为执行exp
	// Required: false
	// Example: s
	Operation string `json:"operation,omitempty"`

	// Targets 只下属漏洞扫描/验证时使用到, 资产扫描会忽略此字段
	//
	// Required: false
	// Example: ["127.0.0.1:3306", "127.0.0.1:6375"]
	Target []string `json:"target,omitempty"`
}

// CreateRequest request of Create
type CreateRequest struct {
	// 任务ID
	//
	// Required: true
	// Example: xx-xx-xxxx
	TaskID string `json:"task_id,omitempty" binding:"required"`

	// 扫描目标端口
	//
	// Required: true
	// Example: 0-65535,21,22,T:33:ssh,U:161,U:162:snmp
	Ports *string `json:"ports,omitempty" binding:"omitempty"`

	// 扫描目标对象
	//
	// Required: true
	IP *IPTarget `json:"ip,omitempty" binding:"omitempty"`

	// 主机信息
	//
	// Example: ["abc","def"]
	HostInfos []string `json:"host_infos"`

	// 资产扫描配置
	//
	// Required: true
	PortScanner *PortScanner `json:"port_scanner,omitempty"`

	// 漏洞扫描配置
	//
	// Required: true
	Vulnerability *Vulnerability `json:"vulnerability,omitempty"`

	Options RequestOptions `json:"options,omitempty"`
}

// RPCRequest convert to related rpc request
func (req *CreateRequest) RPCRequest() (*rpcx.DispatcherTaskStartRequest, error) {
	pt := &rpcx.DispatcherTaskStartRequest{
		TaskId:    req.TaskID,
		HostInfos: req.HostInfos,
	}

	if req.Ports != nil {
		pt.Ports = strings.Split(*req.Ports, ",")
		if *req.Ports == "" {
			pt.Ports = []string{}
		}
	}

	if req.PortScanner != nil {
		// 给一个默认值,否则会造成扫描卡死问题
		if req.PortScanner.Options.Rate == 0 {
			req.PortScanner.Options.Rate = 100
		}
		// ResumeFilename

		pt.PortScanner = &rpcx.DispatcherPortScannerConfig{
			TaskType: uint32(req.PortScanner.TaskType),
			Options: &rpcx.DispatcherPortScannerOption{
				Rate:       Int[int32](req.PortScanner.Options.Rate),
				Blacklist:  JoinString(req.PortScanner.Options.Blacklist, ","),
				PingScan:   &req.PortScanner.Options.PingScan,
				GatewayMac: &req.PortScanner.Options.GatewayMac,
				Retries:    Int[int32](req.PortScanner.Options.Retries),
				SendEth:    &req.PortScanner.Options.SendETH,
				DeepGetOs:  &req.PortScanner.Options.DeepGetOS,
				DeepGetMac: &req.PortScanner.Options.DeepGetMac,
				IsIpv6:     &req.PortScanner.Options.IsIPV6,
				TreckScan:  &req.PortScanner.Options.TreckScan,
			},
		}
	}

	if req.Vulnerability != nil {
		pt.Vulnerability = &rpcx.VulnerabilityConfig{
			FofaUrl:     req.Vulnerability.FofaUrl,
			ExploitName: req.Vulnerability.ExploitName,
			ExploitFile: req.Vulnerability.ExploitFile,
			Target:      req.Vulnerability.Target,
			Operation:   req.Vulnerability.Operation,
		}
	}

	pt.Options = &rpcx.DispatcherTaskStartOptions{
		GrabConcurrent:      int32(req.Options.GrabConcurrent),
		ProtocolUpdateCycle: int32(req.Options.ProtocolUpdateCycle),
		UnknownProtocolIndb: req.Options.UnknownProtocolInDB,
		MaxAssetNum:         int32(req.Options.MaxAssetNum),
		CrawlerAllUrl:       req.Options.CrawlerAllUrl,
		CrawlerUrlBlackKey:  req.Options.CrawlerUrlBlackKey,
		CrawlerSpecificUrl:  req.Options.CrawlerSpecificURL,
	}

	if req.Options.Extra != nil {
		s, err := structpb.NewStruct(req.Options.Extra)
		if err != nil {
			return nil, errors.Wrap(err, "req.Options.Extra transfer to protobuf struct error")
		}
		extra, err := anypb.New(s)

		if err != nil {
			return nil, errors.Wrap(err, "req.Options.Extra transfer from protobuf struct to protobuf any error")
		}
		pt.Options.Extra = extra
	}

	if len(req.Options.IPDomainRelations) > 0 {
		r := make(map[string]*rpcx.StringList)

		for k, v := range req.Options.IPDomainRelations {
			r[k] = &rpcx.StringList{
				Domains: v,
			}
		}

		pt.Options.IpDomainRelations = r
	}

	if req.Options.PortGroup != nil {
		var pg []*rpcx.PortGroupItem

		err := jsonx.Convert(req.Options.PortGroup, &pg)
		if err != nil {
			return nil, errors.Wrap(err, "req.Options.PortGroup Convert error")
		}
		pt.Options.PortGroup = pg
	}

	return pt, nil
}

// RequestOptions
type RequestOptions struct {
	// 协议识别并发数
	//
	// Example: 0
	GrabConcurrent uint `json:"grab_concurrent,omitempty"`

	// 协议更新周期，0表示无限制，其他表示更新间隔秒
	//
	// Example: 0
	ProtocolUpdateCycle uint `json:"protocol_update_cycle,omitempty"`

	// 未知协议入库
	//
	// Example: false
	UnknownProtocolInDB bool `json:"unknown_protocol_indb,omitempty"`

	// 最大资产数,大于0表示限制
	//
	// Example: 0
	MaxAssetNum uint `json:"max_asset_num,omitempty"`
	// 全协议识别
	FullProtocolDetect bool `json:"full_protocol_detect,omitempty"`

	// 是否爬取全站url
	CrawlerAllUrl bool `json:"crawler_all_url,omitempty"`
	// 爬取url黑名单，使用竖线分隔
	CrawlerUrlBlackKey string `json:"crawler_url_black_key,omitempty"`
	// 是否开启域名自动解析
	ResolveHost bool `json:"resolve_host,omitempty"`
	// 如果开启域名解析需要带入需要dns服务器地址
	// Example: ***************:53
	NameServer string `json:"name_server,omitempty"`

	// 爬取指定URL
	//  Example: /aabbccddee|/druid/basic.json|/containers
	CrawlerSpecificURL string `json:"crawler_specific_url,omitempty"`

	// 端口分组
	//
	// Example: [{"ports":"80,443","ip_array":["***********","***********"]}]
	PortGroup         []PortGroupItem        `json:"port_group" binding:"omitempty"`
	IPDomainRelations map[string][]string    `json:"ip_domain_relations" binding:"omitempty"`
	Extra             map[string]interface{} `json:"extra"  binding:"omitempty"`
}

// CreateResponse response of Create
type CreateResponse struct {
	// 任务 ID
	//
	// Required: true
	TaskID string `json:"task_id"`
}
