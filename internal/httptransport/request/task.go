package request

import (
	"api/internal"
	"errors"
	"fmt"
	"strconv"
)

func (req *AddTaskRequest) GetBandwidth() (uint, error) {
	i, err := strconv.Atoi(req.Bandwidth)
	if err != nil {
		return 0, err
	}

	if i <= 0 {
		return 0, errors.New(fmt.Sprintf("Bandwidth %s: %d", internal.ParamInValid, i))
	}

	return uint(i), nil
}

type AddTaskRequest struct {
	// 任务ID
	//
	// Required: true
	// Example: xx-xx-xxxx
	TaskID string `json:"task_id,omitempty" binding:"required"`

	// 扫描目标端口
	//
	// Required: true
	// Example: 0-65535,21,22,T:33:ssh,U:161,U:162:snmp
	Ports string `json:"ports,omitempty" binding:"omitempty"`

	// 扫描类型
	//
	// Required: true
	// Example: common
	TaskType string `json:"task_type,omitempty" binding:"omitempty"`

	// 扫描目标列表
	//
	// Required: true
	// Example: 0
	IPList string `json:"ip_list,omitempty" binding:"omitempty"`

	// 扫描目标的文件路径
	//
	// Required: true
	// Example: 0
	IPListFileName string `json:"ip_list_filename,omitempty" binding:"omitempty"`

	// 扫描带宽
	Bandwidth string `json:"bandwidth,omitempty" binding:"required"`

	// 重复次数
	RepeatTimes uint `json:"repeat_times,omitempty"`

	// 排除扫描目标
	//
	// Example: **********,**********/24
	Blacklist string `json:"blacklist,omitempty" binding:"omitempty"`

	// 协议更新周期，0表示无限制，其他表示更新间隔秒
	//
	// Example: 0
	ProtocolUpdateCycle uint `json:"protocol_update_cycle,omitempty"`
	// 未知协议入库
	//
	// Example: false
	UnknownProtocolInDB bool `json:"unknown_protocol_indb,omitempty"`
	// 是否启用ping识别资产
	//
	// Example: false
	PingScan bool `json:"ping_scan,omitempty"`

	// 深度识别操作系统
	//
	// Example: false
	DeepGetOS bool `json:"deep_get_os,omitempty"`
	// 深度获取MAC地址
	//
	// Example: false
	DeepGetMac bool `json:"deep_get_mac,omitempty"`

	// 网关MAC地址
	//
	// Example: false
	GatewayMac string `json:"gateway_mac,omitempty"`
	// 主机信息
	//
	// Example: ["abc","def"]
	HostInfos []string `json:"hostinfos,omitempty"`

	// 暂停的任务继续运行的时候必须要
	ResumeFilename string `json:"resume_filename,omitempty"`

	// 最大资产数,大于0表示限制
	//
	// Example: 0
	MaxAssetNum uint `json:"max_asset_num,omitempty"`
	// 启用IPv6扫描
	//
	// Example: false
	IsIPV6 bool `json:"is_ipv6,omitempty"`

	// 启用treck协议识别资产
	//
	// Example: false
	TreckScan bool `json:"treck_scan,omitempty"`
	// 协议并发抓取数量
	// Example: 10
	GrabConcurrent uint `json:"grab_concurrent,omitempty"`
	// 扫描使用的网卡名称
	//
	// Example: eth0
	SendETH string `json:"send_eth,omitempty"`
	// 是否爬取全站url
	CrawlerAllUrl bool `json:"crawler_all_url,omitempty"`
	// 爬取url黑名单，使用竖线分隔 URL如果包含就不爬取
	CrawlerUrlBlackKey string `json:"crawler_url_black_key,omitempty"`

	// 爬取指定URL
	//  Example: /aabbccddee|/druid/basic.json|/containers
	CrawlerSpecificURL string `json:"crawler_specific_url,omitempty"`

	// 端口分组
	//
	// Example: [{"ports":"80,443","ip_list":["***********","***********"]}]
	PortGroup []PortGroupItem `json:"port_group" binding:"omitempty"`

	// ip域名对应关系 一对多
	//
	// Example: [{"***********":["a.com","b.com"]}]
	IPDomainRelations map[string][]string `json:"ip_domain_relations" binding:"omitempty"`

	// 透传字段，原样入库到service、subdomain
	//
	// Example: {"user_id":"111","other":"2","another":[1,2,3]}
	Extra map[string]interface{} `json:"extra"  binding:"omitempty"`
}

type TaskIDRequest struct {
	// 任务ID
	//
	// Required: true
	TaskID string `json:"task_id" binding:"required"`
}

type TaskResumeRequest struct {
	// 任务ID
	//
	// Required: true
	TaskID string `json:"task_id" binding:"required"`
	// 扫描带宽
	Bandwidth string `json:"bandwidth,omitempty"`
}

func (req *TaskResumeRequest) GetRate() (*int32, error) {
	i, err := strconv.Atoi(req.Bandwidth)

	if err != nil {
		return nil, err
	}

	return Int[int32](uint(i)), nil
}

type PortGroupItem struct {
	// IP列表
	//
	// Example: ["***********","***********"]
	IPList []string `json:"ip_list" binding:"omitempty"`

	// 端口
	//
	// Example: 90,100,111
	Ports string `json:"ports" binding:"omitempty"`
}
