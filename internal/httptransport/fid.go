package httptransport

import (
	"api/internal/httptransport/request"
	response2 "api/internal/httptransport/response"
	"api/internal/response"
	"git.gobies.org/shared-platform/fid_service/pkg/fid"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) getFID(ctx *gin.Context) {
	var req request.GetFID
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	srv := fid.NewFidService()

	foid, err := srv.GetFid(req.URL)
	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", response2.Fid{
		Fid: foid,
	})
}
