package httptransport

import (
	"api/internal"
	"api/internal/response"
	"git.gobies.org/fofa-backend/fofacore/datastruct/request"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) parse(ctx *gin.Context) {
	var req request.QueryInput
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	if len(req.Query) == 0 {
		response.Return(ctx, http.StatusUnprocessableEntity, internal.RequestParamMiss, nil)
		return
	}

	q, err := DecQBase64(req.Query)

	if err != nil {
		response.Return(ctx, http.StatusUnprocessableEntity, err.Error(), nil)
		return
	}

	if len(req.ESSubdomain) == 0 {
		req.ESSubdomain = "fofaee_subdomain"
	}

	if len(req.ESService) == 0 {
		req.ESService = "fofaee_service"
	}

	if len(req.ESIps) == 0 {
		req.ESIps = "fofaee_assets"
	}

	req.Query = q
	queryParse, err := h.srv.QueryParse(req)
	if err != nil {
		response.Return(ctx, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	response.Return(ctx, http.StatusOK, "", queryParse)
}
