package response

type GetIDKeyResponse struct {
	IDKey string `json:"id_key"`
}

type GetSystemInfoResponse struct {
	// Version
	//
	// Required: true
	// Example: "v1.0"
	Version string `json:"version"`

	// LicenseState
	//
	// Required: true
	// Example: true
	LicenseState bool `json:"license_state"`

	// AssetLimitNum
	//
	// Required: true
	// Example: 10000
	AssetLimitNum int `json:"asset_limit_num"`

	// ProductLimitDate
	//
	// Required: true
	// Example: "2020-05-01"
	ProductLimitDate string `json:"product_limit_date"`

	// LicenseState
	//
	// Required: true
	// Example: "2020-05-01"
	UpgradeLimitDate string `json:"upgrade_limit_date"`

	Modules Modules `json:"modules"`
}

type Modules struct {
	Asset           int `json:"asset"`
	Compliance      int `json:"compliance"`
	CustomPoc       int `json:"custom_poc"`
	CustomProtocol  int `json:"custom_protocol"`
	FeatureEngine   int `json:"feature_engine"`
	KafkaConnection int `json:"kafka_connection"`
	Report          int `json:"report"`
	Threat          int `json:"threat"`
	FoFaLink        int `json:"fofa_link"`
	RiskModule      int `json:"risk_module"`
	AssetPanorama   int `json:"asset_panorama"`
}
