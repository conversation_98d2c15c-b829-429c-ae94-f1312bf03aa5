package httptransport

import (
	"api/internal"
	"api/internal/license"
	"api/internal/middleware"
	"api/internal/service"
	"github.com/gin-gonic/gin"
	"go-micro.dev/v4/logger"
	"net/http"
	"strconv"
)

type handle struct {
	srv     service.Service
	license *license.License
}

func NewGinRouter(srv service.Service, license *license.License) http.Handler {
	internal.TranslateInit()

	h := &handle{
		srv: srv,
	}

	r := gin.New()

	// license
	r.GET("/api/v1/system/infos", h.getSystemInfo)
	r.GET("/api/v1/id-key", h.getIDKey)
	r.POST("/api/v1/activated", h.activatedLicense)

	v1 := r.Group("/api/v1/")
	licenseVerifySwitch, err := strconv.ParseBool(internal.LicenseVerifySwitch)

	if err != nil {
		logger.Warnf("the build param internal.LicenseVerifySwitch:%s is not success", internal.LicenseVerifySwitch)
	}

	if err == nil && licenseVerifySwitch {
		v1.Use(middleware.License(license))
	}

	// task
	v1.POST("tasks/add", h.addTask)
	v1.GET("tasks/:task_id", h.getTask)
	v1.POST("tasks/pause", h.pauseTask)
	v1.POST("tasks/resume", h.resumeTask)
	v1.POST("tasks/stop", h.deleteTask)

	// protocol
	v1.GET("protocols", h.allProtocols)
	v1.POST("protocols", h.addProtocols)
	v1.PUT("protocols/:id", h.updateProtocols)
	v1.DELETE("protocols", h.deleteProtocol)

	// rule
	v1.GET("rules", h.allRules)
	v1.POST("rules", h.addRule)
	v1.PUT("rules/:rule_id", h.updateRule)
	v1.DELETE("rules/:rule_id", h.deleteRule)

	// query_parse
	v1.POST("query_parse", h.parse)

	// foid
	v1.POST("fid/query", h.getFID)

	return r
}
