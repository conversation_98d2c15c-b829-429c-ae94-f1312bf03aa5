package task

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"strings"
	"testing"
	"time"
)

var tmpMasscanResult [][4]string

func masscanProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {
	fmt.Println("  masscan progress  tid:", tid, "state:", state, "progress:", progress, "remaintime:", remainTime, "errmsg:", errMsg)
}

//func startTestPortServ() string {
//	retPort := "51368"
//	listener, err := net.Listen("tcp", ":0")
//	if err != nil {
//		panic(err)
//	}
//
//	retPort = strconv.Itoa(listener.Addr().(*net.TCPAddr).Port)
//	fmt.Println("  tmp port is:", retPort)
//
//	go func() {
//		panic(http.Serve(listener, nil))
//	}()
//
//	return retPort
//}

func tmpMasscanHandleTaskOut(ch <-chan [][4]string, options map[string]interface{}) {
	if ch == nil {
		log.Println("tmp_handle_task_output ch is nil")
		return
	}

	for hostports := range ch {
		if hostports == nil {
			log.Println("tmp_handle_task_output done")
			return
		}

		fmt.Println("  found masscan result:", hostports)
		tmpMasscanResult = hostports
	}

	log.Println("  tmp_handle_task_output finished")
}

func TestNewMasscan(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan
	curDir, _ := os.Getwd()
	if strings.Contains(curDir, "/internal/task") {
		cfg.dir = curDir + "/../../tools"
	} else {
		cfg.dir = curDir + "/tools"
	}

	//设置回调
	cfg.SetCallback(nil, masscanProgress, nil)

	mass := NewMasscan(&cfg)
	assert.NotNil(t, mass)

	assert.False(t, mass.ResumeFileExists())

	_, err := mass.HandlePaused("", 1)
	assert.Nil(t, err)

	execPath := os.Getenv("PATH")
	execPath += ":" + cfg.dir
	fmt.Println("  exec PATH:", execPath)

	err = os.Setenv("PATH", execPath)
	assert.Nil(t, err)

	//port := startTestPortServ()
	time.Sleep(100 * time.Millisecond) //  稍微暂停一下

	mass.batchSize = 1
	mass.ipMaxPortCount = 100
	mass.reserveInnerIp = true
	mass.sourcePort = 58914 // 不设置这个扫描不出来结果
	ch, err := mass.Run("100", "**********", "80,443", "", "", "", 1, 1)
	assert.Nil(t, err)

	time.Sleep(300 * time.Millisecond)

	tmpMasscanHandleTaskOut(ch, nil)

	if len(tmpMasscanResult) > 0 {
		assert.Equal(t, "**********", tmpMasscanResult[0][0])
	}

	mass.Stop()
}
