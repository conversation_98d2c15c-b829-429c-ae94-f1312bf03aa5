package task

import (
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/util"
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func createTmpFile(filename string) error {
	fileTmp, err := os.OpenFile(filename, os.O_TRUNC|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return err
	}

	fileTmp.Write([]byte("abcd"))

	return nil
}

func TestParseHostport(t *testing.T) {
	var retTag bool
	var pHostObj PortHostinfo
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	assert.NotNil(t, m)

	retTag, _ = m.parseHostport(nil, -1)
	assert.False(t, retTag)

	retTag, _ = m.parseHostport([]byte("abcd"), 1)
	assert.False(t, retTag)

	m.reserveInnerIp = true
	retTag, pHostObj = m.parseHostport([]byte("Discovered open port 443/tcp on **********"), 1)
	assert.True(t, retTag)
	assert.Equal(t, "**********", pHostObj.ip)

	// 虽然返回false，不过内容还是有的
	m.reserveInnerIp = false
	retTag, pHostObj = m.parseHostport([]byte("Discovered open port 443/tcp on **********"), 1)
	assert.False(t, retTag)
	assert.Equal(t, "**********", pHostObj.ip)
}

func TestAppendIpPortNum(t *testing.T) {
	var taskid, ip, keys string
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	assert.NotNil(t, m)

	taskid = "1"
	ip = "*******"
	keys = taskid + "_" + ip
	m.appendIpPortNum(taskid, ip)
	assert.Equal(t, 1, m.mapIpPortCount[keys])
	m.appendIpPortNum(taskid, ip)
	assert.Equal(t, 2, m.mapIpPortCount[keys])
}

func TestProcUdpAllReturn(t *testing.T) {
	var retTag bool
	var cfg ScantaskBaseConf
	var hostport PortHostinfo
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	assert.NotNil(t, m)

	queues := queue.NewQue(10)
	hostport.baseProto = "tcp"
	retTag = m.procUdpAllReturn(hostport, queues)
	assert.Empty(t, queues.Size())
	assert.False(t, retTag)

	hostport.baseProto = "udp"
	hostport.ip = "127.0.0.1"
	retTag = m.procUdpAllReturn(hostport, queues)
	assert.Equal(t, 1, queues.Size())
	assert.False(t, retTag)

	//fmt.Println("queues:", queues)

	// 第二次已经存在了
	retTag = m.procUdpAllReturn(hostport, queues)
	assert.Equal(t, 1, queues.Size())
	assert.True(t, retTag)

	// 不同ip入队列
	hostport.ip = "**********"
	retTag = m.procUdpAllReturn(hostport, queues)
	assert.Equal(t, 2, queues.Size())
	assert.False(t, retTag)
}

func TestAppendIpPorts(t *testing.T) {
	var retSt bool
	var taskid, baseProto, ip, port, keys string
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	assert.NotNil(t, m)

	retSt = m.appendIpPorts(taskid, baseProto, ip, port)
	assert.False(t, retSt)
	keys = taskid + "_" + baseProto + "_" + ip + "_" + port
	assert.Equal(t, 1, m.mapIPPorts[keys])

	retSt = m.appendIpPorts(taskid, baseProto, ip, port)
	assert.True(t, retSt)
	keys = taskid + "_" + baseProto + "_" + ip + "_" + port
	assert.Equal(t, 2, m.mapIPPorts[keys])
}

func TestReleaseTaskCache(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	m.tid = 123

	m.appendIpPorts(strconv.Itoa(m.tid), "TCP", "**********0", "3306")
	m.appendIpPorts("456", "UDP", "************", "161")

	assert.Equal(t, 2, len(m.mapIPPorts))

	m.releaseTaskCache()
	assert.Equal(t, 1, len(m.mapIPPorts))
}

func TestRemovePausedFile(t *testing.T) {
	var exists bool
	var err error
	curDir, _ := os.Getwd()
	filename := curDir + "/test-paused-file-unittest.tmp"

	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	m := NewMasscan(&cfg)
	assert.NotNil(t, m)
	m.pausedFilename = filename

	m.removePausedFile()
	exists, err = util.FileExists(filename)
	assert.False(t, exists)

	err = createTmpFile(filename)
	assert.Nil(t, err)

	m.removePausedFile()
	exists, err = util.FileExists(filename)
	assert.False(t, exists)
}
