package task

import (
	"log"
	"os"
	"os/exec"
	"regexp"
	"sync"
)

type EngineType int32

const (
	ETMasscan         EngineType = 0
	ETNmap            EngineType = 1
	ETMasscanPing     EngineType = 2
	ETNmapPing        EngineType = 3
	ETTreckScan       EngineType = 4
	ETMasscanWithPing EngineType = 7
	ETNmapWithPing    EngineType = 8
)

type ScantaskBaseConf struct {
	blackipList     string     // 配置文件里面的黑名单
	typ             EngineType // 引擎类型
	dir             string     // 执行程序目录
	pausedFilename  string     // 暂停文件名
	sourcePort      int        // 源端口
	hostname        string     // 主机名
	putExecInfo     bool       // 是否上传扫描IP信息
	reserveInnerIp  bool       // 是否保留内网IP
	ignoreProced    bool       // 是否过滤处理过的IP
	batchSize       int        // 一个批次上传的数据大小
	mu              sync.Mutex
	done            chan struct{}
	shutdown        bool
	fnExec          ExecCallback          // 扫描IP信息的回调函数
	fnProgress      ProgressCallback      // 扫描进度的回调函数
	fnChkLastProced ChkLastProcedCallback // 最近是否扫描过的回调函数
	scanEngine      interface{}           // 扫描引擎信息（回调用）
	ipMaxPortCount  int                   // 同一个IP最大上报端口个数
	maxAssetNum     int                   // 最大资产数限制
	errMsg          string                // 报错信息
	routerMac       string                // 路由MAC
}

type PortscanStub interface {
	SetCallback(fn ExecCallback, fnProg ProgressCallback, fnLastChk ChkLastProcedCallback)                                 //设置回调
	SetScanEngine(engine interface{})                                                                                      // 设置当前扫描引擎
	SetEngType(typ EngineType)                                                                                             // 设置引擎类型
	SetMaxAssetNum(num int)                                                                                                // 设置最大资产数
	IsAssetExceedLimit() bool                                                                                              // 是否资产超过限制
	HandlePaused(blackIps string, tid int) (<-chan [][4]string, error)                                                     // 继续任务
	Run(rate, hosts, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error)        // 运行任务
	RunFile(rate, filename, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error) // 运行任务
	MergeExcludeIps(ips string) string                                                                                     // 合并黑名单
	Stop() error                                                                                                           // 停止任务
	IsStopSt() bool                                                                                                        // 获取停止状态
	IsFinished() bool                                                                                                      // 获取完成状态
	ResumeFileExists() bool                                                                                                // 继续运行文件存在
	SetScanIPv6(ipv6 bool)                                                                                                 // 设置是否扫描IPv6
	HandleResume(pausedFile string, blackIps string, tid int) (<-chan [][4]string, error)                                  // 指定恢复文件继续任务
	GetPausedFile() string                                                                                                 // 获得默认暂停文件
}

// 运行信息的回调
type ExecCallback func(classObj interface{}, ip string)
type ProgressCallback func(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string)
type ChkLastProcedCallback func(id string) bool // 检查是否最近已经处理过的回调函数

type PortHostinfo struct {
	port      string
	baseProto string
	ip        string
}

type PortCmd struct {
	*exec.Cmd
	Killed bool
}

var regLocalhost = regexp.MustCompile(`(^127\.)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^192\.168\.)`)

func NewScantaskBaseConf(typ EngineType, dir, hostname, blackip string, sourcePort, batSize, sameIpSize int,
	putExec, reserveInner, ignoreProc bool) *ScantaskBaseConf {
	if dir == "" {
		if typ == 0 {
			dir = "/masscan"
		}
	}
	if dir[len(dir)-1] == '/' {
		dir = dir[:len(dir)-1]
	}

	return &ScantaskBaseConf{
		typ:            typ,
		dir:            dir,
		pausedFilename: dir + "/paused.conf",
		blackipList:    blackip,
		sourcePort:     sourcePort,
		hostname:       hostname,
		putExecInfo:    putExec,
		reserveInnerIp: reserveInner,
		ignoreProced:   ignoreProc,
		batchSize:      batSize,
		ipMaxPortCount: sameIpSize,
	}
}

func (sbc *ScantaskBaseConf) GetDir() string {
	return sbc.dir
}

func (sbc *ScantaskBaseConf) FileExists(filename string) bool {
	_, err := os.Stat(filename)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	log.Println("[ERROR]:", err)
	return false
}

func (sbc *ScantaskBaseConf) IpIsInner(ip string) bool {
	if regLocalhost.Match([]byte(ip)) {
		return true
	} else {
		return false
	}
}

func (sbc *ScantaskBaseConf) MergeExcludeIps(ips string) string {
	allExcludes := "***************"
	if len(ips) > 6 {
		allExcludes += "," + ips
	}

	if len(sbc.blackipList) > 6 {
		if string(allExcludes[len(allExcludes)-1]) != "," && string(sbc.blackipList[0]) != "," {
			allExcludes += ","
		}

		allExcludes += sbc.blackipList
	}

	return allExcludes
}

func (sbc *ScantaskBaseConf) SetCallback(fn ExecCallback, fnProg ProgressCallback, fnLastChk ChkLastProcedCallback) {
	if fn != nil {
		sbc.fnExec = fn
	}

	if fnProg != nil {
		sbc.fnProgress = fnProg
	}

	if fnLastChk != nil {
		sbc.fnChkLastProced = fnLastChk
	}
}

func (sbc *ScantaskBaseConf) SetScanEngine(engine interface{}) {
	sbc.scanEngine = engine
}

func (sbc *ScantaskBaseConf) SetEngType(typ EngineType) {
	sbc.typ = typ
}

func (sbc *ScantaskBaseConf) SetRouterMac(mac string) {
	sbc.routerMac = mac
}

func NewScantask(cfg *ScantaskBaseConf) PortscanStub {
	if cfg.typ == ETMasscan || cfg.typ == ETMasscanPing || cfg.typ == ETMasscanWithPing {
		return NewMasscan(cfg)
	} else if cfg.typ == ETNmap || cfg.typ == ETNmapPing || cfg.typ == ETNmapWithPing {
		return NewNmapScan(cfg)
	} else if cfg.typ == ETTreckScan {
		return NewMasscan(cfg)
	}

	return nil
}
