package task

import (
	"bytes"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"runtime"
	"strings"
	"testing"
)

func TestMasscan_SetMaxAssetNum(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	mass := NewMasscan(&cfg)
	assert.NotNil(t, mass)
	mass.SetMaxAssetNum(1000)
	assert.Equal(t, 1000, mass.maxAssetNum)

	assert.False(t, mass.IsAssetExceedLimit())
	assert.False(t, mass.IsStopSt())

	mass.SetMaxAssetNum(-1)
	assert.False(t, mass.IsAssetExceedLimit())
}

func TestMasscan_RunCmd(t *testing.T) {
	var hosts, filename, ports, rate, excludes, sendEth, blackFilename string
	var isHost bool
	var retryNum int

	// 处理目录
	curDir, _ := os.Getwd()
	if strings.Contains(curDir, "internal") {
		loc := strings.Index(curDir, "/internal")
		curDir = curDir[0:loc]
	}

	fmt.Println("os:", runtime.GOOS)
	// 不同系统路径修改
	masscanProgramDir := curDir + "/tools/masscan"
	masscantreckProgramDir := curDir + "/tools/masscan_treck"
	if runtime.GOOS != "darwin" {
		masscanProgramDir = "/usr/local/bin/masscan"
		masscantreckProgramDir = "masscan_treck"
	}

	var cfg ScantaskBaseConf
	cfg.typ = ETMasscan

	mass := NewMasscan(&cfg)

	assert.NotNil(t, mass)
	mass.runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename, isHost, retryNum)
	assert.Equal(t, masscanProgramDir+" -iL  --ports  --exclude  --source-port 0 --rate  --wait 1", mass.cmd.String())

	cfg.typ = ETMasscanPing
	hosts = "127.0.0.1"
	isHost = true
	mass.runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename, isHost, retryNum)
	assert.Equal(t, masscanProgramDir+" 127.0.0.1 --ping --exclude  --source-port 0 --rate  --wait 1", mass.cmd.String())

	cfg.typ = ETTreckScan
	mass.routerMac = "11:22:33:44:55:66"
	retryNum = 2
	isHost = false
	sendEth = "eth1"
	blackFilename = "abcd.black.list"
	mass.runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename, isHost, retryNum)
	assert.Equal(t, masscantreckProgramDir+" -iL  --ping --router-mac 11:22:33:44:55:66 --retries 2 --adapter eth1 --excludefile abcd.black.list --source-port 0 --rate  --wait 1", mass.cmd.String())

	hosts = "127.0.0.1"
	isHost = true
	mass.runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename, isHost, retryNum)
	assert.Equal(t, masscantreckProgramDir+" 127.0.0.1 --ping --router-mac 11:22:33:44:55:66 --retries 2 --adapter eth1 --excludefile abcd.black.list --source-port 0 --rate  --wait 1", mass.cmd.String())

	cfg.typ = ETMasscan
	ports = "80"
	mass.runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename, isHost, retryNum)
	assert.Equal(t, masscanProgramDir+" 127.0.0.1 --ports 80 --router-mac 11:22:33:44:55:66 --retries 2 --adapter eth1 --excludefile abcd.black.list --source-port 0 --rate  --wait 1", mass.cmd.String())

	var stderr bytes.Buffer
	go mass.runCrontab(&stderr)
	//go mass.runInit()

	mass.procFinish()
}
