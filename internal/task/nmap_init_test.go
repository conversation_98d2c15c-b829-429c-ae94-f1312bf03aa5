package task

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func TestNmap_SetMaxAssetNum(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	ns := NewNmapScan(&cfg)
	assert.NotNil(t, ns)
	ns.SetMaxAssetNum(1000)
	assert.Equal(t, 1000, ns.maxAssetNum)

	assert.False(t, ns.IsAssetExceedLimit())
	assert.False(t, ns.IsStopSt())

	ns.SetMaxAssetNum(-1)
	assert.False(t, ns.IsAssetExceedLimit())

	ns.SetScanIPv6(true)
	assert.True(t, ns.isScanIPv6)
}

func TestNmap_RunCmd(t *testing.T) {
	var hosts, filename, ports, rate, excludes, sendEth string
	var isHost, isIpv6 bool
	var retryNum int

	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	ns := NewNmapScan(&cfg)
	assert.NotNil(t, ns)
	ns.dir = "./"

	ns.runCmd(hosts, filename, ports, rate, excludes, "", sendEth, isHost, isIpv6, retryNum)
	ns.cmd.Dir = "./"
	ns.cmd.Path = "nmap"
	assert.True(t, strings.Contains(ns.cmd.String(), "nmap -iL  -sS -sU -Pn -p  --source-port 0 --min-rate  --max-rate  -n -v -T3 --open --stats-every 5s --min-hostgroup 100 --max-hostgroup 100 -oG .gnmap "))

	cfg.typ = ETNmapPing
	hosts = "127.0.0.1"
	isHost = true
	ns.runCmd(hosts, filename, ports, rate, excludes, "", sendEth, isHost, isIpv6, retryNum)
	assert.True(t, strings.Contains(ns.cmd.String(), "nmap 127.0.0.1 -sn --source-port 0 --min-rate  --max-rate  -n -v -T3 --open --stats-every 5s --min-hostgroup 100 --max-hostgroup 100 -oG .gnmap "))

	ns.routerMac = "11:22:33:44:55:66"
	retryNum = 2
	isHost = false
	sendEth = "eth1"
	ns.runCmd(hosts, filename, ports, rate, excludes, "", sendEth, isHost, isIpv6, retryNum)
	assert.True(t, strings.Contains(ns.cmd.String(), "nmap -iL  -sn --max-retries 2 -e eth1 --source-port 0 --min-rate  --max-rate  -n -v -T3 --open --stats-every 5s --min-hostgroup 100 --max-hostgroup 100 -oG .gnmap "))

	hosts = "127.0.0.1"
	rate = "100"
	isHost = true
	ns.runCmd(hosts, filename, ports, rate, excludes, "", sendEth, isHost, isIpv6, retryNum)
	assert.True(t, strings.Contains(ns.cmd.String(), "nmap 127.0.0.1 -sn --max-retries 2 -e eth1 --source-port 0 --min-rate 100 --max-rate 100 -n -v -T3 --open --stats-every 5s --min-hostgroup 100 --max-hostgroup 100 -oG .gnmap "))

	var stderr bytes.Buffer
	go ns.runCrontab(&stderr)
	go ns.runInit()

	ns.procFinish()
}
