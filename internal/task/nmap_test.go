package task

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"strings"
	"testing"
	"time"
)

var tmpNmapResult [][4]string

func nmapProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {
	fmt.Println("  nmap progress  tid:", tid, "state:", state, "progress:", progress, "remaintime:", remainTime, "errmsg:", errMsg)
}

func tmpNmapHandleTaskOutput(ch <-chan [][4]string, options map[string]interface{}) {
	if ch == nil {
		log.Println("tmp_handle_task_output ch is nil")
		return
	}

	for hostports := range ch {
		if hostports == nil {
			log.Println("tmp_handle_task_output done")
			return
		}

		fmt.Println("  found nmap result:", hostports)
		tmpNmapResult = hostports
	}

	log.Println("  tmp_handle_task_output finished")
}

func TestNewNmapScan(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap
	curDir, _ := os.Getwd()
	if strings.Contains(curDir, "/internal/task") {
		cfg.dir = curDir + "/../../tools"
	} else {
		cfg.dir = curDir + "/tools"
	}

	//设置回调
	cfg.SetCallback(nil, nmapProgress, nil)

	ns := NewNmapScan(&cfg)
	assert.NotNil(t, ns)

	assert.False(t, ns.ResumeFileExists())

	_, err := ns.HandlePaused("", 1)
	assert.Nil(t, err)

	execPath := os.Getenv("PATH")
	execPath += ":" + cfg.dir
	fmt.Println("  exec PATH:", execPath)

	err = os.Setenv("PATH", execPath)
	assert.Nil(t, err)

	//port := startTestPortServ()
	time.Sleep(100 * time.Millisecond) //  稍微暂停一下

	ns.batchSize = 1
	ns.ipMaxPortCount = 100
	ns.reserveInnerIp = true
	ns.sourcePort = 58914 // 不设置这个扫描不出来结果
	ch, err := ns.Run("100", "**********", "80,443", "", "", "", 1, 1)
	assert.Nil(t, err)

	time.Sleep(300 * time.Millisecond)

	tmpNmapHandleTaskOutput(ch, nil)

	//assert.Equal(t, "**********", tmpNmapResult[0][0])

	ns.Stop()
}
