package task

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

func (m *NmapScan) parseHostport(line []byte, taskid int) (bool, PortHostinfo) {
	var hostinfo PortHostinfo
	retSt := true
	ctx := string(line)
	isRemain := m.isRemainContent(ctx)
	log.Printf("   %t  ctx=%s\n", isRemain, strings.Trim(ctx, " "))

	if !isRemain {
		return false, hostinfo
	}

	if m.fetchScanProgress(ctx) {
		return false, hostinfo
	}

	m.fetchHostCompleted(ctx)

	if m.typ == ETNmap {
		// 先提取端口结果
		portSt, tmpHostinfo := m.fetchPortResult(ctx)
		if !portSt {
			return false, hostinfo
		}

		hostinfo = tmpHostinfo
	} else if m.typ == ETNmapPing {
		//再提取ping结果
		pingSt, tmpHostinfo := m.fetchPingResult(ctx)
		if !pingSt {
			return false, hostinfo
		}

		hostinfo = tmpHostinfo
	}

	// 排除内网ip
	if !m.reserveInnerIp && m.IpIsInner(hostinfo.ip) {
		log.Println("[ERROR] nmap host is inner-ip", hostinfo.ip)
		retSt = false
	}

	if retSt {
		if m.putExecInfo && m.fnExec != nil {
			m.fnExec(m, hostinfo.ip)
		}
	}

	return retSt, hostinfo
}

func (m *NmapScan) isRemainContent(ctx string) bool {
	// 没有这些的直接过滤
	if !strings.Contains(ctx, "open port ") &&
		!strings.Contains(ctx, "Scan Timing:") &&
		!strings.Contains(ctx, "scan report for") &&
		!strings.Contains(ctx, "hosts completed") {
		return false
	}

	return true
}

// 提取进度
func (m *NmapScan) fetchScanProgress(line string) bool {
	fullMatch := regNmapFullProgress.MatchString(line)
	partMatch := regNmapPartProgress.MatchString(line)
	if !fullMatch && !partMatch {
		return false
	}
	var progress, remainingTime string

	if fullMatch {
		submatch := regNmapFullProgress.FindStringSubmatch(line)
		progress = submatch[1]
		remainingTime = submatch[2]
	} else if partMatch {
		submatch := regNmapPartProgress.FindStringSubmatch(line)
		progress = submatch[1]
	}

	if m.fnProgress != nil && !m.stoped && !m.finished {
		m.fnProgress(m.scanEngine, m.tid, "2", m.hostCompleted, progress, remainingTime, "")
	} else {
		log.Println("    progress not upload     ~~~")
	}
	return true
}

func (m *NmapScan) fetchPortResult(line string) (bool, PortHostinfo) {
	var hostinfo PortHostinfo

	// 正则提取host,port
	matches := RegNmap.FindStringSubmatch(line)
	if len(matches) < 3 {
		return false, hostinfo
	}

	hostinfo.port = matches[1]
	hostinfo.baseProto = matches[2]
	if m.isScanIPv6 {
		// IPv6必须特殊处理
		hostinfo.ip = "[" + matches[3] + "]"
	} else {
		hostinfo.ip = matches[3]
	}

	//fmt.Println("  port result", hostinfo)

	return true, hostinfo
}

func (m *NmapScan) fetchPingResult(line string) (bool, PortHostinfo) {
	var hostinfo PortHostinfo

	matches := regNmapPing.FindStringSubmatch(line)
	if len(matches) < 1 {
		return false, hostinfo
	}

	hostinfo.port = "0"
	hostinfo.baseProto = "icmp"
	hostinfo.ip = matches[1]

	//fmt.Println("  ping result", hostinfo)

	return true, hostinfo
}

func (m *NmapScan) fetchHostCompleted(line string) bool {
	// 正则提取host,port
	matches := regNmapCompleted.FindStringSubmatch(line)
	if len(matches) < 2 {
		return false
	}

	m.hostCompleted = matches[1]

	return true
}

//func (m *NmapScan) procUdpAllReturn(hostport PortHostinfo, queue *queue.Queue) bool {
//	retSt := false
//	// udp 排除总是返回的端口
//	if hostport.baseProto == "udp" {
//		checkQue := queue.CheckQue(hostport.ip)
//		if checkQue == true {
//			retSt = true
//		}
//		queue = queue.Poll(hostport.ip)
//	}
//
//	return retSt
//}

func (m *NmapScan) appendIpPortNum(taskid, ip string) int {
	keys := taskid + "_" + ip
	if _, ok := m.mapIpPortCount[keys]; ok {
		m.mapIpPortCount[keys] += 1
	} else {
		m.mapIpPortCount[keys] = 1
	}

	return m.mapIpPortCount[keys]
}

/*
追加ip:port信息到队列

返回true表示存在，false表示不存在
*/
func (m *NmapScan) appendIpPorts(taskid, baseProto, ip, port string) bool {
	exists := false
	keys := taskid + "_" + baseProto + "_" + ip + "_" + port
	if _, ok := m.mapIPPorts[keys]; ok {
		exists = true
		m.mapIPPorts[keys] += 1
	} else {
		m.mapIPPorts[keys] = 1
	}

	return exists
}

func (m *NmapScan) procFinish() {
	if !m.cmd.Killed {
		m.finished = true
		time.Sleep(1 * time.Second) // 暂停一下，防止发送5之后马上又出现2的情况

		log.Println("nmap stopped")
		if m.fnProgress != nil {
			m.fnProgress(m.scanEngine, m.tid, "5", m.hostCompleted, "100%", "00:00:00", "")
		}
		m.removePausedFile()
	} else {
		tmpPausedFilename := m.pausedFilename + ".gnmap"
		if m.FileExists(tmpPausedFilename) {
			log.Println("nmap exists paused file:", tmpPausedFilename)

			err := os.Rename(tmpPausedFilename, m.pausedFilename)
			if err != nil {
				log.Println("nmap mv paused file failed", err)
			}
		} else {
			log.Println("nmap paused file not exists", tmpPausedFilename)
		}

		state := "4"
		progress := "0%"
		// 如果是资产达到限制的，状态修改
		if m.IsAssetExceedLimit() {
			state = "5"
			progress = "100%"
		}
		m.fnProgress(m.scanEngine, m.tid, state, m.hostCompleted, progress, "", "")
	}
	m.releaseTaskCache()
}

func (m *NmapScan) releaseTaskCache() {
	for key, _ := range m.mapIpPortCount {
		if strings.HasPrefix(key, strconv.Itoa(m.tid)) {
			delete(m.mapIpPortCount, key)
		}
	}

	for key, _ := range m.mapIPPorts {
		if strings.HasPrefix(key, strconv.Itoa(m.tid)) {
			delete(m.mapIPPorts, key)
		}
	}
}

func (m *NmapScan) removePausedFile() {
	if m.FileExists(m.pausedFilename) {
		log.Println("rm paused file", m.pausedFilename)
		err := os.Remove(m.pausedFilename) // 正常退出，保证没有暂停文件
		if err != nil {
			log.Println("[ERROR] nmap remove paused file failed", err)
		}
	}
}
