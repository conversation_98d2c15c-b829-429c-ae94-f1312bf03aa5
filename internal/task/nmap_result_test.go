package task

import (
	"baimaohui/portscan_new/internal/util"
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNmapParseHostport(t *testing.T) {
	var retTag bool
	var pHostObj PortHostinfo
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)

	retTag, _ = m.parseHostport(nil, -1)
	assert.False(t, retTag)

	retTag, _ = m.parseHostport([]byte("abcd"), 1)
	assert.False(t, retTag)

	m.reserveInnerIp = true
	retTag, pHostObj = m.parseHostport([]byte("Discovered open port 443/tcp on **********"), 1)
	assert.True(t, retTag)
	assert.Equal(t, "**********", pHostObj.ip)

	// 虽然返回false，不过内容还是有的
	m.reserveInnerIp = false
	retTag, pHostObj = m.parseHostport([]byte("Discovered open port 443/tcp on **********"), 1)
	assert.False(t, retTag)
	assert.Equal(t, "**********", pHostObj.ip)

	// ping 的任务
	cfg.typ = ETNmapPing
	m = NewNmapScan(&cfg)
	assert.NotNil(t, m)
	m.reserveInnerIp = true
	retTag, pHostObj = m.parseHostport([]byte("Nmap scan report for **********0"), 1)
	assert.True(t, retTag)
	assert.Equal(t, "**********0", pHostObj.ip)
}

func TestNmapParseHostportIpv6(t *testing.T) {
	var retTag bool
	var pHostObj PortHostinfo
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)

	// ipv6任务
	m.reserveInnerIp = true
	m.isScanIPv6 = true
	retTag, pHostObj = m.parseHostport([]byte("Discovered open port 443/tcp on 2603:1046:c01:248c::8"), 1)
	assert.True(t, retTag)
	assert.Equal(t, "[2603:1046:c01:248c::8]", pHostObj.ip)
}

func TestNmapReleaseTaskCache(t *testing.T) {
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewMasscan(&cfg)
	m.tid = 123

	m.appendIpPortNum(strconv.Itoa(m.tid), "**********0")
	m.appendIpPortNum("456", "**********00")

	assert.Equal(t, 2, len(m.mapIpPortCount))

	m.releaseTaskCache()
	assert.Equal(t, 1, len(m.mapIpPortCount))
}

func TestNmapAppendIpPortNum(t *testing.T) {
	var taskid, ip, keys string
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)

	taskid = "1"
	ip = "*******"
	keys = taskid + "_" + ip
	m.appendIpPortNum(taskid, ip)
	assert.Equal(t, 1, m.mapIpPortCount[keys])
	m.appendIpPortNum(taskid, ip)
	assert.Equal(t, 2, m.mapIpPortCount[keys])
}

func TestNmapAppendIpPorts(t *testing.T) {
	var retSt bool
	var taskid, baseProto, ip, port, keys string
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)

	retSt = m.appendIpPorts(taskid, baseProto, ip, port)
	assert.False(t, retSt)
	keys = taskid + "_" + baseProto + "_" + ip + "_" + port
	assert.Equal(t, 1, m.mapIPPorts[keys])

	retSt = m.appendIpPorts(taskid, baseProto, ip, port)
	assert.True(t, retSt)
	keys = taskid + "_" + baseProto + "_" + ip + "_" + port
	assert.Equal(t, 2, m.mapIPPorts[keys])

	m.removePausedFile()
}

func TestFetchScanProgress(t *testing.T) {
	var exists bool
	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)

	exists = m.fetchScanProgress("")
	assert.False(t, exists)

	exists = m.fetchScanProgress("SYN Stealth Scan Timing: About 52.56% done; ETC: 17:11 (0:00:03 remaining)")
	assert.True(t, exists)
}

func TestNmapRemovePausedFile(t *testing.T) {
	var exists bool
	var err error
	curDir, _ := os.Getwd()
	filename := curDir + "/test-paused-file-unittest.tmp"

	var cfg ScantaskBaseConf
	cfg.typ = ETNmap

	m := NewNmapScan(&cfg)
	assert.NotNil(t, m)
	m.pausedFilename = filename

	m.removePausedFile()
	exists, err = util.FileExists(filename)
	assert.False(t, exists)

	err = createTmpFile(filename)
	assert.Nil(t, err)

	m.removePausedFile()
	exists, err = util.FileExists(filename)
	assert.False(t, exists)
}
