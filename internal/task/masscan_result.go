package task

import (
	"baimaohui/portscan_new/internal/queue"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

func (m *Masscan) parseHostport(line []byte, taskid int) (bool, PortHostinfo) {
	var hostinfo PortHostinfo
	retSt := true

	if len(line) <= 0 {
		log.Println("[ERROR] masscan parse-hostport invalid input  taskid:", taskid)
		return false, hostinfo
	}

	s := string(line)
	log.Println(strings.Trim(string(line), " "), taskid, m.hostname)
	// 正则提取host,port
	matches := regMasscan.FindStringSubmatch(s)
	// 尝试treck正则
	if len(matches) < 3 {
		matches = regMasscanTreck.FindStringSubmatch(s)
	}

	if len(matches) < 3 {
		log.Println("[ERROR] masscan fetch array failed  len:", len(matches), "taskid:", taskid, "ctx:", s)
		return false, hostinfo
	}

	hostinfo.port = matches[1]
	hostinfo.baseProto = matches[2]
	hostinfo.ip = matches[3]
	// 排除内网ip
	if !m.reserveInnerIp && m.IpIsInner(hostinfo.ip) {
		log.Println("[ERROR] masscan host is inner-ip", hostinfo.ip)
		retSt = false
	}

	if retSt && m.typ != ETTreckScan {
		if m.putExecInfo && m.fnExec != nil {
			m.fnExec(m, hostinfo.ip)
		}
	}

	return retSt, hostinfo
}

func (m *Masscan) procUdpAllReturn(hostport PortHostinfo, queue *queue.Queue) bool {
	retSt := false
	// udp 排除总是返回的端口
	if hostport.baseProto == "udp" {
		checkQue := queue.CheckQue(hostport.ip)
		if checkQue == true {
			retSt = true
		} else {
			queue = queue.Poll(hostport.ip)
		}
	}

	return retSt
}

func (m *Masscan) appendIpPortNum(taskid, ip string) int {
	keys := taskid + "_" + ip
	if _, ok := m.mapIpPortCount[keys]; ok {
		m.mapIpPortCount[keys] += 1
	} else {
		m.mapIpPortCount[keys] = 1
	}

	return m.mapIpPortCount[keys]
}

//追加ip:port信息到队列。返回true表示存在，false表示不存在
func (m *Masscan) appendIpPorts(taskid, baseProto, ip, port string) bool {
	exists := false
	keys := taskid + "_" + baseProto + "_" + ip + "_" + port
	if _, ok := m.mapIPPorts[keys]; ok {
		exists = true
		m.mapIPPorts[keys] += 1
	} else {
		m.mapIPPorts[keys] = 1
	}

	return exists
}

func (m *Masscan) procFinish() {
	if !m.cmd.Killed {
		m.finished = true
		time.Sleep(1 * time.Second) // 暂停一下，防止发送5之后马上又出现2的情况

		log.Println("masscan stopped")
		if m.fnProgress != nil {
			m.fnProgress(m.scanEngine, m.tid, "5", "0", "100%", "00:00:00", "")
		}
		m.removePausedFile()
	} else {
		if m.FileExists(m.pausedFilename) {
			log.Println("exists masscan paused file:", m.pausedFilename)
		} else {
			log.Println("masscan paused file:", m.pausedFilename, "not exists")
		}
		state := "4"
		progress := "0%"
		// 如果是资产达到限制的，状态修改
		if m.IsAssetExceedLimit() {
			state = "5"
			progress = "100%"
		}
		m.fnProgress(m.scanEngine, m.tid, state, "0", progress, "", "")
	}
	m.releaseTaskCache()
}

func (m *Masscan) releaseTaskCache() {
	for key, _ := range m.mapIpPortCount {
		if strings.HasPrefix(key, strconv.Itoa(m.tid)) {
			delete(m.mapIpPortCount, key)
		}
	}

	for key, _ := range m.mapIPPorts {
		if strings.HasPrefix(key, strconv.Itoa(m.tid)) {
			delete(m.mapIPPorts, key)
		}
	}
}

func (m *Masscan) removePausedFile() {
	if m.FileExists(m.pausedFilename) {
		log.Println("rm paused file", m.pausedFilename)
		err := os.Remove(m.pausedFilename) // 正常退出，保证没有暂停文件
		if err != nil {
			log.Println("[ERROR] masscan remove paused file failed", err)
		}
	}
}
