package task

import (
	"bufio"
	"bytes"
	"github.com/robfig/cron"
	"log"
	"os/exec"
	"strconv"
	"strings"

	"github.com/pkg/errors"
)

func (m *Masscan) SetMaxAssetNum(num int) {
	m.maxAssetNum = num
}

func (m *Masscan) SetScanIPv6(ipv6 bool) {
}

func (m *Masscan) IsAssetExceedLimit() bool {
	if m.maxAssetNum <= 0 {
		return false
	}

	return len(m.mapIpPortCount) >= m.maxAssetNum
}

// stderr 必须传地址，否则取不到信息
func (m *Masscan) runCrontab(stderr *bytes.Buffer) {
	// 开启定时任务
	v1 := func() {
		aa := make([]byte, 1024)
		stderr.Read(aa)
		line := string(aa)
		if regMasscanStderr.MatchString(line) {
			submatch := regMasscanStderr.FindStringSubmatch(line)
			progress := submatch[1]
			remainingTime := submatch[2]

			if m.fnProgress != nil && !m.stoped && !m.finished {
				log.Printf(" cron progress  tid:%v state:2 progress:%v remaintime:%v\n",
					m.tid, progress, remainingTime)
				m.fnProgress(m.scanEngine, m.tid, "2", "0", progress, remainingTime, "")
			} else {
				log.Println("  progress not upload     ~~~", progress, remainingTime)
			}
		} else {
			if len(line) > 1 && strings.Contains(line, "FAIL") {
				m.errMsg = line
				log.Printf("[ERROR] masscan failed:[[[%s]]]\n", m.errMsg)
			}
		}
	}

	c := cron.New()
	// 每5秒钟执行一次
	spec := "*/5 * * * * ?"
	c.AddFunc(spec, v1)
	c.Start()
	log.Println("start masscan progress cron succeed")
}

func (m *Masscan) runInit() (*bufio.Reader, error) {
	var stderr bytes.Buffer
	m.cmd.Dir = m.dir
	log.Println(strings.Join(m.cmd.Args, " "))

	m.cmd.Stderr = &stderr
	stdout, err := m.cmd.StdoutPipe()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	log.Println("start masscan at", m.dir)
	err = m.cmd.Start()
	if err != nil {
		return nil, errors.Wrap(err, "failed to start masscan")
	}
	log.Println("start masscan succeed")

	// 开始执行就置状态
	m.fnProgress(m.scanEngine, m.tid, "2", "0", "0.0%", "0:0:0", "")

	m.done = make(chan struct{})
	rd := bufio.NewReader(stdout)

	m.runCrontab(&stderr)

	return rd, nil
}

func (m *Masscan) IsStopSt() bool {
	return m.stoped
}

func (m *Masscan) IsFinished() bool {
	return m.finished
}

func (m *Masscan) runCmd(hosts, filename, ports, rate, excludes, sendEth, blackFilename string, isHost bool, retryNum int) {
	var cmd *exec.Cmd

	// 判断是host还是文件
	if m.typ == ETTreckScan {
		if isHost {
			cmd = exec.Command("masscan_treck", hosts, "--ping")
		} else {
			cmd = exec.Command("masscan_treck", "-iL", filename, "--ping")
		}
	} else {
		if isHost {
			cmd = exec.Command("masscan", hosts)
		} else {
			cmd = exec.Command("masscan", "-iL", filename)
		}
	}

	// treck扫描不需要扫描端口
	if m.typ != ETTreckScan {
		if m.typ == ETMasscanPing {
			cmd.Args = append(cmd.Args, "--ping")
		} else {
			cmd.Args = append(cmd.Args, "--ports", ports)

			if m.typ == ETMasscanWithPing {
				cmd.Args = append(cmd.Args, "--ping")
			}
		}
	}

	if len(m.routerMac) > 0 {
		cmd.Args = append(cmd.Args, "--router-mac", m.routerMac)
	}

	if retryNum > 1 {
		cmd.Args = append(cmd.Args, "--retries", strconv.Itoa(retryNum))
	}

	if len(sendEth) > 0 {
		cmd.Args = append(cmd.Args, "--adapter", sendEth)
	}

	if len(blackFilename) > 0 {
		cmd.Args = append(cmd.Args, "--excludefile", blackFilename)
	} else {
		cmd.Args = append(cmd.Args, "--exclude", excludes)
	}

	srcPort := strconv.Itoa(m.sourcePort)

	if srcPort != "0" {
		cmd.Args = append(cmd.Args, "--source-port", srcPort, "--rate", rate, "--wait", "1")
	} else {
		cmd.Args = append(cmd.Args, "--rate", rate, "--wait", "1")
	}

	m.cmd = &PortCmd{Cmd: cmd}
}
