package task

import (
	"bufio"
	"bytes"
	"github.com/robfig/cron"
	"log"
	"regexp"
	"strings"

	"github.com/pkg/errors"
)

var RegNmap = regexp.MustCompile(`Discovered open port (\d+)/(\w*) on (\S*)\s*`)
var regNmapFullProgress = regexp.MustCompile(`Scan Timing: About (.*?) done; ETC:\s*.*?\s*\((.*?) remaining\)`)
var regNmapPartProgress = regexp.MustCompile(`Scan Timing: About (.*?) done`)
var regNmapPing = regexp.MustCompile(`Nmap scan report for\s*(\S*)\s*`)
var regNmapCompleted = regexp.MustCompile(`elapsed; (\d+) hosts completed`)

func (m *NmapScan) SetMaxAssetNum(num int) {
	m.maxAssetNum = num
}

func (m *NmapScan) SetScanIPv6(ipv6 bool) {
	m.isScanIPv6 = ipv6
}

func (m *NmapScan) IsAssetExceedLimit() bool {
	if m.maxAssetNum <= 0 {
		return false
	}

	return len(m.mapIpPortCount) >= m.maxAssetNum
}

// stderr 必须传地址，否则取不到信息
func (m *NmapScan) runCrontab(stderr *bytes.Buffer) {
	// 开启定时任务
	v1 := func() {
		aa := make([]byte, 1024)
		stderr.Read(aa)
		line := string(aa)

		if len(line) > 1 && strings.Contains(line, "FAIL") {
			m.errMsg = line
			log.Printf("[ERROR] nmapscan failed:[[[%s]]]\n", m.errMsg)
		}
	}

	c := cron.New()
	// 每5秒钟执行一次
	spec := "*/5 * * * * ?"
	c.AddFunc(spec, v1)
	c.Start()
	log.Println("start nmap progress cron succeed")
}

func (m *NmapScan) runInit() (*bufio.Reader, error) {
	var stderr bytes.Buffer
	m.cmd.Dir = m.dir
	log.Println(strings.Join(m.cmd.Args, " "))

	m.cmd.Stderr = &stderr
	stdout, err := m.cmd.StdoutPipe()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	log.Println("start nmap at", m.dir)
	err = m.cmd.Start()
	if err != nil {
		return nil, errors.Wrap(err, "failed to start nmapscan")
	}
	log.Println("start nmapscan succeed")

	// 开始执行就置状态
	if m.fnProgress != nil {
		m.fnProgress(m.scanEngine, m.tid, "2", "0", "0.0%", "0:0:0", "")
	}

	m.done = make(chan struct{})
	rd := bufio.NewReader(stdout)

	m.runCrontab(&stderr)

	return rd, nil
}
