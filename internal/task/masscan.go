package task

import (
	"bufio"
	"io"
	"log"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
)

type Masscan struct {
	stoped         bool           // 是否为停止状态
	tid            int            // 任务id
	cmd            *PortCmd       // 命令对象
	finished       bool           // 是否为任务结束状态
	mapIpPortCount map[string]int // ip发现端口计数器（任务id+IP的格式）
	mapIPPorts     map[string]int // ip:port计数器
	*ScantaskBaseConf
}

var regMasscan = regexp.MustCompile(`Discovered open port (\d+)/(\w*) on ([\d\.]+)`)
var regMasscanTreck = regexp.MustCompile(`Discovered open port (\d+)/(\w*) (\S*)`)
var regMasscanStderr = regexp.MustCompile(`-kpps,\s*(.*?) done,\s*(.*?) remaining,\s*found=.*`)

func NewMasscan(cfg *ScantaskBaseConf) *Masscan {
	return &Masscan{
		ScantaskBaseConf: cfg,
		mapIpPortCount:   make(map[string]int),
		mapIPPorts:       make(map[string]int),
	}
}

func (m *Masscan) ResumeFileExists() bool {
	return m.FileExists(m.pausedFilename)
}

func (m *Masscan) GetPausedFile() string {
	return m.pausedFilename
}

func (m *Masscan) HandlePaused(blackIps string, tid int) (<-chan [][4]string, error) {
	if !m.FileExists(m.pausedFilename) { // 没有暂停文件
		log.Printf("masscan paused file [%s] not exists\n", m.pausedFilename)
		return nil, nil
	}

	if tid == 0 {
		log.Fatal("masscan handlePaused invalid taskid")
	}
	m.tid = tid

	newExcludes := m.MergeExcludeIps(blackIps)
	cmd := exec.Command("masscan", "--resume", m.pausedFilename, "--exclude", newExcludes, "--wait 1")
	m.cmd = &PortCmd{Cmd: cmd}

	return m.run()
}

func (m *Masscan) HandleResume(pausedFile, blackIps string, tid int) (<-chan [][4]string, error) {
	if !m.FileExists(pausedFile) { // 没有暂停文件
		log.Printf("masscan paused filename [%s] not exists\n", pausedFile)
		return nil, nil
	}

	if tid == 0 {
		log.Fatal("masscan handlePaused invalid taskid")
	}
	m.tid = tid
	m.stoped = false

	newExcludes := m.MergeExcludeIps(blackIps)
	cmd := exec.Command("masscan", "--resume", pausedFile, "--exclude", newExcludes, "--wait 1")
	m.cmd = &PortCmd{Cmd: cmd}

	return m.run()
}

func (m *Masscan) run() (<-chan [][4]string, error) {
	if m.cmd == nil {
		return nil, errors.New("no masscan")
	}

	taskid := strconv.Itoa(m.tid)
	var rd *bufio.Reader
	var err error
	ch := make(chan [][4]string, 1)

	rd, err = m.runInit()
	if err != nil {
		return nil, err
	}

	go func() {
		defer func() {
			m.cmd = nil   // 清除命令，防止多次关闭
			ch <- nil     // 通知上层取数据的masscan已停止
			close(m.done) // 通知Stop函数停止完成
		}()

		var que [][4]string
		newQueue := func() {
			que = make([][4]string, 0, 100)
		}
		newQueue()
		lastTime := time.Now()
		// 队列检查重复ip
		//queues := queue.NewQue(100)

		for {
			// 读取打印信息
			line, _, err := rd.ReadLine()
			if err != nil {
				if err != io.EOF {
					log.Println("[ERROR] masscan not EOF:", err)
				}
				break
			}

			parseSt, hostport := m.parseHostport(line, m.tid)
			if !parseSt {
				continue
			}

			//if m.procUdpAllReturn(hostport, queues) {
			//	log.Println("[WARN] masscan udp all return", taskid, hostport.baseProto, hostport.ip)
			//	continue
			//}

			if m.typ != ETTreckScan {
				// 限制同IP上传端口个数
				if m.ipMaxPortCount > 0 && m.appendIpPortNum(taskid, hostport.ip) > m.ipMaxPortCount {
					log.Println("[WARN] masscan ip port too much", taskid, hostport.baseProto, hostport.ip)
					continue
				}

				// 判断ip和端口是否已经存在，不限制数量的先不处理（比如fofa）
				if m.ipMaxPortCount > 0 && m.appendIpPorts(taskid, hostport.baseProto, hostport.ip, hostport.port) {
					log.Println("[WARN] masscan baseproto&ip&port already exists",
						taskid, hostport.baseProto, hostport.ip, hostport.port)
					continue
				}

				// 判断最近是否处理过
				if m.ignoreProced && m.fnChkLastProced != nil {
					sid := hostport.ip + ":" + hostport.port
					proced := m.fnChkLastProced(sid)
					if proced {
						log.Println("[INFO] masscan ip&port already proced",
							taskid, hostport.baseProto, hostport.ip, hostport.port)
						continue
					}
				}
			}

			if m.IsAssetExceedLimit() && !m.stoped {
				log.Println("[WARN] masscan asset exceed limit found:", len(m.mapIpPortCount), "syslimit:", m.maxAssetNum)
				go m.Stop()
			}

			que = append(que, [4]string{hostport.ip, hostport.port, taskid, hostport.baseProto})
			// 按批次数量传递一次，或者每30秒传递一次
			if len(que) >= m.batchSize || (len(que) > 0 && time.Now().Sub(lastTime).Seconds() > 30) {
				ch <- que
				newQueue()
				lastTime = time.Now()
			}
		} // for end

		err = m.cmd.Wait()
		if err != nil {
			if strings.Contains(err.Error(), "signal: terminated") {
				m.stoped = true
			}

			// 暂停一下，等待获取错误信息
			for i := 0; i < 30; i++ {
				log.Print("  wait errmsg")
				if len(m.errMsg) > 0 {
					break
				}

				time.Sleep(1 * time.Second)
			}
			m.fnProgress(m.scanEngine, m.tid, "6", "0", "0%", "", m.errMsg)
			log.Println("[ERROR] masscan wait:", err)
			return
		}

		if len(que) > 0 { // 处理尾数据
			ch <- que
		}

		m.procFinish()
	}()

	return ch, nil
}

// fofa没使用，foeye有
func (m *Masscan) Run(rate, hosts, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cmd != nil {
		return nil, errors.New("masscan alreay started")
	}

	if m.fnProgress == nil {
		return nil, errors.New("masscan invalid progress function")
	}

	m.tid = tid
	newExcludes := m.MergeExcludeIps(blackIps)
	m.runCmd(hosts, "", ports, rate, newExcludes, sendEth, blackFilename, true, retryNum)

	return m.run()
}

// 从文件中加载目标扫描
func (m *Masscan) RunFile(rate, filename, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cmd != nil {
		return nil, errors.New("masscan file alreay started")
	}

	if m.fnProgress == nil {
		return nil, errors.New("masscan file invalid progress function")
	}

	m.tid = tid
	newExcludes := m.MergeExcludeIps(blackIps)

	m.runCmd("", filename, ports, rate, newExcludes, sendEth, blackFilename, false, retryNum)

	return m.run()
}

func (m *Masscan) Stop() error {
	// masscan扫完了，不需要暂停
	if m.cmd == nil || m.cmd.Process == nil {
		log.Println("  masscan stop process is not running")
		return nil
	}

	// masscan正在扫，需要暂停
	log.Println("stopping masscan...")

	m.stoped = true

	if !m.finished {
		state := "3"
		// 如果资产超过限制了，返回扫描成功
		if m.IsAssetExceedLimit() {
			state = "5"
		}
		m.fnProgress(m.scanEngine, m.tid, state, "0", "99%", "", "")
	} else {
		// 有些情况可能会刚好完成的时候停止
		log.Println("  masscan stop not in running")
		m.removePausedFile()
		m.fnProgress(m.scanEngine, m.tid, "5", "0", "100%", "", "")
		return nil
	}

	log.Println("  masscan sent stop signal")
	m.cmd.Killed = true
	err := m.cmd.Process.Signal(os.Interrupt)
	if err != nil {
		return errors.Wrap(err, "failed to kill masscan")
	}

	// 等待masscan停止
	<-m.done
	log.Println("stopped masscan...")

	return nil
}
