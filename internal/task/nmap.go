package task

import (
	"bufio"
	"io"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
)

type NmapScan struct {
	stoped         bool           // 是否为停止状态
	tid            int            // 任务id
	cmd            *PortCmd       // 命令对象
	finished       bool           // 是否为任务结束状态
	isScanIPv6     bool           // 是否扫描的IPv6地址
	mapIpPortCount map[string]int // ip发现端口计数器（任务id+IP的格式）
	mapIPPorts     map[string]int // ip:port计数器
	grepPausedFile string         // nmap的暂停文件名称，没有这个无法生成文件
	hostCompleted  string
	*ScantaskBaseConf
}

func NewNmapScan(cfg *ScantaskBaseConf) *NmapScan {
	return &NmapScan{
		ScantaskBaseConf: cfg,
		mapIpPortCount:   make(map[string]int),
		mapIPPorts:       make(map[string]int),
		grepPausedFile:   cfg.pausedFilename + ".gnmap",
	}
}

func (m *NmapScan) ResumeFileExists() bool {
	return m.FileExists(m.pausedFilename)
}

func (m *NmapScan) GetPausedFile() string {
	return m.pausedFilename
}

func (m *NmapScan) HandlePaused(blackIps string, tid int) (<-chan [][4]string, error) {
	if !m.FileExists(m.pausedFilename) { // 没有暂停文件
		log.Println("nmapscan paused filename not exists")
		return nil, nil
	}

	if tid == 0 {
		log.Fatal("nmapscan handlePaused invalid taskid")
	}
	m.tid = tid

	cmd := exec.Command("nmap", "--resume", m.pausedFilename)
	m.cmd = &PortCmd{Cmd: cmd}

	return m.run()
}

func (m *NmapScan) HandleResume(pausedFile, blackIps string, tid int) (<-chan [][4]string, error) {
	if !m.FileExists(pausedFile) { // 没有暂停文件
		log.Println("nmapscan paused filename not exists")
		return nil, nil
	}

	if tid == 0 {
		log.Fatal("nmapscan handlePaused invalid taskid")
	}
	m.tid = tid
	m.stoped = false

	cmd := exec.Command("nmap", "--resume", pausedFile)
	m.cmd = &PortCmd{Cmd: cmd}

	return m.run()
}

func (m *NmapScan) run() (<-chan [][4]string, error) {
	if m.cmd == nil {
		return nil, errors.New("no nmap")
	}

	taskid := strconv.Itoa(m.tid)
	var rd *bufio.Reader
	var err error
	ch := make(chan [][4]string, 1)

	rd, err = m.runInit()
	if err != nil {
		return nil, err
	}

	go func() {
		defer func() {
			m.cmd = nil   // 清除命令，防止多次关闭
			ch <- nil     // 通知上层取数据的进程已停止
			close(m.done) // 通知Stop函数停止完成
		}()

		var que [][4]string
		newQueue := func() {
			que = make([][4]string, 0, 100)
		}
		newQueue()
		lastTime := time.Now()
		// 队列检查重复ip
		//queues := queue.NewQue(100)

		for {
			// 读取打印信息
			line, _, err := rd.ReadLine()
			if err != nil {
				if err != io.EOF {
					log.Println("[ERROR] nmap not EOF:", err)
				}
				break
			}

			parseSt, hostport := m.parseHostport(line, m.tid)
			if !parseSt {
				continue
			}

			//if m.procUdpAllReturn(hostport, queues) {
			//	log.Println("[WARN] nmap udp all return", taskid, hostport.baseProto, hostport.ip)
			//	continue
			//}

			// 限制同IP上传端口个数
			if m.ipMaxPortCount > 0 && m.appendIpPortNum(taskid, hostport.ip) > m.ipMaxPortCount {
				log.Println("[WARN] nmap ip port too much", taskid, hostport.baseProto, hostport.ip)
				continue
			}

			// 判断ip和端口是否已经存在，不限制数量的先不处理（比如fofa）
			if m.ipMaxPortCount > 0 && m.appendIpPorts(taskid, hostport.baseProto, hostport.ip, hostport.port) {
				log.Println("[WARN] nmap baseproto&ip&port already exists",
					taskid, hostport.baseProto, hostport.ip, hostport.port)
				continue
			}

			if m.IsAssetExceedLimit() && !m.stoped {
				log.Println("[WARN] nmap asset exceed limit found:", len(m.mapIpPortCount), "syslimit:", m.maxAssetNum)
				go m.Stop()
			}

			que = append(que, [4]string{hostport.ip, hostport.port, taskid, hostport.baseProto})
			// 按批次数量传递一次，或者每30秒传递一次
			if len(que) >= m.batchSize || (len(que) > 0 && time.Now().Sub(lastTime).Seconds() > 30) {
				ch <- que
				newQueue()
				lastTime = time.Now()
			}
		} // for end

		err = m.cmd.Wait()
		if err != nil {
			if strings.Contains(err.Error(), "signal: terminated") {
				m.stoped = true
			}

			// 暂停一下，等待获取错误信息
			for i := 0; i < 10; i++ {
				log.Print("  wait errmsg")
				if len(m.errMsg) > 0 {
					break
				}

				time.Sleep(1 * time.Second)
			}
			//m.fnProgress(m.scanEngine, m.tid, "6", "0%", "", m.errMsg)
			log.Println("[ERROR] nmap wait:", err)
			//return
		}

		if len(que) > 0 { // 处理尾数据
			ch <- que
		}

		m.procFinish()
	}()

	return ch, nil
}

func (m *NmapScan) Run(rate, hosts, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cmd != nil {
		return nil, errors.New("nmap alreay started")
	}

	if m.fnProgress == nil {
		return nil, errors.New("nmap invalid progress function")
	}

	m.tid = tid
	newExcludes := m.MergeExcludeIps(blackIps)

	// WUYB 20221202 其实这个没啥用
	// newHosts := strings.ReplaceAll(hosts, ",", "%20") // 必须这样exec.Command才能正常执行

	if strings.Contains(hosts, ":") || m.isScanIPv6 { // IPv6
		m.isScanIPv6 = true
		m.runCmd(hosts, "", ports, rate, newExcludes, blackFilename, sendEth, true, true, retryNum)
	} else {
		m.runCmd(hosts, "", ports, rate, newExcludes, blackFilename, sendEth, true, false, retryNum)
	}

	return m.run()
}

func (m *NmapScan) runCmd(hosts, filename, ports, rate, excludes, excludeFile, sendEth string, isHost, ipv6 bool, retryNum int) {
	var cmd *exec.Cmd

	// 判断是host还是文件
	if isHost {
		// WUYB 20221202 解决nmap多个target无法扫描问题
		hostList := strings.Split(hosts, ",")
		cmd = exec.Command("nmap", hostList...)
	} else {
		cmd = exec.Command("nmap", "-iL", filename)
	}

	if ipv6 {
		cmd.Args = append(cmd.Args, "-6")
	}

	if m.typ == ETNmapPing {
		cmd.Args = append(cmd.Args, "-sn")
	} else {
		cmd.Args = append(cmd.Args, "-sS")
		cmd.Args = append(cmd.Args, "-sU")
		cmd.Args = append(cmd.Args, "-Pn")
		cmd.Args = append(cmd.Args, "-p", ports)
		if m.typ == ETNmapWithPing {
			cmd.Args = append(cmd.Args, "-PE")
		}
	}

	if retryNum > 1 {
		cmd.Args = append(cmd.Args, "--max-retries", strconv.Itoa(retryNum))
	}

	if len(sendEth) > 0 {
		cmd.Args = append(cmd.Args, "-e", sendEth)
	}

	srcPort := strconv.Itoa(m.sourcePort)
	cmd.Args = append(cmd.Args, "--source-port", srcPort, "--min-rate", rate, "--max-rate", rate,
		"-n", "-v", "-T3", "--open", "--stats-every", "5s", "--min-hostgroup", "100", "--max-hostgroup", "100",
		"-oG", m.grepPausedFile)

	if len(excludeFile) > 0 {
		cmd.Args = append(cmd.Args, "--excludefile", excludeFile)
	} else if len(excludes) > 0 {
		cmd.Args = append(cmd.Args, "--exclude", excludes)
	}

	m.cmd = &PortCmd{Cmd: cmd}
}

// 从文件中加载目标扫描
func (m *NmapScan) RunFile(rate, filename, ports, blackIps, sendEth, blackFilename string, tid, retryNum int) (<-chan [][4]string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cmd != nil {
		return nil, errors.New("nmap file alreay started")
	}

	if m.fnProgress == nil {
		return nil, errors.New("nmap file invalid progress function")
	}

	m.tid = tid
	newExcludes := m.MergeExcludeIps(blackIps)
	m.runCmd("", filename, ports, rate, newExcludes, blackFilename, sendEth, false, m.isScanIPv6, retryNum)

	return m.run()
}

func (m *NmapScan) Stop() error {
	// 扫完了，不需要暂停
	if m.cmd == nil || m.cmd.Process == nil {
		log.Println("  nmap stop process is not running")
		return nil
	}

	// 正在扫，需要暂停
	log.Println("stopping nmap...")

	m.stoped = true

	if !m.finished {
		state := "3"
		// 如果资产超过限制了，返回扫描成功
		if m.IsAssetExceedLimit() {
			state = "5"
		}
		m.fnProgress(m.scanEngine, m.tid, state, m.hostCompleted, "99%", "", "")
	} else {
		// 有些情况可能会刚好完成的时候停止
		log.Println("  nmap stop not in running")
		m.removePausedFile()
		m.fnProgress(m.scanEngine, m.tid, "5", m.hostCompleted, "100%", "", "")
		return nil
	}

	log.Println("  nmap sent stop signal")
	m.cmd.Killed = true
	err := m.cmd.Process.Signal(os.Interrupt)
	if err != nil {
		return errors.Wrap(err, "failed to kill nmap")
	}

	// 等待进程停止
	<-m.done
	log.Println("stopped nmap...")

	return nil
}

func (m *NmapScan) IsStopSt() bool {
	return m.stoped
}

func (m *NmapScan) IsFinished() bool {
	return m.finished
}
