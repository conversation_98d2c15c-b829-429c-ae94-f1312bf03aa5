package conf

import (
	"baimaohui/portscan_new/internal/config"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"strings"
	"testing"
)

func TestConf(t *testing.T) {
	curDir, _ := os.Getwd()
	configFullPath := ""
	//fmt.Println("  config cur dir:", curDir)

	var cfg Config
	if strings.Contains(curDir, "internal/conf") {
		configFullPath = curDir + "/../../conf.toml"
	} else {
		configFullPath = curDir + "conf.toml"
	}

	err := config.Parse(configFullPath, &cfg)
	assert.Nil(t, err)
	//assert.Equal(t, "./tools/", cfg.Masscan.Dir)

	// 统一函数测试
	if strings.Contains(curDir, "internal/conf") {
		if err := os.Chdir(curDir + "/../../"); err != nil {
			log.Println(err)
		}
	}

	newCfg := GetConfig()
	assert.NotNil(t, newCfg)
}
