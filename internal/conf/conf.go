package conf

import (
	"baimaohui/portscan_new/internal/config"
)

type LimitRedisConf struct {
	Enable   bool   `toml:"enable"`
	Addr     string `toml:"addr"`
	Db       int    `toml:"db"`
	Password string `toml:"password"`
	Prefix   string `toml:"prefix"`
}

type Config struct {
	Log      config.LogConf        `yaml:"log"`
	Redis    *config.RedisConf     `yaml:"redis"`
	LmtRds   *LimitRedisConf       `toml:"limit_redis"`
	Masscan  config.MasscanConf    `yaml:"masscan"`
	Consumer config.ConsumerConfig `yaml:"consumer"`
	Producer config.ProducerConfig `yaml:"producer"`
	Pprof    config.PprofConf      `yaml:"pprof"`
	Worker   config.WorkerConf     `yaml:"worker"`
	RPC      config.RPCConf        `toml:"rpc"`
}

func GetConfig() *Config {
	var conf Config
	config.MustGet(&conf)
	return &conf
}
