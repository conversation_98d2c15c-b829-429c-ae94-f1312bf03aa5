package internal

const (
	LicenseKey = "foscan:license"
)

var (
	Version             = ""
	BuildAt             = ""
	Private             = ""
	SHA265              = ""
	LicenseVerifySwitch = ""

	ModuleRelation = map[string]string{
		"at": "asset",
		"tt": "threat",
		"ce": "compliance",
		"rt": "report",
		"kc": "kafka_connection",
		"cp": "custom_poc",
		"ct": "custom_protocol",
		"fe": "feature_engine",
		"fl": "fofa_link",
		"rm": "risk_module",
		"ap": "asset_panorama",
	}
)
