package db

import (
	"checkurl/config"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func InitRedisMock() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestRedis_Black(t *testing.T) {
	rMock := InitRedisMock()
	defer rMock.Close()
	assert.NotNil(t, rMock)

	rds := NewRedis(config.RedisConf{
		Addr:     rMock.Addr(),
		Password: "",
		Prefix:   "test:",
		Db:       12,
	})
	assert.NotNil(t, rds)

	host := "https://feedback.hoobill.com"
	domain := "hoobill.com"
	assert.False(t, rds.IsBlackHost(host))
	assert.False(t, rds.IsBlackDomain(domain))

	r := rds.pool.Get()
	r.Do("sadd", rds.blackH<PERSON><PERSON><PERSON><PERSON>, host)
	r.Do("sadd", rds.blackD<PERSON><PERSON><PERSON><PERSON>, domain)

	assert.True(t, rds.IsBlackHost(host))
	assert.True(t, rds.IsBlackDomain(domain))
}

func TestRedis_HostChecked(t *testing.T) {
	rMock := InitRedisMock()
	defer rMock.Close()
	assert.NotNil(t, rMock)
	/*var key, value string
	var err error
	var exists bool*/

	rds := NewRedis(config.RedisConf{
		Addr:     rMock.Addr(),
		Password: "",
		Prefix:   "test:",
		Db:       12,
	})

	host := "https://feedback.hoobill.com"
	is := rds.HostChecked(host)
	assert.False(t, is)

	// 添加
	rds.AddHost(host, 10)

	is = rds.HostChecked(host)
	assert.True(t, is)
}
