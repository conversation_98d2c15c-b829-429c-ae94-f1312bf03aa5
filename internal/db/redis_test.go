package db

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"log"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
)

func initRedisMocks() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestRedis(t *testing.T) {
	var cfg conf.Config
	// fmt.Printf("conf = %+v\n\n", conf)

	mockRds := initRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = mockRds.Addr()

	dbBaseConf := NewDbBaseConfig(DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())

	assert.Equal(t, "mk:stop_task_flag", dbAct.GetKeyName("stop_task"))
	assert.Equal(t, "mk:exec_ip_info", dbAct.GetKeyName("exec_ip"))
	assert.Equal(t, "mk:scan_task", dbAct.GetKeyName("task_progress"))
	assert.Equal(t, "mk:portscan_port_black_ips", dbAct.GetKeyName("port_blacklist"))
	assert.Equal(t, "", dbAct.GetKeyName("abcd"))

	ttt := time.Now()
	dbAct.UpdateLastChkTime(ttt)
	assert.Equal(t, ttt, dbAct.GetLastChkTime())

	retB := dbAct.DeleteKey("abcd")
	assert.Equal(t, true, retB)

	var taskidKey, chkSt, chkProgress, chkRemainTime, chkFinishTime, tmpTimeString string

	// task state 2 init
	taskidKey = "taskid:111"
	//dbAct.DeleteKey(taskidKey)
	dbAct.SetTaskStateProgress(taskidKey, "2", "0.0%", "0:0:0")
	chkSt, err := dbAct.GetHashValue(taskidKey, "state")
	assert.Nil(t, err)
	assert.Equal(t, "2", chkSt)
	chkProgress, err = dbAct.GetHashValue(taskidKey, "progress")
	assert.Equal(t, "0.0%", chkProgress)
	chkRemainTime, err = dbAct.GetHashValue(taskidKey, "remaining_time")
	assert.Equal(t, "0:0:0", chkRemainTime)
	chkFinishTime, err = dbAct.GetHashValue(taskidKey, "finish_time")
	assert.Equal(t, "", chkFinishTime)
	nilValue, err := dbAct.GetHashValue("not_exists_fields", "not_exists_fields")
	assert.Equal(t, "", nilValue)

	// task state 2 have progress
	dbAct.DeleteKey(taskidKey)
	dbAct.SetTaskStateProgress(taskidKey, "2", "1.1%", "0:22:33")
	chkSt, err = dbAct.GetHashValue(taskidKey, "state")
	assert.Equal(t, "2", chkSt)
	chkProgress, err = dbAct.GetHashValue(taskidKey, "progress")
	assert.Equal(t, "1.1%", chkProgress)
	chkRemainTime, err = dbAct.GetHashValue(taskidKey, "remaining_time")
	assert.Equal(t, "0:22:33", chkRemainTime)
	chkFinishTime, err = dbAct.GetHashValue(taskidKey, "finish_time")
	assert.Equal(t, "", chkFinishTime)

	// task state 5
	time.Sleep(1 * time.Second)
	dbAct.DeleteKey(taskidKey)
	tmpTimeString = time.Now().Format("2006-01-02 15:04:05")
	dbAct.SetTaskStateProgress(taskidKey, "5", "100%", "0:0:0")
	chkSt, err = dbAct.GetHashValue(taskidKey, "state")
	assert.Equal(t, "5", chkSt)
	chkProgress, err = dbAct.GetHashValue(taskidKey, "progress")
	assert.Equal(t, "100%", chkProgress)
	chkRemainTime, err = dbAct.GetHashValue(taskidKey, "remaining_time")
	assert.Equal(t, "0:0:0", chkRemainTime)
	chkFinishTime, err = dbAct.GetHashValue(taskidKey, "finish_time")
	assert.Equal(t, tmpTimeString, chkFinishTime)

	// AddTaskStartTime
	var tmpKey, stValue string
	tmpKey = "testkey"
	dbAct.DeleteKey(tmpKey)
	tmpTimeString = time.Now().Format("2006-01-02 15:04:05")
	dbAct.SetTaskStartTime(tmpKey)
	stValue, err = dbAct.GetHashValue(tmpKey, "state")
	assert.Equal(t, "0", stValue)
	stValue, err = dbAct.GetHashValue(tmpKey, "progress")
	assert.Equal(t, "0.00", stValue)
	stValue, err = dbAct.GetHashValue(tmpKey, "begin_scan_time")
	assert.Equal(t, tmpTimeString, stValue)
	dbAct.DeleteKey(tmpKey)

	tmpKey = "TestYYYKey"
	dbAct.DeleteKey(tmpKey)
	stValue, err = dbAct.GetValue(tmpKey)
	assert.Equal(t, "", stValue)
	err = dbAct.SetValue(tmpKey, "123abcd")
	assert.Nil(t, err)
	stValue, err = dbAct.GetValue(tmpKey)
	assert.Equal(t, "123abcd", stValue)
	dbAct.DeleteKey(tmpKey)

	tmpKey = "Testabcdefg"
	dbAct.DeleteKey(tmpKey)
	err = dbAct.SetHashValue(tmpKey, "aaa", "123")
	assert.Nil(t, err)
	stValue, err = dbAct.GetHashValue(tmpKey, "aaa")
	assert.Equal(t, "123", stValue)

	dbAct.AddCurrentTid("test_key", 1000)
	cTid, err := dbAct.GetValue("test_key")
	assert.Nil(t, err)
	assert.Equal(t, "1000", cTid)
	iTid := dbAct.GetCurrentTid("test_key")
	assert.Equal(t, 1000, iTid)
	exists := dbAct.DelCurrentTid("test_key")
	assert.True(t, exists)

	keyTtl := dbAct.GetKeyTtl("test_key")
	assert.True(t, keyTtl <= 7*24*3600)

	blackIps := dbAct.GetBlackIps()
	assert.Empty(t, blackIps)

	dbAct.Close()
}

func TestRedisInvalid(t *testing.T) {
	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = "127.0.0.1:36379"

	dbBaseConf := NewDbBaseConfig(DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())
	err := dbAct.SetHashValue("test_hash", "test_field", "test_value")
	assert.NotNil(t, err)

	err = dbAct.SetValue("test_key", "test_value")
	assert.NotNil(t, err)

	dbAct.AddCurrentTid("test_key", 0)
	iTid := dbAct.GetCurrentTid("test_key")
	assert.Equal(t, -1, iTid)
	iTtl := dbAct.GetKeyTtl("test_key")
	assert.Equal(t, 0, int(iTtl))
	dbAct.DeleteKey("test_key")
	exists := dbAct.DelCurrentTid("test_key")
	assert.False(t, exists)
	exists, err = dbAct.ExistKey("test_key")
	assert.NotNil(t, err)
	assert.False(t, exists)
	blackIps := dbAct.GetBlackIps()
	assert.Empty(t, blackIps)

	dbBaseConf = NewDbBaseConfig(100, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	newDbAct := NewDb(dbBaseConf)
	assert.Nil(t, newDbAct)
}
