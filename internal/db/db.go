package db

import (
	"baimaohui/portscan_new/internal/config"
	"log"
	"time"
)

type DBType int32

const (
	DBTRedis DBType = 0
)

type DbBaseConfig struct {
	dbType          DBType //类型
	dbConf          *config.RedisConf
	prefix          string    // 前缀
	stopTaskKey     string    //停止任务键
	execIpKey       string    //执行信息键
	lastUpExecTime  time.Time //最近更新时间
	taskProgressKey string    // 任务进度信息键
}

func NewDbBaseConfig(typ DBType, prefix, stopTask, execIp, progress string, rconf *config.RedisConf) *DbBaseConfig {
	return &DbBaseConfig{
		dbType:          typ,
		dbConf:          rconf,
		prefix:          prefix,
		stopTaskKey:     prefix + stopTask,
		execIpKey:       prefix + execIp,
		taskProgressKey: prefix + progress,
	}
}

type DbAction interface {
	GetConf() *DbBaseConfig
	DeleteKey(keys string) bool                               // 删除key
	GetKeyName(typ string) string                             // 获取key名称
	GetHashValue(keys, fields string) (string, error)         // 获取hash键值
	SetHashValue(keys, fields, value string) error            // 设置hash键值
	GetValue(keys string) (string, error)                     // 获取普通键值
	SetValue(keys, value string) error                        // 设置普通键值
	ExistKey(keys string) (bool, error)                       // 判断键是否存在
	UpdateLastChkTime(t time.Time)                            // 设置最近检查时间
	GetLastChkTime() time.Time                                // 获取最近检查时间
	SetTaskStartTime(key string)                              // 设置启动任务信息
	SetTaskStateProgress(key, state, progress, remain string) // 设置任务状态、进度信息
	GetBlackIps() string                                      // 获取黑名单
	GetCurrentTid(key string) int                             // 获取当前机器tid
	DelCurrentTid(key string) bool                            // fofa用
	AddCurrentTid(key string, tid int)                        // fofa用
	GetKeyTtl(key string) int32                               // 测试用
	Close() error
}

func NewDb(conf *DbBaseConfig) DbAction {
	switch conf.dbType {
	case DBTRedis:
		return NewRedis(conf)

	default:
		log.Println("[FATAL] error db type")
		return nil
	}
}
