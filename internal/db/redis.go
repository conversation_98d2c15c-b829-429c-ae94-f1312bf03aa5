package db

import (
	"log"
	"strings"
	"time"

	"github.com/garyburd/redigo/redis"
	"github.com/pkg/errors"
)

type Redis struct {
	blackIpsKey     string
	blackPortIpsKey string
	pool            *redis.Pool
	*DbBaseConfig
}

func NewRedis(conf *DbBaseConfig) *Redis {
	pool := NewRedisPool(conf.dbConf.Addr, conf.dbConf.Db, conf.dbConf.Password)
	return &Redis{
		DbBaseConfig:    conf,
		pool:            pool,
		blackIpsKey:     conf.prefix + "portscan_black_ips",
		blackPortIpsKey: conf.prefix + "portscan_port_black_ips",
	}
}

func (r *Redis) GetKeyName(typ string) string {
	switch typ {
	case "stop_task":
		return r.stopTaskKey

	case "exec_ip":
		return r.execIpKey

	case "task_progress":
		return r.taskProgressKey

	case "port_blacklist":
		return r.blackPortIps<PERSON>ey
	}

	return ""
}

func (r *Redis) GetConf() *DbBaseConfig {
	return r.DbBaseConfig
}

func (r *Redis) UpdateLastChkTime(t time.Time) {
	r.lastUpExecTime = t
}

func (r *Redis) GetLastChkTime() time.Time {
	return r.lastUpExecTime
}

func (r *Redis) Close() error {
	return r.pool.Close()
}

func (r *Redis) SetTaskStartTime(key string) {
	timeString := time.Now().Format("2006-01-02 15:04:05")
	_ = r.SetHashValue(key, "state", "0")
	_ = r.SetHashValue(key, "progress", "0.00")
	_ = r.SetHashValue(key, "begin_scan_time", timeString)
}

// 任务状态、进度、完成时间
func (r *Redis) SetTaskStateProgress(key, stateStr, progress, remainTime string) {
	_ = r.SetHashValue(key, "state", stateStr)
	_ = r.SetHashValue(key, "progress", progress)
	_ = r.SetHashValue(key, "task_pct", progress)
	_ = r.SetHashValue(key, "remaining_time", remainTime)
	_ = r.SetHashValue(key, "task_remaining", remainTime)

	if stateStr == "5" {
		r.AddTaskFinishTime(key)
	}
}

func (r *Redis) AddTaskFinishTime(key string) {
	timeString := time.Now().Format("2006-01-02 15:04:05")
	_ = r.SetHashValue(key, "finish_time", timeString)
}

// 添加任务执行进度
func (r *Redis) SetHashValue(key string, field, value string) error {
	c := r.pool.Get()
	defer c.Close()

	_, err := c.Do("hset", key, field, value)
	if err != nil {
		return err
	}

	return nil
}

// 获取hash键值
func (r *Redis) GetHashValue(keys, fields string) (string, error) {
	c := r.pool.Get()
	defer c.Close()
	var values string

	res, err := redis.String(c.Do("HGET", keys, fields))
	if err != nil {
		if !strings.Contains(err.Error(), "nil returned") {
			return values, err
		}
	} else {
		values = res
	}

	return values, nil
}

// 获取普通键值
func (r *Redis) GetValue(keys string) (string, error) {
	c := r.pool.Get()
	defer c.Close()
	var values string

	res, err := redis.String(c.Do("GET", keys))
	if err != nil {
		if !strings.Contains(err.Error(), "nil returned") {
			return values, err
		}
	} else {
		values = res
	}

	return values, nil
}

// 设置普通键值
func (r *Redis) SetValue(keys, value string) error {
	c := r.pool.Get()
	defer c.Close()

	_, err := c.Do("SET", keys, value)
	if err != nil {
		return err
	}

	return nil
}

func (r *Redis) DelCurrentTid(key string) bool {
	c := r.pool.Get()
	defer c.Close()
	_, err := c.Do("DEL", key)
	if err != nil {
		log.Println("[ERROR] del current tid failed:", err)
		return false
	}

	exists, _ := r.ExistKey(key)
	return !exists
}

func (r *Redis) AddCurrentTid(key string, tid int) {
	c := r.pool.Get()
	defer c.Close()
	_, err := c.Do("SET", key, tid)
	if err != nil {
		log.Println("[ERROR] add current tid failed:", err)
	} else {
		_, exErr := c.Do("EXPIRE", key, 7*24*3600)
		if exErr != nil {
			log.Println("[ERROR] add current tid set expire failed:", err)
		}
	}
}

func (r *Redis) GetCurrentTid(key string) int {
	c := r.pool.Get()
	defer c.Close()
	var tid int

	tmpTid, err := redis.Int(c.Do("GET", key))
	if err != nil {
		log.Println("[ERROR] get current tid failed:", err)
		return -1
	} else {
		tid = tmpTid
	}

	return tid
}

func (r *Redis) DeleteKey(keys string) bool {
	c := r.pool.Get()
	defer c.Close()

	_, err := c.Do("DEL", keys)
	if err != nil {
		log.Println("[ERROR] delete key failed:", err)
		return false
	}

	exists, _ := r.ExistKey(keys)
	return !exists
}

func (r *Redis) GetKeyTtl(key string) int32 {
	c := r.pool.Get()
	defer c.Close()
	var remainTtl int32
	remainTtl = 0

	tmpTtl, err := c.Do("TTL", key)
	if err != nil {
		log.Println("[ERROR] get ttl failed:", err)
	} else {
		remainTtl = int32(tmpTtl.(int64))
	}

	return remainTtl
}

// 获取ip黑名单
func (r *Redis) GetBlackIps() string {
	c := r.pool.Get()
	defer c.Close()
	blackIps := ""

	curBlackips, err := redis.Values(c.Do("SMEMBERS", r.blackIpsKey))
	if err != nil {
		log.Println("[ERROR] get black ip failed:", err)
	} else {
		for _, v := range curBlackips {
			blackIps += "," + string(v.([]byte))
		}
	}

	return blackIps
}

func (r *Redis) ExistKey(key string) (bool, error) {
	conn := r.pool.Get()
	defer conn.Close()
	isKeyExit, err := redis.Bool(conn.Do("EXISTS", key))
	if err != nil {
		return false, errors.Wrap(err, "redis error")
	}

	return isKeyExit, nil
}

func NewRedisPool(addr string, db int, password string) *redis.Pool {
	return &redis.Pool{
		MaxIdle:     3,
		MaxActive:   5000,
		IdleTimeout: 240 * time.Second,
		Dial: func() (redis.Conn, error) {
			option := []redis.DialOption{redis.DialDatabase(db)}
			if password != "" {
				option = append(option, redis.DialPassword(password))
			}
			c, err := redis.Dial(
				"tcp",
				addr,
				option...,
			)
			if err != nil {
				return nil, err
			}
			return c, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("ping")
			if err != nil {
				log.Println("ping redis fail", err)
			}
			return err
		},
	}
}
