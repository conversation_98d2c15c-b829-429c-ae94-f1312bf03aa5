package db

import (
	"checkurl/config"
	"log"
	"time"

	"github.com/garyburd/redigo/redis"
	"github.com/pkg/errors"
)

type Redis struct {
	blackHostsKey  string
	blackDomainKey string
	checkedPrefix  string
	pool           *redis.Pool
}

func NewRedis(conf config.RedisConf) *Redis {
	pool := NewRedisPool(conf.Addr, conf.Db, conf.Password, conf.PoolSize)
	return &Redis{
		pool:           pool,
		blackHostsKey:  conf.Prefix + "black_hosts",
		blackDomainKey: conf.Prefix + "black_domains",
		//hostsKey:       conf.Prefix + "hosts",
		checkedPrefix: conf.CheckedPrefix,
	}
}

func (r *Redis) Close() error {
	return r.pool.Close()
}

func (r *Redis) HostChecked(host string) bool {
	c := r.pool.Get()
	defer c.Close()

	tmpKey := r.checkedPrefix + host
	is, err := redis.Bool(c.Do("EXISTS", tmpKey))
	if err != nil {
		log.Println("[ERROR] host-checked failed  host:", host, "errdesc:", err)
		return false
	}

	return is
}

// 将已经处理的host设置为单独的key，并设置自动超时
func (r *Redis) AddHost(host string, timeout int) {
	c := r.pool.Get()
	defer c.Close()

	tmpKey := r.checkedPrefix + host
	_, err := c.Do("SET", tmpKey, "1")
	if err != nil {
		log.Println("[ERROR] add-host set key failed  host:", host, "errdesc:", err)
		return
	}

	_, exErr := c.Do("EXPIRE", tmpKey, timeout)
	if exErr != nil {
		log.Println("[ERROR] add-host set expire failed delete it  host:", host, "errdesc:", exErr)

		err := r.delKey(tmpKey) // 置超时失败的时候先删除key，避免永远都不扫描
		if err != nil {
			log.Println("[ERROR] add-host delete keys failed  host:", host, "errdesc:", err)
		}

		return
	}
}

func (r *Redis) delKey(keys string) error {
	conn := r.pool.Get()
	defer conn.Close()
	_, err := redis.Bool(conn.Do("DEL", keys))
	if err != nil {
		return errors.Wrap(err, "redis error")
	}

	return nil
}

// 判断域名黑名单
func (r *Redis) IsBlackDomain(domain string) bool {
	black, err := r.exist(r.blackDomainKey, domain)
	if err != nil {
		log.Println("[ERROR] is-black-domain failed  domain:", domain, "errdesc:", err)
		return false
	}
	return black
}

// 判断host黑名单
func (r *Redis) IsBlackHost(host string) bool {
	black, err := r.exist(r.blackHostsKey, host)
	if err != nil {
		log.Println("[ERROR] is-black-host failed  host:", host, "errdesc:", err)
		return false
	}
	return black
}

// TODO 重试次数配置
func (r *Redis) exist(key, value string) (bool, error) {
	conn := r.pool.Get()
	defer conn.Close()
	exist, err := redis.Bool(conn.Do("SISMEMBER", key, value))
	if err != nil {
		return false, errors.Wrap(err, "redis error")
	}
	return exist, nil
}

func NewRedisPool(addr string, db int, password string, poolSize int) *redis.Pool {
	return &redis.Pool{
		MaxIdle:     poolSize,
		MaxActive:   poolSize,
		IdleTimeout: 240 * time.Second,
		Dial: func() (redis.Conn, error) {
			option := []redis.DialOption{redis.DialDatabase(db)}
			if password != "" {
				option = append(option, redis.DialPassword(password))
			}
			c, err := redis.Dial(
				"tcp",
				addr,
				option...,
			)
			if err != nil {
				return nil, err
			}
			return c, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("ping")
			if err != nil {
				log.Println("ping redis fail", err)
			}
			return err
		},
	}
}
