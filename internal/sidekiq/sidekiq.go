package sidekiq

import (
	"strconv"

	workers "github.com/jrallison/go-workers"
)

var Enable bool

func Init(addr, password string, db int) {
	m := map[string]string{
		// location of redis instance
		"server": addr,
		// instance of the database
		"database": strconv.Itoa(db),
		// unique process id for this instance of workers (for proper recovery of inprogress jobs on crash)
		"process":       "checkurl-1",
		"poll_interval": "5",
		"pool":          "5",
	}
	if password != "" {
		m["password"] = password
	}
	workers.Configure(m)
	Enable = true
}

type SidekiqConsumer struct {
	msgCh chan []byte
	errCh chan error
}

func (sc *SidekiqConsumer) Messages() <-chan []byte {
	return sc.msgCh
}

func (sc *SidekiqConsumer) Close() error {
	//workers.Quit()
	return nil
}

func (sc *SidekiqConsumer) Errors() <-chan error {
	return sc.errCh
}

func NewConsumer(queue string) *SidekiqConsumer {
	sc := &SidekiqConsumer{
		msgCh: make(chan []byte),
		errCh: make(chan error),
	}
	job := func(msg *workers.Msg) {
		data, err := msg.Args().MarshalJSON()
		if err != nil {
			sc.errCh <- err
			return
		}
		sc.msgCh <- data
	}
	workers.Process(queue, job, 1)
	go workers.Run()
	return sc
}

func Publish(queue, class string, args interface{}) error {
	_, err := workers.EnqueueWithOptions(queue, class, args, workers.EnqueueOptions{Retry: true, RetryCount: 3})
	if err != nil {
		return err
	}
	return nil
}
