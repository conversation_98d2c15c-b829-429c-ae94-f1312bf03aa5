package sidekiq

import (
	"log"
	"strconv"

	workers "github.com/jrallison/go-workers"
)

var Enable bool

func Init(addr, password string, db int, hostName string) {
	if len(hostName) == 0 {
		hostName = "portscan"
	}

	m := map[string]string{
		// location of redis instance
		"server": addr,
		// instance of the database
		"database": strconv.Itoa(db),
		// unique process id for this instance of workers (for proper recovery of inprogress jobs on crash)
		"process": hostName,
	}
	if password != "" {
		m["password"] = password
	}
	workers.Configure(m)
	Enable = true
}

type SidekiqConsumer struct {
	msgCh chan []byte
	errCh chan error
	done  chan struct{}
	queue string
}

func (sc *SidekiqConsumer) Messages() <-chan []byte {
	return sc.msgCh
}

func (sc *SidekiqConsumer) Close() error {
	//workers.Quit()
	return nil
}

func (sc *SidekiqConsumer) Errors() <-chan error {
	return sc.errCh
}

func (sc *SidekiqConsumer) Done() {
	log.Println("close Sidekiq wait ")
	sc.done <- struct{}{}
}

func NewConsumer(queue string) *SidekiqConsumer {
	sc := &SidekiqConsumer{
		msgCh: make(chan []byte),
		errCh: make(chan error),
		done:  make(chan struct{}),
		queue: queue,
	}

	return sc
}

func (sc *SidekiqConsumer) Run() {
	job := func(msg *workers.Msg) {
		data, err := msg.Args().MarshalJSON()
		if err != nil {
			sc.errCh <- err
			return
		}
		sc.msgCh <- data

		// 等待 masscan执行结束，否则会提取多个任务
		<-sc.done
	}
	workers.Process(sc.queue, job, 1)
	go workers.Run()
}

func Publish(queue, class string, args interface{}) error {
	_, err := workers.EnqueueWithOptions(queue, class, args, workers.EnqueueOptions{Retry: true, RetryCount: 3})
	if err != nil {
		return err
	}

	return nil
}
