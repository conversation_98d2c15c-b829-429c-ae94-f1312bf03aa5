package sidekiq

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func InitRedisMock() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestInit(t *testing.T) {
	s := InitRedisMock()
	queueName := "test_go_scan_service_worker"

	Init(s.Addr(), "", 5, "test_portscan")
	assert.True(t, Enable)
	cg := NewConsumer(queueName)
	assert.NotNil(t, cg)

	// 数据测试
	go cg.Run()
	mpTest := make(map[string]interface{})
	mpTest["abcd"] = 1234
	err := Publish(queueName, "TestClass", mpTest)
	assert.Nil(t, err)
	msg := <-cg.Messages()
	assert.Equal(t, `{"abcd":1234}`, string(msg))

	go cg.Done()
	go cg.Close()
	go cg.Errors()
}

func TestInitPwd(t *testing.T) {
	s := InitRedisMock()

	Init(s.Addr(), "password", 5, "test_portscan")
	cg := NewConsumer("test_go_scan_service_worker")
	assert.NotNil(t, cg)
}
