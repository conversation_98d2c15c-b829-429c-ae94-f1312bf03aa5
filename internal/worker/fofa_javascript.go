package worker

import (
	"crawler/internal/http"
	"git.gobies.org/longzhuan/go_common/tools/pack"
	"time"

	"log"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
)

/*
获取body中js，并将其转换为md5

流程：
1. 先获取网页里面的js地址，提取前三个相对路径js，合成为完整url。
2. 请求地址，判断是否成功。
3. 将内容转换为MD5
*/

var reg = regexp.MustCompile(`(?i:<script\s+[^\s]*\s*[^\s]*\s*src)=['\"]?([^'\"\ ]+)['\"]?`)
var reg1 = regexp.MustCompile(`(?i:\s+src)=['\"]?([^'\"\ ]+)['\"]?`)

func GetBodyJsInfo(body string, surl string) (jsInfo *http.JsInfo, isSensitive bool) {
	if !strings.Contains(body, ".js") {
		return
	}

	uu, err := url.Parse(surl)
	if err != nil {
		log.Println("[ERROR] proc parse url failed:", surl, "errdesc:", err)
		return
	}
	bodyJsArr := getJs(uu, body)
	jsInfo = new(http.JsInfo)

	jsInfo.JsNum = len(bodyJsArr)
	count := 0
	reqCnt := 0
	continuousFailCnt := 0
	for _, reqUrl := range bodyJsArr {
		reqFail := false
		timePreTmp := time.Now()
		resp, _, err := http.RequestWithLocation(reqUrl[0], 10, http.RTJS)

		reqCnt++
		// 不管是否报错，限制最多请求次数
		if reqCnt > 10 {
			break
		}

		if err != nil {
			reqFail = true
			log.Println("[ERROR] proc http req failed:", surl, "failCnt:", continuousFailCnt, "errdesc:", err)
		} else {
			// 请求时间非常长的也算失败
			if time.Since(timePreTmp).Seconds() >= (DefaultTimeout.Seconds()/2 + 1) {
				reqFail = true
			}
		}

		if reqFail {
			continuousFailCnt++
			// 连续3次都失败的，直接退出
			if continuousFailCnt >= 3 {
				log.Println("  request js continuous fail", surl, continuousFailCnt)
				break
			}

			continue
		}

		// 类型不对的丢弃
		ctxType := resp.Response().Header.Get("Content-Type")
		ctxType = strings.ToLower(ctxType)
		if !strings.Contains(ctxType, "javascript") {
			continue
		}

		//判断js长度为0时，跳过
		jsLen := len(resp.Bytes())
		if jsLen <= 0 {
			continue
		}

		//截掉?后面的参数
		if index := strings.Index(reqUrl[1], "?"); index != -1 {
			reqUrl[1] = reqUrl[1][:index]
		}
		//截掉#后面的参数
		if index := strings.Index(reqUrl[1], "#"); index != -1 {
			reqUrl[1] = reqUrl[1][:index]
		}
		//name长度超过200，跳过
		if len(reqUrl[1]) > 200 {
			log.Println("[info] proc js.name length is too large:", surl, "name:", reqUrl[1])
			continue
		}

		//如果有host，截取，只存相对路径
		if index := strings.LastIndex(reqUrl[1], uu.Host); index != -1 {
			reqUrl[1] = reqUrl[1][index+len(uu.Host):]
		}

		jsBody := resp.Bytes()
		jsInfo.JsName = append(jsInfo.JsName, reqUrl[1])
		jsMd5 := pack.ByteToMD5(jsBody)
		jsInfo.Md5 = append(jsInfo.Md5, jsMd5)
		jsInfo.JsLen = append(jsInfo.JsLen, jsLen)

		// 判断是否敏感内容
		if jsLen > 10 && !isSensitive && http.NeedCheckFraud(surl, "") {
			lowerBody := strings.ToLower(string(jsBody))
			docwriteCnt := strings.Count(lowerBody, "document.writeln")
			if docwriteCnt > 20 {
				log.Println("    sensitive maybe document.writeln too much", docwriteCnt, " url:", surl, reqUrl[1], jsMd5)

				if docwriteCnt > 100 {
					isSensitive = true
				}
			}
		}

		if count == 5 {
			break
		}
		count++
	}
	return
}

func getFullJsUrl(uu *url.URL, s string) string {
	if s == "" {
		return ""
	}

	var isContainsHost bool
	//截掉?后面的参数
	if index := strings.Index(s, "?"); index != -1 {
		isContainsHost = strings.Contains(s[:index], uu.Host)
	} else {
		isContainsHost = strings.Contains(s, uu.Host)
	}

	isContainsHttp := false
	if strings.Contains(s, "http://") || strings.Contains(s, "https://") {
		isContainsHttp = true
	}
	//如果http开头但不包含host
	if isContainsHttp && !isContainsHost {
		return ""
	}
	surl := uu.Scheme + "://" + uu.Host

	urlPath := uu.Path
	//这个其实是跳转到/ui/
	if urlPath == "/ui" {
		urlPath += "/"
	}
	if len(urlPath) > 1 {
		tmpDir := filepath.Dir(urlPath)
		if string(tmpDir[len(tmpDir)-1]) == "/" {
			tmpDir = tmpDir[:len(tmpDir)-1]
		}
		surl = uu.Scheme + "://" + uu.Host + tmpDir
		if strings.Contains(s, "../") {
			index := strings.LastIndex(tmpDir, "/")
			if index != -1 {
				surl = uu.Scheme + "://" + uu.Host + tmpDir[:index]
			}
		}
	}

	s = strings.Replace(s, "../", "", -1)
	s = strings.Replace(s, "./", "", -1)

	if isContainsHost && isContainsHttp {
		return s
	} else if strings.HasPrefix(s, "//") {
		return uu.Scheme + ":" + s
	} else if strings.HasPrefix(s, "/") {
		return surl + s
	} else {
		return surl + "/" + s
	}
}

func getJs(uu *url.URL, html string) (js [][]string) {
	mpJsUrls := make(map[string]bool) // 去重用

	html = reg.ReplaceAllStringFunc(html, func(str string) string {
		match := reg1.FindStringSubmatch(str)
		turl := ""
		if len(match) >= 2 {
			turl = match[1]
		}
		if turl != "" {
			newurl := getFullJsUrl(uu, turl)
			ext, _ := http.GetUrlFilename(newurl)
			if ext == ".js" || ext == ".script" {
				// 判断是否已经存在了
				if _, ok := mpJsUrls[newurl]; !ok {
					mpJsUrls[newurl] = true
					js = append(js, []string{newurl, turl})
				}
			}
		}
		return str
	})

	return
}
