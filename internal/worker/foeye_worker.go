package worker

import (
	"baimaohui/portscan_new/internal/sidekiq"
	"baimaohui/portscan_new/internal/task"
	"encoding/json"
	"log"
	"strings"
)

type FoeyeWorker struct {
	*BaseWorker
}

func NewFoeyeWorker(bw *BaseWorker) *FoeyeWorker {
	fw := &FoeyeWorker{
		BaseWorker: bw,
	}
	fw.setProducerCallback(fw.checkServiceProducer)
	fw.setOnFinishCallback(fw.finishTaskProducer)
	fw.setOnStopTaskCallback(fw.procStopTask)

	return fw
}

func (fw *FoeyeWorker) checkServiceProducer(hostports [][4]string, options map[string]interface{}) error {
	var skErr error
	if hostports[0][3] == "icmp" && hostports[0][1] == "0" {
		if fw.taskObjMgr.GetEngineType() == task.ETTreckScan {
			mapTest := make(map[string]interface{})
			err := json.Unmarshal([]byte(hostports[0][0]), &mapTest)
			if err != nil {
				log.Println("[ERROR] treck info unmarshal failed", err, hostports[0][0])
				return nil
			}
			args := []interface{}{mapTest}
			if options != nil {
				args = append(args, []interface{}{options}...)
			}
			log.Println("  treck info", "assets_insert", args)
			skErr = sidekiq.Publish("assets_insert", "ServiceInsertWorker", args)
		} else {
			// ping result
			newHostports := make([]string, 0)
			for _, value := range hostports {
				newHostports = append(newHostports, value[0])
			}
			args := []interface{}{newHostports}
			if options != nil {
				args = append(args, []interface{}{options}...)
			}

			log.Println("  pingassets info", fw.pingQueue, args)
			skErr = sidekiq.Publish(fw.pingQueue, "AssetsInsertWorker", args)
		}
	} else {
		args := []interface{}{hostports}
		if options != nil {
			newOptions := mergeArgsExtra(options)
			args = append(args, []interface{}{newOptions}...)
		}
		log.Println("  grab task info", fw.grabQueue, args)
		skErr = sidekiq.Publish(fw.grabQueue, "CheckServiceWorker", args)
	}

	if skErr != nil {
		return skErr
	}

	return nil
}

func mergeArgsExtra(extra map[string]interface{}) map[string]interface{} {
	newExtra := make(map[string]interface{})
	// 去掉超级长的参数往后传，不需要了
	for k, v := range extra {
		if k != "ip_list" && k != "hosts" {
			newExtra[k] = v
		}
	}

	return newExtra
}

func (fw *FoeyeWorker) finishTaskProducer() {
	log.Println("    foeye_worker finish task producer in")

	// 扫描任务结束处理
	push := false
	stopSt, err := fw.dbAct.GetValue(fw.dbAct.GetKeyName("stop_task"))
	if err != nil {
		log.Println("[ERROR] on_task_finish publish domain sidekiq failed:", err)
	}
	log.Println("  on_task_finish stopSt:", stopSt)
	if stopSt == "2" || stopSt == "" {
		push = true
	}

	if !push {
		return
	}

	// 下发域名扫描任务
	tmpDomains, tmpOptions := fw.taskObjMgr.GetMsgDomains()
	if len(tmpDomains) > 0 {
		args := []interface{}{tmpDomains}
		if tmpOptions != nil {
			args = append(args, tmpOptions)
		}

		log.Println("  on_task_finish push domain info:", args)
		skErr := sidekiq.Publish(fw.checkurlQueue, "CheckUrlWorker", args)
		if skErr != nil {
			log.Println("[ERROR] on_task_finish publish domain sidekiq failed", skErr)
		}
	}
}

func (fw *FoeyeWorker) procStopTask(classObj interface{}) {
	stopScanSt, err := fw.dbAct.GetValue(fw.dbAct.GetKeyName("stop_task"))
	if err != nil {
		log.Println("  worker proc_stop_task get info failed:", err)
	}
	if !strings.Contains(stopScanSt, ":1") {
		return
	}

	log.Println("  worker task stop-state:", stopScanSt)
	err = fw.taskObjMgr.Stop()
	if err != nil {
		log.Println("  [ERROR] worker task stop failed", err)
	}
}
