package worker

/*type FoeyeWorker struct {
	*BaseWorker
}

func NewFoeyeWorker(bw *BaseWorker) *FoeyeWorker {
	fw := &FoeyeWorker{
		BaseWorker: bw,
	}

	fw.SetProcOneCallback(fw.ProcOne)

	return fw
}

func (fw *FoeyeWorker) ProcOne(msg []byte) {
	foeyeObj, err := fw.msgParse.ParseMsg(msg)
	if err != nil {
		log.Println("[ERROR] proc_one parse msg failed:", err)
		return
	}
	foeyeMsg := foeyeObj.(parse.FoeyeMessage)

	for _, url := range foeyeMsg.ArrUrl {
		atomic.AddUint64(&totalTask, 1)
		foeyeMsg.Url = url.(string)
		foeyeMsg.AddLinkHost = fw.addLinkHost

		fw.checkUrl(foeyeMsg.Url, false, foeyeMsg)
	}
}

func (fw *FoeyeWorker) checkUrl(u string, force bool, feMsg parse.FoeyeMessage) {
	log.Println("check url:", u)
	hostinfo, err := url.GetHostInfo(u)
	if err != nil {
		atomic.AddUint64(&badUrl, 1)
		log.Println("[WARN] get host info failed", u, err)
		return
	}

	onlyhost := hostinfo.OnlyHost
	if ip.Valid(onlyhost) { // 是IP
		if !ip.Decimal(onlyhost) { // 但不是普通十进制形式视为非法(防止垃圾IP,单个IP用无限种字符串形式表示)
			atomic.AddUint64(&badIp, 1)
			log.Println("stop illegal IP:", onlyhost)
			return
		}
	}

	//// 更新时间检查
	//m, err := fw.esStub.Get(hostinfo.Host)
	//if err != nil {
	//	log.Println("[ERROR] check_url get es failed", hostinfo.Host, err)
	//}
	//if err == nil {
	//	// 检查全部通过，可以更新
	//	fw.esStub.UpdateCheckTime(hostinfo.Host, m)
	//}

	atomic.AddUint64(&successTask, 1)

	fw.sendCrawlerTask(hostinfo, feMsg)
}

func (fw *FoeyeWorker) sendCrawlerTask(hostinfo *url.HostInfo, feMsg parse.FoeyeMessage) {
	args := make([]interface{}, 2)
	args[0] = hostinfo.Host
	args[1] = hostinfo.Domain
	if len(feMsg.Url) > 0 {
		opt := make(map[string]interface{})
		opt["subdomain"] = hostinfo.Subdomain
		opt["task_id"] = feMsg.Options.TaskId
		opt["addlinkhosts"] = feMsg.AddLinkHost
		opt["retry"] = 3
		opt["max_asset_num"] = feMsg.Options.MaxAssetNum
		args = append(args, []interface{}{opt}...)
	}
	skErr := sidekiq.Publish(fw.processurlQueue, "ProcessUrlWorker", args)
	if skErr != nil {
		log.Println("[ERROR] send to ProcessUrl-wk failed", skErr)
	}
}*/
