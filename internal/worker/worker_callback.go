package worker

import (
	"baimaohui/portscan_new/internal/taskmgr"
	"log"
	"strconv"
	"time"
)

/*
调用相应的任务，获取结果
处理停止
*/
func (bw *BaseWorker) taskExec(classObj interface{}, ip string) {
	if time.Now().Sub(bw.dbAct.GetLastChkTime()).Seconds() > 3 {
		log.Println("  worker Scanning asset:", ip)
		err := bw.dbAct.SetValue(bw.dbAct.GetKeyName("exec_ip"), ip)
		if err != nil {
			log.Println("  worker task_exec update exec ip info failed:", err)
			return
		}

		bw.dbAct.UpdateLastChkTime(time.Now())
	}

	// 处理任务停止
	if bw.onRecvStopTaskCB != nil {
		bw.onRecvStopTaskCB(classObj)
	}
}

// 更新进度信息
func (bw *BaseWorker) taskProgress(engine interface{}, tid int, state, hostCompleted, progress, remainTime, errMsg string) {
	keys := bw.taskObjMgr.GetTaskKey(tid)
	if string(progress[len(progress)-1]) == "%" {
		progress = progress[:len(progress)-1] // masscan进度后面有%
	}

	log.Printf("   worker orig progress  tid:%d key:%s state:%s hostCompleted:%s progress:%s remaintime:%s errmsg:%s\n",
		tid, keys, state, hostCompleted, progress, remainTime, errMsg)

	// 不是停止才计算进度
	newProgress := progress
	var err error
	if !bw.taskObjMgr.IsStoped() {
		newProgress, err = bw.taskObjMgr.GetFullProgress(progress)
		if err != nil {
			return
		}
	}

	// 任务没全完成的不能直接上报完成状态
	if state == "5" && newProgress != "99" && newProgress != "99.9" && newProgress != "100" {
		state = "2"
	}
	log.Printf("   worker new progress  tid:%d key:%s state:%s progress:%s\n",
		tid, keys, state, newProgress)

	// rpc上传进度信息
	if bw.rpcCli != nil {
		ip, err := bw.dbAct.GetValue(bw.dbAct.GetKeyName("exec_ip"))
		if err != nil {
			log.Println("[ERROR] worker task_progress get exec ip failed", tid, err)
		}
		tidStr := strconv.Itoa(tid)
		bw.rpcCli.SendProgress(tidStr, ip, state, hostCompleted, newProgress, remainTime, errMsg)
	}

	// 处理任务停止
	if curEng, ok := engine.(*taskmgr.TaskEngine); ok {
		bw.onRecvStopTaskCB(curEng.ScanClass)
	}

	bw.dbAct.SetTaskStateProgress(keys, state, newProgress, remainTime)
}

/*
删除停止标记
下发域名扫描任务
*/
func (bw *BaseWorker) onTaskFinish(tid string, initStoped, runStoped bool) {
	if initStoped {
		// 初始化的时候碰到停止的需要发送停止成功回调
		if bw.rpcCli != nil {
			bw.rpcCli.SendProgress(tid, "", "6", "0", "0.1", "", "init task have stop flag")
		}
	}

	if initStoped || runStoped {
		return
	}

	bw.onFinishCB()
}

/*
判断是否最近处理过
*/
func (bw *BaseWorker) onCheckLastProced(id string) bool {
	// 没启用的时候都是未处理
	if !bw.sysConf.LmtRds.Enable {
		return false
	}

	skey := bw.sysConf.LmtRds.Prefix + id
	exists, err := bw.dbLmtAct.ExistKey(skey)
	if err != nil {
		log.Println("[ERROR] check last proced failed:", err)
		return false
	}

	return exists
}
