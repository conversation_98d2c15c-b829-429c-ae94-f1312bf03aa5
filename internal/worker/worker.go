package worker

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/rpc"
	"baimaohui/portscan_new/internal/taskmgr"
	"baimaohui/portscan_new/internal/util"
	"go.uber.org/zap"
	"log"
)

type BaseWorker struct {
	hostname          string                  // 主机名
	grabQueue         string                  // 协议识别队列
	pingQueue         string                  // ping入库队列
	checkurlQueue     string                  // checkurl队列
	sysConf           *conf.Config            // 系统配置文件
	consumer          queue.SimpleConsumer    // 消费者对象
	producer          queue.SimpleProducer    // 生产者对象
	msgParse          parse.ParseMsgStub      // 消息解析对象
	taskObjMgr        taskmgr.TaskMgrStub     // 具体任务对象管理
	dbAct             db.DbAction             // 数据库对象
	dbLmtAct          db.DbAction             // limit数据库对象
	rpcCli            *rpc.RPCClient          // RPC客户端
	stopper           chan struct{}           // 用于停止任务
	onProducerCB      producerCallback        // 生产者的处理回调函数
	onFinishCB        onFinishCallback        // 结束任务处理回调函数
	onRecvStopTaskCB  onRecvStopTaskCallback  // 处理停止扫描任务回调
	programStopProcCB programStopProcCallback // 处理程序退出停止任务回调
}

type producerCallback func(hostports [][4]string, options map[string]interface{}) error
type onFinishCallback func()
type onRecvStopTaskCallback func(classObj interface{}) // 接收到微内核停止消息回调函数
type programStopProcCallback func()                    // 手工停止进程触发的停止回调函数

func NewBaseWorker(consumer queue.SimpleConsumer, producer queue.SimpleProducer, opts ...Option) *BaseWorker {
	w := &BaseWorker{
		consumer: consumer,
		producer: producer,
		stopper:  make(chan struct{}),
	}

	for _, opt := range opts {
		opt(w)
	}

	return w
}

type WorkerStub interface {
	Start()
	Stop()
}

func NewInstanceWorker(typ util.ProgramType, bw *BaseWorker) WorkerStub {
	switch typ {
	case util.PTFoeye:
		return NewFoeyeWorker(bw)

	case util.PTFofa:
		return NewFofaWorker(bw)

	default:
		panic("invalid worker type")
	}
}

// 设置通用回调
func (bw *BaseWorker) setCallbacks() {
	bw.taskObjMgr.SetExecCallback(bw.taskExec, bw.taskProgress, bw.onCheckLastProced)
	bw.taskObjMgr.SetFoundCallback(bw.handleTaskOutput)
	bw.taskObjMgr.SetTaskFinishCallback(bw.onTaskFinish)
}

// 设置生产者处理回调（用于基类调用派生类方法）
func (bw *BaseWorker) setProducerCallback(pcb producerCallback) {
	bw.onProducerCB = pcb
}

// 设置任务完成处理回调（用于基类调用派生类方法）
func (bw *BaseWorker) setOnFinishCallback(fcb onFinishCallback) {
	bw.onFinishCB = fcb
}

func (bw *BaseWorker) setOnStopTaskCallback(stcb onRecvStopTaskCallback) {
	bw.onRecvStopTaskCB = stcb
}

func (bw *BaseWorker) setProgramStopProcCallback(pspcb programStopProcCallback) {
	bw.programStopProcCB = pspcb
}

func (bw *BaseWorker) Start() {
	// 设置回调
	bw.setCallbacks()

	if bw.onProducerCB == nil {
		panic("[ERROR] base_worker producer callback nil")
	}

	// 继续之前没完成的任务
	ch, err := bw.taskObjMgr.MgrHandlePaused()
	if err != nil {
		log.Println(err)
	} else if ch != nil {
		bw.handleTaskOutput(ch, nil)
	}

	log.Println("consumer run")
	bw.consumer.Run()

	// 消费消息队列
	for {
		log.Println("worker for in")

		select {
		case msg := <-bw.consumer.Messages():
			//log.Println("msg", string(msg))

			bw.handleOne(msg)
			log.Println("worker handle_one finished")

			//  置完成事件
			bw.consumer.Done()
			log.Println("worker consumer done")

			/*case <-w.redis.Subscribe():
			log.Println("server stopping...")
			w.Stop()
			log.Println("server stopped")
			os.Exit(0)*/

		case <-bw.stopper:
			return
		}
	}
}

// 处理端口扫描的输出结果
func (bw *BaseWorker) handleTaskOutput(ch <-chan [][4]string, options map[string]interface{}) {
	if ch == nil {
		log.Println("handle_task_output ch is nil")
		return
	}

	for hostports := range ch {
		if hostports == nil {
			log.Println("handle_task_output done")
			return
		}

		err := bw.onProducerCB(hostports, options)
		if err != nil {
			log.Println("[ERROR] to grab task json",
				zap.String("err", err.Error()),
			)
			continue
		}
	}

	log.Println("handle_task_output finished")
}

func (w *BaseWorker) Stop() {
	log.Println("stopping worker...")
	close(w.stopper)
	var err error

	if w.programStopProcCB != nil {
		log.Println("program stop callback proc")
		w.programStopProcCB()
	}

	// 不再处理新消息
	err = w.consumer.Close()
	if err != nil {
		log.Println("[ERROR] consumer close",
			zap.String("err", err.Error()),
		)
	}

	if w.producer != nil {
		// 关闭producer
		err = w.producer.Close()
		if err != nil {
			log.Println("[ERROR] producer close",
				zap.String("err", err.Error()),
			)
		}
	}
}

// 处理单条数据(任务)
func (w *BaseWorker) handleOne(msg []byte) {
	log.Println("port scan msg",
		zap.String("msg", string(msg)),
	)

	msgInter, err := w.msgParse.ParseMsg(msg, w.sysConf.Worker.SendEth)
	if err != nil {
		log.Println("[ERROR] handle_one parse msg failed", err)
		return
	}

	swtErr := w.taskObjMgr.StartWholeTask(msgInter)
	if swtErr != nil {
		log.Println("[ERROR] handle_one start whole task failed", swtErr)
		return
	}
}
