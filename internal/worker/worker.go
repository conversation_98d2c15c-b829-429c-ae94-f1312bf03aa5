package worker

import (
	"golang.org/x/sync/errgroup"
	"log"
)

func (bw *BaseWorker) Start() {
	bw.mu.Lock()
	if !bw.shutdown {
		return
	}
	bw.shutdown = false
	bw.mu.Unlock()

	if bw.onProcOneCB == nil {
		log.Fatal("worker start proc_one_callback invalid")
	}

	bw.startCrontab()

	for {
		select {
		case <-bw.stopper:
			log.Println("worker stopper in")
			bw.done <- struct{}{}

		case msg := <-bw.consumer.Messages():
			bw.taskAdd()
			go bw.handleOne(msg) // 处理消息，如果不调用协程会导致无法退出
		}
	}
}

func (bw *BaseWorker) Stop() {
	bw.mu.Lock()
	defer bw.mu.Unlock()
	if bw.shutdown {
		return
	}

	// 不再处理新消息
	err := bw.consumer.Close()
	if err != nil {
		log.Println("ERROR:", err)
	}

	// 通知关闭，等待最后一条处理完
	log.Println("stop worker stopper")
	bw.stopper <- struct{}{}

	log.Println("wait worker done")
	<-bw.done

	// 等待处理完旧数据
	log.Println("wg wait")
	bw.wg.Wait() // 处理完旧消息

	// 释放资源
	var g errgroup.Group
	if bw.crawlerProducer != nil {
		g.Go(bw.crawlerProducer.Close)
	}
	g.Go(bw.redis.Close)
	err = g.Wait()
	if err != nil {
		log.Println("ERROR:", err)
	}
	bw.shutdown = true
}

// 处理单条数据(任务)
func (bw *BaseWorker) handleOne(msg []byte) {
	log.Println("handle-one recv task:", string(msg))
	if !unitTestEnv {
		defer bw.taskDone(msg)
	}

	bw.onProcOneCB(msg)
}

func (bw *BaseWorker) taskAdd() {
	bw.ch <- struct{}{}
	bw.wg.Add(1)
}

func (bw *BaseWorker) taskDone(msg []byte) {
	//todo w.kafka.Commit(msg)
	<-bw.ch
	bw.wg.Done()
}
