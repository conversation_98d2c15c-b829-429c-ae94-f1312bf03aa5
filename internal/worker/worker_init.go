package worker

import (
	"checkurl/config"
	"checkurl/internal/db"
	"checkurl/internal/es"
	"checkurl/internal/mqqueue"
	"checkurl/internal/parse"
	"fmt"
	"golang.org/x/text/language"
	"golang.org/x/text/message"
	"log"
	"sync"
	"time"
)

var totalTask, successTask uint64
var badUrl, checkedHost, badIp, blackDomain, blackHost, tooShort uint64
var unitTestEnv bool

type onProcOneCallback func(msg []byte)

type BaseWorker struct {
	checkurlQueue    string
	processurlQueue  string
	config           config.Config
	esStub           es.ElasticStub // ES查询
	redis            *db.Redis      // redis查询
	consumer         mqqueue.SimpleConsumer
	crawlerProducer  mqqueue.SimpleProducer
	msgParse         parse.ParseMsgStub // 参数解析对象
	wg               sync.WaitGroup     //  优雅关闭
	ch               chan struct{}      //  并发限制
	done             chan struct{}
	stopper          chan struct{}
	shutdown         bool
	mu               sync.Mutex
	addLinkHost      bool
	disableHostChk   bool              // 是否禁用hostChecked判断
	onProcOneCB      onProcOneCallback // 处理任务回调
	chkKeyExpireTime int               // 已经处理的host键自动超时时间
}

func NewWorker(conf config.Config, es es.ElasticStub, rd *db.Redis, consumer mqqueue.SimpleConsumer,
	crawlerProducer mqqueue.SimpleProducer, parseStub parse.ParseMsgStub) *BaseWorker {
	num := conf.Worker.Num
	if num <= 0 {
		num = 1
	}
	log.Println("set limit:", num)

	bw := &BaseWorker{
		config:          conf,
		redis:           rd,
		esStub:          es,
		consumer:        consumer,
		crawlerProducer: crawlerProducer,
		msgParse:        parseStub,
		ch:              make(chan struct{}, num),
		done:            make(chan struct{}),
		stopper:         make(chan struct{}),
		shutdown:        true,
		addLinkHost:     conf.Worker.AddLinkHost,
		disableHostChk:  conf.Worker.DisableHostChecked,
	}

	timeout, err := time.ParseDuration(conf.Redis.CheckedKeyExpire)
	if err != nil {
		timeout = 30 * 24 * time.Hour
		log.Println("WARN: illegal timeout:", conf.Redis.CheckedKeyExpire, "use default:", timeout)
	}
	bw.chkKeyExpireTime = int(timeout.Seconds())
	log.Println("checked host expire time is:", bw.chkKeyExpireTime)

	return bw
}

func (bw *BaseWorker) SetProcOneCallback(opocb onProcOneCallback) {
	bw.onProcOneCB = opocb
}

func (bw *BaseWorker) SetSidekiq(checkurlQueue, processurlQueue string) {
	bw.checkurlQueue = checkurlQueue
	bw.processurlQueue = processurlQueue
}

func (bw *BaseWorker) startCrontab() {
	msgPrint := message.NewPrinter(language.English)

	go func() {
		startTime := time.Now()
		for range time.Tick(10 * time.Second) {
			successRate := fmt.Sprintf("%.2f%%", float32(successTask)/float32(totalTask)*100)
			elapsed := time.Now().Sub(startTime) / time.Second
			rateQPS := totalTask / uint64(elapsed)
			s := fmt.Sprintf("allmsg:%v success:%v succRate:%v badUrl:%v checkedHost:%v badIp:%v blackDomain:%v blackHost:%v tooShort:%v qps:%v\n",
				msgPrint.Sprintf("%v", totalTask),
				msgPrint.Sprintf("%v", successTask),
				msgPrint.Sprintf("%v", successRate),
				msgPrint.Sprintf("%v", badUrl),
				msgPrint.Sprintf("%v", checkedHost),
				msgPrint.Sprintf("%v", badIp),
				msgPrint.Sprintf("%v", blackDomain),
				msgPrint.Sprintf("%v", blackHost),
				msgPrint.Sprintf("%v", tooShort),
				rateQPS)
			log.Println(s)
		}
	}()
}
