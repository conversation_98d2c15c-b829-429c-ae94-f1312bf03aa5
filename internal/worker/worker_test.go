package worker

/*func TestFofaWorker(t *testing.T) {
	var cfg config.Config
	var workCfg config.WorkerConf
	var esCfg config.ElasticsearchConf
	var consumer mqqueue.SimpleConsumer
	var crawlerProducer mqqueue.SimpleProducer
	var rdsCfg config.RedisConf
	var sideCfg config2.SidekiqCfg
	var rds *db2.Redis

	rdsCfg.Addr = "10.10.10.199:6379"
	rdsCfg.Db = 10
	rds = db2.NewRedis(rdsCfg)

	esCfg.Url = "http://10.10.10.199:9200"
	esCfg.EsVersion = 6
	esCfg.IndexSubdomain = "fofaee_subdomain"
	esCfg.TypeSubdomain = "subdomain"
	esBaseConf := es.NewElasitcBaseConf(esCfg.EsVersion, esCfg.Url, esCfg.IndexName, esCfg.IndexSubdomain, esCfg.TypeSubdomain)
	esStub := es.NewElastic(esBaseConf)

	sideCfg.Addr = "10.10.10.199:6379"
	sideCfg.Db = 10
	sideCfg.CheckurlQueue = "check_url"
	sideCfg.ProcessurlQueue = "process_url"
	sideCfg.Foeye = true
	sidekiq.Init(sideCfg.Addr, sideCfg.Password, sideCfg.Db)

	msgParse := parse.NewMsgParse(util.PTFoeye)

	cfg.Worker = workCfg
	bw := NewWorker(cfg, esStub, rds, consumer, crawlerProducer, msgParse)
	bw.SetSidekiq(sideCfg.CheckurlQueue, sideCfg.ProcessurlQueue)

	wkFofa := NewInstanceWorker(util.PTFoeye, bw)
	assert.NotNil(t, wkFofa)

	unitTestEnv = true

	msg := "[[\"http://[2401:3480:2000:2::6a4b:e0d9]:80\"],{\"bandwidth\":\"200\",\"blacklist\":\"************\",\"deep_get_mac\":false,\"deep_get_os\":false,\"getway_mac\":\"\",\"hostinfos\":null,\"hosts\":\"**********/24\",\"ip_list\":\"**********/24\",\"nmap_scan\":false,\"ping_scan\":false,\"protocol_update_cycle\":0,\"repeat_times\":1,\"resume_filename\":\"\",\"task_id\":\"105\",\"unknown_protocol_indb\":false}]"
	wkFofa.ProcOne([]byte(msg))

	//msgTwo := "[[[\"http://***********:8080\",\"\",\"\"],[\"http://***********:8080\",\"\",\"\"]]]"
	//wkFofa.handleOne([]byte(msgTwo))
}*/
