package worker

import (
	"baimaohui/portscan_new/flow"
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/sidekiq"
	"baimaohui/portscan_new/internal/util"
	"bytes"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"log"
	"os/exec"
	"strings"
	"testing"
	"time"
)

func TstNewSidekiqConsumer(conf *config.SidekiqConfig) queue.SimpleConsumer {
	if conf == nil {
		log.Fatal("sidekiq config is nil")
		return nil
	}

	sidekiq.Init(conf.Addr, conf.Password, conf.Db, conf.Hostname)
	return sidekiq.NewConsumer(conf.PortScanQueue)
}

func wkInitRedisMocks() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestNewWorker(t *testing.T) {
	var sysConf conf.Config
	sysConf.LmtRds = new(conf.LimitRedisConf)
	sysConf.LmtRds.Enable = false
	var masscanCfg config.MasscanConf
	var consumerCfg config.ConsumerConfig
	var sidekiqCfg config.SidekiqConfig
	var consumer queue.SimpleConsumer
	var grabProducer queue.SimpleProducer
	var rdsCfg config.RedisConf
	var rds *db.Redis

	masscanCfg.BlackipList = ""
	masscanCfg.Dir = "./"
	masscanCfg.SourcePort = 58914

	mockRds := wkInitRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	sidekiqCfg.Addr = mockRds.Addr()
	sidekiqCfg.Db = 10
	sidekiqCfg.Foeye = false

	consumerCfg.Sidekiq = &sidekiqCfg
	consumer = TstNewSidekiqConsumer(consumerCfg.Sidekiq)

	rdsCfg.Addr = mockRds.Addr()
	rdsCfg.Prefix = "mk:"
	rdsCfg.Db = 10
	dbBase := db.NewDbBaseConfig(db.DBTRedis, rdsCfg.Prefix, "", "", "", &rdsCfg)
	rds = db.NewRedis(dbBase)

	var option []Option
	option = append(option, SetDbAct(rds))
	option = append(option, SetSidekiq("", "", ""))
	option = append(option, SetDbLimitAct(nil))
	option = append(option, SetRpcClient(nil))
	option = append(option, SetHostname("test_hostname"))
	option = append(option, SetMsgParse(nil))
	option = append(option, SetTaskObjMgr(nil))
	option = append(option, SetSysConf(&sysConf))

	wk := NewBaseWorker(consumer, grabProducer, option...)
	assert.NotNil(t, wk)

	wk.setProducerCallback(nil)
	wk.setOnFinishCallback(nil)
	wk.setOnStopTaskCallback(nil)
	wk.setProgramStopProcCallback(nil)

	tid := "1"
	proced := wk.onCheckLastProced(tid)
	assert.False(t, proced)

	wk.onTaskFinish(tid, true, false)

	wk.taskExec(nil, "127.0.0.1")
	//wk.taskProgress(nil, 1, "2", "10", "20.20", "00:12:13", "")

	wk.handleTaskOutput(nil, nil)

	fofaWK := NewInstanceWorker(util.PTFofa, wk)
	assert.NotNil(t, fofaWK)

	foeyeWK := NewInstanceWorker(util.PTFoeye, wk)
	assert.NotNil(t, foeyeWK)

	//sidekiqCfg.Foeye = true
	//consumer = main.NewConsumer(consumerCfg)
	//wkFE := NewBaseWorker(consumer, grabProducer, option...)
	//assert.Nil(t, wkFE)
}

func execShell(s string) (string, error) {
	cmd := exec.Command("/bin/bash", "-c", s)

	var out bytes.Buffer
	cmd.Stdout = &out

	err := cmd.Run()

	return out.String(), err
}

func waitProcessExit() {
	for {
		cmdStr, cErr := execShell("ps -ef | grep masscan | grep -v grep")
		if cErr != nil {
			fmt.Println("wait masscan exit error:", cErr)
			break
		} else {
			if strings.Contains(cmdStr, "masscan --source-port") {
				log.Println("wait masscan to exit")
				time.Sleep(3 * time.Second)
			}

			if cmdStr == "" {
				break
			}
		}
	}
}

func TestFoeyeHandleOne(t *testing.T) {
	var sysConf conf.Config
	var masscanCfg config.MasscanConf
	var cgCfg config.ConsumerConfig
	var sidekiqCfg config.SidekiqConfig
	var consumer queue.SimpleConsumer
	var gragProducer queue.SimpleProducer
	var rdsCfg config.RedisConf
	//var rds *db.Redis
	var logCfg config.LogConf

	masscanCfg.BlackipList = ""
	masscanCfg.Dir = "./"
	masscanCfg.SourcePort = 58914

	mockRds := wkInitRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	sidekiqCfg.Addr = mockRds.Addr()
	sidekiqCfg.Db = 10
	sidekiqCfg.Foeye = true
	cgCfg.Sidekiq = &sidekiqCfg

	cgCfg.Kafka = nil
	consumer = flow.NewConsumer(cgCfg)

	rdsCfg.Addr = mockRds.Addr()
	rdsCfg.Db = 10

	rdsConf := new(config.RedisConf)
	rdsConf.Addr = mockRds.Addr()
	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		rdsConf)
	dbAct := db.NewDb(dbBaseConf)

	//rds.DelKeys("begin_clear_all_data")
	//rds.DelKeys("stop_scan_flag")

	var option []Option
	option = append(option, SetDbAct(dbAct))
	option = append(option, SetSidekiq("grabQueue", "pingQueue", "checkurlQueue"))
	option = append(option, SetDbLimitAct(nil))
	option = append(option, SetRpcClient(nil))
	option = append(option, SetHostname("test_hostname"))
	var parseStub parse.ParseMsgStub
	parseStub = parse.NewMsgParse(util.PTFoeye)
	option = append(option, SetMsgParse(parseStub))
	option = append(option, SetTaskObjMgr(nil))
	option = append(option, SetSysConf(&sysConf))

	wkFE := NewBaseWorker(consumer, gragProducer, option...)
	assert.NotNil(t, wkFE)

	assert.Equal(t, "grabQueue", wkFE.grabQueue)
	assert.Equal(t, "pingQueue", wkFE.pingQueue)

	logCfg.Output = "stdout"
	logCfg.ErrOutput = "stdout"
	util.SetupLogger(logCfg)
	msg := "[{\"bandwidth\":\"150\",\"blacklist\":\"************\",\"depth_get_mac\":\"0\",\"hosts\":\"**********/30\",\"nmap_scan\":\"0\",\"open_ping\":\"1\",\"ping_dectet_os\":\"0\",\"ports\":\"[{\"port\":\"22\",\"protocol\":\"ssh\",\"standard_port\":\"22\"},{\"port\":\"80\",\"protocol\":\"http\",\"standard_port\":\"80\"}]\",\"portscanperiod\":\"0\",\"unknownprotoindb\":\"0\"}]"
	go wkFE.handleOne([]byte(msg))
	//time.Sleep(3 * time.Second)
	//cmdStr, cmdErr := execShell("ps -ef | grep masscan | grep -v grep")
	//assert.Nil(t, cmdErr)
	//assert.Contains(t, cmdStr, "masscan --source-port")

	waitProcessExit()
}

//func TestFofaHandleOne(t *testing.T) {
//	var masscanCfg task.MasscanConf
//	var cfg util.ConsumerConfig
//	var sidekiqCfg sidekiq.SidekiqConfig
//	var consumer queue.SimpleConsumer
//	var gragProducer queue.SimpleProducer
//	var rdsCfg db.RedisConf
//	var rds *db.Redis
//	var logCfg util2.LogConf
//	var wkFofa *Worker
//
//	masscanCfg.BlackipList = ""
//	masscanCfg.Dir = "./"
//	masscanCfg.SourcePort = 58914
//
//	sidekiqCfg.Addr = "************:6379"
//	sidekiqCfg.Db = 10
//	sidekiqCfg.Foeye = false
//	cfg.Sidekiq = &sidekiqCfg
//	cfg.Redis = nil
//	cfg.Kafka = nil
//	consumer = util.NewConsumer(cfg)
//
//	rdsCfg.Addr = "************:6379"
//	rdsCfg.Db = 10
//	rds = db.NewRedis(rdsCfg)
//
//	rds.DelKeys("begin_clear_all_data")
//	rds.DelKeys("stop_scan_flag")
//
//	logCfg.Output = "stdout"
//	logCfg.ErrOutput = "stdout"
//	util2.SetupLogger(logCfg)
//
//	wkFofa = NewWorker(masscanCfg, consumer, gragProducer, rds)
//	assert.NotNil(t, wkFofa.Masscan)
//
//	msg := "[{\"bandwidth\":\"100\",\"hosts\":\"**********/29\",\"ports\":\"21,80,81,443\",\"start_status_work\":true,\"tid\":3151}]"
//	//msg := "[{\"bandwidth\":\"200\",\"hosts\":\"**********/24\",\"ports\":\"80,81,82,83,84,88,138,3000,5985,8080,50070,7547,4567,16010,4022,1234,4000,1991,3689,3128,1311,9981,32400,28017,25105,23424,13579,9944,9595,5357,10243,8880,9000,8889,8112,8098,8090,8089,8086,8081,8069,8060,8010,7779,5560,4848,4040,3780,3749,3541,2480,2376,2080,1471,1177,7474,2086,2082,2375,4369,1025,55553,51106,9191,9080,8888,8834,8334,7548,6664,5986,3542,8008,8161,16992,16993,7288,8001,8009,7001,8200,5678,880,9010,8377,8378,8800,443,8443,9443,8139,4443,2087,444,2083,7071,5986,4064,8080-8090\",\"start_status_work\":true,\"tid\":3156}]"
//	go wkFofa.handleOne([]byte(msg))
//	time.Sleep(3 * time.Second)
//	cmdStr, cmdErr := execShell("ps -ef | grep masscan | grep -v grep")
//	assert.Nil(t, cmdErr)
//	assert.Contains(t, cmdStr, "masscan --source-port")
//
//	waitProcessExit()
//}
//
//func TestSetEngine(t *testing.T) {
//	var masscanCfg task.MasscanConf
//	var cfg util.ConsumerConfig
//	var sidekiqCfg sidekiq.SidekiqConfig
//	var consumer queue.SimpleConsumer
//	var gragProducer queue.SimpleProducer
//	var rdsCfg db.RedisConf
//	var rds *db.Redis
//	var logCfg util2.LogConf
//	var retStr string
//
//	masscanCfg.BlackipList = ""
//	masscanCfg.Dir = "./"
//	masscanCfg.SourcePort = 58914
//
//	sidekiqCfg.Addr = "************:6379"
//	sidekiqCfg.Db = 10
//	sidekiqCfg.Foeye = true
//	cfg.Sidekiq = &sidekiqCfg
//	cfg.Redis = nil
//	cfg.Kafka = nil
//	consumer = util.NewConsumer(cfg)
//
//	rdsCfg.Addr = "************:6379"
//	rdsCfg.Db = 10
//	rds = db.NewRedis(rdsCfg)
//
//	wkFE := NewWorker(masscanCfg, consumer, gragProducer, rds)
//	assert.Nil(t, wkFE.Masscan)
//
//	wkFE.SetSidekiq("grabQueue", "pingQueue", "checkurlQueue")
//	assert.Equal(t, "grabQueue", wkFE.grabQueue)
//	assert.Equal(t, "pingQueue", wkFE.pingQueue)
//
//	logCfg.Output = "stdout"
//	logCfg.ErrOutput = "stdout"
//	util2.SetupLogger(logCfg)
//
//	//--------- set engine test
//	var feTask taskmgr.FoeyeScanTask
//	var setSt bool
//
//	setSt = setCurEngine(&feTask)
//	assert.Equal(t, false, setSt)
//	wkFE.wholeTaskInit(&feTask)
//
//	// set one engine
//	var fTaskOne taskmgr.FoeyeScanTask
//	taskmgr.setTaskEngine(&fTaskOne, ETMasscan)
//	wkFE.wholeTaskInit(&fTaskOne)
//	assert.Equal(t, 1, len(fTaskOne.Engines))
//	setSt = setCurEngine(&fTaskOne)
//	assert.Equal(t, true, setSt)
//	assert.Equal(t, "0", wkFE.redis.GetHashKey("scan_task", "state"))
//	assert.Equal(t, "0.00", wkFE.redis.GetHashKey("scan_task", "progress"))
//	assert.Equal(t, 1, int(fTaskOne.CurEngine.ProgressRate))
//	assert.Equal(t, 0.0, fTaskOne.CurEngine.MinProgress)
//	assert.Equal(t, 99.9, fTaskOne.CurEngine.MaxProgress)
//
//	// set two engine(masscan&ping)
//	var fTaskMP taskmgr.FoeyeScanTask
//	taskmgr.setTaskEngine(&fTaskMP, ETMasscan)
//	taskmgr.setTaskEngine(&fTaskMP, ETMasscanPing)
//	setSt = setCurEngine(&fTaskMP)
//	assert.Equal(t, true, setSt)
//	wkFE.wholeTaskInit(&fTaskMP)
//	assert.Equal(t, 2, len(fTaskMP.Engines))
//	setSt = setCurEngine(&fTaskMP)
//	assert.Equal(t, true, setSt)
//	assert.Equal(t, "0", wkFE.redis.GetHashKey("scan_task", "state"))
//	assert.Equal(t, "0.00", wkFE.redis.GetHashKey("scan_task", "progress"))
//	assert.Equal(t, 0.9, fTaskMP.Engines[0].ProgressRate)
//	assert.Equal(t, 0.0, fTaskMP.Engines[0].MinProgress)
//	assert.Equal(t, 89.9, fTaskMP.Engines[0].MaxProgress)
//	assert.Equal(t, 0.1, fTaskMP.Engines[1].ProgressRate)
//	assert.Equal(t, 90.0, fTaskMP.Engines[1].MinProgress)
//	assert.Equal(t, 99.9, fTaskMP.Engines[1].MaxProgress)
//
//	// set two engine(masscan&nmap)
//	var fTaskMN taskmgr.FoeyeScanTask
//	taskmgr.setTaskEngine(&fTaskMN, ETMasscan)
//	taskmgr.setTaskEngine(&fTaskMN, ETNmap)
//	setSt = setCurEngine(&fTaskMN)
//	assert.Equal(t, true, setSt)
//	wkFE.wholeTaskInit(&fTaskMN)
//	assert.Equal(t, 2, len(fTaskMN.Engines))
//	setSt = setCurEngine(&fTaskMN)
//	assert.Equal(t, true, setSt)
//	assert.Equal(t, "0", wkFE.redis.GetHashKey("scan_task", "state"))
//	assert.Equal(t, "0.00", wkFE.redis.GetHashKey("scan_task", "progress"))
//	assert.Equal(t, 0.5, fTaskMN.Engines[0].ProgressRate)
//	assert.Equal(t, 0.0, fTaskMN.Engines[0].MinProgress)
//	assert.Equal(t, 49.9, fTaskMN.Engines[0].MaxProgress)
//	assert.Equal(t, 0.5, fTaskMN.Engines[1].ProgressRate)
//	assert.Equal(t, 50.0, fTaskMN.Engines[1].MinProgress)
//	assert.Equal(t, 99.9, fTaskMN.Engines[1].MaxProgress)
//
//	// set three engine(masscan&nmap&ping)
//	var fTaskThree taskmgr.FoeyeScanTask
//	taskmgr.setTaskEngine(&fTaskThree, ETMasscan)
//	taskmgr.setTaskEngine(&fTaskThree, ETNmap)
//	taskmgr.setTaskEngine(&fTaskThree, ETMasscanPing)
//	setSt = setCurEngine(&fTaskThree)
//	assert.Equal(t, true, setSt)
//	wkFE.wholeTaskInit(&fTaskThree)
//	assert.Equal(t, 3, len(fTaskThree.Engines))
//	setSt = setCurEngine(&fTaskThree)
//	assert.Equal(t, true, setSt)
//	assert.Equal(t, "0", wkFE.redis.GetHashKey("scan_task", "state"))
//	assert.Equal(t, "0.00", wkFE.redis.GetHashKey("scan_task", "progress"))
//	assert.Equal(t, 0.45, fTaskThree.Engines[0].ProgressRate)
//	assert.Equal(t, 0.0, fTaskThree.Engines[0].MinProgress)
//	assert.Equal(t, 44.9, fTaskThree.Engines[0].MaxProgress)
//	assert.Equal(t, 0.45, fTaskThree.Engines[1].ProgressRate)
//	assert.Equal(t, 45.0, fTaskThree.Engines[1].MinProgress)
//	assert.Equal(t, 89.9, fTaskThree.Engines[1].MaxProgress)
//	assert.Equal(t, 0.1, fTaskThree.Engines[2].ProgressRate)
//	assert.Equal(t, 90.0, fTaskThree.Engines[2].MinProgress)
//	assert.Equal(t, 99.9, fTaskThree.Engines[2].MaxProgress)
//
//	var tmpEng taskmgr.TaskEngine
//	tmpEng.Engine = ETNmap
//	tmpEng.ProgressRate = 0.45
//	tmpEng.MinProgress = 45.0
//	tmpEng.MaxProgress = 89.9
//	taskProgress(rds, tmpEng, true, 1, "2", "38.121", "4:35")
//	retStr = rds.GetHashKey("scan_task", "progress")
//	assert.Equal(t, "62.15%", retStr)
//
//	_, hErr := getHost()
//	assert.Nil(t, hErr)
//
//	wkFE.wholeTaskFinish(fTaskThree, true)
//}
