package worker

import (
	"bytes"
	_ "crawler/internal/golang-ico-bmh"
	"crawler/internal/http"
	"crawler/internal/util"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"git.gobies.org/longzhuan/go_common/tools/pack"
	"github.com/corona10/goimagehash"
	"github.com/pkg/errors"
	"github.com/spaolacci/murmur3"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"log"
	"math/bits"
	"net/url"
	"strconv"
	"strings"
)

/*
抓取favicon.ico，并将其转换为phash

流程：
1. 先获取网页里面的地址，合成为完整url；如果没有则使用默认的。
2. 请求地址，判断是否成功。
3. 将文件保存为本地文件，然后转换phash
*/
func ProcGetIcon(surl string, info map[string]interface{}, disableDefaultReq bool) {
	if info["utf8html"] == nil {
		log.Println("[ERROR] proc-get-url failed empty body  url:", surl)
		return
	}

	uu, err := url.Parse(surl)
	// url解析失败后不继续处理
	if err != nil {
		log.Println("[ERROR] proc get icon parse url failed:", surl, "errdesc:", err)
		return
	}

	retryIconUrl := uu.Scheme + "://" + uu.Host + "/favicon.ico"
	iconUrl, state := http.GetIconUrl(info["utf8html"].(string), uu)
	//fmt.Println("  get icon url:", iconUrl)
	if !state {
		procSt, tmpUrl := http.GetRelativeFullUrl(uu, "favicon.ico")
		if procSt {
			iconUrl = tmpUrl
		} else {
			log.Println("  merge icon url failed", surl)
			iconUrl = surl + "/favicon.ico"
		}
	}

	if strings.Contains(iconUrl, ";base64") {
		procBase64Icon(surl, iconUrl, info)
	} else {
		procRequestIcon(surl, iconUrl, retryIconUrl, info, disableDefaultReq)
	}
}

func procBase64Icon(surl, ctx string, info map[string]interface{}) {
	simage, _ := json.Marshal(ctx)
	coI := strings.Index(string(simage), ",")
	rawImage := string(simage)[coI+1:]

	// Encoded Image DataUrl
	unbased, _ := base64.StdEncoding.DecodeString(string(rawImage))
	iconLen := len(unbased)
	if iconLen > 1048576 {
		log.Println(" ignore ctx very big icon generate  url:", surl, "size:", (iconLen / 1024), "K")
		return
	}

	var img image.Image
	var err error
	img, _, err = image.Decode(bytes.NewReader(unbased))
	if err != nil {
		log.Println("[ERROR] image decode failed:", surl, "errdesc:", err)
		return
	}

	phash, err := goimagehash.PerceptionHash(img)
	if err != nil {
		log.Println("[ERROR] image get phash failed:", surl, "errdesc:", err)
		return
	}
	sHash, err := getMurmur3Hash(rawImage, surl)
	if err != nil {
		log.Println("[ERROR] image get shash failed:", surl, "errdesc:", err)
		return
	}

	phashVal := getPhashVal(phash)
	log.Printf("favicon url:%s base64_len:%d\n", surl, len(rawImage))

	hashInt, err := strconv.ParseInt(sHash, 10, 64)

	if err != nil {
		log.Println("change favicon failed:", err)
	}

	phashInt, err := strconv.ParseInt(phashVal, 10, 64)
	if err != nil {
		log.Println("change favicon failed:", err)
	}
	info["favicon"] = map[string]interface{}{
		"url":    "",
		"phash":  phashInt,
		"hash":   hashInt,
		"base64": rawImage,
	}
	if phash.GetHash() != 0 && bits.OnesCount64(phash.GetHash()) > 1 {
		ubit := getPhashBit(phash.GetHash())
		p := divPhashBit(ubit)
		hashInt, err := strconv.ParseInt(sHash, 10, 64)

		if err != nil {
			log.Println("change favicon failed:", err)
		}

		phashInt, err := strconv.ParseInt(phashVal, 10, 64)
		if err != nil {
			log.Println("change favicon failed:", err)
		}

		info["favicon"] = map[string]interface{}{
			"url":       "",
			"phash":     phashInt,
			"hash":      hashInt,
			"phash_bit": ubit,
			"p":         p,
			"base64":    rawImage,
		}
	}
}

func procRequestIcon(origUrl, iconUrl, retryUrl string, info map[string]interface{}, disableDefaultReq bool) {
	useRetry := false
	followLocations := 2
	filename, _ := http.GetUrlFilename(iconUrl)
	if filename == ".css" {
		log.Println("[ERROR] icon is css:", iconUrl)
		return
	}

	resp, _, err := http.RequestWithLocation(iconUrl, followLocations, http.RTIcon)
	if err != nil {
		log.Println("[ERROR] failed to request icon:", err)
		return
	}
	if resp == nil {
		log.Println("[ERROR] request icon nil resp", iconUrl)
		return
	}

	// 如果第一url请求为404的时候，用首页图片地址重试一下
	if resp.Response().StatusCode == 404 && iconUrl != retryUrl {
		resp, _, err = http.RequestWithLocation(retryUrl, followLocations, http.RTIcon)
		if err != nil {
			log.Println("[ERROR] failed to request default icon:", err)
			return
		}
		useRetry = true
	}
	if resp.Response().StatusCode != 200 {
		//log.Println("[ERROR] ignore icon code:", resp.Response().StatusCode, "url:", iconUrl)
		return
	}
	// 类型不对的丢弃
	ctxType := resp.Response().Header.Get("Content-Type")
	ctxType = strings.ToLower(ctxType)
	if !strings.Contains(ctxType, "image") {
		ignore := false
		remain := false
		if strings.Contains(ctxType, "text/html") || strings.Contains(ctxType, "/json") ||
			strings.Contains(ctxType, "/xml") || strings.Contains(ctxType, "/javascript") ||
			strings.Contains(ctxType, "text/text") || strings.Contains(ctxType, "/x-javascript") ||
			strings.Contains(ctxType, "text/css") || strings.Contains(ctxType, "/x-httpd") ||
			strings.Contains(ctxType, "unknown") || strings.Contains(ctxType, "plain/text") {
			ignore = true
		}

		if ignore {
			//log.Println("[ERROR] invalid icon content_type:", ctxType, "url:", iconUrl)
			return
		}

		if strings.Contains(ctxType, "application/ico") || strings.Contains(ctxType, "application/ocstream") ||
			strings.Contains(ctxType, "/octet-stream") || strings.Contains(ctxType, "/x-icon") ||
			strings.Contains(ctxType, "/binary") || strings.Contains(ctxType, "text/plain") ||
			strings.Contains(ctxType, "(null)/ico") {
			remain = true
		}

		if !remain {
			log.Println("[ERROR] invalid icon content_type:", ctxType, "url:", iconUrl)
		}
		//return
	}

	rawBody, err := http.GetDecodeHtml(iconUrl, resp, false)
	if err != nil {
		log.Println("[ERROR] icon to bytes failed:", iconUrl, "errdesc:", err)
	}

	// 没有内容的丢弃
	bodyLen := len(rawBody)
	if bodyLen < 10 {
		log.Println("[ERROR] icon body too short:", iconUrl)
		return
	}

	if bodyLen > 1048576 {
		log.Println(" ignore ctx very big icon generate  url:", origUrl, "size:", (bodyLen / 1024), "K")
		return
	}

	resultUrl := iconUrl
	if useRetry {
		resultUrl = retryUrl
	}
	saveBuildPHash(resultUrl, []byte(rawBody), info)
}

func getPhashVal(phash *goimagehash.ImageHash) string {
	return strconv.FormatInt(int64(phash.GetHash()), 10)
}

func getPhashBit(phash uint64) string {
	return strconv.FormatUint(phash, 2)
}

func divPhashBit(ubit string) (p map[string]interface{}) {
	p = make(map[string]interface{}, 64)
	for i := 0; i < 64; i++ {
		if ubit[i] == 48 {
			p[strconv.Itoa(i)] = fmt.Sprintf("%1d", 0)
		} else if ubit[i] == 49 {
			p[strconv.Itoa(i)] = fmt.Sprintf("%1d", 1)
		}
	}
	return
}

func saveBuildPHash(surl string, html []byte, info map[string]interface{}) {
	var img image.Image
	var err error
	img, _, err = image.Decode(bytes.NewReader(html))
	if err != nil {
		log.Println("[ERROR] image decode failed:", surl, "errdesc:", err)
		return
	}

	phash, err := goimagehash.PerceptionHash(img)
	if err != nil {
		log.Println("[ERROR] image get phash failed:", surl, "errdesc:", err)
		return
	}

	// 转换为base64，为下一步运算hash做准备
	base64Str := base64.StdEncoding.EncodeToString(html)
	sHash, err := getMurmur3Hash(base64Str, surl)
	if err != nil {
		log.Println("[ERROR] image get shash failed:", surl, "errdesc:", err)
		return
	}

	log.Printf("favicon url:%s  base64_len:%d\n", surl, len(base64Str))
	phashVal := getPhashVal(phash)

	hashInt, err := strconv.ParseInt(sHash, 10, 64)

	if err != nil {
		log.Println("change favicon failed:", err)
	}

	phashInt, err := strconv.ParseInt(phashVal, 10, 64)
	if err != nil {
		log.Println("change favicon failed:", err)
	}

	info["favicon"] = map[string]interface{}{
		"url":    surl,
		"phash":  phashInt,
		"hash":   hashInt,
		"base64": base64Str,
	}

	if phash.GetHash() != 0 && bits.OnesCount64(phash.GetHash()) > 1 {
		ubit := getPhashBit(phash.GetHash())
		p := divPhashBit(ubit)
		hashInt, err := strconv.ParseInt(sHash, 10, 64)

		if err != nil {
			log.Println("change favicon failed:", err)
		}

		phashInt, err := strconv.ParseInt(phashVal, 10, 64)
		if err != nil {
			log.Println("change favicon failed:", err)
		}

		info["favicon"] = map[string]interface{}{
			"url":       surl,
			"phash":     phashInt,
			"hash":      hashInt,
			"phash_bit": ubit,
			"p":         p,
			"base64":    base64Str,
		}
	}
	return
}

func getMurmur3Hash(base64Ctx, url string) (string, error) {

	iconBase64Str := util.ProcBase64(base64Ctx)

	// 运算hash
	var iconBase64Bytes []byte = []byte(iconBase64Str)
	murmur3Number := murmur3.Sum32(iconBase64Bytes)
	p := new(pack.Protocol)
	p.Format = []string{"N4"}
	unPackNumbers := p.UnPack(p.Pack(int64(murmur3Number)))
	if len(unPackNumbers) == 0 {
		err := errors.New("Parse Icon unPackNumbers error:" + strconv.Itoa(len(unPackNumbers)))
		return "", err
	}
	hash := strconv.FormatInt(unPackNumbers[0], 10)
	return hash, nil
}
