package worker

import (
	"crawler/internal/http"
	"crawler/internal/parse"
	"crawler/internal/req"
	"crawler/internal/util"
	"encoding/json"
	"github.com/pkg/errors"
	"log"
	"strings"
	"time"
)

var DefaultTimeout = 20 * time.Second

func ProcHttpsReq(url string, resp *req.Resp, task *parse.CrawlerTask) (*req.Resp, error) {

	if strings.Contains(url, "https://") {
		return nil, nil
	}

	origUrl := url
	html, err := http.GetDecodeHtml(url, resp, true)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if http.NeedProcHttps(html) {
		task.Url = http.GetProcHttpsUrl(url)
		httpsResp, _, err := http.RequestWithLocation(task.Url, 1, http.RTHost)
		if err != nil {
			task.Url = origUrl // 出错还原url
			return nil, err
		} else {
			task.Host = util.Url2Hostinfo(task.Url) // host也要替换为https

			resp = httpsResp // 没报错才用新请求的内容
		}
	}

	return resp, nil
}

type Result struct {
	//1:dns程序 2:爬虫
	From           int             `json:"from"`
	SubdomainsInfo []subdomainInfo `json:"subdomains_info"`
}

type subdomainInfo struct {
	Host   string   `json:"host"`
	Ip     []string `json:"ip"`
	Cnames []string `json:"cnames"`
}

func sendFailMsg(url string, ip string) {
	result := Result{
		From: 2,
	}
	subdomainsInfo := make([]subdomainInfo, 0)
	subdomainsInfo = append(subdomainsInfo, subdomainInfo{
		Host: url,
		Ip:   []string{ip},
	})
	result.SubdomainsInfo = subdomainsInfo
	data, err := json.Marshal(result)
	if err != nil {
		log.Println("[ERROR] worker send-fail-msg json encode failed:", err)
		return
	}
	log.Println("send fail url msg：", string(data))
}
