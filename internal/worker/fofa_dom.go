package worker

import (
	"fmt"
	"git.gobies.org/longzhuan/go_common/tools/algo"
	simhash "git.gobies.org/longzhuan/go_common/tools/sim_hash"
	"log"
	"regexp"
	"strconv"
)

/*
获取body中dom树，生成simHash

流程：
1. 先获取网页里面的所有标签。
2. 拼接成字符串。
3. 字符串转换为simHash，bit
*/

var hrefRegexp = regexp.MustCompile("<[?!]?(/?[a-zA-Z]+) ?.*?>")

func ProcGetDom(surl string, body string, info map[string]interface{}) {
	//获取标签字符串、数量
	if len(body) <= 0 {
		return
	}

	log.Println("get body dom info in  host:", surl)

	info["domv2"] = map[string]interface{}{
		"fhash": algo.Murmur3Hash(body),
	}
	domStr, tagCount := listHref(body)
	if tagCount < 1 {
		return
	}

	info["fhash"] = algo.Murmur3Hash(body)
	saveBuildSimHash(surl, domStr, tagCount, info)
}

func saveBuildSimHash(surl string, domStr string, tagCount int, info map[string]interface{}) {
	simHash := simhash.CalculateSimHash(domStr)
	ubit := getPhashBit(uint64(simHash))
	p := divShashBit(ubit)
	nhash := algo.Murmur3Hash(domStr)
	ehash := algo.SignString(nhash)
	foid := algo.EncryptStringToBase64(nhash, "fofafofa")

	info["fid"] = ehash // 打标签的字段
	info["dom"] = map[string]interface{}{
		"sim_hash":  strconv.Itoa(simHash),
		"shash_bit": ubit,
		"p":         p,
		"foid":      foid,
		"fhash":     info["fhash"], // fofa hash
		"nhash":     nhash,
		"ehash":     ehash,
		"tag_count": tagCount,
		"tag_len":   len(domStr),
	}
}

func divShashBit(ubit string) (p map[string]interface{}) {
	p = make(map[string]interface{}, 64)
	for i := 0; i < len(ubit); i++ {
		if ubit[i] == 48 {
			p[strconv.Itoa(i)] = fmt.Sprintf("%1d", 0)
		} else if ubit[i] == 49 {
			p[strconv.Itoa(i)] = fmt.Sprintf("%1d", 1)
		}
	}
	return
}

func listHref(html string) (string, int) {
	s := ""

	match := hrefRegexp.FindAllStringSubmatch(html, -1)
	if match != nil {
		for _, v := range match {
			s = s + v[1] + ">"
		}
	}
	return s, len(match)
}
