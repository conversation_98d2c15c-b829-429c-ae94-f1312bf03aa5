package worker

import (
	"checkurl/internal/url"
	"encoding/csv"
	"io"
	"log"
	"os"
	"strings"
)

func (fw *FofaWorker) InitAlexa() {
	if fw.config.Worker.AlexaDomainPath == "" {
		return
	}
	filename := fw.config.Worker.AlexaDomainPath

	f, err := os.Open(filename)
	if err != nil {
		log.Fatal(err)
	}

	r := csv.NewReader(f)
	loop := 0
	for {
		record, err := r.Read()
		if err == io.EOF {
			break
		}
		if len(record) >= 2 {
			hostinfo, err := url.GetHostInfo(strings.TrimSpace(record[1]))
			if err != nil {
				log.Printf("url gethostinfo host:%s,error:%s", record[1], err.Error())
				continue
			}
			fw.mpAlexa[hostinfo.Domain] = true
		}

		loop += 1
		if loop >= fw.config.Worker.AlexaNum {
			break
		}
	}

	log.Println("loaded alexa:", len(fw.mpAlexa))
}
