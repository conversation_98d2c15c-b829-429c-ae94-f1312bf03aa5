package worker

import (
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/util"
	"encoding/json"
	"go.uber.org/zap"
	"log"
	"strings"
)

type FofaWorker struct {
	*BaseWorker
}

func NewFofaWorker(bw *BaseWorker) *FofaWorker {
	if bw.producer == nil {
		log.Println("[FATAL] new fofa worker failed:producer nil")
	}

	fw := &FofaWorker{
		BaseWorker: bw,
	}
	fw.setProducerCallback(fw.checkServiceProducer)
	fw.setOnStopTaskCallback(fw.procStopTask)
	fw.setProgramStopProcCallback(fw.ProgramStopProc)

	return fw
}

func (fw *FofaWorker) checkServiceProducer(hostports [][4]string, options map[string]interface{}) error {
	if fw.producer == nil {
		log.Println("[ERROR] check_service_producer invalid")
		return nil
	}

	// 打印扫描日志
	if util.Logger != nil {
		// 每个元素内容是：ip/port/taskid/base_protocol
		for _, v := range hostports {
			util.Logger.Info("succ",
				zap.String("proc_serv", fw.hostname),
				zap.String("ip", v[0]),
				zap.String("port", v[1]),
				zap.String("tid", v[2]),
				zap.String("transport", v[3]))
		}
	}

	if hostports[0][3] == "icmp" && hostports[0][1] == "0" {
		// ping results
		newHostports := make([][]string, 0)
		for _, value := range hostports {
			newHostports = append(newHostports, []string{value[0], value[2]})
		}
		args := interface{}(newHostports)
		data, err := json.Marshal(args)
		if err != nil {
			return err
		}

		log.Println("  publish ping_assets info:", fw.sysConf.Producer.Kafka.PingTopic, string(data))
		fw.producer.Publish(fw.sysConf.Producer.Kafka.PingTopic, data)
	} else {
		data, err := json.Marshal(hostports)
		if err != nil {
			return err
		}

		log.Println("grab task:", string(data))
		fw.producer.Publish(fw.sysConf.Producer.Kafka.Topic, data)
	}

	return nil
}

func (fw *FofaWorker) procStopTask(classObj interface{}) {
	stopScanSt, err := fw.dbAct.GetValue(fw.dbAct.GetKeyName("stop_task"))
	if err != nil {
		log.Println("  worker proc_stop_task get info failed:", err)
	}
	if !strings.Contains(stopScanSt, ":1") {
		return
	}
	log.Println("  worker task stop-state:", stopScanSt)

	// fofa需要判断是否为本机任务tid才能停止
	tmpAttr := fw.taskObjMgr.GetAttr()
	if tmpAttr == nil {
		log.Println("  worker proc_stop_task get attr is nil")
		return
	}

	if taskAttr, ok := tmpAttr.(parse.BaseTaskMsg); !ok {
		log.Println("  worker proc_stop_task change attr failed", taskAttr)
		return
	} else {
		arrStopTid := strings.Split(stopScanSt, ":")
		if len(arrStopTid) <= 0 {
			log.Println("  worker proc_stop_task parse msg failed", stopScanSt, taskAttr)
			return
		}

		if arrStopTid[0] != taskAttr.TaskId {
			log.Println("  worker proc_stop_task not same tid", stopScanSt, taskAttr)
			return
		}
	}

	err = fw.taskObjMgr.Stop()
	if err != nil {
		log.Println("  [ERROR] worker task stop failed", err)
	}
}

func (fw *FofaWorker) ProgramStopProc() {
	//fw.consumer.Done()

	log.Println("catch program stop")
	err := fw.taskObjMgr.Stop()
	if err != nil {
		log.Println("[ERROR] masscan stop failed", err)
	}
}
