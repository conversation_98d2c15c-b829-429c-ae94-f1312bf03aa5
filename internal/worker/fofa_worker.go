package worker

import (
	"checkurl/internal/ip"
	"checkurl/internal/parse"
	"checkurl/internal/url"
	"encoding/json"
	"log"
	"sync/atomic"
)

type FofaWorker struct {
	*BaseWorker
	mpAlexa map[string]bool
}

func NewFofaWorker(bw *BaseWorker) *FofaWorker {
	if bw.crawlerProducer == nil {
		log.Fatal("worker crawler producer is nil")
	}

	fw := &FofaWorker{
		BaseWorker: bw,
		mpAlexa:    make(map[string]bool),
	}

	fw.SetProcOneCallback(fw.ProcOne)
	fw.InitAlexa()

	return fw
}

func (fw *FofaWorker) ProcOne(msg []byte) {
	fofaObj, err := fw.msgParse.ParseMsg(msg)
	if err != nil {
		log.Println("[ERROR] parse msg failed", err)
		return
	}
	fofaMsg := fofaObj.(parse.FofaMessage)

	// 根域名黑名单增加缓存
	mpDomainBlacklist := make(map[string]bool)

	// check url
	for _, u := range fofaMsg.Url {
		atomic.AddUint64(&totalTask, 1)
		fw.checkUrl(u, fofaMsg.Force, fofaMsg.Jarm, mpDomainBlacklist)
	}

	mpDomainBlacklist = nil // 释放内存
}

func (fw *FofaWorker) checkUrl(u string, force bool, jarm string, mpDomainBlacklist map[string]bool) {
	log.Println("check url:", u)
	hostinfo, err := url.GetHostInfo(u)
	if err != nil {
		atomic.AddUint64(&badUrl, 1)
		log.Println("[WARN] get host info failed", u, err)
		return
	}

	if !force && !fw.disableHostChk && fw.redis.HostChecked(hostinfo.Host) {
		atomic.AddUint64(&checkedHost, 1)
		log.Println("stop checked host", hostinfo.Host)
		return
	}

	onlyhost := hostinfo.OnlyHost
	if ip.Valid(onlyhost) { // 是IP
		if !ip.Decimal(onlyhost) { // 但不是普通十进制形式视为非法(防止垃圾IP,单个IP用无限种字符串形式表示)
			atomic.AddUint64(&badIp, 1)
			log.Println("stop illegal IP:", onlyhost)
			return
		}
	} else { // 是域名
		if !force {
			isDomainBlack := false
			// 先从内存里面判断
			if tmpBlack, ok := mpDomainBlacklist[hostinfo.Domain]; ok {
				//fmt.Println("  chk domain by map", hostinfo, tmpBlack)
				if tmpBlack {
					isDomainBlack = true
				}
			} else {
				// 如果内存里面没有，从Redis里面判断根域名黑名单
				if fw.redis.IsBlackDomain(hostinfo.Domain) && !fw.mpAlexa[hostinfo.Domain] {
					isDomainBlack = true
				}

				// 保存信息
				if _, ok := mpDomainBlacklist[hostinfo.Domain]; !ok {
					//fmt.Println("  set map domain info", hostinfo, isDomainBlack)
					mpDomainBlacklist[hostinfo.Domain] = isDomainBlack
				}
			}

			if isDomainBlack {
				atomic.AddUint64(&blackDomain, 1)
				log.Println("stop black domain:", hostinfo.Domain, "host:", hostinfo.Host)
				return
			}
		}
	}

	if !force && fw.redis.IsBlackHost(hostinfo.Host) { // host黑名单
		atomic.AddUint64(&blackHost, 1)
		log.Println("stop black host:", hostinfo.Host)
		return
	}

	fw.redis.AddHost(hostinfo.Host, fw.chkKeyExpireTime)

	if !fw.config.Elasticsearch.DisableRecordsInfo {
		// 更新时间检查
		m, err := fw.esStub.Get(hostinfo.Host)
		if err == nil && !fw.esStub.NeedUpdate(m) {
			atomic.AddUint64(&tooShort, 1)
			log.Println("stop update time is less than limit:", hostinfo.Host)
			return
		}

		if err == nil {
			// 检查全部通过，可以更新
			fw.esStub.UpdateCheckTime(hostinfo.Host, m)
		}
	}

	atomic.AddUint64(&successTask, 1)

	fw.sendCrawlerTask(hostinfo, jarm)
}

func (fw *FofaWorker) sendCrawlerTask(hostinfo *url.HostInfo, jarm string) {
	// 提交任务到kafka, 让crawler处理
	task := parse.CrawlerTask{
		Url:         hostinfo.Url,
		Host:        hostinfo.Host,
		Domain:      hostinfo.Domain,
		Subdomain:   hostinfo.Subdomain,
		AddLinkHost: fw.addLinkHost,
		Retry:       3,
		Jarm:        jarm,
	}
	data, err := json.Marshal(&task)
	if err != nil {
		log.Println("[ERROR] json marshal failed:", err)
		return
	}

	log.Println("publish to crawler", string(data))
	fw.crawlerProducer.Publish(fw.config.Producer.Crawler.Kafka.Topic, data)
}
