package worker

import (
	"encoding/json"
	"errors"
	"log"
	"net"
	"strings"

	"github.com/ipipdotnet/datx-go"
	"github.com/ipipdotnet/ipdb-go"
	"github.com/ipplus360/awdb-golang/awdb-golang"
	"github.com/oschwald/geoip2-golang"
)

var (
	cityDb         *geoip2.Reader
	asnDb          *geoip2.Reader
	ipipCityDb     *ipdb.City
	ipipCityDbDatx *datx.City
	awIpDb         *awdb.Reader
)

// LoadGeoDb 加载数据库
func LoadGeoDb(geoipFilename, asnFilename, ipipFilename, awipFilename string) (err error) {
	cityDb, err = geoip2.Open(geoipFilename)
	if err != nil {
		log.Fatal("FATAL open geoip-city:", err)
	}

	asnDb, err = geoip2.Open(asnFilename)
	if err != nil {
		log.Fatal("FATAL open geoip-asn:", err)
	}

	if len(ipipFilename) > 0 {
		if strings.Contains(ipipFilename, "ipdb") {
			ipipCityDb, err = ipdb.NewCity(ipipFilename)
			if err != nil {
				log.Fatal("FATAL open ip2.ipdb:", err)
			} else {
				log.Println(ipipCityDb.IsIPv4())    // check database support ip type
				log.Println(ipipCityDb.IsIPv6())    // check database support ip type
				log.Println(ipipCityDb.BuildTime()) // database build time
				log.Println(ipipCityDb.Languages()) // database support language
				log.Println(ipipCityDb.Fields())    // database support fields
			}
		} else {
			// datx
			ipipCityDbDatx, err = datx.NewCity(ipipFilename)
			if err != nil {
				log.Fatal("FATAL open ip2.datx:", err)
			}
		}
	} else {
		log.Println("no geoip2 filename")
	}

	if len(awipFilename) > 0 {
		awIpDb, err = awdb.Open(awipFilename)
		if err != nil {
			log.Fatal("FATAL open ip3:", err)
		}

		log.Println("ip3 metadata:", awIpDb.Metadata)
	} else {
		log.Println("no geoip3 filename")
	}

	return
}

func setGeoip(m map[string]interface{}, ip string, record *geoip2.City) {
	if record == nil {
		log.Println("[ERROR] geoip record is nil", ip)
		return
	}

	var regionName, realRegionName string
	if len(record.Subdivisions) > 0 {
		region := record.Subdivisions[0]
		regionName = region.IsoCode
		realRegionName = region.Names["en"]
	}
	var dmaCode interface{}
	if record.Location.MetroCode != 0 {
		dmaCode = record.Location.MetroCode
	}
	m["geoip"] = map[string]interface{}{
		"location": map[string]interface{}{
			"lat": record.Location.Latitude,
			"lon": record.Location.Longitude,
		},
		//"request":          ip,
		//"ip":               ip,
		"country_name":     record.Country.Names["en"],
		"continent_code":   record.Continent.Code,
		"region_name":      regionName,
		"real_region_name": realRegionName,
		"city_name":        record.City.Names["en"],
		"postal_code":      record.Postal.Code,
		"latitude":         record.Location.Latitude,
		"longitude":        record.Location.Longitude,
		"timezone":         record.Location.TimeZone,
		"country_code2":    record.Country.IsoCode,
		"country_code3":    record.RegisteredCountry.IsoCode,
		"dma_code":         dmaCode, // geoip1中的dma_code在geoip2中是location的metro code
		// TODO geoip2不支持area_code
	}
}

func setAsn(m map[string]interface{}, ip string, asn *geoip2.ASN) {
	if asn == nil {
		return
	}
	if asn.AutonomousSystemNumber <= 0 {
		return
	}

	m["asn"] = map[string]interface{}{
		"as_number":       asn.AutonomousSystemNumber,
		"as_organization": asn.AutonomousSystemOrganization,
	}
}

func setISP(m map[string]interface{}, ip string, ipips *ipdb.CityInfo) {
	if ipips == nil {
		return
	}

	if ipips.CountryName == "局域网" || ipips.RegionName == "局域网" {
		return
	}

	ip2Map := new(GeoipSecondMap)
	ip2Map.CopyIpdbValue(ipips)

	var chgMap map[string]interface{}
	changeJson, err := json.Marshal(ip2Map)
	if err != nil {
		log.Println("[ERROR] set isp datax failed", ip, err)
		return
	}
	err = json.Unmarshal(changeJson, &chgMap)
	if err != nil {
		log.Println("[ERROR] set isp datax failed", ip, err)
		return
	}
	m["geoip2"] = chgMap
}

func setISPDatx(m map[string]interface{}, ip string, ipips datx.Location) {
	ip2Map := new(GeoipSecondMap)
	ip2Map.CopyDatxValue(ipips)

	var chgMap map[string]interface{}
	changeJson, err := json.Marshal(ip2Map)
	if err != nil {
		log.Println("[ERROR] set isp datax failed", ip, err)
		return
	}
	err = json.Unmarshal(changeJson, &chgMap)
	if err != nil {
		log.Println("[ERROR] set isp datax failed", ip, err)
		return
	}
	m["geoip2"] = chgMap
}

func setIPCNet(m map[string]interface{}, ipaddr net.IP) {
	if ipaddr.To4() != nil {
		// ipv4
		ipnet := ipaddr.Mask(net.IPv4Mask(255, 255, 255, 0))
		m["ipcnet"] = ipnet.String()
	} else {
		// ipv6
	}

}

// SetAdditionInfo 带上附加的信息，如geo以及asn信息.
func SetAdditionInfo(m map[string]interface{}, ip string) {
	// IP如果解析失败，则忽略本次处理.
	ipaddr := net.ParseIP(ip)
	if ipaddr == nil {
		log.Println("[ERROR] set addition info ip parse failed", ip)
		return
	}

	// c网段地址，方便后续统计
	setIPCNet(m, ipaddr)

	// geo
	record, err := cityDb.City(ipaddr)
	if err == nil {
		setGeoip(m, ip, record)
	} else {
		log.Println("query geoip failed of:", ip, err)
	}

	// asn
	asn, err := asnDb.ASN(ipaddr)
	if err == nil {
		setAsn(m, ip, asn)
	} else {
		log.Println("query geoip failed of:", ip, err)
	}

	// ipip
	if ipipCityDb != nil {
		isp, err := ipipCityDb.FindInfo(ip, "CN") //todo：目前还没有下载到en的库
		if err == nil {
			setISP(m, ip, isp)
		} else {
			log.Println("query isp failed of:", ip, err)
		}
	} else if ipipCityDbDatx != nil {
		isp, err := ipipCityDbDatx.FindLocation(ip)
		if err == nil {
			setISPDatx(m, ip, isp)
		} else {
			log.Println("query isp failed of:", ip, err)
		}
	}

	// awdb
	if awIpDb != nil && !strings.Contains(ip, ":") {
		err = SetAwdbInfo(m, ip)
		if err != nil {
			log.Println("[ERROR] query ip3 failed of:", ip, err)
		}
	}
}

func SetAwdbInfo(m map[string]interface{}, ip string) error {
	var record interface{}
	err := awIpDb.Lookup(net.ParseIP(ip), &record)
	if err != nil {
		return err
	}
	result, err := GetAwGeoInfo(ip)
	if err != nil {
		return err
	}
	//fmt.Println(result)

	if m == nil {
		return errors.New("SetIP3 info map is nil")
	}

	ip3Map := new(GeoipThreeMap)
	ip3Map.CopyValue(result)

	var chgMap map[string]interface{}
	changeJson, err := json.Marshal(ip3Map)
	if err != nil {
		return err
	}
	err = json.Unmarshal(changeJson, &chgMap)
	if err != nil {
		return err
	}

	m["geoip3"] = chgMap

	return nil
}
