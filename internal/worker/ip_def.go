package worker

import (
	"github.com/ipipdotnet/datx-go"
	"github.com/ipipdotnet/ipdb-go"
	"log"
	"strconv"
)

type GeoipSecondMap struct {
	Location      *AwLocat `json:"location,omitempty"`
	CountryName   string   `json:"country,omitempty"`
	Province      string   `json:"province,omitempty"`
	City          string   `json:"city,omitempty"`
	CityCode      string   `json:"city_code,omitempty"`
	Organization  string   `json:"organization,omitempty"`
	Isp           string   `json:"isp,omitempty"`
	CountryCode2  string   `json:"country_code2,omitempty"`
	CountryCode3  string   `json:"country_code3,omitempty"`
	Idc           string   `json:"idc,omitempty"`
	ContinentCode string   `json:"continent_code,omitempty"`
	Timezone      string   `json:"timezone,omitempty"`
	Timezone2     string   `json:"timezone2,omitempty"`
	PhonePrefix   string   `json:"phone_prefix,omitempty"`
	BaseStation   string   `json:"base_station,omitempty"`
	CurrencyCode  string   `json:"currency_code,omitempty"`
	CurrencyName  string   `json:"currency_name,omitempty"`
}

type GeoipThreeMap struct {
	Location       *AwLocat `json:"location,omitempty"`
	CountryName    string   `json:"country_name,omitempty"`
	ContinentCode  string   `json:"continent_code,omitempty"`
	RegionName     string   `json:"region_name,omitempty"`
	RealRegionName string   `json:"real_region_name,omitempty"`
	City           string   `json:"city_name,omitempty"`
	District       string   `json:"district,omitempty"`
	PostCode       string   `json:"postal_code,omitempty"`
	Latitude       float64  `json:"latitude,omitempty"`
	Longitude      float64  `json:"longitude,omitempty"`
	TimezoneUTC    string   `json:"timezone_utc,omitempty"`
	CountryCode2   string   `json:"country_code2,omitempty"`
	CountryCode3   string   `json:"country_code3,omitempty"`
	DmaCode        string   `json:"dma_code,omitempty"`
	Scene          string   `json:"scene,omitempty"`
	User           string   `json:"user,omitempty"`
	UserType       string   `json:"user_type,omitempty"`
	Isp            string   `json:"isp,omitempty"`
	Asnumber       int      `json:"asnumber,omitempty"`
}

func (g3m *GeoipThreeMap) CopyValue(awi *AwGeoInfo) {
	if awi == nil {
		log.Println("[ERROR] copy value failed: nil input")
		return
	}

	g3m.Location = awi.Location
	g3m.CountryName = awi.CountryName
	g3m.ContinentCode = awi.ContinentCode
	g3m.RegionName = awi.RegionName
	g3m.RealRegionName = awi.RealRegionName
	g3m.City = awi.City
	g3m.District = awi.District
	g3m.PostCode = awi.PostCode
	g3m.Latitude = awi.Latitude
	g3m.Longitude = awi.Longitude
	g3m.TimezoneUTC = awi.Timezone
	g3m.CountryCode2 = awi.CountryCode2
	g3m.CountryCode3 = awi.CountryCode3
	g3m.DmaCode = awi.DmaCode
	g3m.Scene = awi.Scene
	g3m.User = awi.User
	g3m.UserType = awi.UserType
	g3m.Isp = awi.Isp
	g3m.Asnumber = awi.Asnumber
}

func (g2m *GeoipSecondMap) CopyIpdbValue(ipips *ipdb.CityInfo) {
	if ipips == nil {
		log.Println("[ERROR] copy ipdb value failed: nil input")
		return
	}

	Latitude := 0.0
	Longitude := 0.0
	Latitude, _ = strconv.ParseFloat(ipips.Latitude, 64)
	Longitude, _ = strconv.ParseFloat(ipips.Longitude, 64)

	var loc = &AwLocat{}
	loc.Latitude = Latitude
	loc.Longitude = Longitude
	g2m.Location = loc
	g2m.CountryName = ipips.CountryName
	g2m.Province = ipips.RegionName
	g2m.City = ipips.CityName
	g2m.Organization = ipips.OwnerDomain
	g2m.Isp = ipips.IspDomain
	g2m.CountryCode2 = ipips.CountryCode
	g2m.CountryCode3 = ipips.CountryCode3
	g2m.Idc = ipips.IDC
	g2m.ContinentCode = ipips.ContinentCode
	g2m.Timezone = ipips.Timezone
	g2m.Timezone2 = ipips.UtcOffset
	g2m.CityCode = ipips.ChinaAdminCode
	g2m.PhonePrefix = ipips.IddCode
	g2m.BaseStation = ipips.BaseStation
	g2m.CurrencyCode = ipips.CurrencyCode
	g2m.CurrencyName = ipips.CurrencyName
}

func (g2m *GeoipSecondMap) CopyDatxValue(ipips datx.Location) {
	Latitude := 0.0
	Longitude := 0.0
	Latitude, _ = strconv.ParseFloat(ipips.Latitude, 64)
	Longitude, _ = strconv.ParseFloat(ipips.Longitude, 64)

	var loc = &AwLocat{}
	loc.Latitude = Latitude
	loc.Longitude = Longitude
	g2m.Location = loc
	g2m.CountryName = ipips.Country
	g2m.Province = ipips.Province
	g2m.City = ipips.City
	g2m.Organization = ipips.Organization
	g2m.Isp = ipips.ISP
	g2m.CountryCode2 = ipips.CountryCode
	g2m.CountryCode3 = ipips.CountryCode3
	g2m.Idc = ipips.IDC
	g2m.ContinentCode = ipips.ContinentCode
	g2m.Timezone = ipips.TimeZone
	g2m.Timezone2 = ipips.TimeZone2
	g2m.CityCode = ipips.CityCode
	g2m.PhonePrefix = ipips.PhonePrefix
	g2m.BaseStation = ipips.BaseStation
	g2m.CurrencyCode = ipips.CurrencyCode
	g2m.CurrencyName = ipips.CurrencyName
}
