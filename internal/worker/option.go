package worker

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/rpc"
	"baimaohui/portscan_new/internal/taskmgr"
)

// Worker选项
type Option func(w *BaseWorker)

func SetSidekiq(grabQueue, pingQueue, checkurlQueue string) Option {
	return func(w *BaseWorker) {
		w.grabQueue = grabQueue
		w.pingQueue = pingQueue
		w.checkurlQueue = checkurlQueue
	}
}

func SetDbAct(dba db.DbAction) Option {
	return func(w *BaseWorker) {
		w.dbAct = dba
	}
}

func SetDbLimitAct(dba db.DbAction) Option {
	return func(w *BaseWorker) {
		w.dbLmtAct = dba
	}
}

func SetRpcClient(cli *rpc.RPCClient) Option {
	return func(w *BaseWorker) {
		w.rpcCli = cli
	}
}

func SetHostname(hostname string) Option {
	return func(w *BaseWorker) {
		w.hostname = hostname
	}
}

func SetMsgParse(msg parse.ParseMsgStub) Option {
	return func(w *BaseWorker) {
		w.msgParse = msg
	}
}

func SetTaskObjMgr(tMgr taskmgr.TaskMgrStub) Option {
	return func(w *BaseWorker) {
		w.taskObjMgr = tMgr
	}
}

func SetSysConf(conf *conf.Config) Option {
	return func(w *BaseWorker) {
		w.sysConf = conf
	}
}
