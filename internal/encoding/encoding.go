package encoding

import (
	"bytes"
	"crawler/internal/req"
	"errors"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/korean"
	"golang.org/x/text/encoding/traditionalchinese"
	"golang.org/x/text/transform"
	"io"
	"log"
	"regexp"
	"strings"
	// iconv "github.com/djimenez/iconv-go"
	"github.com/axgle/mahonia"
	"github.com/saintfish/chardet"
)

func DumpHead(resp *req.Resp) string {
	if resp == nil {
		return ""
	}
	s := resp.Dump()
	if len(s) < 2 {
		return ""
	}
	s = s[:len(s)-2]
	return s
}

func GetAllCharset(contentType, body string) string {
	encoding := Guess(contentType, body)
	if encoding == "utf-8" {
		encoding = "default"
	}

	if len(encoding) == 0 || encoding == "none" {
		encoding = "utf-8"
	}

	return encoding
}

func byteToUTF8(from string, s []byte) ([]byte, error) {
	var reader *transform.Reader
	switch strings.ToLower(from) {
	//case "gbk", "cp936", "windows-936":
	//	reader = transform.NewReader(bytes.NewReader(s), simplifiedchinese.GBK.NewDecoder())
	//case "gb18030":
	//	reader = transform.NewReader(bytes.NewReader(s), simplifiedchinese.GB18030.NewDecoder())
	//case "gb2312":
	//	reader = transform.NewReader(bytes.NewReader(s), simplifiedchinese.HZGB2312.NewDecoder())
	case "big5", "big-5", "cp950":
		reader = transform.NewReader(bytes.NewReader(s), traditionalchinese.Big5.NewDecoder())
	case "euc-kr", "euckr", "cp949":
		reader = transform.NewReader(bytes.NewReader(s), korean.EUCKR.NewDecoder())
	case "euc-jp", "eucjp":
		reader = transform.NewReader(bytes.NewReader(s), japanese.EUCJP.NewDecoder())
	//case "shift-jis":
	//	reader = transform.NewReader(bytes.NewReader(s), japanese.ShiftJIS.NewDecoder())
	case "iso-2022-jp", "cp932", "windows-31j":
		reader = transform.NewReader(bytes.NewReader(s), japanese.ISO2022JP.NewDecoder())
	case "win-1250":
		reader = transform.NewReader(bytes.NewReader(s), charmap.Windows1250.NewDecoder())
	case "win-1253":
		reader = transform.NewReader(bytes.NewReader(s), charmap.Windows1253.NewDecoder())
	default:
		return s, errors.New("Unsupported encoding " + from)
	}

	d, e := io.ReadAll(reader)
	if e != nil {
		return nil, e
	}
	return d, nil
}

func getValidUtf8(charsets string, srcStr []byte) (s string, err error) {
	// 某些特殊的字符集需要特殊处理
	if charsets == "euc-kr" || charsets == "win-1250" {
		var utfByte []byte
		utfByte, err = byteToUTF8(charsets, srcStr)
		if err != nil {
			return
		}

		s = string(utfByte)
	} else {
		if charsets == "utf-16" {
			charsets = chardetHtmlDetector(srcStr)
		}
		enc := mahonia.NewDecoder(charsets)
		if enc == nil {
			enc = mahonia.NewDecoder("utf-8")
		}

		s = enc.ConvertString(string(srcStr))
	}

	return
}

// 字符串转换为utf8
//func GetStrUTF8(charsets, str string) (s string, err error) {
//	if len(str) < 1 {
//		return
//	}
//
//	if charsets == "default" {
//		return
//	}
//
//	defer func() {
//		if r := recover(); r != nil {
//			log.Panicf("encoding conversion failed: %s, %s", charsets, s)
//			panic(r)
//		}
//	}()
//
//	s, err = getValidUtf8(charsets, []byte(str))
//
//	return
//}

func UTF8(contentType, body string) (s string, err error) {
	s = body
	if len(s) < 1 {
		return
	}

	ctxCharset := GetAllCharset(contentType, body)
	if ctxCharset == "default" {
		return
	}

	defer func() {
		if r := recover(); r != nil {
			log.Panicf("encoding conversion failed: %s, %s", ctxCharset, s)
			panic(r)
		}
	}()

	s, err = getValidUtf8(ctxCharset, []byte(s))

	return
}

func changeCharset(encoding string) string {
	switch encoding {
	case "Windows-31J":
		return "Shift_JIS"
	case "x-sjis":
		return "Shift_JIS"
	case "ks_c_5601-1987":
		return "Euc-KR"
	case "gb_2312-80":
		return "gb2312"
	}

	return encoding
}

var regMeta = regexp.MustCompile(`(?i:<meta[^>]+charset=['"]?([\w\-]+)[^>]*?>)`)

func getEncodingFromBody(body string) string {
	result := regMeta.FindStringSubmatch(body)
	if len(result) < 2 {
		return ""
	}
	encoding := result[1]
	if len(encoding) == 0 {
		return ""
	}

	encoding = changeCharset(encoding)

	return strings.ToLower(encoding)
}

var regCharset = regexp.MustCompile(`charset=['"]?([\w\-]+)['"]?`)

func getEncodingFromHeader(contentType string) string {
	//contentType := header.Get("Content-Type")
	if contentType == "" {
		return ""
	}
	result := regCharset.FindStringSubmatch(contentType)
	if len(result) < 2 {
		return ""
	}
	encoding := result[1]
	if len(encoding) == 0 {
		return ""
	}

	encoding = changeCharset(encoding)

	return strings.ToLower(encoding)
}

func GetRealCharset(contentType, body string) string {
	return guess(contentType, body)
}

func guess(contentType, body string) string {
	encoding := getEncodingFromHeader(contentType)
	if encoding != "" && encoding != "none" {
		return encoding
	}
	encoding = getEncodingFromBody(body)
	if encoding != "" {
		return encoding
	}

	// 使用库识别一次
	return chardetHtmlDetector([]byte(body))
}

func chardetHtmlDetector(body []byte) string {
	ntd := chardet.NewHtmlDetector()
	result, err := ntd.DetectBest(body)
	if err != nil {
		log.Println("chardet detect best failed:", err)
		return "utf-8"
	}

	result.Charset = changeCharset(result.Charset)

	return strings.ToLower(result.Charset)
}

func Guess(contentType, body string) string {
	encoding := guess(contentType, body)
	if encoding == "gb2312" {
		return "gbk"
	} else if encoding == `%s` {
		return "utf-8"
	} else if encoding == "plain" {
		return "utf-8"
	}
	return encoding
}
