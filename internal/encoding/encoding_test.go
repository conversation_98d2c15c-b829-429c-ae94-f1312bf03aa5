package encoding

import (
	"bytes"
	"strings"
	"testing"

	"github.com/imroc/req"
	"github.com/stretchr/testify/assert"
)

func TestGuess(t *testing.T) {
	//header charset=none
	//body charset=utf-8
	/*
		HTTP/1.1 200 OK
		Date: Fri, 09 Mar 2018 06:45:16 GMT
		Server: Apache/2.2.31 (Unix) mod_ssl/2.2.31 OpenSSL/1.0.1e-fips PHP/5.6.19
		Last-Modified: Tu<PERSON>, 18 Apr 2017 02:27:16 GMT
		ETag: "1a04fd-1fb-54d67a5709d1b"
		Accept-Ranges: bytes
		Content-Length: 507
		Content-Type: text/html; charset=none

		<!DOCTYPE html>
		<html lang="en">
		<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title>椹噷浜氱撼瑙傚厜灞€</title>
		<base target="_blank" />
		<script type="text/javascript" src="/Public/js/app_js/base.js"></script>
		</head>

		<body>
		<script>
		        if(ispc()){
		                window.location.href = "/index/index.html";
		                window.event.returnValue = false;
		        }else{
		                window.location.href = "/apps/index.html";
		                window.event.returnValue = false;
		        }

		</script>
		</body>
		</html>
	*/
	resp, err := req.Get("https://fofa.info", &req.Header{})
	if err != nil {
		panic(err)
	}
	encoding := Guess("", resp.String())
	assert.Equal(t, "utf-8", encoding)

	contentType := "Content-Type: text/html; charset=gb2312"
	encoding = Guess(contentType, "")
	assert.Equal(t, "gbk", encoding)

	contentType = "Content-Type: text/html; charset=plain"
	encoding = Guess(contentType, "")
	assert.Equal(t, "utf-8", encoding)
}

func TestUTF8(t *testing.T) {
	resp, err := req.Get("https://fofa.info", &req.Header{})
	if err != nil {
		panic(err)
	}
	s, err := UTF8("utf-8", resp.String())
	if err != nil {
		panic(err)
	}
	assert.True(t, strings.Contains(s, `charset="utf-8"`))

	data := ""
	s, err = UTF8("", data)
	assert.Nil(t, err)
	assert.Equal(t, "", s)
}

func TestUTF8_2(t *testing.T) {
	//请求头为UTF-16，但实际为utf8,导致出现乱码
	data := `<html><head><META http-equiv="Content-Type" content="text/html; charset=UTF-16"><title>Dealer Portal</title><meta content="text/html;charset=utf-8">`
	utf8, err := UTF8("utf-16", data)
	assert.Nil(t, err)
	assert.Equal(t, `<html><head><META http-equiv="Content-Type" content="text/html; charset=UTF-16"><title>Dealer Portal</title><meta content="text/html;charset=utf-8">`, utf8)
}

func TestGetAllCharset(t *testing.T) {
	var encoding, newCharset string

	encoding = ""
	newCharset = changeCharset(encoding)
	assert.Empty(t, newCharset)

	encoding = "Windows-31J"
	newCharset = changeCharset(encoding)
	assert.Equal(t, "Shift_JIS", newCharset)

	encoding = "x-sjis"
	newCharset = changeCharset(encoding)
	assert.Equal(t, "Shift_JIS", newCharset)

	encoding = "ks_c_5601-1987"
	newCharset = changeCharset(encoding)
	assert.Equal(t, "Euc-KR", newCharset)

	encoding = "gb_2312-80"
	newCharset = changeCharset(encoding)
	assert.Equal(t, "gb2312", newCharset)
}

func TestDumpHead(t *testing.T) {
	var header string

	header = DumpHead(nil)
	assert.Empty(t, header)
}

func Test_byteToUTF8(t *testing.T) {
	type args struct {
		from string
		s    []byte
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "big5, big-5, cp950",
			args: args{
				from: "big5",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail�桐辣蝟餌�</title> <base href="),
			wantErr: false,
		},

		{
			name: "euckr",
			args: args{
				from: "euckr",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail��뻑楹사퍨</title> <base href="),
			wantErr: false,
		},

		{
			name: "eucjp",
			args: args{
				from: "eucjp",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail���散膤紫��</title> <base href="),
			wantErr: false,
		},
		{
			name: "cp932",
			args: args{
				from: "cp932",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail������������</title> <base href="),
			wantErr: false,
		},
		{
			name: "win-1250",
			args: args{
				from: "win-1250",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremailé‚®ä»¶çł»ç»ź</title> <base href="),
			wantErr: false,
		},
		{
			name: "win-1253",
			args: args{
				from: "win-1253",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremailι‚®δ»¶η³»η»�</title> <base href="),
			wantErr: false,
		},
		{
			name: "error",
			args: args{
				from: "error",
				s:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			},
			want:    []byte("<!DOCTYPE html> <html> <head> <title>Coremail邮件系统</title> <base href="),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := byteToUTF8(tt.args.from, tt.args.s)
			t.Log(string(got))
			if (err != nil) != tt.wantErr {
				t.Error(err)
			}

			if !bytes.Equal(got, tt.want) {
				t.Error("got not same tt.want")
			}
		})
	}
}
