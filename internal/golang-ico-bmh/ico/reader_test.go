package ico

import (
	"encoding/binary"
	"fmt"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"image"
	"image/png"
	"math"
	"os"
	"testing"
)

type ReaderSuit struct {
	suite.Suite
	decoder *decoder
}

func TestReaderSuit(t *testing.T) {
	s := &ReaderSuit{}
	s.decoder = &decoder{
		head: head{
			Zero:   2,
			Type:   2,
			Number: 2,
		},
		entries: []direntry{
			{
				Size: 2,
			},
		},
		images: []image.Image{},
	}

	suite.Run(t, s)
}

func (s *ReaderSuit) Test_decoder_forgeBMPHead() {
	<PERSON>vey("Test_decoder_forgeBMPHead", s.T(), func() {
		Convey("imageSize 0", func() {
			defer ApplyFuncReturn(binary.LittleEndian.Uint16, 1).Reset()
			r := s.decoder.forgeBMPHead([]byte("BM 38 00 00 00"), &direntry{
				Width:  10,
				Height: 10,
				Size:   100,
			})
			So(r, ShouldBeNil)
		})

		<PERSON><PERSON>("pass", func() {
			defer ApplyFuncReturn(binary.LittleEndian.Uint16, 1).Reset()
			r := s.decoder.forgeBMPHead([]byte("// BMP 文件头\nBM                // BMP 文件标识符，\"BM\" 表示这是一个 BMP 文件\n42 4D             // 文件大小（以字节为单位），例如：BM 38 00 00 00 表示文件大小为 0x0038 = 56 字节\n00 00 00 00       // 保留字段\n00 00             // 保留字段\n00 00             // 图像数据起始地址\n\n// BMP 信息头\n0C 00 00 00       // 信息头大小（以字节为单位），例如：0C 00 00 00 表示信息头大小为 0x000C = 12 字节\n02 00             // 图像宽度（以像素为单位），例如：02 00 表示图像宽度为 2 像素\n02 00             // 图像高度（以像素为单位），例如：02 00 表示图像高度为 2 像素\n01 00             // 图像面数（必须为 1）\n08 00             // 每像素位数（以位为单位），例如：08 00 表示每像素占用 8 位（即一个字节）\n00 00 00 00       // 压缩方式，0 表示不压缩\n00 00 00 00       // 图像数据大小（以字节为单位），例如：04 00 00 00 表示图像数据大小为 0x0004 = 4 字节\n00 00 00 00       // 水平分辨率（像素/米），例如：00 00 00 00 表示未设置\n00 00 00 00       // 垂直分辨率（像素/米），例如：00 00 00 00 表示未设置\n00 00 00 00       // 使用的颜色数，例如：00 00 00 00 表示使用的颜色数为 0 表示使用所有颜色\n00 00 00 00       // 重要颜色数，例如：00 00 00 00 表示重要颜色数为 0 表示所有颜色都重要\n\n// 图像数据\n00 00 00 00       // 第一行第一个像素（黑色）\nFF FF 00 00       // 第一行第二个像素（白色）\n00 00 FF FF       // 第二行第一个像素（白色）\nFF FF FF FF       // 第二行第二个像素（白色）"), &direntry{
				Width:  10,
				Height: 10,
				Size:   100,
				Bits:   32,
			})
			So(r, ShouldBeNil)
		})
	})
}

func sqDiffUInt8(x, y uint8) uint64 {
	d := uint64(x) - uint64(y)
	return d * d
}
func fastCompare(img1, img2 *image.NRGBA) (int64, error) {
	if img1.Bounds() != img2.Bounds() {
		return 0, fmt.Errorf("image bounds not equal: %+v, %+v", img1.Bounds(), img2.Bounds())
	}

	accumError := int64(0)

	for i := 0; i < len(img1.Pix); i++ {
		accumError += int64(sqDiffUInt8(img1.Pix[i], img2.Pix[i]))
	}

	return int64(math.Sqrt(float64(accumError))), nil
}

func TestDecodeConfig(t *testing.T) {
	t.Parallel()
	file := "testdata/golang.ico"
	copyFile := "testdata/golang.png"
	reader, err := os.Open(file)
	if err != nil {
		t.Fatal(err)
	}
	icoImage, err := DecodeConfig(reader)
	reader.Close()
	if err != nil {
		t.Fatal(err)
	}
	reader, err = os.Open(copyFile)
	if err != nil {
		t.Fatal(err)
	}
	pngImage, err := png.DecodeConfig(reader)
	reader.Close()
	if err != nil {
		t.Fatal(err)
	}

	if icoImage != pngImage {
		t.Errorf("%v - %v", icoImage, pngImage)
	}

}

//func TestDecode(t *testing.T) {
//	t.Parallel()
//	file := "testdata/golang.ico"
//	copyFile := "testdata/golang.png"
//	reader, err := os.Open(file)
//	if err != nil {
//		t.Fatal(err)
//	}
//	icoImage, err := Decode(reader)
//	if err != nil {
//		t.Fatal(err)
//	}
//	reader.Close()
//
//	reader, err = os.Open(copyFile)
//	if err != nil {
//		t.Fatal(err)
//	}
//	pngImage, err := png.Decode(reader)
//	if err != nil {
//		t.Fatal(err)
//	}
//	reader.Close()
//
//	if icoImage == nil || !icoImage.Bounds().Eq(pngImage.Bounds()) {
//		t.Fatal("bounds differ")
//	}
//	inrgba, ok := icoImage.(*image.NRGBA)
//	if !ok {
//		t.Fatal("not nrgba")
//	}
//	pnrgba, ok := pngImage.(*image.NRGBA)
//	if !ok {
//		t.Fatal("png not nrgba")
//	}
//
//	if b, err := fastCompare(inrgba, pnrgba); err != nil || b != 0 {
//		t.Fatalf("pix differ %d %v\n", b, err)
//	}
//}

//func (s *ReaderSuit) Test_decoder_decode() {
//	Convey("Test_decoder_decode", s.T(), func() {
//		Convey("decode as BMP", func() {
//			defer ApplyFunc(io.ReadFull, func(r io.Reader, buf []byte) (n int, err error) {
//				return 0, nil
//			}).Reset()
//			//defer ApplyFuncReturn(bytes.Compare, 1).Reset()
//			err := s.decoder.decode(bytes.NewReader([]byte("Hello, World!")))
//			So(err, ShouldBeNil)
//		})
//	})
//}
