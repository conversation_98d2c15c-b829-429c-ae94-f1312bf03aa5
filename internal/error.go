package internal

import (
	"bytes"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"go-micro.dev/v4/logger"
)

var (
	LicenseError       = "license授权未激活"
	NoScanTarget       = "缺少扫描目标"
	TaskTypeError      = "不支持的TaskType"
	RequestParamMiss   = "缺少请求参数"
	DataNotExisted     = "数据不存在"
	ParamInValid       = "参数错误"
	InValidIPv4        = "不是有效的IPv4扫描目标"
	InValidIPv6        = "不是有效的IPv6扫描目标"
	ExcludeInValidIPv4 = "不是有效的IPv4禁扫目标"
	ExcludeInValidIPv6 = "不是有效的IPv6禁扫目标"
	trans              ut.Translator // 全局翻译器T
)

func TranslateInit() {
	cn := zh.New()
	uni := ut.New(cn, cn)
	t, ok := uni.GetTranslator("zh")

	if !ok {
		logger.Fatal("uni.GetTranslator error")
	}
	trans = t
	//获取gin的校验器
	validate := binding.Validator.Engine().(*validator.Validate)
	//注册翻译器
	err := zh_translations.RegisterDefaultTranslations(validate, trans)
	if err != nil {
		logger.Fatal("zh_translations.RegisterDefaultTranslations error")
	}
}

func Translate(err error) string {
	var buf bytes.Buffer
	errors, ok := err.(validator.ValidationErrors)

	if !ok {
		return err.Error()
	}

	for _, err := range errors {
		buf.WriteString(err.Field())
		buf.WriteString(":")
		buf.WriteString(err.Translate(trans))
		buf.WriteString("\n")
	}

	return buf.String()
}
