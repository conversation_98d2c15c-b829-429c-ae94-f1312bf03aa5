package internal

import (
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"go-micro.dev/v4/errors"
	"net/http"
)

var (
	TaskAddBadRequestError       = errors.New(constant.ServiceDispatcher, "任务添加失败，请求参数错误", http.StatusBadRequest)
	TaskAddError                 = errors.New(constant.ServiceDispatcher, "任务添加失败", http.StatusInternalServerError)
	TaskPauseError               = errors.New(constant.ServiceDispatcher, "任务暂停失败", http.StatusInternalServerError)
	TaskResumeError              = errors.New(constant.ServiceDispatcher, "任务恢复失败", http.StatusInternalServerError)
	TaskStopError                = errors.New(constant.ServiceDispatcher, "任务停止失败", http.StatusInternalServerError)
	CantPauseTaskError           = errors.New(constant.ServiceDispatcher, "任务扫描已结束处理数据中不能暂停", http.StatusLocked)
	NoNeedResumeTaskError        = errors.New(constant.ServiceDispatcher, "任务处理中不需要恢复", http.StatusLocked)
	PauseTaskErrorCauseResuming  = errors.New(constant.ServiceDispatcher, "任务恢复中不能暂停", http.StatusLocked)
	PauseTaskErrorCausePausing   = errors.New(constant.ServiceDispatcher, "任务暂停中不要重复操作", http.StatusLocked)
	PauseTaskErrorCausePaused    = errors.New(constant.ServiceDispatcher, "任务已暂停不要重复操作", http.StatusLocked)
	PauseResumeErrorCausePausing = errors.New(constant.ServiceDispatcher, "任务暂停中不能恢复", http.StatusLocked)
)

const (
	PausedError = "任务暂停失败"
)
