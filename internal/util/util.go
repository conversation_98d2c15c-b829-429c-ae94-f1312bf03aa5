package util

import (
	"github.com/goware/urlx"
	"github.com/imroc/domain"
	"github.com/pkg/errors"
	"log"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type ProgramType int32

const (
	PTFofa  ProgramType = 0
	PTFoeye ProgramType = 1
)

type HostInfo struct {
	Host      string
	OnlyHost  string
	Domain    string
	Subdomain string
}

var regBase64Proc = regexp.MustCompile("(.{76})")

func GetHost() (host string, err error) {
	host, err = os.Hostname()
	if err != nil {
		return "", err
	}
	return host, nil
}

// http://w-tokyosaito.jp#main这个会崩溃
func PortOfUrl(urlStr string) (port int, err error) {
	if !strings.Contains(urlStr, "http://") && !strings.Contains(urlStr, "https://") {
		urlStr = "http://" + urlStr
	}
	uri, err := url.ParseRequestURI(urlStr)
	if err != nil || uri == nil {
		err = errors.Wrap(err, "invalid uri:"+urlStr)
	}

	//var port int
	if uri != nil {
		p := uri.Port()
		if p != "" {
			port, err = strconv.Atoi(p)
			return
		}
	}

	// 没有解析到端口的置默认端口
	if strings.HasPrefix(urlStr, "https://") {
		port = 443
	} else {
		port = 80
	}

	return
}

func Url2Hostinfo(url string) string {
	hostinfo := ""
	if len(url) == 0 {
		return ""
	}
	u, err := urlx.Parse(url)
	if err != nil {
		log.Println("INFO:", err)
		return ""
	}

	hostname := u.Hostname()
	chgHostname := hostname
	if strings.Contains(hostname, ":") {
		chgHostname = "[" + hostname + "]"
	}
	if u.Scheme == "https" {
		chgHostname = "https://" + chgHostname
		if u.Port() != "" && u.Port() != "443" {
			hostinfo = chgHostname + ":" + u.Port()
		} else {
			hostinfo = chgHostname
		}
	} else {
		if u.Port() != "" && u.Port() != "80" {
			hostinfo = chgHostname + ":" + u.Port()
		} else {
			hostinfo = chgHostname
		}
	}

	return hostinfo
}

func Min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

func UrlAddPrefix(urls string) string {
	if len(urls) <= 0 {
		return urls
	} else {
		if !strings.HasPrefix(urls, "https://") && !strings.HasPrefix(urls, "http") {
			urls = "http://" + urls
		}
	}

	return urls
}

func UrlAddHttps(urls string) string {
	if len(urls) <= 0 {
		return urls
	}

	if !strings.HasPrefix(urls, "https://") {
		urls = "https://" + urls
	}

	return urls
}

func GetCause(err error) string {
	if err == nil {
		return ""
	}

	s := err.Error()
	ss := strings.Split(s, ":")
	if len(ss) == 0 {
		return ""
	}
	cause := ss[len(ss)-1]
	return strings.TrimSpace(cause)
}

// 功能: 通过正则批量替换字符串，相当于ruby的gsub
func ReplaceAllStringSubmatchFunc(re *regexp.Regexp, str string, repl func([]string) string) string {
	result := ""
	lastIndex := 0
	for _, v := range re.FindAllSubmatchIndex([]byte(str), -1) {
		groups := []string{}
		for i := 0; i < len(v); i += 2 {
			groups = append(groups, str[v[i]:v[i+1]])
		}
		result += str[lastIndex:v[0]] + repl(groups)
		lastIndex = v[1]
	}
	return result + str[lastIndex:]
}

func ProcBase64(base64Ctx string) string {
	// 转换格式，为下一步运算hash做准备
	iconBase64Str := ReplaceAllStringSubmatchFunc(regBase64Proc, base64Ctx, func(groups []string) string {
		return groups[1] + "\n"
	})
	// 必须以\n结束
	if !strings.HasSuffix(iconBase64Str, "\n") {
		iconBase64Str += "\n"
	}

	return iconBase64Str
}

func FileExists(filename string) (bool, error) {
	_, err := os.Stat(filename)
	if err == nil {
		return true, nil
	}

	if os.IsNotExist(err) {
		return false, err
	}
	return false, err
}

func UrlToHostinfo(url string) (hostinfo *HostInfo, err error) {
	// 根域名
	u, err := domain.Parse(url)
	if err != nil {
		err = errors.Wrap(err, "域名解析失败")
		return
	}
	hostinfo = new(HostInfo)
	if u.Domain != "" && u.PublicSuffix != "" {
		hostinfo.Domain = u.Domain + "." + u.PublicSuffix
	}

	hostinfo.Subdomain = u.Subdomain

	// Host,OnlyHost
	uu, err := urlx.Parse(url)
	if err != nil {
		err = errors.Wrap(err, "url解析失败")
		return
	}
	port := uu.Port()
	hostname := uu.Hostname()
	hostinfo.OnlyHost = hostname

	chgHostname := hostname
	if strings.Contains(hostname, ":") {
		chgHostname = "[" + hostname + "]"
	}

	host := ""
	if uu.Scheme == "https" {
		host = "https://" + chgHostname
		if port != "" && port != "443" {
			host += ":" + port
		}
		hostinfo.Host = host
		return
	}
	if port != "" && port != "80" {
		host = chgHostname + ":" + port
	} else {
		host = chgHostname
	}
	hostinfo.Host = host
	return
}

func ProcUrl(url string) string {
	scheme := "http"
	if strings.HasPrefix(url, "https://") {
		scheme = "https"
	}
	newUrl := url
	port, err := PortOfUrl(url)
	if err != nil {
		return newUrl
	}

	// 80和443把默认端口去掉
	if scheme == "http" && port == 80 {
		newUrl = strings.ReplaceAll(url, ":80", "")
		newUrl = strings.ReplaceAll(newUrl, "http://", "")
	}

	if scheme == "https" && port == 443 {
		newUrl = strings.ReplaceAll(url, ":443", "")
	}

	// http的网站去掉http://
	if !strings.Contains(url, "https://") {
		newUrl = strings.ReplaceAll(newUrl, "http://", "")
	}

	return newUrl
}

var (
	regBullshitIp = regexp.MustCompile(`(^127\.)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^192\.168\.)|(^0\.0\.0\.0)`)
)

// CheckUrlIsSendFail 检查url中是否包含端口
func CheckUrlIsSendFail(host string, ip string) bool {
	//内网ip
	if regBullshitIp.MatchString(ip) {
		return false
	}
	//协议过来的数据
	if strings.Contains(host, ip) {
		return false
	}
	//包含两个以上冒号的，直接去掉
	if strings.Count(host, ":") >= 2 {
		return false
	}
	//包含一个冒号，但没有http/https的也去掉
	if strings.Contains(host, ":") && !strings.Contains(host, "http:") && !strings.Contains(host, "https:") {
		return false
	}
	return true
}

func TrackFnTime(printLog bool, name, hostinfo string) func() {
	start := time.Now()
	return func() {
		execTime := time.Since(start)
		if execTime.Seconds() > 3 && printLog {
			log.Printf(" [TRACE] <%s>  host: %s time: %s\n", name, hostinfo, execTime)
		}
	}
}
