package util

import (
	"fmt"
	"log"
	"os"
	"strconv"
)

type ProgramType int32

const (
	PTFofa  ProgramType = 0
	PTFoeye ProgramType = 1
)

func GetHost() (host string, err error) {
	host, err = os.Hostname()
	if err != nil {
		return "", err
	}

	return host, nil
}

func Decimal(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return value
}

func DeleteFile(filename string) error {
	exists, _ := FileExists(filename)
	if exists {
		log.Println("delete file exists:", filename)

		err := os.Remove(filename)
		if err != nil {
			return err
		}
	}

	return nil
}

func FileExists(filename string) (bool, error) {
	_, err := os.Stat(filename)
	if err == nil {
		return true, nil
	}

	if os.IsNotExist(err) {
		return false, err
	}
	return false, err
}
