package util

import (
	"baimaohui/portscan_new/internal/config"
	"log"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Logger *zap.Logger

func SetupLogger(conf config.LogConf) {
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	cfg := zap.NewProductionConfig()
	cfg.OutputPaths = []string{conf.Output}
	cfg.ErrorOutputPaths = []string{conf.ErrOutput}
	cfg.EncoderConfig.EncodeTime = TimeEncoder
	cfg.EncoderConfig.StacktraceKey = ""
	cfg.DisableCaller = true

	var err error
	Logger, err = cfg.Build()
	if err != nil {
		log.Fatal(err)
	}

	log.Printf("config:%+v\n", conf)
}

func TimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02T15:04:05"))
}
