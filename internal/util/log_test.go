package util

import (
	"baimaohui/portscan_new/internal/config"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestSetupLogger(t *testing.T) {
	curDir, _ := os.Getwd()
	//fmt.Println("  current dir:", curDir)
	var logCfg config.LogConf
	var err error
	logCfg.Output = curDir + "/log.log"
	logCfg.ErrOutput = "stderr"

	// 如果文件存在，则先删除
	exists, _ := FileExists(logCfg.Output)
	if exists {
		err := DeleteFile(logCfg.Output)
		assert.Nil(t, err)
	}

	SetupLogger(logCfg)
	exists, err = FileExists(logCfg.Output)
	assert.Nil(t, err)
	assert.True(t, exists)

	err = DeleteFile(logCfg.Output)
	assert.Nil(t, err)
}
