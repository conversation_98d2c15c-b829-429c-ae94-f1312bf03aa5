package util

import (
	"baimaohui/portscan_new/internal/config"
	"log"
	"net/http"
	_ "net/http/pprof"
	"strconv"
)

//func SaveMem() {
//	fm, err := os.OpenFile("/var/log/portscan/mem.prof", os.O_RDWR|os.O_CREATE, 0644)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	pprof.WriteHeapProfile(fm)
//	fm.Close()
//}

func InitPporf(conf config.PprofConf) {
	if !conf.Enable {
		return
	}

	log.Println("start pprof on port", conf.Port)
	go func() {
		http.HandleFunc("/heap", func(w http.ResponseWriter, r *http.Request) {
			//SaveMem()
			w.Write([]byte("save heap success"))
		})
		log.Fatalln(http.ListenAndServe(":"+strconv.Itoa(conf.Port), nil))
	}()
}
