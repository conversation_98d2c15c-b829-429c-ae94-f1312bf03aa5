package util

import (
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestFofaWorker_procBase64(t *testing.T) {
	defer TrackFnTime(true, "test_procbase64", "")()

	var origStr, procdStr string

	origStr = "abcd"
	origStr = base64.StdEncoding.EncodeToString([]byte(origStr))
	procdStr = ProcBase64(origStr)
	assert.Equal(t, procdStr, origStr+"\n")

	origStr = "abcdefghijklmnopqrstuvwxyz0123456789"
	origStr = base64.StdEncoding.EncodeToString([]byte(origStr))
	procdStr = ProcBase64(origStr)
	assert.Equal(t, procdStr, origStr+"\n")

	origStr = "abcdefghijklmnopqrstuvwxyz0123456789~!@#$%^&*()_+{}|:<>?"
	origStr = base64.StdEncoding.EncodeToString([]byte(origStr))
	procdStr = ProcBase64(origStr)
	assert.Equal(t, procdStr, origStr+"\n")

	origStr = "abcdefghijklmnopqrstuvwxyz0123456789~!@#$%^&*()_+{}|:<>?abcdefghijklmnopqrstuvwxyz0123456789~!@#$%^&*()_+{}|:<>?"
	origStr = base64.StdEncoding.EncodeToString([]byte(origStr))
	procdStr = ProcBase64(origStr)
	assert.Equal(t, procdStr, "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5fiFAIyQlXiYqKClfK3t9fDo8Pj9h\nYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODl+IUAjJCVeJiooKV8re318Ojw+Pw==\n")

	origStr = "abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@abcdefghijklmn0123456789~!@"
	origStr = base64.StdEncoding.EncodeToString([]byte(origStr))
	procdStr = ProcBase64(origStr)
	assert.Equal(t, procdStr, "YWJjZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJj\nZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVm\nZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVmZ2hp\namtsbW4wMTIzNDU2Nzg5fiFAYWJjZGVmZ2hpamtsbW4wMTIzNDU2Nzg5fiFA\n")

}

func Test_procUrl(t *testing.T) {
	var origUrl, newUrl string

	// ----------- 1: http IP
	origUrl = "127.0.0.1"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "127.0.0.1", newUrl)

	origUrl = "127.0.0.1:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "127.0.0.1", newUrl)

	origUrl = "127.0.0.1:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "127.0.0.1:808", newUrl)

	origUrl = "***********:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "***********:443", newUrl)

	// 带http://的
	origUrl = "http://***********"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "***********", newUrl)

	origUrl = "http://***********:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "***********", newUrl)

	origUrl = "http://***********:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "***********:808", newUrl)

	origUrl = "http://***********:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "***********:443", newUrl)

	// ----------- 2: http域名
	origUrl = "www.baidu.com"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "www.baidu.com:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "www.baidu.com:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com:808", newUrl)

	origUrl = "www.baidu.com:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com:443", newUrl)

	// 带http://的
	origUrl = "http://www.baidu.com"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "http://www.baidu.com:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com", newUrl)

	origUrl = "http://www.baidu.com:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com:808", newUrl)

	origUrl = "http://www.baidu.com:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "www.baidu.com:443", newUrl)

	// ----------- 3: https ip
	origUrl = "https://127.0.0.1"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1", newUrl)

	origUrl = "https://127.0.0.1:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:80", newUrl)

	origUrl = "https://127.0.0.1:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://127.0.0.1:808", newUrl)

	origUrl = "https://***********:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://***********", newUrl)

	// 带http://的
	origUrl = "https://***********"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://***********", newUrl)

	origUrl = "https://***********:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://***********:80", newUrl)

	origUrl = "https://***********:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://***********:808", newUrl)

	origUrl = "https://***********:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://***********", newUrl)

	// ----------- 4: https域名
	origUrl = "https://www.baidu.com"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com", newUrl)

	origUrl = "https://www.baidu.com:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com:80", newUrl)

	origUrl = "https://www.baidu.com:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com:808", newUrl)

	origUrl = "https://www.baidu.com:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com", newUrl)

	// 带http://的
	origUrl = "https://www.baidu.com:80"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com:80", newUrl)

	origUrl = "https://www.baidu.com:808"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com:808", newUrl)

	origUrl = "https://www.baidu.com:443"
	newUrl = ProcUrl(origUrl)
	assert.Equal(t, "https://www.baidu.com", newUrl)
}

func TestUrlAddHttps(t *testing.T) {
	var urls, resultUrls string

	urls = ""
	resultUrls = UrlAddHttps(urls)
	assert.Empty(t, resultUrls)

	urls = "baidu.com"
	resultUrls = UrlAddHttps(urls)
	assert.Equal(t, "https://baidu.com", resultUrls)

	urls = "www.baidu.com"
	resultUrls = UrlAddHttps(urls)
	assert.Equal(t, "https://www.baidu.com", resultUrls)

	urls = "www.baidu.com/a.php?https://url.com"
	resultUrls = UrlAddHttps(urls)
	assert.Equal(t, "https://www.baidu.com/a.php?https://url.com", resultUrls)

	urls = "https://www.baidu.com/"
	resultUrls = UrlAddHttps(urls)
	assert.Equal(t, "https://www.baidu.com/", resultUrls)
}

func TestGetHost(t *testing.T) {
	fmt.Println(GetHost())
}

func TestUrl2Hostinfo(t *testing.T) {
	var surl, retHostinfo string

	surl = ""
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "", retHostinfo)

	surl = "abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd", retHostinfo)

	surl = "https://abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, surl, retHostinfo)

	surl = "abcd.com"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	// http
	surl = "http://abcd.com"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	surl = "http://abcd.com/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	surl = "http://abcd.com/abcd/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	surl = "http://abcd.com/abcd/index.asp"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	surl = "http://abcd.com:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com", retHostinfo)

	surl = "http://abcd.com:81"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com:81", retHostinfo)

	surl = "http://abcd.com:81/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com:81", retHostinfo)

	surl = "http://abcd.com:81/abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com:81", retHostinfo)

	surl = "http://abcd.com:81/abcd/1.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "abcd.com:81", retHostinfo)

	// https
	surl = "https://abcd.com"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com", retHostinfo)

	surl = "https://abcd.com/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com", retHostinfo)

	surl = "https://abcd.com/abcd/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com", retHostinfo)

	surl = "https://abcd.com/abcd/index.asp"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com", retHostinfo)

	surl = "https://abcd.com:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com:80", retHostinfo)

	surl = "https://abcd.com:443"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com", retHostinfo)

	surl = "https://abcd.com:81"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com:81", retHostinfo)

	surl = "https://abcd.com:81/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com:81", retHostinfo)

	surl = "https://abcd.com:81/abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com:81", retHostinfo)

	surl = "https://abcd.com:81/abcd/1.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://abcd.com:81", retHostinfo)

	// http ip
	surl = "http://**********00"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00", retHostinfo)

	surl = "http://**********00/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00", retHostinfo)

	surl = "http://**********00/abcd/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00", retHostinfo)

	surl = "http://**********00/abcd/index.asp"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00", retHostinfo)

	surl = "http://**********00:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00", retHostinfo)

	surl = "http://**********00:81"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "**********00:81", retHostinfo)

	surl = "http://*********:81/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*********:81", retHostinfo)

	surl = "http://*********:81/abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*********:81", retHostinfo)

	surl = "http://*********:81/abcd/1.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*********:81", retHostinfo)

	// https
	surl = "https://*********"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*********", retHostinfo)

	surl = "https://*********/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*********", retHostinfo)

	surl = "https://*********/abcd/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*********", retHostinfo)

	surl = "https://*********/abcd/index.asp"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*********", retHostinfo)

	surl = "https://*******:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:80", retHostinfo)

	surl = "https://*******:443"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******", retHostinfo)

	surl = "https://*******:81"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	surl = "https://*******:81/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	surl = "https://*******:81/abcd"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	surl = "https://*******:81/abcd/1.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	// other
	surl = "https://*******:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	surl = "http://*******:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*******:81", retHostinfo)

	surl = "https://*******/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******", retHostinfo)

	surl = "http://*******/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*******", retHostinfo)

	// other root domain
	surl = "https://a.com:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://a.com:81", retHostinfo)

	surl = "http://a.com:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "a.com:81", retHostinfo)

	surl = "https://a.com/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://a.com", retHostinfo)

	surl = "http://a.com/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "a.com", retHostinfo)

	// other host
	surl = "https://www.a.com:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://www.a.com:81", retHostinfo)

	surl = "http://www.a.com:81/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "www.a.com:81", retHostinfo)

	surl = "https://www.a.com/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://www.a.com", retHostinfo)

	surl = "http://www.a.com/abcd/1.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "www.a.com", retHostinfo)

	// other have param
	surl = "https://*******:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******:81", retHostinfo)

	surl = "http://*******:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*******:81", retHostinfo)

	surl = "https://*******/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://*******", retHostinfo)

	surl = "http://*******/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "*******", retHostinfo)

	// other root domain
	surl = "https://a.com:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://a.com:81", retHostinfo)

	surl = "http://a.com:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "a.com:81", retHostinfo)

	surl = "https://a.com/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://a.com", retHostinfo)

	surl = "http://a.com/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "a.com", retHostinfo)

	// other host
	surl = "https://www.a.com:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://www.a.com:81", retHostinfo)

	surl = "http://www.a.com:81/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "www.a.com:81", retHostinfo)

	surl = "https://www.a.com/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://www.a.com", retHostinfo)

	surl = "http://www.a.com/abcd/1.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "www.a.com", retHostinfo)

	surl = "https://www.a.com/abcd/1.php?a=1#"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://www.a.com", retHostinfo)

	surl = "http://www.a.com/abcd/1.php?a=1#"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "www.a.com", retHostinfo)

	// --------------------ipv6
	surl = "[2600:140b:c000:1a9::fea2]"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]/aaa"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]/aaa/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]/aaa/a.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:808"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:443"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:443/aaa"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:443/aaa/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "[2600:140b:c000:1a9::fea2]:443/aaa/a.php?b=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	// 带http://的
	surl = "http://[2600:140b:c000:1a9::fea2]"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]/aaa"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]/abcd/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]/a/php.php"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:808"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:443"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:443/a"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:443/ab/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	surl = "http://[2600:140b:c000:1a9::fea2]:443/abc/12.php?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "[2600:140b:c000:1a9::fea2]:443", retHostinfo)

	// https://
	surl = "https://[2600:140b:c000:1a9::fea2]"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]/aa"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]/aa/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]/a/b/c"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]/aa/12.php?a=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:80"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]:80", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:808"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:808/a"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:808/a/bc"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:808/a/bc/a.php?1=1"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]:808", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:443"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:443/a"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:443/a/b"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:443/a/bb/"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

	surl = "https://[2600:140b:c000:1a9::fea2]:443/1/?"
	retHostinfo = Url2Hostinfo(surl)
	assert.Equal(t, "https://[2600:140b:c000:1a9::fea2]", retHostinfo)

}

func TestGetHostInfo(t *testing.T) {
	subTest := []struct {
		url         string
		expHostInfo HostInfo
		expErr      error
	}{
		{
			url: "https://feedback.hoobill.com",
			expHostInfo: HostInfo{
				Host:      "https://feedback.hoobill.com",
				OnlyHost:  "feedback.hoobill.com",
				Domain:    "hoobill.com",
				Subdomain: "feedback",
			},
			expErr: nil,
		},
		{
			url: "https://***********:995",
			expHostInfo: HostInfo{
				Host:      "https://***********:995",
				OnlyHost:  "***********",
				Domain:    "",
				Subdomain: "",
			},
			expErr: nil,
		},
		{
			url:    "",
			expErr: errors.New("域名解析失败"),
		},
	}

	for _, subT := range subTest {
		info, err := UrlToHostinfo(subT.url)
		if subT.expErr == nil {
			assert.Equal(t, subT.expErr, err)
		} else {
			assert.NotEqual(t, nil, err)
			continue
		}

		assert.Equal(t, subT.expHostInfo.Host, info.Host)
		assert.Equal(t, subT.expHostInfo.OnlyHost, info.OnlyHost)
		assert.Equal(t, subT.expHostInfo.Domain, info.Domain)
		assert.Equal(t, subT.expHostInfo.Subdomain, info.Subdomain)
	}
}

func TestCheckUrlIncludePort(t *testing.T) {
	subTest := []struct {
		url string
		ip  string
		res bool
	}{
		{
			url: "https://www.baidu.com",
			ip:  "**************",
			res: true,
		},
		{
			url: "http://baidu.com",
			ip:  "**************",
			res: true,
		},
		{
			url: "baidu.com:9090",
			ip:  "**************",
			res: false,
		},
		{
			url: "http://www.baidu.com:9090",
			ip:  "**************",
			res: false,
		},
		{
			url: "www.baidu.com:80",
			ip:  "**************",
			res: false,
		},
		{
			url: "https://saravl.itssaconsulting.com:82",
			ip:  "**************",
			res: false,
		},
		{
			url: "https://saravl.itssaconsulting.com:82:110",
			ip:  "**************",
			res: false,
		},
		{
			url: "https://saravl.itssaconsulting.com",
			ip:  "**************",
			res: true,
		},
		{
			url: "http://***********",
			ip:  "***********",
			res: false,
		},
		{
			url: "http://**********",
			ip:  "**********",
			res: false,
		},
	}

	for _, subT := range subTest {
		assert.Equal(t, subT.res, CheckUrlIsSendFail(subT.url, subT.ip))
	}
}

func TestMin(t *testing.T) {
	var ret int

	ret = Min(0, 0)
	assert.Equal(t, 0, ret)

	ret = Min(0, 1)
	assert.Equal(t, 0, ret)
}

func TestUrlAddPrefix(t *testing.T) {
	var reqUrl, resultUrl string

	reqUrl = ""
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "", resultUrl)

	reqUrl = "*******"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://*******", resultUrl)

	reqUrl = "http://*******"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://*******", resultUrl)

	reqUrl = "http://*******/"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://*******/", resultUrl)

	reqUrl = "http://*******/admin.php"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://*******/admin.php", resultUrl)

	reqUrl = "ab.com"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://ab.com", resultUrl)

	reqUrl = "http://ab.com"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://ab.com", resultUrl)

	reqUrl = "http://a.ab.com/"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://a.ab.com/", resultUrl)

	reqUrl = "http://abc.aaa.com/admin.php"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "http://abc.aaa.com/admin.php", resultUrl)

	// https
	reqUrl = "https://*******"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://*******", resultUrl)

	reqUrl = "https://*******/"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://*******/", resultUrl)

	reqUrl = "https://*******/admin.php"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://*******/admin.php", resultUrl)

	reqUrl = "https://ab.com"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://ab.com", resultUrl)

	reqUrl = "https://ab.com"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://ab.com", resultUrl)

	reqUrl = "https://a.ab.com/"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://a.ab.com/", resultUrl)

	reqUrl = "https://abc.aaa.com/admin.php"
	resultUrl = UrlAddPrefix(reqUrl)
	assert.Equal(t, "https://abc.aaa.com/admin.php", resultUrl)
}

func TestGetCause(t *testing.T) {
	var result string
	var err error

	err = nil
	result = GetCause(err)
	assert.Equal(t, "", result)

	err = errors.New("")
	result = GetCause(err)
	assert.Equal(t, "", result)

	err = errors.New("aaa")
	result = GetCause(err)
	assert.Equal(t, "aaa", result)

	err = errors.New("aaa:bbb")
	result = GetCause(err)
	assert.Equal(t, "bbb", result)
}

func TestFileExists(t *testing.T) {
	var filename string

	filename = "aajalskdjflasdjflasjdfljasldf"
	exists, err := FileExists(filename)
	assert.NotNil(t, err)
	assert.False(t, exists)
}

func TestPortOfUrl(t *testing.T) {
	type args struct {
		urlStr string
	}
	tests := []struct {
		name     string
		args     args
		wantPort int
	}{
		// TODO: Add test cases.
		{
			name: "pass",
			args: args{
				urlStr: "https://10.11.12.22:443",
			},
			wantPort: 443,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotPort, err := PortOfUrl(tt.args.urlStr)
			fmt.Println("port", gotPort)
			assert.NoError(t, err)
			assert.Equalf(t, tt.wantPort, gotPort, "PortOfUrl(%v)", tt.args.urlStr)
		})
	}
}
