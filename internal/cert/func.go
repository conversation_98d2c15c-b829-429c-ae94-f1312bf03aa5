package cert

import (
	"bytes"
	"crypto/dsa"
	"crypto/ecdsa"
	"crypto/rsa"
	"crypto/x509"
	"fmt"
	"net"
	"regexp"
	"strings"
	"time"

	"github.com/spiffe/go-spiffe/uri"
)

var keyUsages = []x509.KeyUsage{
	x509.KeyUsageDigitalSignature,
	x509.KeyUsageContentCommitment,
	x509.KeyUsageKeyEncipherment,
	x509.KeyUsageDataEncipherment,
	x509.KeyUsageKeyAgreement,
	x509.KeyUsageCertSign,
	x509.KeyUsageCRLSign,
	x509.KeyUsageEncipherOnly,
	x509.KeyUsageDecipherOnly,
}

var keyUsageStrings = map[x509.KeyUsage]string{
	x509.KeyUsageDigitalSignature:  "Digital Signature",
	x509.KeyUsageContentCommitment: "Content Commitment",
	x509.KeyUsageKeyEncipherment:   "Key Encipherment",
	x509.KeyUsageDataEncipherment:  "Data Encipherment",
	x509.KeyUsageKeyAgreement:      "Key Agreement",
	x509.KeyUsageCertSign:          "Cert Sign",
	x509.KeyUsageCRLSign:           "CRL Sign",
	x509.KeyUsageEncipherOnly:      "Encipher Only",
	x509.KeyUsageDecipherOnly:      "Decipher Only",
}

var extKeyUsageStrings = map[x509.ExtKeyUsage]string{
	x509.ExtKeyUsageAny:                        "Any",
	x509.ExtKeyUsageServerAuth:                 "Server Auth",
	x509.ExtKeyUsageClientAuth:                 "Client Auth",
	x509.ExtKeyUsageCodeSigning:                "Code Signing",
	x509.ExtKeyUsageEmailProtection:            "Email Protection",
	x509.ExtKeyUsageIPSECEndSystem:             "IPSEC End System",
	x509.ExtKeyUsageIPSECTunnel:                "IPSEC Tunnel",
	x509.ExtKeyUsageIPSECUser:                  "IPSEC User",
	x509.ExtKeyUsageTimeStamping:               "Time Stamping",
	x509.ExtKeyUsageOCSPSigning:                "OCSP Signing",
	x509.ExtKeyUsageMicrosoftServerGatedCrypto: "Microsoft ServerGatedCrypto",
	x509.ExtKeyUsageNetscapeServerGatedCrypto:  "Netscape ServerGatedCrypto",
}

var algoName = [...]string{
	x509.MD2WithRSA:      "MD2-RSA",
	x509.MD5WithRSA:      "MD5-RSA",
	x509.SHA1WithRSA:     "SHA1-RSA",
	x509.SHA256WithRSA:   "SHA256-RSA",
	x509.SHA384WithRSA:   "SHA384-RSA",
	x509.SHA512WithRSA:   "SHA512-RSA",
	x509.DSAWithSHA1:     "DSA-SHA1",
	x509.DSAWithSHA256:   "DSA-SHA256",
	x509.ECDSAWithSHA1:   "ECDSA-SHA1",
	x509.ECDSAWithSHA256: "ECDSA-SHA256",
	x509.ECDSAWithSHA384: "ECDSA-SHA384",
	x509.ECDSAWithSHA512: "ECDSA-SHA512",
}

// keyUsage decodes/prints key usage from a certificate.
func keyUsage(ku x509.KeyUsage) []string {
	out := []string{}
	for _, key := range keyUsages {
		if ku&key > 0 {
			out = append(out, keyUsageStrings[key])
		}
	}
	return out
}

// extKeyUsage decodes/prints extended key usage from a certificate.
func extKeyUsage(ekus []x509.ExtKeyUsage) (ret []string) {
	for _, eku := range ekus {
		val, ok := extKeyUsageStrings[eku]
		if ok {
			ret = append(ret, val)
		}
	}
	return
}

// isSelfSigned returns true iff the given certificate has a valid self-signature.
func isSelfSigned(cert *x509.Certificate) bool {
	return cert.CheckSignatureFrom(cert) == nil
}
func detail(v interface{}) string {
	return fmt.Sprintf("%+v", v)
}

var regNewLine = regexp.MustCompile("\n")

func indent(idt, str string) string {
	return idt + strings.Replace(str, "\n", "\n"+idt, -1)
	//var buf bytes.Buffer
	//ss := strings.Split(str, "\n")
	//last := len(ss) - 1
	//for i, s := range ss {
	//buf.WriteString(idt)
	//buf.WriteString(s)
	//if i != last {
	//buf.WriteString("\n")
	//}
	//}
	//return buf.String()
}

func publicKeyAlgorithm(algo x509.PublicKeyAlgorithm) (s string) {
	switch algo {
	case x509.RSA:
		s = "RSA"
	case x509.DSA:
		s = "DSA"
	case x509.ECDSA:
		s = "ECDSA"
	default:
		s = "Unknown"
	}
	return
}

// timeString formats a time in UTC with minute precision, in the given color.
func timeString(t time.Time) string {
	return t.Format("2006-01-02 15:04 MST")
}

func printHexWidth(p []byte, width int) string {
	var buf bytes.Buffer
	last := len(p) - 1
	for i, b := range p {
		if i > 0 && i%width == 0 {
			buf.WriteByte('\n')
		}
		buf.WriteString(fmt.Sprintf("%.2X", b))
		if i != last {
			buf.WriteByte(':')
		}
	}
	return buf.String()
}

func printHex(p []byte) string {
	return printHexWidth(p, 16)
}

func printPublicKey(key interface{}) string {
	var buf bytes.Buffer
	switch v := key.(type) {
	case *rsa.PublicKey:
		buf.WriteString(fmt.Sprintf("Exponent: %d\n", v.E))
		//modulus := cert.RawSubjectPublicKeyInfo[32 : len(cert.RawSubjectPublicKeyInfo)-5]
		modulus := v.N.Bytes()
		buf.WriteString(fmt.Sprintf("Public Key Modulus: (%d bits) :\n", len(modulus)*8))
		buf.WriteString(indent("  ", printHex(modulus)))
	case *dsa.PublicKey:
		buf.WriteString("pub:\n")
		buf.WriteString(indent("  ", printHex(v.Y.Bytes())))
		buf.WriteString("\nP:\n")
		buf.WriteString(indent("  ", printHex(v.P.Bytes())))
		buf.WriteString("\nQ:\n")
		buf.WriteString(indent("  ", printHex(v.Q.Bytes())))
		buf.WriteString("\nG:\n")
		buf.WriteString(indent("  ", printHex(v.G.Bytes())))
	case *ecdsa.PublicKey:
		buf.WriteString(printHex(append(v.X.Bytes(), v.Y.Bytes()...)))
	}
	return buf.String()
}

// algo changes the color of the signing algorithm
// based on a set color map, e.g. to make SHA-1 show up red.
func algo(sig x509.SignatureAlgorithm) string {
	return sig.String()
}

func getUriNames(cert *x509.Certificate) []string {
	uriNames, _ := uri.GetURINamesFromCertificate(cert)
	return uriNames
}

func parseIP(ips []net.IP) []string {
	var ipstr []string
	for _, ip := range ips {
		ipstr = append(ipstr, ip.String())
	}
	return ipstr
}
