package cert

import "encoding/asn1"

// OidDescription returns a human-readable name, a short acronym from RFC1485, a snake_case slug suitable as a json key,
// and a boolean describing whether multiple copies can appear on an X509 cert.
type OidDescription struct {
	Name     string
	Short    string
	Slug     string
	Multiple bool
}

func describeOid(oid asn1.ObjectIdentifier) OidDescription {
	raw := oid.String()
	// Multiple should be true for any types that are []string in x509.pkix.Name. When in doubt, set it to true.
	names := map[string]OidDescription{
		"*******":                   {"CommonName", "CN", "common_name", false},
		"*******":                   {"EV Incorporation Registration Number", "", "ev_registration_number", false},
		"*******":                   {"Country", "C", "country", true},
		"*******":                   {"Locality", "L", "locality", true},
		"*******":                   {"Province", "ST", "province", true},
		"********":                  {"Organization", "O", "organization", true},
		"********":                  {"Organizational Unit", "OU", "organizational_unit", true},
		"********":                  {"Business Category", "", "business_category", true},
		"1.2.840.113549.1.9.1":      {"Email Address", "", "email_address", true},
		"*******.4.1.311.********":  {"EV Incorporation Locality", "", "ev_locality", true},
		"*******.4.1.311.********":  {"EV Incorporation Province", "", "ev_province", true},
		"*******.4.1.311.********":  {"EV Incorporation Country", "", "ev_country", true},
		"0.9.2342.19200300.100.1.1": {"User ID", "UID", "user_id", true},
	}
	if description, ok := names[raw]; ok {
		return description
	}
	return OidDescription{raw, "", raw, true}
}

func oidShort(oid asn1.ObjectIdentifier) string {
	return describeOid(oid).Short
}

func oidName(oid asn1.ObjectIdentifier) string {
	return describeOid(oid).Name
}
