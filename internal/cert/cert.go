/*-
 * Copyright 2016 Square Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cert

import (
	"bufio"
	"bytes"
	"crypto/x509"
	"text/template"

	"github.com/Masterminds/sprig"
)

var layout = `
{{- define "PkixName" -}}
{{- range .Names}}
  {{ .Type | oidName }}: {{ .Value }}
{{- end -}}
{{end -}}
Version:  v{{.Version}}
Serial Number: {{.SerialNumber}}
Signature Algorithm: {{.SignatureAlgorithm}}{{if isSelfSigned .}} (self-signed){{end}}

Issuer:
{{- template "PkixName" .Issuer}}

Validity:
  Not Before: {{timeString .NotBefore}}
  Not After : {{timeString .NotAfter}}

Subject: 
  {{- template "PkixName" .Subject}}

Subject Public Key Info:
  Public Key Algorithm: {{publicKeyAlgorithm .PublicKeyAlgorithm}}
  Public Key:
{{printPublicKey .PublicKey | indent "    "}}{{with .AuthorityKeyId}}

Authority Key Identifier:
{{printHexWidth . 20 | indent "  "}}{{end}}{{with .SubjectKeyId}}

Subject Key Identifier:
{{printHexWidth . 20 | indent "  "}}{{end}}{{with .CRLDistributionPoints}}

CRL Distribution Points:
{{join "\n" . | indent "  "}}{{end}}{{if .BasicConstraintsValid}}

Basic Constraints:
  CA : {{.IsCA}}
  Path Length Constraint: {{if eq .MaxPathLen -1}}UNLIMITED{{else}}{{.MaxPathLen}}{{end}}{{end}}{{with .OCSPServer}}

OCSP Server:
{{join "\n" . | indent "  "}}{{end}}{{with .IssuingCertificateURL}}

Issuing Certificate URL:
{{join "\n" . | indent "  "}}{{end}}{{with .KeyUsage}}

Key Usage:
{{keyUsage . | join "\n" | indent "  "}}{{end}}{{with .ExtKeyUsage}}

Extended Key Usage:
{{extKeyUsage . | join "\n" | indent "  "}}{{end}}{{with .DNSNames}}

DNS Names:
{{join "\n" . | indent "  "}}{{end}}{{with $ips := parseIP .IPAddresses}}

IP Addresses:
{{join "\n" $ips | indent "  "}}{{end}}{{with $uriNames := getUriNames .}}

URI Names:
{{join "\n" . | indent "  "}}{{end}}{{with .EmailAddresses}}

Email Addresses:
{{join "\n" . | indent "  "}}{{end}}

Certificate Signature Algorithm: {{.SignatureAlgorithm}}
Certificate Signature:
{{printHex .Signature | indent "  "}}`

// EncodeX509ToText encodes an X.509 certificate into human-readable text.
func EncodeX509ToText(cert *x509.Certificate, terminalWidth int) []byte {
	var buffer bytes.Buffer
	w := bufio.NewWriter(&buffer)
	err := tpl.Execute(w, cert)
	if err != nil {
		// Should never happen
		panic(err)
	}
	w.Flush()
	return buffer.Bytes()

}

var tpl *template.Template

func init() {

	// Use template functions from sprig, but add some extras
	funcMap := sprig.TxtFuncMap()

	extras := template.FuncMap{
		"detail":             detail,
		"indent":             indent,
		"parseIP":            parseIP,
		"getUriNames":        getUriNames,
		"printHex":           printHex,
		"printHexWidth":      printHexWidth,
		"printPublicKey":     printPublicKey,
		"publicKeyAlgorithm": publicKeyAlgorithm,
		"timeString":         timeString,
		"isSelfSigned":       isSelfSigned,
		"algo":               algo,
		"keyUsage":           keyUsage,
		"extKeyUsage":        extKeyUsage,
		"oidName":            oidName,
		"oidShort":           oidShort,
	}
	for k, v := range extras {
		funcMap[k] = v
	}

	t := template.New("Cert template").Funcs(funcMap)
	var err error

	tpl, err = t.Parse(layout)
	if err != nil {
		// Should never happen
		panic(err)
	}
}
