//go:build foeye
// +build foeye

package license

import (
	"api/internal"
	go_crypto "git.gobies.org/foeye/key-license/go-crypto"
	"git.gobies.org/foeye/key-license/secret"
)

var (
	a string
)

func NewLicense() *License {
	sec := secret.NewSecret(internal.Private, internal.SHA265, embeds, "embed/rsa/id_rsa.pub")
	licensePath := go_crypto.NewFilePath("./data", sec)

	return &License{
		Crypto: licensePath,
		Info:   go_crypto.ValidationRes{},
	}
}
