package license

import (
	"embed"

	go_crypto "git.gobies.org/foeye/key-license/go-crypto"
	"go-micro.dev/v4/logger"
)

//go:embed embed
var embeds embed.FS

type License struct {
	Crypto *go_crypto.FilePath
	Info   go_crypto.ValidationRes
}

// 默认实现，当没有指定构建标签时使用
//func NewLicense() *License {
//	sec := secret.NewSecret(internal.Private, internal.SHA265, embeds, "embed/rsa/id_rsa.pub")
//	licensePath := go_crypto.NewFilePath("./data", sec)
//
//	return &License{
//		Crypto: licensePath,
//		Info:   go_crypto.ValidationRes{},
//	}
//}

func (lic *License) Verify() {
	b := go_crypto.IsFileExist(lic.Crypto.Path + "/id.key")
	lb := go_crypto.IsFileExist(lic.Crypto.Path + "/license.txt")

	if !b {
		logger.Info("id.key does not exist...generating id.key...")
		lic.Crypto.GenId()
		return
	}

	if !lb {
		logger.Error("id.key does not exist...generating id.key...")
		return
	}

	if b && lb {
		lic.Info = lic.Crypto.IsValid()
	}
}
