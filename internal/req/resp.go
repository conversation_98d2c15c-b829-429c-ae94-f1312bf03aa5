package req

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"regexp"
)

const READBUFMAXSIZE = 2 * 1024 * 1024 // 最大读取内容2M

// Resp represents a request with it's response
type Resp struct {
	r        *Req
	req      *http.Request
	resp     *http.Response
	client   *http.Client
	kaClient *http.Client
	*multipartHelper
	reqBody          []byte
	respBody         []byte
	downloadProgress DownloadProgress
	err              error // delayed error
}

// Request returns *http.Request
func (r *Resp) Request() *http.Request {
	return r.req
}

// Response returns *http.Response
func (r *Resp) Response() *http.Response {
	return r.resp
}

func (r *Resp) EmptyBodyAndError() {
	r.err = nil
	r.respBody = nil
}

// Bytes returns response body as []byte
func (r *Resp) Bytes() []byte {
	data, _ := r.ToBytes()
	return data
}

// ToBytes returns response body as []byte,
// return error if error happend when reading
// the response body
func (r *Resp) ToBytes() ([]byte, error) {
	if r.err != nil {
		return nil, r.err
	}

	if r.resp != nil {
		if r.resp.StatusCode > 0 && r.resp.StatusCode <= 199 {
			if r.req != nil {
				fmt.Println("    resp toBytes info   url:", r.req.URL, "status_code:", r.resp.StatusCode)
			}

			return []byte(""), nil
		}
	}

	if r.respBody != nil {
		return r.respBody, nil
	}
	defer r.resp.Body.Close()

	respBody, err := io.ReadAll(r.resp.Body)
	if err != nil {
		fmt.Printf("  read body error url:%s, info:%s\n", r.req.URL, err)
		r.err = err
		return nil, err
	}
	r.respBody = respBody

	// 换一种方式实现（这样修改也有问题，如果某个icon非常大，会读取不完全）
	//bufLastSize := int(r.resp.ContentLength)
	//if bufLastSize <= 0 {
	//	bufLastSize = READBUFMAXSIZE
	//}
	//buf := make([]byte, bufLastSize)
	//n, err := io.ReadAtLeast(r.resp.Body, buf, bufLastSize)
	////fmt.Println("  read len info  url:", r.req.URL, "ctx_length:", r.resp.ContentLength, "buf_len:", len(buf), "len:", n)
	//if err != nil && !strings.Contains(err.Error(), "unexpected EOF") {
	//	fmt.Printf("  read body error url:%s, info:%s\n", r.req.URL, err)
	//	r.err = err
	//	return nil, err
	//}
	//r.respBody = buf[0:n]

	return r.respBody, nil
}

// String returns response body as string
func (r *Resp) String() string {
	data, _ := r.ToBytes()
	return string(data)
}

// ToString returns response body as string,
// return error if error happend when reading
// the response body
func (r *Resp) ToString() (string, error) {
	data, err := r.ToBytes()
	return string(data), err
}

// ToJSON convert json response body to struct or map
func (r *Resp) ToJSON(v interface{}) error {
	data, err := r.ToBytes()
	if err != nil {
		return err
	}
	return json.Unmarshal(data, v)
}

// ToXML convert xml response body to struct or map
func (r *Resp) ToXML(v interface{}) error {
	data, err := r.ToBytes()
	if err != nil {
		return err
	}
	return xml.Unmarshal(data, v)
}

var regNewline = regexp.MustCompile(`\n|\r`)

func (r *Resp) autoFormat(s fmt.State) {
	req := r.req
	fmt.Fprint(s, req.Method, " ", req.URL.String())

	// test if it is should be outputed pretty
	var pretty bool
	var parts []string
	addPart := func(part string) {
		if part == "" {
			return
		}
		parts = append(parts, part)
		if !pretty && regNewline.MatchString(part) {
			pretty = true
		}
	}
	if r.r.flag&LreqBody != 0 { // request body
		addPart(string(r.reqBody))
	}
	if r.r.flag&LrespBody != 0 { // response body
		addPart(r.String())
	}

	for _, part := range parts {
		if pretty {
			fmt.Fprint(s, "\n")
		}
		fmt.Fprint(s, " ", part)
	}
}

func (r *Resp) miniFormat(s fmt.State) {
	req := r.req
	fmt.Fprint(s, req.Method, " ", req.URL.String())
	if r.r.flag&LreqBody != 0 && len(r.reqBody) > 0 { // request body
		str := regNewline.ReplaceAllString(string(r.reqBody), " ")
		fmt.Fprint(s, " ", str)
	}
	if r.r.flag&LrespBody != 0 && r.String() != "" { // response body
		str := regNewline.ReplaceAllString(r.String(), " ")
		fmt.Fprint(s, " ", str)
	}
}

func (r *Resp) Format(s fmt.State, verb rune) {
	if r == nil || r.req == nil {
		return
	}
	if s.Flag('+') { // include header and format pretty.
		fmt.Fprint(s, r.Dump())
	} else if s.Flag('-') { // keep all informations in one line.
		r.miniFormat(s)
	} else { // auto
		r.autoFormat(s)
	}
}
