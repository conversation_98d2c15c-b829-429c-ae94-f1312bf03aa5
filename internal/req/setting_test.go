package req

import (
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func newDefaultTestServer() *httptest.Server {
	handler := func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("hi"))
	}
	return httptest.NewServer(http.HandlerFunc(handler))
}

func TestSetClient(t *testing.T) {

	ts := newDefaultTestServer()

	client := &http.Client{}
	SetClient(client)
	_, err := Get(ts.URL)
	if err != nil {
		t.Errorf("error after set client: %v", err)
	}

	SetClient(nil)
	_, err = Get(ts.URL)
	if err != nil {
		t.<PERSON>rrorf("error after set client to nil: %v", err)
	}

	client = Client()
	if trans, ok := client.Transport.(*http.Transport); ok {
		trans.MaxIdleConns = 1
		trans.DisableKeepAlives = true
		_, err = Get(ts.URL)
		if err != nil {
			t.<PERSON><PERSON><PERSON>("error after change client's transport: %v", err)
		}
	} else {
		t.<PERSON>("transport is not http.Transport: %+#v", client.Transport)
	}
}

func TestSetting(t *testing.T) {
	defer func() {
		if rc := recover(); rc != nil {
			t.Errorf("panic happened while change setting: %v", rc)
		}
	}()
	SetTimeout(2 * time.Second)
	EnableCookie(false)
	EnableCookie(true)
	EnableInsecureTLS(true)
	SetJSONIndent("", "    ")
	SetJSONEscapeHTML(false)
	SetXMLIndent("", "\t")
	SetProxyUrl("http://localhost:8080")
	SetProxy(nil)
}

func Test_procHost(t *testing.T) {
	defer func() {
		if rc := recover(); rc != nil {
			t.Errorf("panic happened while change setting: %v", rc)
		}
	}()
	var host, newHost, scheme string

	scheme = "http"
	host = "127.0.0.1"
	newHost = procHost(scheme, host)
	assert.Equal(t, "127.0.0.1", newHost)

	host = "127.0.0.1:80"
	newHost = procHost(scheme, host)
	assert.Equal(t, "127.0.0.1", newHost)

	host = "127.0.0.1:443"
	newHost = procHost(scheme, host)
	assert.Equal(t, "127.0.0.1:443", newHost)

	// 带schema
	host = "http://127.0.0.1"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://127.0.0.1", newHost)

	host = "http://127.0.0.1:80"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://127.0.0.1", newHost)

	// 别的ip和端口
	host = "**********"
	newHost = procHost(scheme, host)
	assert.Equal(t, "**********", newHost)

	host = "**********:808"
	newHost = procHost(scheme, host)
	assert.Equal(t, "**********:808", newHost)

	host = "**********:80"
	newHost = procHost(scheme, host)
	assert.Equal(t, "**********", newHost)

	host = "**********:443"
	newHost = procHost(scheme, host)
	assert.Equal(t, "**********:443", newHost)

	host = "**********:4433"
	newHost = procHost(scheme, host)
	assert.Equal(t, "**********:4433", newHost)

	// 别的ip带schema
	host = "http://**********"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://**********", newHost)

	host = "http://**********:808"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://**********:808", newHost)

	host = "http://**********:80"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://**********", newHost)

	host = "http://www.baidu.com"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://www.baidu.com", newHost)

	host = "http://www.baidu.com:808"
	newHost = procHost(scheme, host)
	assert.Equal(t, "http://www.baidu.com:808", newHost)

	scheme = "https"
	host = "https://127.0.0.1:443"
	newHost = procHost(scheme, host)
	assert.Equal(t, "https://127.0.0.1", newHost)

	host = "https://**********:443"
	newHost = procHost(scheme, host)
	assert.Equal(t, "https://**********", newHost)

	// 域名
	host = "https://www.baidu.com:443"
	newHost = procHost(scheme, host)
	assert.Equal(t, "https://www.baidu.com", newHost)

	host = "https://www.baidu.com:4433"
	newHost = procHost(scheme, host)
	assert.Equal(t, "https://www.baidu.com:4433", newHost)
}
