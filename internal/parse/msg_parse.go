package parse

import "crawler/internal/util"

type CrawlerTask struct {
	Url         string `json:"url"`
	Host        string `json:"host"`
	Domain      string `json:"domain"`
	Subdomain   string `json:"subdomain"`
	AddLinkHost bool   `json:"add_link_host,omitempty"`
	Retry       int    `json:"retry"`
	Extra       []interface{}
	TaskId      string `json:"task_id"`
	Jarm        string `json:"jarm"`
	Ip          string `json:"ip"`
	Port        int    `json:"port"`
}

type ParseMsgStub interface {
	ParseMsg(msg []byte) (CrawlerTask, error)
	FinishProc() // 结束任务之后的处理
}

func NewMsgParse(typ util.ProgramType) ParseMsgStub {
	ct := new(CrawlerTask)
	switch typ {
	case util.PTFofa:
		return NewFofaMessage(ct)
	}

	return nil
}
