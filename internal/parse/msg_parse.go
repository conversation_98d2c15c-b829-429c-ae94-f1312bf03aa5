package parse

import "baimaohui/portscan_new/internal/util"

type BaseTaskMsg struct {
	Bandwidth       string `json:"bandwidth"`
	Hosts           string `json:"hosts"`
	IpList          string `json:"ip_list"`
	Ports           string `json:"ports"`
	Tid             int    `json:"tid"`
	TaskId          string `json:"task_id"`
	StartStatusWork bool   `json:"start_status_work"`
	ResumeFilename  string `json:"resume_filename"`
	IsIPv6          bool   `json:"is_ipv6"`
	CanTreckScan    bool   `json:"treck_scan"`
	SendEth         string `json:"send_eth"`
	ScanMode        string `json:"scan_mode"` // common为正常的Fofa模式，默认是这个；ip_alive为IP存活探测模式；port_alive为端口存活探测模式
}

type ParseMsgStub interface {
	ParseMsg(msg []byte, sendEth string) (interface{}, error)
}

func NewMsgParse(typ util.ProgramType) ParseMsgStub {
	switch typ {
	case util.PTFofa:
		return new(BaseTaskMsg)

	case util.PTFoeye:
		return new(FoeyeTaskMsg)
	}

	return nil
}
