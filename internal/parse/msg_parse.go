package parse

import "checkurl/internal/util"

type CrawlerTask struct {
	Url         string `json:"url"`
	Host        string `json:"host"`
	Domain      string `json:"domain"`
	Subdomain   string `json:"subdomain"`
	AddLinkHost bool   `json:"add_link_host,omitempty"`
	Retry       int    `json:"retry"`
	Jarm        string `json:"jarm"`
}

type ParseMsgStub interface {
	ParseMsg(msg []byte) (interface{}, error)
}

func NewMsgParse(typ util.ProgramType) ParseMsgStub {
	switch typ {
	case util.PTFofa:
		return new(FofaMessage)

	case util.PTFoeye:
		return new(FoeyeMessage)
	}

	return nil
}
