package parse

import (
	"encoding/json"
	"github.com/pkg/errors"
)

type FoeyeTaskMsg struct {
	TaskId              string   `json:"task_id"`
	Hosts               string   `json:"hosts"`
	IpList              string   `json:"ip_list"`
	Bandwidth           string   `json:"bandwidth"`
	TaskType            string   `json:"task_type"`
	RepeatTimes         int      `json:"repeat_times"`
	Blacklist           string   `json:"blacklist"`
	CanDeepGetMAC       bool     `json:"deep_get_mac"`
	CanOpenPing         bool     `json:"ping_scan"`
	CanDeepGetOS        bool     `json:"deep_get_os"`
	ProtocolUpdateCycle int      `json:"protocol_update_cycle"`
	UnknownProtocolIndb bool     `json:"unknown_protocol_indb"`
	GatewayMAC          string   `json:"getway_mac"`
	Ports               string   `json:"ports"`
	Domains             []string `json:"hostinfos"`
	ResumeFilename      string   `json:"resume_filename"`
	IpListFilename      string   `json:"ip_list_filename"`
	MaxAssetNum         int      `json:"max_asset_num"`
	IsIPv6              bool     `json:"is_ipv6"`
	CanTreckScan        bool     `json:"treck_scan"`
	SendEth             string   `json:"send_eth"`
}

/*
解析命令行参数，Ruby的worker传过来的参数接收后是一个数组对象
扫描ip、port、引擎、设置等
*/
func (ftm *FoeyeTaskMsg) ParseMsg(msg []byte, sendEth string) (interface{}, error) {
	var scanmsg []FoeyeTaskMsg
	err := json.Unmarshal(msg, &scanmsg)
	if err != nil {
		return nil, errors.Wrap(err, "parse param json unmarshal")
	}

	if len(scanmsg) <= 0 {
		return nil, errors.Wrap(err, "parse param len<=0")
	}

	// 兼容不同参数
	if len(scanmsg[0].Hosts) <= 0 {
		scanmsg[0].Hosts = scanmsg[0].IpList
	}

	return scanmsg[0], nil
}
