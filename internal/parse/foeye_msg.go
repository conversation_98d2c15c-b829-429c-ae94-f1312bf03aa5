package parse

import (
	"encoding/json"
	"fmt"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"log"
)

type FoeyeOptions struct {
	Bandwidth           string   `json:"bandwidth"`
	Blacklist           string   `json:"blacklist"`
	CanDeepGetMac       bool     `json:"deep_get_mac"`
	CanDeepGetOS        bool     `json:"deep_get_os"`
	GatewayMac          string   `json:"getway_mac"`
	Hostinfos           []string `json:"hostinfos"`
	Hosts               string   `json:"hosts"`
	IpList              string   `json:"ip_list"`
	CanNmapScan         bool     `json:"nmap_scan"`
	CanPingScan         bool     `json:"ping_scan"`
	ProtocolUpdateCycle int      `json:"protocol_update_cycle"`
	RepeatTimes         int      `json:"repeat_times"`
	ResumeFilename      string   `json:"resume_filename"`
	TaskId              string   `json:"task_id"`
	UnknownProtocolIndb bool     `json:"unknown_protocol_indb"`
	MaxAssetNum         int      `json:"max_asset_num"`
}

type FoeyeMessage struct {
	ArrUrl      []interface{}
	Url         string
	AddLinkHost bool
	Options     FoeyeOptions
}

/*
解析命令行参数，Ruby的worker传过来的参数接收后是一个数组对象
扫描ip、port、引擎、设置等
*/
func (ftm *FoeyeMessage) ParseMsg(msg []byte) (interface{}, error) {
	var foeyeMsg FoeyeMessage
	var msgArgs interface{}
	var tmpArrs []interface{}
	var ok bool
	err := json.Unmarshal(msg, &msgArgs)
	if err != nil {
		return nil, errors.Wrap(err, "parse msg")
	}

	if tmpArrs, ok = msgArgs.([]interface{}); !ok {
		return nil, fmt.Errorf("parse msg data change failed, raw:%s", string(msg))
	}
	if len(tmpArrs) < 2 {
		return nil, fmt.Errorf("parse msg bad args: %s", string(msg))
	}

	if foeyeMsg.ArrUrl, ok = tmpArrs[0].([]interface{}); !ok {
		return nil, fmt.Errorf("parse msg data change subarr failed, raw:%s", string(msg))
	}

	err = InterfaceToStruct(tmpArrs[1].(map[string]interface{}), &foeyeMsg.Options)
	if err != nil {
		return nil, fmt.Errorf("parse msg get options failed:%v", err)
	}

	return foeyeMsg, nil
}

func InterfaceToStruct(data map[string]interface{}, msg interface{}) error {
	config := &mapstructure.DecoderConfig{TagName: "json", Result: msg}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		log.Println("[WARNING] interfaceToStruct error:", err)
		return err
	}

	err = decoder.Decode(data)
	if err != nil {
		log.Println("[WARNING] interfaceToStruct error:", err)
		return err
	}

	return nil
}
