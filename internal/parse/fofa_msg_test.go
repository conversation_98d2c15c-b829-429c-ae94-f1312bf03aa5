package parse

import (
	"crawler/internal/util"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewMessage(t *testing.T) {
	fm := NewMsgParse(util.PTFofa)
	msg := `{"url":"https://*************","host":"https://*************","domain":"","subdomain":"","add_link_host":true,"retry":3,"jarm":"3fd3fd15d3fd3fd21c42d42d0000006f254909a73bf62f6b28507e9fb451b5"}`
	fmMsg, err := fm.ParseMsg([]byte(msg))
	assert.Nil(t, err)
	assert.Equal(t, 3, fmMsg.Retry)
	assert.Equal(t, "https://*************", fmMsg.Url)
	assert.Equal(t, "https://*************", fmMsg.Host)
	assert.Equal(t, true, fmMsg.AddLinkHost)
	assert.Equal(t, "3fd3fd15d3fd3fd21c42d42d0000006f254909a73bf62f6b28507e9fb451b5", fmMsg.Jarm)
	fm.FinishProc()

	//测试解析错误情况
	msg = `{"url":"https://*************","host":"https://*************","domain":"","subdomain":"","add_link_host":true,"retry":"3","jarm":"3fd3fd15d3fd3fd21c42d42d0000006f254909a73bf62f6b28507e9fb451b5"}`
	fmMsg, err = fm.ParseMsg([]byte(msg))
	assert.NotNil(t, err)
}
