package parse

import (
	"encoding/json"
	"github.com/pkg/errors"
	"strconv"
)

/*
解析命令行参数，Ruby的worker传过来的参数接收后是一个数组对象
扫描ip、port、引擎、设置等
*/
func (st *BaseTaskMsg) ParseMsg(msg []byte, sendEth string) (interface{}, error) {
	var scantask []BaseTaskMsg
	err := json.Unmarshal(msg, &scantask)
	if err != nil {
		return nil, errors.Wrap(err, "parse param json unmarshal")
	}

	if len(scantask) <= 0 {
		return nil, errors.Wrap(err, "parse param len<=0")
	}

	// 兼容web和微内核
	if scantask[0].Tid <= 0 {
		tmpTid, _ := strconv.Atoi(scantask[0].TaskId)
		scantask[0].Tid = tmpTid
	}

	// 设置扫描模式默认值
	if len(scantask[0].ScanMode) == 0 {
		scantask[0].ScanMode = "common"
	}

	// 支持配置发包网卡
	if len(sendEth) > 0 {
		scantask[0].SendEth = sendEth
	}

	return scantask[0], nil
}
