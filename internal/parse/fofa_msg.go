package parse

import (
	"encoding/json"
)

type FofaMessage struct {
	*CrawlerTask
}

func NewFofaMessage(ct *CrawlerTask) *FofaMessage {
	return &FofaMessage{
		CrawlerTask: ct,
	}
}

func (fw *FofaMessage) ParseMsg(msg []byte) (CrawlerTask, error) {
	var ct CrawlerTask
	err := json.Unmarshal(msg, &ct)
	if err != nil {
		return ct, err
	} else {
		fw.CrawlerTask = &ct
	}

	return ct, nil
}

func (fw *FofaMessage) FinishProc() {
	fw.TaskId = ""
	fw.Url = ""
	fw.Extra = nil
	fw.AddLinkHost = false
}
