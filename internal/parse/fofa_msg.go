package parse

import (
	"encoding/json"
)

type FofaMessage struct {
	Url         []string `json:"url"`
	Force       bool     `json:"force,omitempty"`
	AddLinkHost bool     `json:"add_link_host,omitempty"`
	Jarm        string   `json:"jarm"`
}

/*
解析命令行参数，Ruby的worker传过来的参数接收后是一个数组对象
扫描ip、port、引擎、设置等
*/
func (st *FofaMessage) ParseMsg(msg []byte) (interface{}, error) {
	var fofaMsg FofaMessage
	err := json.Unmarshal(msg, &fofaMsg)
	if err != nil {
		return nil, err
	}

	return fofaMsg, nil
}
