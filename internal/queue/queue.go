package queue

import (
	"baimaohui/portscan_new/pkg/errorhandle"
	"io"
)

type ErrorCloser interface {
	errorhandle.ErrorChan
	io.Closer
}

type SimpleConsumer interface {
	Messages() <-chan []byte
	Run()
	Done()
	ErrorCloser
}

type SimpleProducer interface {
	Publish(string, []byte)
	ErrorCloser
}

//udp黑名单数据
type Queue struct {
	Que   []interface{}
	Limet int
}

func NewQue(litmet int) *Queue {
	return &Queue{
		make([]interface{}, 0),
		litmet}
}

//移除队列中最前面的额元素,添加新元素大于litmet删除首个元素
func (entry *Queue) Poll(ip interface{}) *Queue {
	if len(entry.Que) == entry.Limet {
		entry.Que = entry.Que[1:]
		entry.Que = append(entry.Que, ip)
	}
	entry.Que = append(entry.Que, ip)

	return entry
}

//检查队列长度
func (entry *Queue) Size() int {
	return len(entry.Que)
}

//检查ip是否在队列中
func (entry *Queue) CheckQue(ip interface{}) bool {
	for _, ips := range entry.Que {
		if ip == ips {
			return true
		}
	}
	return false
}
