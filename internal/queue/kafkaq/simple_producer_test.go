package kafkaq

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewCommonSimpleProducer(t *testing.T) {
	//config := sarama.NewConfig()
	//p := mocks.NewAsyncProducer(t, config)
	//assert.NotNil(t, p)

	csp, err := NewCommonSimpleProducer([]string{"127.0.0.1:9092"}, true, true, nil, t)
	assert.<PERSON>l(t, err)
	assert.NotNil(t, csp)

	//csp.Publish("unit_test", []byte("unittest-abcd"))  mocks不支持

	errCh := csp.Errors()
	assert.NotNil(t, errCh)

	err = csp.Close()
	assert.Nil(t, err)
}
