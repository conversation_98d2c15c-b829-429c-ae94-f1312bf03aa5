package kafkaq

import (
	"github.com/Shopify/sarama/mocks"
	"log"
	"os"
	"testing"
	"time"

	"baimaohui/portscan_new/pkg/errorhandle"

	"github.com/Shopify/sarama"
	"github.com/pkg/errors"
)

func init() {
	sarama.Logger = log.New(os.<PERSON>, "[<PERSON><PERSON>] ", log.LstdFlags)
}

type SimpleProducer struct {
	sarama.AsyncProducer
	errCh chan error
}

func NewSimpleProducer(addrs []string, config *sarama.Config, isMock bool, t *testing.T) (*SimpleProducer, error) {
	var producer sarama.AsyncProducer
	var err error

	if !isMock {
		producer, err = sarama.NewAsyncProducer(addrs, config)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		producer = mocks.NewAsyncProducer(t, config)
	}

	sp := &SimpleProducer{
		AsyncProducer: producer,
		errCh:         make(chan error),
	}

	return sp, nil
}

func NewCommonSimpleProducer(brokers []string, enableCompression, isMock bool, errorHandler errorhandle.Handler, t *testing.T) (*SimpleProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.NoResponse
	config.Producer.Partitioner = sarama.NewHashPartitioner
	config.Producer.Return.Errors = true
	// config.Producer.Return.Successes = true
	//config.Producer.Flush.Messages = 1
	//config.Producer.Flush.Frequency = 100 * time.Millisecond
	config.Net.DialTimeout = 10 * time.Second

	if enableCompression {
		config.Producer.Compression = sarama.CompressionGZIP // gzip 压缩
		config.Producer.Flush.Bytes = 1024 * 1024            // 1MB 批量压缩
		config.Producer.MaxMessageBytes = 104850000          // 比broker的socket.request.max.bytes少一点
		config.Producer.Flush.Frequency = 1 * time.Minute
	}

	sp, err := NewSimpleProducer(brokers, config, isMock, t)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	go func() {
		for err := range sp.AsyncProducer.Errors() {
			sp.errCh <- err.Err
		}
	}()

	// go func() {
	// 	for msgs := range sp.AsyncProducer.Successes() {
	// 		log.Println("+++Success produce topic:", msgs.Topic)
	// 	}
	// }()

	if errorHandler != nil {
		go func() {
			for err := range sp.Errors() {
				errorHandler(err)
			}
		}()
	}

	return sp, nil
}

func (sp *SimpleProducer) Close() error {
	return sp.AsyncProducer.Close()
}

func (sp *SimpleProducer) Publish(topic string, data []byte) {
	// log.Println("publish", topic, string(data))
	sp.Input() <- &sarama.ProducerMessage{Topic: topic, Value: sarama.ByteEncoder(data)}
}

func (sp *SimpleProducer) Errors() <-chan error {
	return sp.errCh
}
