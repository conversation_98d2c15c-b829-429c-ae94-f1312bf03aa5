package foeye

import (
	"fmt"
	"net/url"
)

type ServiceConfig struct {
	Id       string
	LoadTask bool
}

type Config struct {
	Service     ServiceConfig
	ThirdSystem ThirdSystem
}

func (c *Config) ToString() string {
	return fmt.Sprintf("path=%s", c.ThirdSystem.ToString())
}

// ThirdSystem is other system
type ThirdSystem struct {
	Scheme string
	Host   string
	// 资产列表
	PathAssets string
	// 回调进度,
	// | 字段 | 类型 | 说明 |
	// | ------ | ------ | ------ |
	// | task_id | int64 | 任务id|
	// | state | int | 状态信息（1：调度中/2：扫描中/3：停止中） |
	// | host_completed | int | 主机完成（1：完成 ）|
	// | progress | string | 扫描总体进度，不带百分号 |
	// | remain_time | string | 剩余时间 |
	// | scan_info| string | 当前正在扫描的信息 |
	// /callbacks/asset_scan_progress?task_id=123&state=1&progress=12&remain_time=0:01:10&scan_info=**********
	PathNotifyProgress string
	// 回调状态, 通知频率5秒钟通知一次
	// | 字段 | 类型 | 说明 |
	// | ------ | ------ | ------ |
	// | task_id | int64 | 任务id|
	// | state | int | 状态信息（4：已停止/5：扫描成功/6：任务扫描失败/7：已暂停）|
	// | message | string | base64编码，返回信息（失败的情况下才有值，否则是空字符串）|
	//
	// /callbacks/asset_scan_state?task_id=1&state=5&message=
	// /callbacks/asset_scan_state?task_id=1&state=6&message=dGFzayBzdGFydCBmYWlsZWQ=
	PathNotifyStatus string

	// 失败回调次数
	FailRetryTimes int

	// 失败回调间隔，单位秒
	FailRetryInterval int

	Enabled bool
}

func (t *ThirdSystem) ToString() string {
	return fmt.Sprintf("%s,%s,%s", t.PathAssets, t.PathNotifyProgress, t.PathNotifyStatus)
}

func (t *ThirdSystem) GetPathAssets() string {
	return t.buildFullURL(t.PathAssets, nil)
}

func (t *ThirdSystem) GetPathNotifyProgress(params url.Values) string {
	return t.buildFullURL(t.PathNotifyProgress, params)
}

func (t *ThirdSystem) GetPathNotifyStatus(params url.Values) string {
	return t.buildFullURL(t.PathNotifyStatus, params)
}

func (t *ThirdSystem) buildFullURL(path string, query url.Values) string {
	u := url.URL{
		Scheme:   t.Scheme,
		Host:     t.Host,
		Path:     path,
		RawQuery: query.Encode(),
	}

	return u.String()
}
