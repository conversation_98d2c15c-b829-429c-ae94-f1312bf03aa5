package foeye

import (
	"context"
	"dispatcher/pkg/bloom"
	"errors"
	"fmt"
	"github.com/imroc/req/v3"
	"go-micro.dev/v4/logger"
)

type AssetsCallback struct {
	Status  bool   `json:"status"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Count int      `json:"count"`
		Items []string `json:"items"`
	} `json:"data"`
}

type AssetsLimit struct {
	conf     *Config
	filter   *bloom.Store
	http     *req.Client
	maxCount int32
}

func NewAssetsLimit(c *Config) *AssetsLimit {
	return &AssetsLimit{
		conf:   c,
		filter: bloom.NewBloomStore(),
		http:   req.NewClient(),
	}
}

func (svc *AssetsLimit) Initial(ctx context.Context, taskID string, maxCount int32) error {
	svc.maxCount = maxCount

	var result AssetsCallback
	url := svc.conf.ThirdSystem.GetPathAssets()

	resp, err := svc.http.R().SetResult(&result).Get(url)
	if err != nil {
		return err
	}
	if resp.Err != nil {
		return errors.New("请求资产列表失败: " + resp.Err.Error())
	}
	if result.Code != 200 {
		return fmt.Errorf("请求资产列表失败 %d: %s", result.Code, result.Message)
	}

	logger.Infof("init asset count. taskID=%s, count=%d, len=%d", taskID, result.Data.Count, len(result.Data.Items))

	svc.filter.Init(taskID, result.Data.Items)
	return nil
}

func (svc *AssetsLimit) Exceed(ctx context.Context, taskID, ip string, port uint32) bool {
	return svc.filter.Push(taskID, ip) >= uint(svc.maxCount)
}

func (svc *AssetsLimit) Destroy(ctx context.Context, taskID string) error {
	svc.filter.Destroy(taskID)
	return nil
}
