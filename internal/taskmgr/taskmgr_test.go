package taskmgr

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/util"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"testing"
)

func TestDecimal(t *testing.T) {
	var retFloat float64

	retFloat = util.Decimal(0.01)
	assert.Equal(t, 0.01, retFloat)

	retFloat = util.Decimal(0.001)
	assert.Equal(t, 0.0, retFloat)

	retFloat = util.Decimal(0.00001)
	assert.Equal(t, 0.0, retFloat)

	retFloat = util.Decimal(1.1)
	assert.Equal(t, 1.1, retFloat)

	retFloat = util.Decimal(1.01)
	assert.Equal(t, 1.01, retFloat)

	retFloat = util.Decimal(1.001)
	assert.Equal(t, 1.0, retFloat)

	retFloat = util.Decimal(1.00001)
	assert.Equal(t, 1.0, retFloat)

	retFloat = util.Decimal(5.01)
	assert.Equal(t, 5.01, retFloat)

	retFloat = util.Decimal(10.2233)
	assert.Equal(t, 10.22, retFloat)
}

func TestTaskerMgr_GetRateProgress(t *testing.T) {
	var retStr string
	tmpHostname, err := util.GetHost()
	if err != nil {
		log.Fatal("[FATAL] get hostname failed", err)
	}

	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = "127.0.0.1:6379"

	cfg.Masscan.Dir = "./"
	cfg.Worker.BatchSize = 10
	cfg.Worker.SameIpMaxCount = 10

	// fmt.Printf("conf = +v\n\n", conf)

	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := db.NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())
	var mgrOpt []MgrOption
	taskBaseCfg := task.NewScantaskBaseConf(0, cfg.Masscan.Dir, tmpHostname, cfg.Masscan.BlackipList,
		cfg.Masscan.SourcePort, cfg.Worker.BatchSize, cfg.Worker.SameIpMaxCount, true, true, false)
	mgrOpt = append(mgrOpt, SetTaskBaseConf(taskBaseCfg))
	taskMgr := NewTaskMgr(dbAct, mgrOpt...)

	assert.NotNil(t, taskMgr)
	taskMgr.appendTaskEngine(task.ETMasscan)
	assert.Equal(t, 1, len(taskMgr.Engines))
	retStr, err = taskMgr.GetFullProgress("12.33")
	assert.NotNil(t, err)
	assert.Equal(t, "12.33", retStr)

	retStr = taskMgr.GetTaskKey(12)
	assert.Equal(t, "mk:scan_task:12", retStr)

	retBool := taskMgr.getUndoneEngine()
	assert.True(t, retBool)

	// get rate progress
	//batch 1
	retStr = taskMgr.getRateProgress("0.00", 1.0, 0.0, 19.9)
	assert.Equal(t, "0", retStr)
	retStr = taskMgr.getRateProgress("0.11", 1.0, 0.0, 19.9)
	assert.Equal(t, "0.11", retStr)
	retStr = taskMgr.getRateProgress("1.10", 1.0, 0.0, 19.9)
	assert.Equal(t, "1.1", retStr)
	retStr = taskMgr.getRateProgress("1.01", 1.0, 0.0, 19.9)
	assert.Equal(t, "1.01", retStr)
	retStr = taskMgr.getRateProgress("19.01", 1.0, 0.0, 19.9)
	assert.Equal(t, "19.01", retStr)
	retStr = taskMgr.getRateProgress("39.01", 1.0, 0.0, 19.9)
	assert.Equal(t, "19.9", retStr)
	retStr = taskMgr.getRateProgress("39.01", 1.0, 0.0, 19.9)
	assert.Equal(t, "19.9", retStr)
	// batch 2
	retStr = taskMgr.getRateProgress("0.00", 0.4, 39.9, 89.9)
	assert.Equal(t, "39.9", retStr)
	retStr = taskMgr.getRateProgress("0.11", 0.4, 39.9, 89.9)
	assert.Equal(t, "39.94", retStr)
	retStr = taskMgr.getRateProgress("1.50", 0.4, 39.9, 89.9)
	assert.Equal(t, "40.5", retStr)
	retStr = taskMgr.getRateProgress("1.01", 0.4, 39.9, 89.9)
	assert.Equal(t, "40.3", retStr)
	retStr = taskMgr.getRateProgress("19.01", 0.4, 39.9, 89.9)
	assert.Equal(t, "47.5", retStr)
	retStr = taskMgr.getRateProgress("45.45", 0.4, 39.9, 89.9)
	assert.Equal(t, "58.08", retStr)
	retStr = taskMgr.getRateProgress("99.01", 0.4, 39.9, 89.9)
	assert.Equal(t, "79.5", retStr)
	retStr = taskMgr.getRateProgress("99.01", 0.4, 39.9, 89.9)
	assert.Equal(t, "79.5", retStr)

	ports := "21,22,U:161"
	newPorts := taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:21,22,U:161,", newPorts)

	ports = ""
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "", newPorts)

	ports = "a:22"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:a:22,", newPorts)

	ports = "a:22:abcd"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,", newPorts)

	ports = "T:22:http"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,", newPorts)

	ports = "T:22:http,21"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,21,", newPorts)

	ports = "U:22:dns"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "U:22,", newPorts)

	ports = "U:22:dns,2233"
	newPorts = taskMgr.parseFoeyePorts(ports)
	//assert.Equal(t, "U:22,2233,", newPorts) 程序不对

	ports = "21,U:22:dns,2233"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:21,U:22,2233,", newPorts)

	ports = "U:21,U:22:dns"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "U:21,U:22,", newPorts)
}

func TestTaskmgr_ParseFoeyePorts(t *testing.T) {
	tmpHostname, err := util.GetHost()
	if err != nil {
		log.Fatal("[FATAL] get hostname failed", err)
	}

	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = "127.0.0.1:6379"

	cfg.Masscan.Dir = "./"
	cfg.Worker.BatchSize = 10
	cfg.Worker.SameIpMaxCount = 10
	// fmt.Printf("conf = +v\n\n", conf)

	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := db.NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())
	var mgrOpt []MgrOption
	taskBaseCfg := task.NewScantaskBaseConf(0, cfg.Masscan.Dir, tmpHostname, cfg.Masscan.BlackipList,
		cfg.Masscan.SourcePort, cfg.Worker.BatchSize, cfg.Worker.SameIpMaxCount, true, true, false)
	mgrOpt = append(mgrOpt, SetTaskBaseConf(taskBaseCfg))
	taskMgr := NewTaskMgr(dbAct, mgrOpt...)

	assert.NotNil(t, taskMgr)

	ports := "21,22,U:161"
	newPorts := taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:21,22,U:161,", newPorts)

	ports = ""
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "", newPorts)

	ports = "a:22"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:a:22,", newPorts)

	ports = "a:22:abcd"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,", newPorts)

	ports = "T:22:http"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,", newPorts)

	ports = "T:22:http,21"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:22,21,", newPorts)

	ports = "U:22:dns"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "U:22,", newPorts)

	//ports = "U:22:dns,2233"
	//newPorts = taskMgr.parseFoeyePorts(ports)
	//assert.Equal(t, "U:22,2233,", newPorts)

	ports = "21,U:22:dns,2233"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "T:21,U:22,2233,", newPorts)

	ports = "U:21,U:22:dns"
	newPorts = taskMgr.parseFoeyePorts(ports)
	assert.Equal(t, "U:21,U:22,", newPorts)

	taskMgr.SetFoundCallback(nil)
	taskMgr.SetTaskFinishCallback(nil)

	curEng := taskMgr.GetEngineType()
	assert.Equal(t, -1, int(curEng))

	curDir, _ := os.Getwd()
	fullIpFilename := curDir + "/scan_iplist_test.txt"
	// 如果文件存在，则先删除
	exists, _ := util.FileExists(fullIpFilename)
	if exists {
		err := util.DeleteFile(fullIpFilename)
		assert.Nil(t, err)
	}

	err = taskMgr.WriteFile(fullIpFilename, "127.0.0.1,**********,,")
	assert.Nil(t, err)
	exists, _ = util.FileExists(fullIpFilename)
	assert.True(t, exists)

	err = util.DeleteFile(fullIpFilename)
	assert.Nil(t, err)
}
