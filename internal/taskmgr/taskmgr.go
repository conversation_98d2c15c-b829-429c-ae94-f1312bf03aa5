package taskmgr

import (
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/util"
	"errors"
	"log"
	"os"
	"strconv"
	"strings"
)

type TaskerMgr struct {
	hostname      string                 // 主机名
	Engines       []*TaskEngine          //  所有scantask引擎列表
	CurEngine     *TaskEngine            // 当前引擎信息
	mgrStub       TaskMgrStub            // 根据场景执行具体操作
	dbAct         db.DbAction            // 数据库对象
	taskBaseConf  *task.ScantaskBaseConf // 基本任务对象结构体
	scantask      task.PortscanStub      // 具体任务处理对象
	foundResultCB FoundResultCallback    // 发现信息的回调函数
	taskFinishCB  TaskFinishCallback     // 整个任务结束后的回调
}

type TaskMgrStub interface {
	MgrHandlePaused() (<-chan [][4]string, error)                                                       // 继续之前的任务
	StartWholeTask(msg interface{}) error                                                               // 运行整个任务
	SetExecCallback(fne task.ExecCallback, fnp task.ProgressCallback, clpcb task.ChkLastProcedCallback) // 设置进度回调
	SetFoundCallback(fn FoundResultCallback)                                                            // 设置发现资产信息回调
	SetTaskFinishCallback(fn TaskFinishCallback)                                                        // 设置整个任务结束后的回调
	GetTaskKey(tid int) string                                                                          // 获取任务完整键
	GetFullProgress(progress string) (string, error)                                                    // 计算整体进度
	GetMsgDomains() ([]string, map[string]interface{})                                                  // 获取参数中的域名信息
	Stop() error                                                                                        // 停止扫描任务
	GetAttr() interface{}                                                                               // 获取扫描任务信息
	IsStoped() bool
	GetEngineType() task.EngineType
}

type FoundResultCallback func(<-chan [][4]string, map[string]interface{})
type TaskFinishCallback func(string, bool, bool)

func NewTaskMgr(dba db.DbAction, opts ...MgrOption) *TaskerMgr {
	tm := &TaskerMgr{
		dbAct: dba,
	}

	for _, opt := range opts {
		opt(tm)
	}

	return tm
}

func NewTaskObjMgr(typ util.ProgramType, mgr *TaskerMgr) TaskMgrStub {
	switch typ {
	case util.PTFofa:
		return NewFofaTaskMgr(mgr)

	case util.PTFoeye:
		return NewFoeyeTaskMgr(mgr)

	default:
		log.Fatal("[FATAL]  task mgr invalid typ")
	}

	return nil
}

// 追加任务引擎
func (tm *TaskerMgr) appendTaskEngine(etp task.EngineType) {
	tm.Engines = append(tm.Engines, NewTaskEngine(etp))
}

func (tm *TaskerMgr) getRateProgress(progress string, rate, minProg, maxProg float64) string {
	var tmpF float64
	tmpProgress := progress

	tmpF, err := strconv.ParseFloat(tmpProgress, 32)
	if err != nil {
		log.Println("[ERROR] tasker_mgr calc task progress failed:", err)
		return ""
	}

	tmpF = util.Decimal(tmpF * rate)
	if tmpF <= minProg {
		tmpF += minProg
	}
	if tmpF > maxProg {
		tmpF = maxProg
	}
	s64 := strconv.FormatFloat(tmpF, 'f', -1, 64)

	return s64
}

func (tm *TaskerMgr) GetFullProgress(progress string) (string, error) {
	newProgress := progress

	if tm.CurEngine == nil {
		log.Println("[ERROR] task_mgr get full progress curengine is nil")
		return newProgress, errors.New("invalid curengine")
	}

	// 多个任务的情况，进度是个比率
	if tm.CurEngine.ProgressRate < 1 && !tm.scantask.IsAssetExceedLimit() {
		tmpProgress := tm.getRateProgress(progress, tm.CurEngine.ProgressRate,
			tm.CurEngine.MinProgress, tm.CurEngine.MaxProgress)
		if tmpProgress != "" {
			newProgress = tmpProgress
		}
	}

	return newProgress, nil
}

func (tm *TaskerMgr) GetTaskKey(tid int) string {
	var newKey string

	taskid := strconv.Itoa(tid)
	newKey = tm.dbAct.GetKeyName("task_progress") + ":" + taskid

	return newKey
}

// 获取未完成引擎
func (tm *TaskerMgr) getUndoneEngine() bool {
	haveTask := false

	for i := 0; i < len(tm.Engines); i++ {
		tmpEngine := tm.Engines[i]
		if !tmpEngine.Finished {
			tm.CurEngine = tmpEngine
			haveTask = true

			break
		}
	}

	return haveTask
}

func (tm *TaskerMgr) SetFoundCallback(fn FoundResultCallback) {
	tm.foundResultCB = fn
}

func (tm *TaskerMgr) SetTaskFinishCallback(fn TaskFinishCallback) {
	tm.taskFinishCB = fn
}

/*
包含如下几种情况：
  tcp默认端口，不包含冒号
  tcp自定义端口，包含2个冒号
  udp默认端口，包含1个冒号
  udp自定义端口，包含2个冒号
*/
func (tm *TaskerMgr) parseOnePort(port string) string {
	var retPort string

	tmpArr := strings.Split(port, ":")
	if len(tmpArr) == 1 {
		// tcp默认端口
		retPort = tmpArr[0]
	} else if len(tmpArr) == 2 {
		// udp默认端口
		retPort = port
	} else {
		if tmpArr[0] == "U" {
			retPort = tmpArr[0] + ":" + tmpArr[1]
		} else {
			retPort = tmpArr[1]
		}
	}

	return retPort
}

func (tm *TaskerMgr) parseFoeyePorts(ports string) string {
	retPorts := ""
	if len(ports) <= 0 {
		return retPorts
	}

	portArr := strings.Split(ports, ",")
	for _, val := range portArr {
		tmpPort := tm.parseOnePort(val)
		if len(tmpPort) > 0 {
			retPorts += tmpPort + ","
		}
	}

	// 如果有TCP端口，加上T:，解决nmap在tcp端口上也扫描了udp的问题
	portsCount := strings.Count(retPorts, ",")
	udpCount := strings.Count(retPorts, "U:")
	if portsCount > udpCount {
		retPorts = "T:" + retPorts
	}

	return retPorts
}

// 将IP列表拆分后写文件
func (tm *TaskerMgr) WriteFile(filename, ctx string) error {
	fileTmp, err := os.OpenFile(filename, os.O_TRUNC|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return err
	}

	hostSlice := strings.Split(ctx, ",")
	for _, host := range hostSlice {
		if len(host) <= 0 {
			continue
		}

		_, err = fileTmp.Write([]byte(host + "\n"))
		if err != nil {
			return err
		}
	}

	return nil
}

func (tm *TaskerMgr) GetEngineType() task.EngineType {
	if tm.CurEngine == nil {
		return -1
	}

	return tm.CurEngine.Engine
}
