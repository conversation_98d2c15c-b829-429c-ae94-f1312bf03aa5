package taskmgr

import (
	"baimaohui/portscan_new/internal/task"
	"encoding/json"
	"github.com/pkg/errors"
	"log"
	"strconv"
	"strings"
)

func (ftm *FoeyeTaskMgr) SetExecCallback(fne task.ExecCallback, fnp task.ProgressCallback, clpcb task.ChkLastProcedCallback) {
	ftm.taskBaseConf.SetCallback(fne, fnp, nil)
}

func (ftm *FoeyeTaskMgr) GetMsgDomains() ([]string, map[string]interface{}) {
	options, _ := ftm.generateOptions()
	return ftm.attr.Domains, options
}

// 生成运行引擎
func (ftm *FoeyeTaskMgr) generateEngines(canOpenPing, canTreckScan bool) {
	if ftm.getDefaultEngine() == task.ETNmap {
		// 最少得有一个默认引擎
		ftm.appendTaskEngine(task.ETNmap)

		if canOpenPing {
			ftm.appendTaskEngine(task.ETNmapPing)
		}
	} else {
		// 最少得有一个默认引擎
		ftm.appendTaskEngine(task.ETMasscan)

		if canOpenPing {
			ftm.appendTaskEngine(task.ETMasscanPing)
		}

		if canTreckScan {
			ftm.appendTaskEngine(task.ETTreckScan)
		}
	}

	return
}

// 获取默认引擎，如果是二层或IPv6，固定为nmap
func (ftm *FoeyeTaskMgr) getDefaultEngine() task.EngineType {
	// IPv6默认为nmap
	if strings.Contains(ftm.attr.Hosts, ":") || ftm.attr.IsIPv6 {
		return task.ETNmap
	}

	// 判断输入参数
	if ftm.attr.TaskType == "common" {
		return task.ETNmap
	}

	return task.ETMasscan
}

func (ftm *FoeyeTaskMgr) WholeTaskInit() (initStop bool) {
	//删除执行ip信息
	delFlag := ftm.dbAct.DeleteKey(ftm.dbAct.GetKeyName("exec_ip"))
	if !delFlag {
		log.Println("[ERROR] worker whole_task_init del exec_ip failed")
	}

	// 生成运行引擎
	ftm.generateEngines(ftm.attr.CanOpenPing, ftm.attr.CanTreckScan)
	log.Println("  whole_task_init task:", ftm.attr)

	// 设置基础任务信息
	tid, _ := strconv.Atoi(ftm.attr.TaskId)
	ftm.Tid = tid
	ftm.dbAct.SetTaskStartTime(ftm.GetTaskKey(tid))

	// 设置进度比率信息
	ftm.calcProgressRate()

	// 判断是否停止状态
	initStop = ftm.isStoped()

	return
}

// 计算引擎进度比率
func (ftm *FoeyeTaskMgr) calcProgressRate() {
	taskCount := len(ftm.Engines)
	if taskCount <= 0 {
		log.Println("  task engine empty")
		return
	}

	// 获取总比率
	sumRate := 100.0
	if ftm.attr.CanOpenPing {
		sumRate = 90.0
		taskCount -= 1
	}
	avgRate := sumRate / float64(taskCount)
	log.Println("  calcProgressRate tid:", ftm.attr.TaskId, "sumRate:", sumRate, "avgRate:", avgRate, "newTaskCnt:", taskCount)

	// 设置进度比率（除ping外的）
	for i := 0; i < taskCount; i++ {
		ftm.Engines[i].ProgressRate = avgRate / 100.0
		ftm.Engines[i].MinProgress = float64(i) * avgRate
		ftm.Engines[i].MaxProgress = float64(i)*avgRate + avgRate
	}

	// ping只占10%，并且永远是最后执行的任务
	if ftm.attr.CanOpenPing {
		pingLoc := taskCount
		ftm.Engines[pingLoc].ProgressRate = 0.1
		ftm.Engines[pingLoc].MinProgress = 90.0
		ftm.Engines[pingLoc].MaxProgress = 99.9
	}

	log.Println("  task engine info tid:", ftm.attr.TaskId, ftm.Engines)
}

/*
删除停止标记
调用结束扫描worker
下发域名扫描任务
*/
func (ftm *FoeyeTaskMgr) wholeTaskFinish() {
	ftm.dbAct.DeleteKey(ftm.dbAct.GetKeyName("stop_task"))
	log.Println("  task_mgr whole finish task tid:", ftm.attr.TaskId, "initStop:", ftm.initStop, "runStop:", ftm.runStop)

	if ftm.taskFinishCB != nil {
		ftm.taskFinishCB(ftm.attr.TaskId, ftm.initStop, ftm.runStop)
	}

	// 重置状态
	ftm.CurEngine = nil
	ftm.Engines = nil
	ftm.initStop = false
	ftm.runStop = false
}

func (ftm *FoeyeTaskMgr) generateOptions() (opt map[string]interface{}, err error) {
	// 生成options信息，方便下传
	options, jmErr := json.Marshal(ftm.attr)
	if jmErr != nil {
		err = errors.Wrap(err, "task_mgr json generate options failed")
		return
	}

	jumErr := json.Unmarshal(options, &opt)
	if jumErr != nil {
		err = errors.Wrap(err, "task_mgr json to options failed")
		return
	}

	err = nil
	return
}

func (ftm *FoeyeTaskMgr) IsStoped() bool {
	return ftm.scantask.IsStopSt()
}
