package taskmgr

import (
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/task"
	"errors"
	"log"
	"time"
)

type FoeyeTaskMgr struct {
	Tid      int                // 任务id
	attr     parse.FoeyeTaskMsg // foeye任务属性
	initStop bool               // 是否在初始化的时候遇到停止任务
	runStop  bool               // 是否运行任务过程中遇到停止任务
	*TaskerMgr
}

func NewFoeyeTaskMgr(tm *TaskerMgr) *FoeyeTaskMgr {
	return &FoeyeTaskMgr{
		TaskerMgr: tm,
	}
}

func (ftm *FoeyeTaskMgr) GetAttr() interface{} {
	return ftm.attr
}

func (ftm *FoeyeTaskMgr) MgrHandlePaused() (<-chan [][4]string, error) {
	return nil, nil
}

func (ftm *FoeyeTaskMgr) StartWholeTask(msg interface{}) error {
	var runStoped bool
	if msg == nil {
		return errors.New("start_whole_task msg invalid nil")
	}

	defer ftm.wholeTaskFinish()

	if tmpAttr, ok := msg.(parse.FoeyeTaskMsg); ok {
		ftm.attr = tmpAttr
	} else {
		return errors.New("start_whole_task msg change failed")
	}

	initStoped := ftm.WholeTaskInit()
	if initStoped {
		ftm.initStop = true
		return nil
	}

	for {
		exits := ftm.getUndoneEngine()
		if !exits {
			log.Println("  start_whole_task all task finished  tid:", ftm.attr.TaskId)
			break
		}

		log.Println("   start_whole_task curengine  tid:", ftm.attr.TaskId, ftm.CurEngine)

		var err error
		runStoped, err = ftm.runSubTask()
		log.Printf("  start_whole_task subtask finish  tid:%s runStop:%t, err:%v\n",
			ftm.attr.TaskId, runStoped, err)
		if err != nil {
			ftm.initStop = true
			break
		}

		if runStoped {
			ftm.runStop = true
			break
		}
	}

	return nil
}

/*
运行一个子任务
第一个参数：初始化时候停止
第二个参数：运行过长中停止
*/
func (ftm *FoeyeTaskMgr) runSubTask() (bool, error) {
	stopedSt := ftm.subTaskInit()
	if stopedSt {
		return stopedSt, nil
	}

	if ftm.CurEngine.Engine == task.ETMasscan ||
		ftm.CurEngine.Engine == task.ETMasscanPing ||
		ftm.CurEngine.Engine == task.ETNmap ||
		ftm.CurEngine.Engine == task.ETNmapPing ||
		ftm.CurEngine.Engine == task.ETTreckScan ||
		ftm.CurEngine.Engine == task.ETMasscanWithPing ||
		ftm.CurEngine.Engine == task.ETNmapWithPing {
		_, err := ftm.runScan()
		if err != nil {
			return false, err
		}
	} else {
		log.Println("[ERROR] invalid task engine:", ftm.CurEngine.Engine)
	}

	finishSt := ftm.subTaskFinsh()
	if 1 == finishSt {
		stopedSt = true
	}

	return stopedSt, nil
}

func (ftm *FoeyeTaskMgr) runScan() (bool, error) {
	ftm.taskBaseConf.SetEngType(ftm.CurEngine.Engine)
	ftm.taskBaseConf.SetRouterMac(ftm.attr.GatewayMAC)
	ftm.scantask = task.NewScantask(ftm.taskBaseConf)
	if ftm.scantask == nil {
		log.Println("[ERROR] foeye_mgr create scantask failed")
		return false, errors.New("scantask create failed")
	}
	ftm.scantask.SetScanEngine(ftm.CurEngine)
	ftm.scantask.SetMaxAssetNum(ftm.attr.MaxAssetNum)
	ftm.scantask.SetScanIPv6(ftm.attr.IsIPv6)

	var ch <-chan [][4]string
	var err error
	blackIpList := ftm.dbAct.GetBlackIps()
	if len(blackIpList) > 0 {
		blackIpList += ","
	}
	blackIpList += ftm.attr.Blacklist
	if len(ftm.attr.ResumeFilename) > 0 && ftm.scantask.ResumeFileExists() { // 存在暂停文件的时候继续
		ch, err = ftm.scantask.HandlePaused(blackIpList, ftm.Tid)
		if err != nil {
			log.Println("[ERROR] foeye_mgr resume scan failed:", err)
			return false, err
		}
	} else {
		portscanPorts := ftm.parseFoeyePorts(ftm.attr.Ports) // 端口必须解析

		// 判断是否扫描列表为文件
		if len(ftm.attr.Hosts) <= 0 {
			log.Println("  foeye_mgr scan iplist file", ftm.attr.IpListFilename)
			ch, err = ftm.scantask.RunFile(ftm.attr.Bandwidth, ftm.attr.IpListFilename, portscanPorts,
				blackIpList, ftm.attr.SendEth, "", ftm.Tid, ftm.attr.RepeatTimes)
		} else {
			ch, err = ftm.scantask.Run(ftm.attr.Bandwidth, ftm.attr.Hosts, portscanPorts,
				blackIpList, ftm.attr.SendEth, "", ftm.Tid, ftm.attr.RepeatTimes)
		}
		if err != nil {
			log.Println("[ERROR] foeye_mgr run scan failed:", err)
			return false, err
		}
	}

	optMap, err := ftm.generateOptions()
	if err != nil {
		return false, err
	}
	ftm.foundResultCB(ch, optMap)

	return true, nil
}

func (ftm *FoeyeTaskMgr) isStoped() (stoped bool) {
	stopKey, err := ftm.dbAct.GetValue(ftm.dbAct.GetKeyName("stop_task"))
	if err != nil {
		log.Println("[ERROR] foeye_mgr chk stop failed:", err)
		return false
	}

	if stopKey == "1" {
		stoped = true
	}

	return
}

/*
子任务初始化，需要判断是否停止任务
*/
func (ftm *FoeyeTaskMgr) subTaskInit() bool {
	stopSt := ftm.isStoped()

	timeStart, _ := time.Parse("01/02/2006", "01/01/2019")
	ftm.dbAct.UpdateLastChkTime(timeStart)

	return stopSt
}

/*
任务结束处理，返回1的时候说明任务停止了
*/
func (ftm *FoeyeTaskMgr) subTaskFinsh() int {
	// 设置任务完成状态
	for i := 0; i < len(ftm.Engines); i++ {
		if ftm.Engines[i].Engine == ftm.CurEngine.Engine && !ftm.Engines[i].Finished {
			ftm.Engines[i].Finished = true
			break // 只能一次设置一个任务完成
		}
	}

	if ftm.scantask.IsStopSt() {
		log.Println("  subtask finish is stoped")
		return 1
	}

	return 0
}

func (ftm *FoeyeTaskMgr) Stop() error {
	if !ftm.scantask.IsStopSt() {
		log.Println("  task_mgr send stop task to process")

		go ftm.scantask.Stop() // 必须使用协程，不然卡死在这儿了
	}

	return nil
}
