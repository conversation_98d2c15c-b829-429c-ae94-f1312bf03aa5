package taskmgr

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/util"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"testing"
)

func TestNewFoeyeTaskMgr(t *testing.T) {
	tmpHostname, _ := util.GetHost()
	curDir, _ := os.Getwd()

	mockRds := initRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = mockRds.Addr()
	cfg.Masscan.Dir = curDir

	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := db.NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())

	var mgrOpt []MgrOption
	taskBaseCfg := task.NewScantaskBaseConf(0, cfg.Masscan.Dir, tmpHostname, cfg.Masscan.BlackipList,
		cfg.Masscan.SourcePort, cfg.Worker.BatchSize, cfg.Worker.SameIpMaxCount, true, true, false)
	mgrOpt = append(mgrOpt, SetTaskBaseConf(taskBaseCfg))
	taskMgr := NewTaskMgr(dbAct, mgrOpt...)
	assert.NotNil(t, taskMgr)
	foeyeMgr := NewTaskObjMgr(util.PTFoeye, taskMgr)
	assert.NotNil(t, foeyeMgr)

	foeyeMgr.SetExecCallback(nil, nil, nil)

	//var btmMsg parse.BaseTaskMsg
	var parseStub parse.ParseMsgStub
	parseStub = parse.NewMsgParse(util.PTFoeye)
	sendEth := "ifcfg-11"
	msg := "[{\"bandwidth\":\"150\",\"blacklist\":\"\",\"deep_get_mac\":false,\"ip_list\":\"127.0.0.1\",\"nmap_scan\":false,\"ping_scan\":false,\"deep_get_os\":false,\"ports\":\"21,22,23\",\"protocol_update_cycle\":0,\"unknown_protocol_indb\":false}]"
	msgInst, err := parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)

	// 正常扫描
	err = foeyeMgr.StartWholeTask(msgInst)
	assert.Nil(t, err)

	// 单端口扫描
	err = dbAct.SetHashValue(dbAct.GetKeyName("port_blacklist"), "21", "***************")
	assert.Nil(t, err)
	msg = "[{\"bandwidth\":\"150\",\"ip_list\":\"127.0.0.1\",\"ports\":\"21\", \"task_id\": \"10010\",\"scan_mode\":\"common\"}]"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)

	err = foeyeMgr.StartWholeTask(msgInst)
	assert.Nil(t, err)

	// ip存活扫描
	btmMsg := msgInst.(parse.FoeyeTaskMsg)
	btmMsg.IpList = "2406:da18:880:3800:3cf7:d90b:9468:f4a6"
	err = foeyeMgr.StartWholeTask(btmMsg)
	assert.Nil(t, err)

	// error msg
	err = foeyeMgr.StartWholeTask(nil)
	assert.NotNil(t, err)

	assert.NotNil(t, foeyeMgr.GetAttr())

	assert.False(t, foeyeMgr.IsStoped())

	domainCnt, _ := foeyeMgr.GetMsgDomains()
	assert.True(t, len(domainCnt) == 0)

	_, err = foeyeMgr.MgrHandlePaused()
	assert.Nil(t, err)

	remScanHostlistFile := curDir + "/scan_hostlist.txt"
	err = util.DeleteFile(remScanHostlistFile)
	assert.Nil(t, err)

	remBlackIplistFile := curDir + "/black_iplist.txt"
	err = util.DeleteFile(remBlackIplistFile)
	assert.Nil(t, err)
}

func TestFoeyeTaskMgr_GetAttr(t *testing.T) {
	tmpHostname, _ := util.GetHost()
	curDir, _ := os.Getwd()

	mockRds := initRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = mockRds.Addr()
	cfg.Masscan.Dir = curDir

	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "mk:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := db.NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())

	var mgrOpt []MgrOption
	taskBaseCfg := task.NewScantaskBaseConf(0, cfg.Masscan.Dir, tmpHostname, cfg.Masscan.BlackipList,
		cfg.Masscan.SourcePort, cfg.Worker.BatchSize, cfg.Worker.SameIpMaxCount, true, true, false)
	mgrOpt = append(mgrOpt, SetTaskBaseConf(taskBaseCfg))
	taskMgr := NewTaskMgr(dbAct, mgrOpt...)
	assert.NotNil(t, taskMgr)
	foeyeMgr := NewFoeyeTaskMgr(taskMgr)

	// 单端口扫描
	parseStub := parse.NewMsgParse(util.PTFoeye)
	err := dbAct.SetHashValue(dbAct.GetKeyName("port_blacklist"), "21", "***************")
	assert.Nil(t, err)
	msg := "[{\"bandwidth\":\"150\",\"ip_list\":\"127.0.0.1\",\"ports\":\"21\", \"task_id\": \"10010\",\"scan_mode\":\"common\"}]"
	msgInst, err := parseStub.ParseMsg([]byte(msg), "")
	assert.Nil(t, err)
	btmMsg := msgInst.(parse.FoeyeTaskMsg)
	btmMsg.CanOpenPing = true
	foeyeMgr.attr = btmMsg
	foeyeMgr.generateEngines(true, false)
	assert.Equal(t, 0, int(foeyeMgr.getDefaultEngine()))
	foeyeMgr.calcProgressRate()
	assert.Equal(t, 2, len(foeyeMgr.Engines))
	curEngine := foeyeMgr.Engines[0]
	assert.Equal(t, 0.00, curEngine.MinProgress)
	assert.Equal(t, 90.00, curEngine.MaxProgress)

	// 生成参数
	mpOpt, err := foeyeMgr.generateOptions()
	assert.Nil(t, err)
	assert.Equal(t, "127.0.0.1", mpOpt["ip_list"].(string))

	// 进度
	assert.True(t, foeyeMgr.getUndoneEngine())
	foeyeMgr.scantask = task.NewScantask(foeyeMgr.taskBaseConf)
	progress, err := foeyeMgr.GetFullProgress("10.10")
	assert.Nil(t, err)
	assert.Equal(t, "9.09", progress)

	assert.Equal(t, 0, foeyeMgr.subTaskFinsh())

	// nmap
	btmMsg.TaskType = "common"
	foeyeMgr.Engines = nil
	foeyeMgr.attr = btmMsg
	foeyeMgr.generateEngines(false, false)
	assert.Equal(t, 1, int(foeyeMgr.getDefaultEngine()))

	// ipv6
	btmMsg.IsIPv6 = true
	btmMsg.CanOpenPing = true
	foeyeMgr.Engines = nil
	foeyeMgr.attr = btmMsg
	foeyeMgr.generateEngines(true, false)
	assert.Equal(t, 1, int(foeyeMgr.getDefaultEngine()))

	assert.True(t, foeyeMgr.getUndoneEngine())
	assert.Equal(t, 1, int(foeyeMgr.GetEngineType()))

	// 暂停
	assert.False(t, foeyeMgr.isStoped())

	foeyeMgr.dbAct.SetValue(foeyeMgr.dbAct.GetKeyName("stop_task"), "1")
	assert.True(t, foeyeMgr.isStoped())
}
