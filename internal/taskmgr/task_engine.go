package taskmgr

import "baimaohui/portscan_new/internal/task"

type TaskEngine struct {
	ScanClass    interface{}     // 扫描对象类（masscan/nmap）
	Engine       task.EngineType // 任务引擎
	State        int             // 0：未执行，1：调度中，2：执行中，3：停止中，4：已停止，5：成功，6：失败
	Finished     bool            // 是否结束
	ProgressRate float64         // 进度比率（有多个任务的时候比率不是100%）
	MinProgress  float64         // 当前引擎最小进度
	MaxProgress  float64         // 当前引擎最大进度
}

func NewTaskEngine(etp task.EngineType) *TaskEngine {
	return &TaskEngine{
		Engine: etp,
	}
}
