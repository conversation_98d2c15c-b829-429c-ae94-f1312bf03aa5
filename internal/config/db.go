package config

type RedisConf struct {
	Addr            string `yaml:"addr" toml:"Addr"`
	Db              int    `yaml:"db" toml:"db"`
	Prefix          string `yaml:"prefix" toml:"prefix"`
	Password        string `yaml:"password" toml:"password"`
	BlackCount      int    `yaml:"black_count" toml:"black_count"`
	StopTask<PERSON>ey     string `yaml:"stop_task_key" toml:"stop_task_key"`
	ExecIpKey       string `yaml:"exec_ip_key" toml:"exec_ip_key"`
	TaskProgressKey string `yaml:"task_progress_key" toml:"task_progress_key"`
}
