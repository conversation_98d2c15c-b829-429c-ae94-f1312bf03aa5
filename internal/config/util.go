package config

type LogConf struct {
	Output    string `yaml:"output"`
	ErrOutput string `yaml:"err_output" toml:"err_output"`
}

type PprofConf struct {
	Enable bool `yaml:"enable"`
	Port   int  `yaml:"port"`
}

type WorkerConf struct {
	BatchSize      int    `toml:"batch_size"`
	SameIpMaxCount int    `toml:"same_ip_max_count"`
	SendEth        string `toml:"send_eth"`
	ReserveInnerIp bool   `toml:"reserve_inner_ip"`
}

type RPCConf struct {
	Enable bool   `toml:"enable"`
	Addr   string `toml:"addr"`
}

type ConsumerConfig struct {
	Sidekiq *SidekiqConfig `yaml:"sidekiq"`
	Redis   *Redis         `yaml:"redis"`
	Kafka   *KafkaConsumer `yaml:"kafka"`
}

type ProducerConfig struct {
	Redis *Redis         `yaml:"redis"`
	Kafka *KafkaProducer `yaml:"kafka"`
}
