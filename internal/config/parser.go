package config

import (
	"io/ioutil"
	"log"
	"path/filepath"

	"github.com/BurntSushi/toml"
	"github.com/pkg/errors"
)

type Parser struct {
	dir string
}

func (p *Parser) read(filename string) (data []byte, err error) {
	if p.dir == "" { // 第一次解析配置，记录基础目录
		filename, err = filepath.Abs(filename)
		if err != nil {
			err = errors.WithStack(err)
			return
		}
		p.dir = filepath.Dir(filename)
	} else if !filepath.IsAbs(filename) { // 引用其它的配置，相对路径转为绝对路径
		filename = filepath.Join(p.dir, filename)
	}
	log.Println("parse config file:", filename)
	data, err = ioutil.ReadFile(filename)
	if err != nil {
		err = errors.Wrapf(err, "can not open config file %s", filename)
		return
	}
	return
}

func (p *Parser) Parse(filename string, v interface{}) error {
	data, err := p.read(filename)
	if err != nil {
		return errors.WithStack(err)
	}
	return toml.Unmarshal(data, v)
}
