package config

import (
	"flag"
	"log"
)

var defaultParser Parser
var ScanIps, ScanPorts, BandWidth, ScanTaskid string

func MustGet(v interface{}) {
	err := Get(v)
	if err != nil {
		log.Fatal("error config:", err)
	}
}

func Get(v interface{}) error {
	filename := flag.String("c", "conf.toml", "config file")
	flag.StringVar(&ScanIps, "sh", "127.0.0.1", "scan hosts")
	flag.StringVar(&ScanPorts, "sp", "21,22,23,25,465,80,8080,443,8443,6443", "scan ports")
	flag.StringVar(&BandWidth, "sbw", "100", "scan bandwidth")
	flag.StringVar(&ScanTaskid, "st", "1", "scan task_id")
	flag.Parse()
	return Parse(*filename, v)
}

func Parse(filename string, v interface{}) error {
	return defaultParser.Parse(filename, v)
}
