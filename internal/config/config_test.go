package config

import (
	"os"
	"reflect"
	"testing"
)

func TestReadConfig(t *testing.T) {
	os.Setenv("HTTP_ADDR", "127.0.0.1:61234")
	tests := []struct {
		name string
		want *Config
	}{
		{
			name: "pass",
			want: &Config{
				Http: Http{
					Addr: "127.0.0.1:61234",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ReadConfig(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("ReadConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}
