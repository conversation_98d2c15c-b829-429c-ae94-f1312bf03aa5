package config

import (
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
	"time"
)

type Config struct {
	NodeID      string      `json:"node_id" yaml:"node_id" mapstructure:"node_id"`
	WorkerMax   int         `json:"worker_max" yaml:"worker_max" mapstructure:"worker_max"`
	CrawlerConf CrawlerConf `json:"crawler" yaml:"crawler" mapstructure:"crawler"`
}

type CrawlerConf struct {
	Debug                    bool          `json:"debug" yaml:"debug" mapstructure:"debug"`
	Type                     string        `json:"type" yaml:"type" mapstructure:"type"`
	Scope                    uint8         `json:"scope" yaml:"scope" mapstructure:"scope"`
	MaxLinks                 int           `json:"max_links" yaml:"max_links" mapstructure:"max_links"`
	MaxCrawlLinks            int           `json:"max_crawl_links" yaml:"max_crawl_links" mapstructure:"max_crawl_links"`
	MaxCrawlLinksPerHost     int           `json:"max_crawl_links_per_host" yaml:"max_crawl_links_per_host" mapstructure:"max_crawl_links_per_host"`
	LoadTimeout              time.Duration `json:"load_timeout" yaml:"load_timeout" mapstructure:"load_timeout"`
	UniqParams               bool          `json:"uniq_params" yaml:"uniq_params" mapstructure:"uniq_params"`
	AutoScanDirsFromURL      bool          `json:"auto_scan_dirs_from_url" yaml:"auto_scan_dirs_from_url" mapstructure:"auto_scan_dirs_from_url"`
	ScanIndexPageWithDirList bool          `json:"scan_index_page_with_dir_list" yaml:"scan_index_page_with_dir_list" mapstructure:"scan_index_page_with_dir_list"`
	ExcludeUrlFilters        string        `json:"exclude_url_filters" yaml:"exclude_url_filters" mapstructure:"exclude_url_filters"`
}

func ReadConfig() *Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Fatal("can't find the config.yaml file.")
	}

	cc := new(Config)
	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config.yaml failed.")
	}

	return cc
}
