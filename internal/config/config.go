package config

import (
	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"go-micro.dev/v4/logger"
)

type Config struct {
	NodeID string
	Http   Http
}

type Http struct {
	Addr string
}

func ReadConfig() *Config {
	enc := yaml.NewEncoder()

	c, _ := config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(enc),
			),
		),
		config.WithSource(env.NewSource()),
	)
	if err := c.Load(file.NewSource(file.WithPath("./config.yaml"))); err != nil {
		logger.Warn("can't find the config.yaml file. ")
	}

	cc := new(Config)

	if err := c.Scan(&cc); err != nil {
		logger.Fatal("read config.yaml failed.")
	}

	return cc
}
