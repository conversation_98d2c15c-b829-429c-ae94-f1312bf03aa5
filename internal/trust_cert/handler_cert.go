package trust_cert

import (
	"crypto/x509"
	"git.gobies.org/longzhuan/go_common/mcert"
	"strconv"
	"strings"
	"time"
)

type Certs struct {
	V          string   `json:"v,omitempty"`           //Version
	SN         string   `json:"sn,omitempty"`          //SerialNumber
	SigAlth    string   `json:"sig_alth,omitempty"`    //"SignatureAlgorithm",
	IssuerOrg  []string `json:"issuer_org,omitempty"`  //"Issuer.Organization",
	IssuerCn   string   `json:"issuer_cn,omitempty"`   //"Issuer.CommonName",
	IssuerCNS  []string `json:"issuer_cns,omitempty"`  //"Issuer all CommonName",
	SubjectOrg []string `json:"subject_org,omitempty"` //"Subject.Organization",
	SubjectCn  string   `json:"subject_cn,omitempty"`  //"Subject.CommonName",
	SubjectCNS []string `json:"subject_cns,omitempty"` //"Subject all CommonName",
	NotBefore  string   `json:"not_before,omitempty"`  //"NotBefore",
	NotAfter   string   `json:"not_after,omitempty"`   //"NotAfter"
	Domain     string   `json:"domain,omitempty"`      //"domain from DNS name"
	IsValid    bool     `json:"is_valid"`              // cert is valid
	IsMatch    bool     `json:"is_match"`              //证书是否匹配所请求的域名
	IsExpired  bool     `json:"is_expired"`            //证书是否在有效范围
	ValidType  string   `json:"valid_type,omitempty"`  // valid type
	CertDate   string   `json:"cert_date,omitempty"`   // 证书导出日期
	CertNum    int      `json:"cert_num,omitempty"`    // 证书个数
}

func GetCertsObj(lenCert int, sCert string, crt *x509.Certificate, allCert []*x509.Certificate, host string, isDomain bool) Certs {
	issuerCN := getIssuerCN(crt)
	subjectCN := getSubjectCN(crt)
	tmpCerts := Certs{
		V:          "v" + strconv.Itoa(crt.Version),
		SN:         crt.SerialNumber.String(),
		SigAlth:    crt.SignatureAlgorithm.String(),
		IssuerOrg:  crt.Issuer.Organization,
		IssuerCn:   issuerCN,
		SubjectOrg: crt.Subject.Organization,
		SubjectCn:  subjectCN,
		NotBefore:  crt.NotBefore.Format("2006-01-02 15:04:05"),
		NotAfter:   crt.NotAfter.Format("2006-01-02 15:04:05"),
		IsMatch:    true,
	}

	var allIssuerCN, allSubjectCN []string
	if lenCert > 1 {
		tmpCerts.CertNum = lenCert

		// 提取所有颁发者和使用者
		for _, ct := range allCert {
			allIssuerCN = append(allIssuerCN, getIssuerCN(ct))

			allSubjectCN = append(allSubjectCN, getSubjectCN(ct))
		}
	} else {
		allIssuerCN = append(allIssuerCN, issuerCN)
		allSubjectCN = append(allSubjectCN, subjectCN)
	}

	tmpCerts.IssuerCNS = allIssuerCN
	//tmpCerts.SubjectCNS = allSubjectCN

	// dns
	if len(crt.DNSNames) > 0 {
		tmpCerts.Domain = crt.DNSNames[0]
	}

	// 证书有效性
	valid, vType := verifyCertValid(sCert, crt, allCert)
	tmpCerts.IsValid = valid
	tmpCerts.CertDate = certImportTime
	if !valid && len(vType) > 0 {
		tmpCerts.ValidType = vType
	}

	//证书跟域名是否匹配
	if isDomain {
		tmpCerts.IsMatch = mcert.VerifyCertMatch(tmpCerts.IsValid, host, crt)
	} else {
		tmpCerts.IsMatch = mcert.VerifyCertMatch(tmpCerts.IsValid, "", crt)
	}

	// 检查证书是否过期
	//证书有效并且此刻在结束时间之后
	if tmpCerts.IsValid && time.Now().After(crt.NotAfter) {
		tmpCerts.IsExpired = true
	}

	return tmpCerts
}

func getIssuerCN(ct *x509.Certificate) string {
	tmpIssuerCN := ct.Issuer.CommonName
	if len(tmpIssuerCN) <= 0 && len(ct.Issuer.OrganizationalUnit) > 0 {
		tmpIssuerCN = ct.Issuer.OrganizationalUnit[0]
	}

	return tmpIssuerCN
}

func getSubjectCN(ct *x509.Certificate) string {
	tmpSubjectCN := ct.Subject.CommonName
	if len(tmpSubjectCN) <= 0 && len(ct.Subject.OrganizationalUnit) > 0 {
		tmpSubjectCN = ct.Subject.OrganizationalUnit[0]
	}

	return tmpSubjectCN
}

func verifyCertValid(cert string, stCert *x509.Certificate, allCert []*x509.Certificate) (bool, string) {
	valid := true
	vType := ""

	if strings.Contains(stCert.SignatureAlgorithm.String(), "self-signed") ||
		strings.Contains(cert, "self-signed") {
		return false, "SelfSigned"
	}

	// 少字断的
	if stCert.Version <= 0 || len(stCert.SerialNumber.String()) <= 0 || len(stCert.SignatureAlgorithm.String()) <= 0 ||
		(len(stCert.Issuer.Organization) <= 0 && len(stCert.Issuer.CommonName) <= 0) ||
		(len(stCert.Subject.Organization) <= 0 && len(stCert.Subject.CommonName) <= 0) ||
		!strings.Contains(cert, "Not Before") || !strings.Contains(cert, "Not After") {
		return false, "MissField"
	}

	// 根据可信证书判断
	trusted := false
	enter := false
	if len(trustedCerts) > 0 {
		enter = true
	}
	for _, oneCert := range allCert {
		tmpIssuerCN := getIssuerCN(oneCert)

		for _, tcert := range trustedCerts {
			if tmpIssuerCN == tcert {
				trusted = true
				break
			}
		}

		// 只要链上有一个可信的就算可信
		if trusted {
			break
		}
	}
	// 有证书信息才判断有效性
	if enter && !trusted {
		valid = false
		vType = "Untrust"
	}

	return valid, vType
}
