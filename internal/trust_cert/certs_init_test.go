package trust_cert

import (
	"crypto/x509"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestInitTrustCerts(t *testing.T) {
	InitTrustCerts("./trusted_cert_test.txt")
	assert.Equal(t, 5, len(trustedCerts))
	assert.Equal(t, "360 OV Server CA G2", trustedCerts[0])
	assert.Equal(t, "Acunetix WVS Root CA", trustedCerts[4])
	t.<PERSON>g(trustedCerts)
}

func TestGetCertsObj(t *testing.T) {
	lenCert := 2
	sCert := `Version:  v3
Serial Number: 9125946881735400482959016744378213965
Signature Algorithm: SHA256-RSA

Issuer:
  Country: US
  Organization: DigiCert Inc
  Organizational Unit: www.digicert.com
  CommonName: Encryption Everywhere DV TLS CA - G1

Validity:
  Not Before: 2022-08-25 00:00 UTC
  Not After : 2023-08-25 23:59 UTC

Subject:
  CommonName: *.fofa.info

Subject Public Key Info:
  Public Key Algorithm: RSA
  Public Key:
    Exponent: 65537
    Public Key Modulus: (2048 bits) :
      F0:63:B8:B5:FC:62:74:3A:4A:B9:E7:9D:A1:A2:88:7F:
      73:84:63:1A:81:B7:75:17:BB:8F:B0:87:1E:14:73:1E:
      5A:4C:22:36:B4:FE:2F:B9:5A:96:0A:58:4B:BB:31:D4:
      76:CB:12:C3:AC:56:8A:52:23:CC:F2:23:39:91:73:F7:
      EF:71:61:35:9E:5F:49:F6:52:3A:4C:7E:D3:52:47:57:
      5F:DA:27:BE:F8:64:A1:B7:49:A1:B5:CE:C4:C4:ED:41:
      24:7E:E2:D9:77:8B:AB:9F:A5:D0:5E:CF:48:F6:8A:36:
      AA:30:AE:E8:58:A3:34:A9:4A:27:0A:22:16:03:13:19:
      D3:88:DD:E9:AF:50:E3:BC:83:B0:20:3F:4B:1E:32:AF:
      D9:5F:3F:25:D8:61:2B:DA:E3:A9:FE:A7:F8:7F:AB:E6:
      55:CC:50:DF:17:04:B8:D4:E5:FD:F1:9B:E8:DE:A3:18:
      A1:B8:28:D1:2B:AF:AB:E8:6A:00:E7:9A:97:11:EE:0F:
      C6:1F:DF:08:64:EC:AC:5F:96:99:F1:6F:65:A8:6D:CC:
      EA:B6:05:B8:F7:B7:54:02:BB:EC:BE:34:C1:A6:1E:F8:
      79:6D:57:32:09:9F:75:30:C0:64:17:4E:F9:83:CE:AE:
      D9:8A:F2:55:FD:52:61:42:47:4C:FA:E0:F7:57:33:95

Authority Key Identifier:
  55:74:4F:B2:72:4F:F5:60:BA:50:D1:D7:E6:51:5C:9A:01:87:1A:D7

Subject Key Identifier:
  54:C9:29:FB:B4:5A:69:54:CB:24:0B:6B:10:FA:56:0C:BB:2A:A2:0D

Basic Constraints:
  CA : false
  Path Length Constraint: UNLIMITED

OCSP Server:
  http://ocsp.digicert.com

Issuing Certificate URL:
  http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G1.crt

Key Usage:
  Digital Signature
  Key Encipherment

Extended Key Usage:
  Server Auth
  Client Auth

DNS Names:
  *.fofa.info
  fofa.info

Certificate Signature Algorithm: SHA256-RSA
Certificate Signature:
  4E:C9:8D:AD:EC:2D:9C:52:C2:DF:34:7A:82:50:E9:A0:
  C8:CD:9D:D1:A5:A0:57:1D:09:63:D3:FB:18:3D:3B:06:
  9B:E4:19:32:22:DB:9B:3D:EC:B3:E6:17:DB:56:1F:D5:
  A8:7F:F0:8E:FA:61:0F:BD:83:2A:28:98:8E:EB:0A:56:
  EE:11:6F:C0:4B:3D:1D:8B:61:B3:CF:DB:A0:02:C9:AC:
  08:35:96:7C:D6:E6:9D:84:80:31:67:09:16:97:B5:F9:
  35:CF:E4:38:16:2D:37:0A:FF:6B:14:F4:E9:79:4E:38:
  41:A1:CB:98:F1:8E:0D:0C:02:90:56:30:E7:E7:4A:C7:
  30:CD:8C:D4:23:34:53:7F:D6:CF:30:2F:31:B8:05:77:
  D0:05:01:6E:94:5E:90:46:5E:B0:CB:8F:E3:4B:4C:CC:
  DA:15:D8:D1:FE:8E:9F:1E:16:E9:02:CB:3A:97:2B:A4:
  D7:D2:B7:56:E5:2C:D3:48:EE:35:4A:87:CE:0C:24:DC:
  A6:A3:65:85:9F:F5:83:52:78:D5:F1:EA:CC:D0:5F:A2:
  2A:DD:09:AC:D6:39:7F:50:A5:FC:B5:0A:09:95:71:F4:
  41:67:55:99:E3:B6:D5:08:B9:24:76:C5:94:58:1E:13:
  56:79:51:2C:BB:0A:D0:32:75:01:4E:52:EB:4F:A6:B6
firc {"Raw":"MIIF9jCCBN6gAwIBAgIQBt2X631wSAhs94cBCZimTTANBgkqhkiG9w0BAQsFADBuMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cuZGlnaWNlcnQuY29tMS0wKwYDVQQDEyRFbmNyeXB0aW9uIEV2ZXJ5d2hlcmUgRFYgVExTIENBIC0gRzEwHhcNMjIwODI1MDAwMDAwWhcNMjMwODI1MjM1OTU5WjAWMRQwEgYDVQQDDAsqLmZvZmEuaW5mbzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPBjuLX8YnQ6SrnnnaGiiH9zhGMagbd1F7uPsIceFHMeWkwiNrT+L7lalgpYS7sx1HbLEsOsVopSI8zyIzmRc/fvcWE1nl9J9lI6TH7TUkdXX9onvvhkobdJobXOxMTtQSR+4tl3i6ufpdBez0j2ijaqMK7oWKM0qUonCiIWAxMZ04jd6a9Q47yDsCA/Sx4yr9lfPyXYYSva46n+p/h/q+ZVzFDfFwS41OX98Zvo3qMYobgo0Suvq+hqAOealxHuD8Yf3whk7Kxflpnxb2WobczqtgW497dUArvsvjTBph74eW1XMgmfdTDAZBdO+YPOrtmK8lX9UmFCR0z64PdXM5UCAwEAAaOCAuYwggLiMB8GA1UdIwQYMBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrXMB0GA1UdDgQWBBRUySn7tFppVMskC2sQ+lYMuyqiDTAhBgNVHREEGjAYggsqLmZvZmEuaW5mb4IJZm9mYS5pbmZvMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwPgYDVR0gBDcwNTAzBgZngQwBAgEwKTAnBggrBgEFBQcCARYbaHR0cDovL3d3dy5kaWdpY2VydC5jb20vQ1BTMIGABggrBgEFBQcBAQR0MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQwCQYDVR0TBAIwADCCAX4GCisGAQQB1nkCBAIEggFuBIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWBMA0GCSqGSIb3DQEBCwUAA4IBAQBOyY2t7C2cUsLfNHqCUOmgyM2d0aWgVx0JY9P7GD07BpvkGTIi25s97LPmF9tWH9Wof/CO+mEPvYMqKJiO6wpW7hFvwEs9HYths8/boALJrAg1lnzW5p2EgDFnCRaXtfk1z+Q4Fi03Cv9rFPTpeU44QaHLmPGODQwCkFYw5+dKxzDNjNQjNFN/1s8wLzG4BXfQBQFulF6QRl6wy4/jS0zM2hXY0f6Onx4W6QLLOpcrpNfSt1blLNNI7jVKh84MJNymo2WFn/WDUnjV8erM0F+iKt0JrNY5f1Cl/LUKCZVx9EFnVZnjttUIuSR2xZRYHhNWeVEsuwrQMnUBTlLrT6a2","RawTBSCertificate":"MIIE3qADAgECAhAG3ZfrfXBICGz3hwEJmKZNMA0GCSqGSIb3DQEBCwUAMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTAeFw0yMjA4MjUwMDAwMDBaFw0yMzA4MjUyMzU5NTlaMBYxFDASBgNVBAMMCyouZm9mYS5pbmZvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQABo4IC5jCCAuIwHwYDVR0jBBgwFoAUVXRPsnJP9WC6UNHX5lFcmgGHGtcwHQYDVR0OBBYEFFTJKfu0WmlUyyQLaxD6Vgy7KqINMCEGA1UdEQQaMBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8wDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjA+BgNVHSAENzA1MDMGBmeBDAECATApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwgYAGCCsGAQUFBwEBBHQwcjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEoGCCsGAQUFBzAChj5odHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRW5jcnlwdGlvbkV2ZXJ5d2hlcmVEVlRMU0NBLUcxLmNydDAJBgNVHRMEAjAAMIIBfgYKKwYBBAHWeQIEAgSCAW4EggFqAWgAdgDoPtDaPvUGNTLnVyi8iWvJA9PL0RFr7Otp4Xd9bQa9bgAAAYLUKYceAAAEAwBHMEUCIQCFy5T931dX6oSm35Bc5L1TBC01EZ3XkdjRsULhJMRaPgIgTclu837GgwJeIIvUvNtGoptTyugue2mW/SQBEZt18dIAdgA1zxkbv7FsV78PrUxtQsu7ticgJlHqP+Eq76gDwzvWTAAAAYLUKYZkAAAEAwBHMEUCIQDmfK2Mrt1QIXfOVqZJzhi6tMNGjqF9r32GzyEina3fhAIgQg6O2nHBwyMFYt/UX2e2OBUNN0U9RNgvokaXsqteVdMAdgCzc3cH4YRQ+GOG1gWp3BEJSnktsWcMC4fc8AMOeTalmgAAAYLUKYbMAAAEAwBHMEUCIQDDbU6JncBr6HmPKBuaYxvAIN2tUVr/+jDasGB9+UmCvwIgVmaLxim/hziL0UB3IGNDAYcwYoFoH3/+X9rhvTKZdYE=","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQAB","RawSubject":"MBYxFDASBgNVBAMMCyouZm9mYS5pbmZv","RawIssuer":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","Signature":"TsmNrewtnFLC3zR6glDpoMjNndGloFcdCWPT+xg9Owab5BkyItubPeyz5hfbVh/VqH/wjvphD72DKiiYjusKVu4Rb8BLPR2LYbPP26ACyawINZZ81uadhIAxZwkWl7X5Nc/kOBYtNwr/axT06XlOOEGhy5jxjg0MApBWMOfnSscwzYzUIzRTf9bPMC8xuAV30AUBbpRekEZesMuP40tMzNoV2NH+jp8eFukCyzqXK6TX0rdW5SzTSO41SofODCTcpqNlhZ/1g1J41fHqzNBfoirdCazWOX9Qpfy1CgmVcfRBZ1WZ47bVCLkkdsWUWB4TVnlRLLsK0DJ1AU5S60+mtg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"PublicKey":{"N":30346367709519998192436886781530448102306271332455263400212115984683729977524175736349530311192719802886143232251643623202558651273627293040049867156228630519372930452342137097257512145117972676944388599148999721176168943428603255669511391729651354091528276048347410886933731648186607876347929422404995585813188692027295974423580143190279947826308222749788050984291105494435788199548665553329382936015342999709137976197060241457933471417530151429098881342535568495010914838173318160659769215528518176799315342134557930702202720980708060175373739625647208947141354839288754939451243425264187626586024902374331872916373,"E":65537},"Version":3,"SerialNumber":9125946881735400482959016744378213965,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"Subject":{"Country":null,"Organization":null,"OrganizationalUnit":null,"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"*.fofa.info","Names":[{"Type":[2,5,4,3],"Value":"*.fofa.info"}],"ExtraNames":null},"NotBefore":"2022-08-25T00:00:00Z","NotAfter":"2023-08-25T23:59:59Z","KeyUsage":5,"Extensions":[{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrX"},{"Id":[2,5,29,14],"Critical":false,"Value":"BBRUySn7tFppVMskC2sQ+lYMuyqiDQ=="},{"Id":[2,5,29,17],"Critical":false,"Value":"MBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8="},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIFoA=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,32],"Critical":false,"Value":"MDUwMwYGZ4EMAQIBMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQUw=="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQ="},{"Id":[2,5,29,19],"Critical":false,"Value":"MAA="},{"Id":[1,3,6,1,4,1,11129,2,4,2],"Critical":false,"Value":"BIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":false,"MaxPathLen":-1,"MaxPathLenZero":false,"SubjectKeyId":"VMkp+7RaaVTLJAtrEPpWDLsqog0=","AuthorityKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":["http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G1.crt"],"DNSNames":["*.fofa.info","fofa.info"],"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":null,"PolicyIdentifiers":[[2,23,140,1,2,1]]}
pcf [{"Raw":"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","RawTBSCertificate":"MIIE3qADAgECAhAG3ZfrfXBICGz3hwEJmKZNMA0GCSqGSIb3DQEBCwUAMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTAeFw0yMjA4MjUwMDAwMDBaFw0yMzA4MjUyMzU5NTlaMBYxFDASBgNVBAMMCyouZm9mYS5pbmZvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQABo4IC5jCCAuIwHwYDVR0jBBgwFoAUVXRPsnJP9WC6UNHX5lFcmgGHGtcwHQYDVR0OBBYEFFTJKfu0WmlUyyQLaxD6Vgy7KqINMCEGA1UdEQQaMBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8wDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjA+BgNVHSAENzA1MDMGBmeBDAECATApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwgYAGCCsGAQUFBwEBBHQwcjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEoGCCsGAQUFBzAChj5odHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRW5jcnlwdGlvbkV2ZXJ5d2hlcmVEVlRMU0NBLUcxLmNydDAJBgNVHRMEAjAAMIIBfgYKKwYBBAHWeQIEAgSCAW4EggFqAWgAdgDoPtDaPvUGNTLnVyi8iWvJA9PL0RFr7Otp4Xd9bQa9bgAAAYLUKYceAAAEAwBHMEUCIQCFy5T931dX6oSm35Bc5L1TBC01EZ3XkdjRsULhJMRaPgIgTclu837GgwJeIIvUvNtGoptTyugue2mW/SQBEZt18dIAdgA1zxkbv7FsV78PrUxtQsu7ticgJlHqP+Eq76gDwzvWTAAAAYLUKYZkAAAEAwBHMEUCIQDmfK2Mrt1QIXfOVqZJzhi6tMNGjqF9r32GzyEina3fhAIgQg6O2nHBwyMFYt/UX2e2OBUNN0U9RNgvokaXsqteVdMAdgCzc3cH4YRQ+GOG1gWp3BEJSnktsWcMC4fc8AMOeTalmgAAAYLUKYbMAAAEAwBHMEUCIQDDbU6JncBr6HmPKBuaYxvAIN2tUVr/+jDasGB9+UmCvwIgVmaLxim/hziL0UB3IGNDAYcwYoFoH3/+X9rhvTKZdYE=","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQAB","RawSubject":"MBYxFDASBgNVBAMMCyouZm9mYS5pbmZv","RawIssuer":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","Signature":"TsmNrewtnFLC3zR6glDpoMjNndGloFcdCWPT+xg9Owab5BkyItubPeyz5hfbVh/VqH/wjvphD72DKiiYjusKVu4Rb8BLPR2LYbPP26ACyawINZZ81uadhIAxZwkWl7X5Nc/kOBYtNwr/axT06XlOOEGhy5jxjg0MApBWMOfnSscwzYzUIzRTf9bPMC8xuAV30AUBbpRekEZesMuP40tMzNoV2NH+jp8eFukCyzqXK6TX0rdW5SzTSO41SofODCTcpqNlhZ/1g1J41fHqzNBfoirdCazWOX9Qpfy1CgmVcfRBZ1WZ47bVCLkkdsWUWB4TVnlRLLsK0DJ1AU5S60+mtg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"PublicKey":{"N":30346367709519998192436886781530448102306271332455263400212115984683729977524175736349530311192719802886143232251643623202558651273627293040049867156228630519372930452342137097257512145117972676944388599148999721176168943428603255669511391729651354091528276048347410886933731648186607876347929422404995585813188692027295974423580143190279947826308222749788050984291105494435788199548665553329382936015342999709137976197060241457933471417530151429098881342535568495010914838173318160659769215528518176799315342134557930702202720980708060175373739625647208947141354839288754939451243425264187626586024902374331872916373,"E":65537},"Version":3,"SerialNumber":9125946881735400482959016744378213965,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"Subject":{"Country":null,"Organization":null,"OrganizationalUnit":null,"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"*.fofa.info","Names":[{"Type":[2,5,4,3],"Value":"*.fofa.info"}],"ExtraNames":null},"NotBefore":"2022-08-25T00:00:00Z","NotAfter":"2023-08-25T23:59:59Z","KeyUsage":5,"Extensions":[{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrX"},{"Id":[2,5,29,14],"Critical":false,"Value":"BBRUySn7tFppVMskC2sQ+lYMuyqiDQ=="},{"Id":[2,5,29,17],"Critical":false,"Value":"MBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8="},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIFoA=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,32],"Critical":false,"Value":"MDUwMwYGZ4EMAQIBMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQUw=="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQ="},{"Id":[2,5,29,19],"Critical":false,"Value":"MAA="},{"Id":[1,3,6,1,4,1,11129,2,4,2],"Critical":false,"Value":"BIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":false,"MaxPathLen":-1,"MaxPathLenZero":false,"SubjectKeyId":"VMkp+7RaaVTLJAtrEPpWDLsqog0=","AuthorityKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":["http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G1.crt"],"DNSNames":["*.fofa.info","fofa.info"],"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":null,"PolicyIdentifiers":[[2,23,140,1,2,1]]},{"Raw":"MIIEqjCCA5KgAwIBAgIQAnmsRYvBskWr+YBTzSybsTANBgkqhkiG9w0BAQsFADBhMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBDQTAeFw0xNzExMjcxMjQ2MTBaFw0yNzExMjcxMjQ2MTBaMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALPeP6wkab41dyQh6mKcoHqt3jRIxW5MDvf9QyiOR7VfFwK656es0UFiIb74N9pRntzF1UgYzDGu3ppZVMdolbxhm6dWS9OK/lFehKNT0OYI9aqk6F+U7cA6jxSC+iDBPXwdF4rs3KRyp3aQn6pjpp1yr7IB6Y4zv72Ee/PlZ/6rK6InC6WpK0nPVOYR7n9iDuPe1E4IxUMBH/T33+3hyuH3dvfgiWUOUkjdpMbyxX+XNle5uEIiyBsi4IvbcTCh8ruifCIi5mDXkZrnMT8nwfYCV6v6kDdXkbgGRLKsR4pucbJtbKqIkUGxuZI2t7pfewKRc5nWecvDBZf3+p1MpA8CAwEAAaOCAU8wggFLMB0GA1UdDgQWBBRVdE+yck/1YLpQ0dfmUVyaAYca1zAfBgNVHSMEGDAWgBQD3lA1VtFMu2bwo+IbG8OXsj3RVTAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBIGA1UdEwEB/wQIMAYBAf8CAQAwNAYIKwYBBQUHAQEEKDAmMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2VydC5jb20wQgYDVR0fBDswOTA3oDWgM4YxaHR0cDovL2NybDMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsUm9vdENBLmNybDBMBgNVHSAERTBDMDcGCWCGSAGG/WwBAjAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMAgGBmeBDAECATANBgkqhkiG9w0BAQsFAAOCAQEAK3Gp6/aGq7aBZsxf/oQ+TD/BSwW3AU4ETK+GQf2kFzYZkby5SFrHdPomunx2HBzViUchGoofGgg7gHW0W3MlQAXWM0r5LUvStcr82QDWYNPaUy4taCQmyaJ+VB+6wxHstSigOlSNF2a6vg4rgexixeiV4YSB03Yqp2t3TeZHM9ESfkus74nQyW7pRGezj+TC44xCagCQQOzzNmzEAP2SnCrJsNE2DpRVMnL8J6xBRdjmOsC3N6cQuKuRXbzByVBjCqAA8t1L0I+9wXJerLPyErjyrMKWaBFLmfK/AHNF4ZihwPGOc7w6UHczBZXH5RFzJNnww+WnKuTPI0HfnVH8lg==","RawTBSCertificate":"MIIDkqADAgECAhACeaxFi8GyRav5gFPNLJuxMA0GCSqGSIb3DQEBCwUAMGExCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMB4XDTE3MTEyNzEyNDYxMFoXDTI3MTEyNzEyNDYxMFowbjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEtMCsGA1UEAxMkRW5jcnlwdGlvbiBFdmVyeXdoZXJlIERWIFRMUyBDQSAtIEcxMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs94/rCRpvjV3JCHqYpygeq3eNEjFbkwO9/1DKI5HtV8XArrnp6zRQWIhvvg32lGe3MXVSBjMMa7emllUx2iVvGGbp1ZL04r+UV6Eo1PQ5gj1qqToX5TtwDqPFIL6IME9fB0XiuzcpHKndpCfqmOmnXKvsgHpjjO/vYR78+Vn/qsroicLpakrSc9U5hHuf2IO497UTgjFQwEf9Pff7eHK4fd29+CJZQ5SSN2kxvLFf5c2V7m4QiLIGyLgi9txMKHyu6J8IiLmYNeRmucxPyfB9gJXq/qQN1eRuAZEsqxHim5xsm1sqoiRQbG5kja3ul97ApFzmdZ5y8MFl/f6nUykDwIDAQABo4IBTzCCAUswHQYDVR0OBBYEFFV0T7JyT/VgulDR1+ZRXJoBhxrXMB8GA1UdIwQYMBaAFAPeUDVW0Uy7ZvCj4hsbw5eyPdFVMA4GA1UdDwEB/wQEAwIBhjAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwEgYDVR0TAQH/BAgwBgEB/wIBADA0BggrBgEFBQcBAQQoMCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBCBgNVHR8EOzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRHbG9iYWxSb290Q0EuY3JsMEwGA1UdIARFMEMwNwYJYIZIAYb9bAECMCowKAYIKwYBBQUHAgEWHGh0dHBzOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwCAYGZ4EMAQIB","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs94/rCRpvjV3JCHqYpygeq3eNEjFbkwO9/1DKI5HtV8XArrnp6zRQWIhvvg32lGe3MXVSBjMMa7emllUx2iVvGGbp1ZL04r+UV6Eo1PQ5gj1qqToX5TtwDqPFIL6IME9fB0XiuzcpHKndpCfqmOmnXKvsgHpjjO/vYR78+Vn/qsroicLpakrSc9U5hHuf2IO497UTgjFQwEf9Pff7eHK4fd29+CJZQ5SSN2kxvLFf5c2V7m4QiLIGyLgi9txMKHyu6J8IiLmYNeRmucxPyfB9gJXq/qQN1eRuAZEsqxHim5xsm1sqoiRQbG5kja3ul97ApFzmdZ5y8MFl/f6nUykDwIDAQAB","RawSubject":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","RawIssuer":"MGExCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENB","Signature":"K3Gp6/aGq7aBZsxf/oQ+TD/BSwW3AU4ETK+GQf2kFzYZkby5SFrHdPomunx2HBzViUchGoofGgg7gHW0W3MlQAXWM0r5LUvStcr82QDWYNPaUy4taCQmyaJ+VB+6wxHstSigOlSNF2a6vg4rgexixeiV4YSB03Yqp2t3TeZHM9ESfkus74nQyW7pRGezj+TC44xCagCQQOzzNmzEAP2SnCrJsNE2DpRVMnL8J6xBRdjmOsC3N6cQuKuRXbzByVBjCqAA8t1L0I+9wXJerLPyErjyrMKWaBFLmfK/AHNF4ZihwPGOc7w6UHczBZXH5RFzJNnww+WnKuTPI0HfnVH8lg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"PublicKey":{"N":22706251517628495429579306476527582598187764772456720672684249155952023400103586136394210171089904535347787811551564517219581667355326138745910818719263659576498360908861800513494557221083686295417677745472272577142865776936126865749138393824268869001713716190986549288982659575370713993165560184184384319310638190048040901826603060972642673501626645874829161187924743266688319341500734341804720324484149475218144323306019294843761550605245685343743759627920315097313401041546837623248122807931211382629690114120435316354285199985028774302898304486227019144308522247907265767721852164663398861181933523099907787629583,"E":65537},"Version":3,"SerialNumber":3290217995900168375215973871519570865,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"DigiCert Global Root CA","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"DigiCert Global Root CA"}],"ExtraNames":null},"Subject":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"NotBefore":"2017-11-27T12:46:10Z","NotAfter":"2027-11-27T12:46:10Z","KeyUsage":97,"Extensions":[{"Id":[2,5,29,14],"Critical":false,"Value":"BBRVdE+yck/1YLpQ0dfmUVyaAYca1w=="},{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFAPeUDVW0Uy7ZvCj4hsbw5eyPdFV"},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIBhg=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,19],"Critical":true,"Value":"MAYBAf8CAQA="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbQ=="},{"Id":[2,5,29,31],"Critical":false,"Value":"MDkwN6A1oDOGMWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydEdsb2JhbFJvb3RDQS5jcmw="},{"Id":[2,5,29,32],"Critical":false,"Value":"MEMwNwYJYIZIAYb9bAECMCowKAYIKwYBBQUHAgEWHGh0dHBzOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwCAYGZ4EMAQIB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":true,"MaxPathLen":0,"MaxPathLenZero":true,"SubjectKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","AuthorityKeyId":"A95QNVbRTLtm8KPiGxvDl7I90VU=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":null,"DNSNames":null,"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":["http://crl3.digicert.com/DigiCertGlobalRootCA.crl"],"PolicyIdentifiers":[[2,16,840,1,114412,1,2],[2,23,140,1,2,1]]}]
--- PASS: TestGetHttpInfo (0.26s)
PASS


Process finished with the exit code 0
`
	crtstr := `{"Raw":"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","RawTBSCertificate":"MIIE3qADAgECAhAG3ZfrfXBICGz3hwEJmKZNMA0GCSqGSIb3DQEBCwUAMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTAeFw0yMjA4MjUwMDAwMDBaFw0yMzA4MjUyMzU5NTlaMBYxFDASBgNVBAMMCyouZm9mYS5pbmZvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQABo4IC5jCCAuIwHwYDVR0jBBgwFoAUVXRPsnJP9WC6UNHX5lFcmgGHGtcwHQYDVR0OBBYEFFTJKfu0WmlUyyQLaxD6Vgy7KqINMCEGA1UdEQQaMBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8wDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjA+BgNVHSAENzA1MDMGBmeBDAECATApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwgYAGCCsGAQUFBwEBBHQwcjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEoGCCsGAQUFBzAChj5odHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRW5jcnlwdGlvbkV2ZXJ5d2hlcmVEVlRMU0NBLUcxLmNydDAJBgNVHRMEAjAAMIIBfgYKKwYBBAHWeQIEAgSCAW4EggFqAWgAdgDoPtDaPvUGNTLnVyi8iWvJA9PL0RFr7Otp4Xd9bQa9bgAAAYLUKYceAAAEAwBHMEUCIQCFy5T931dX6oSm35Bc5L1TBC01EZ3XkdjRsULhJMRaPgIgTclu837GgwJeIIvUvNtGoptTyugue2mW/SQBEZt18dIAdgA1zxkbv7FsV78PrUxtQsu7ticgJlHqP+Eq76gDwzvWTAAAAYLUKYZkAAAEAwBHMEUCIQDmfK2Mrt1QIXfOVqZJzhi6tMNGjqF9r32GzyEina3fhAIgQg6O2nHBwyMFYt/UX2e2OBUNN0U9RNgvokaXsqteVdMAdgCzc3cH4YRQ+GOG1gWp3BEJSnktsWcMC4fc8AMOeTalmgAAAYLUKYbMAAAEAwBHMEUCIQDDbU6JncBr6HmPKBuaYxvAIN2tUVr/+jDasGB9+UmCvwIgVmaLxim/hziL0UB3IGNDAYcwYoFoH3/+X9rhvTKZdYE=","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQAB","RawSubject":"MBYxFDASBgNVBAMMCyouZm9mYS5pbmZv","RawIssuer":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","Signature":"TsmNrewtnFLC3zR6glDpoMjNndGloFcdCWPT+xg9Owab5BkyItubPeyz5hfbVh/VqH/wjvphD72DKiiYjusKVu4Rb8BLPR2LYbPP26ACyawINZZ81uadhIAxZwkWl7X5Nc/kOBYtNwr/axT06XlOOEGhy5jxjg0MApBWMOfnSscwzYzUIzRTf9bPMC8xuAV30AUBbpRekEZesMuP40tMzNoV2NH+jp8eFukCyzqXK6TX0rdW5SzTSO41SofODCTcpqNlhZ/1g1J41fHqzNBfoirdCazWOX9Qpfy1CgmVcfRBZ1WZ47bVCLkkdsWUWB4TVnlRLLsK0DJ1AU5S60+mtg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"Version":3,"SerialNumber":9125946881735400482959016744378213965,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"Subject":{"Country":null,"Organization":null,"OrganizationalUnit":null,"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"*.fofa.info","Names":[{"Type":[2,5,4,3],"Value":"*.fofa.info"}],"ExtraNames":null},"NotBefore":"2022-08-25T00:00:00Z","NotAfter":"2023-08-25T23:59:59Z","KeyUsage":5,"Extensions":[{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrX"},{"Id":[2,5,29,14],"Critical":false,"Value":"BBRUySn7tFppVMskC2sQ+lYMuyqiDQ=="},{"Id":[2,5,29,17],"Critical":false,"Value":"MBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8="},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIFoA=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,32],"Critical":false,"Value":"MDUwMwYGZ4EMAQIBMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQUw=="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQ="},{"Id":[2,5,29,19],"Critical":false,"Value":"MAA="},{"Id":[1,3,6,1,4,1,11129,2,4,2],"Critical":false,"Value":"BIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":false,"MaxPathLen":-1,"MaxPathLenZero":false,"SubjectKeyId":"VMkp+7RaaVTLJAtrEPpWDLsqog0=","AuthorityKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":["http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G1.crt"],"DNSNames":["*.fofa.info","fofa.info"],"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":null,"PolicyIdentifiers":[[2,23,140,1,2,1]]}`
	crt := x509.Certificate{}
	err := json.Unmarshal([]byte(crtstr), &crt)
	assert.Nil(t, err)

	allCertStr := `[{"Raw":"MIIF9jCCBN6gAwIBAgIQBt2X631wSAhs94cBCZimTTANBgkqhkiG9w0BAQsFADBuMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cuZGlnaWNlcnQuY29tMS0wKwYDVQQDEyRFbmNyeXB0aW9uIEV2ZXJ5d2hlcmUgRFYgVExTIENBIC0gRzEwHhcNMjIwODI1MDAwMDAwWhcNMjMwODI1MjM1OTU5WjAWMRQwEgYDVQQDDAsqLmZvZmEuaW5mbzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPBjuLX8YnQ6SrnnnaGiiH9zhGMagbd1F7uPsIceFHMeWkwiNrT+L7lalgpYS7sx1HbLEsOsVopSI8zyIzmRc/fvcWE1nl9J9lI6TH7TUkdXX9onvvhkobdJobXOxMTtQSR+4tl3i6ufpdBez0j2ijaqMK7oWKM0qUonCiIWAxMZ04jd6a9Q47yDsCA/Sx4yr9lfPyXYYSva46n+p/h/q+ZVzFDfFwS41OX98Zvo3qMYobgo0Suvq+hqAOealxHuD8Yf3whk7Kxflpnxb2WobczqtgW497dUArvsvjTBph74eW1XMgmfdTDAZBdO+YPOrtmK8lX9UmFCR0z64PdXM5UCAwEAAaOCAuYwggLiMB8GA1UdIwQYMBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrXMB0GA1UdDgQWBBRUySn7tFppVMskC2sQ+lYMuyqiDTAhBgNVHREEGjAYggsqLmZvZmEuaW5mb4IJZm9mYS5pbmZvMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwPgYDVR0gBDcwNTAzBgZngQwBAgEwKTAnBggrBgEFBQcCARYbaHR0cDovL3d3dy5kaWdpY2VydC5jb20vQ1BTMIGABggrBgEFBQcBAQR0MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQwCQYDVR0TBAIwADCCAX4GCisGAQQB1nkCBAIEggFuBIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWBMA0GCSqGSIb3DQEBCwUAA4IBAQBOyY2t7C2cUsLfNHqCUOmgyM2d0aWgVx0JY9P7GD07BpvkGTIi25s97LPmF9tWH9Wof/CO+mEPvYMqKJiO6wpW7hFvwEs9HYths8/boALJrAg1lnzW5p2EgDFnCRaXtfk1z+Q4Fi03Cv9rFPTpeU44QaHLmPGODQwCkFYw5+dKxzDNjNQjNFN/1s8wLzG4BXfQBQFulF6QRl6wy4/jS0zM2hXY0f6Onx4W6QLLOpcrpNfSt1blLNNI7jVKh84MJNymo2WFn/WDUnjV8erM0F+iKt0JrNY5f1Cl/LUKCZVx9EFnVZnjttUIuSR2xZRYHhNWeVEsuwrQMnUBTlLrT6a2","RawTBSCertificate":"MIIE3qADAgECAhAG3ZfrfXBICGz3hwEJmKZNMA0GCSqGSIb3DQEBCwUAMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTAeFw0yMjA4MjUwMDAwMDBaFw0yMzA4MjUyMzU5NTlaMBYxFDASBgNVBAMMCyouZm9mYS5pbmZvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQABo4IC5jCCAuIwHwYDVR0jBBgwFoAUVXRPsnJP9WC6UNHX5lFcmgGHGtcwHQYDVR0OBBYEFFTJKfu0WmlUyyQLaxD6Vgy7KqINMCEGA1UdEQQaMBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8wDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjA+BgNVHSAENzA1MDMGBmeBDAECATApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwgYAGCCsGAQUFBwEBBHQwcjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEoGCCsGAQUFBzAChj5odHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRW5jcnlwdGlvbkV2ZXJ5d2hlcmVEVlRMU0NBLUcxLmNydDAJBgNVHRMEAjAAMIIBfgYKKwYBBAHWeQIEAgSCAW4EggFqAWgAdgDoPtDaPvUGNTLnVyi8iWvJA9PL0RFr7Otp4Xd9bQa9bgAAAYLUKYceAAAEAwBHMEUCIQCFy5T931dX6oSm35Bc5L1TBC01EZ3XkdjRsULhJMRaPgIgTclu837GgwJeIIvUvNtGoptTyugue2mW/SQBEZt18dIAdgA1zxkbv7FsV78PrUxtQsu7ticgJlHqP+Eq76gDwzvWTAAAAYLUKYZkAAAEAwBHMEUCIQDmfK2Mrt1QIXfOVqZJzhi6tMNGjqF9r32GzyEina3fhAIgQg6O2nHBwyMFYt/UX2e2OBUNN0U9RNgvokaXsqteVdMAdgCzc3cH4YRQ+GOG1gWp3BEJSnktsWcMC4fc8AMOeTalmgAAAYLUKYbMAAAEAwBHMEUCIQDDbU6JncBr6HmPKBuaYxvAIN2tUVr/+jDasGB9+UmCvwIgVmaLxim/hziL0UB3IGNDAYcwYoFoH3/+X9rhvTKZdYE=","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GO4tfxidDpKueedoaKIf3OEYxqBt3UXu4+whx4Ucx5aTCI2tP4vuVqWClhLuzHUdssSw6xWilIjzPIjOZFz9+9xYTWeX0n2UjpMftNSR1df2ie++GSht0mhtc7ExO1BJH7i2XeLq5+l0F7PSPaKNqowruhYozSpSicKIhYDExnTiN3pr1DjvIOwID9LHjKv2V8/JdhhK9rjqf6n+H+r5lXMUN8XBLjU5f3xm+jeoxihuCjRK6+r6GoA55qXEe4Pxh/fCGTsrF+WmfFvZahtzOq2Bbj3t1QCu+y+NMGmHvh5bVcyCZ91MMBkF075g86u2YryVf1SYUJHTPrg91czlQIDAQAB","RawSubject":"MBYxFDASBgNVBAMMCyouZm9mYS5pbmZv","RawIssuer":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","Signature":"TsmNrewtnFLC3zR6glDpoMjNndGloFcdCWPT+xg9Owab5BkyItubPeyz5hfbVh/VqH/wjvphD72DKiiYjusKVu4Rb8BLPR2LYbPP26ACyawINZZ81uadhIAxZwkWl7X5Nc/kOBYtNwr/axT06XlOOEGhy5jxjg0MApBWMOfnSscwzYzUIzRTf9bPMC8xuAV30AUBbpRekEZesMuP40tMzNoV2NH+jp8eFukCyzqXK6TX0rdW5SzTSO41SofODCTcpqNlhZ/1g1J41fHqzNBfoirdCazWOX9Qpfy1CgmVcfRBZ1WZ47bVCLkkdsWUWB4TVnlRLLsK0DJ1AU5S60+mtg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"Version":3,"SerialNumber":9125946881735400482959016744378213965,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"Subject":{"Country":null,"Organization":null,"OrganizationalUnit":null,"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"*.fofa.info","Names":[{"Type":[2,5,4,3],"Value":"*.fofa.info"}],"ExtraNames":null},"NotBefore":"2022-08-25T00:00:00Z","NotAfter":"2023-08-25T23:59:59Z","KeyUsage":5,"Extensions":[{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFFV0T7JyT/VgulDR1+ZRXJoBhxrX"},{"Id":[2,5,29,14],"Critical":false,"Value":"BBRUySn7tFppVMskC2sQ+lYMuyqiDQ=="},{"Id":[2,5,29,17],"Critical":false,"Value":"MBiCCyouZm9mYS5pbmZvgglmb2ZhLmluZm8="},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIFoA=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,32],"Critical":false,"Value":"MDUwMwYGZ4EMAQIBMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQUw=="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MHIwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBKBggrBgEFBQcwAoY+aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY29tL0VuY3J5cHRpb25FdmVyeXdoZXJlRFZUTFNDQS1HMS5jcnQ="},{"Id":[2,5,29,19],"Critical":false,"Value":"MAA="},{"Id":[1,3,6,1,4,1,11129,2,4,2],"Critical":false,"Value":"BIIBagFoAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0GvW4AAAGC1CmHHgAABAMARzBFAiEAhcuU/d9XV+qEpt+QXOS9UwQtNRGd15HY0bFC4STEWj4CIE3JbvN+xoMCXiCL1LzbRqKbU8roLntplv0kARGbdfHSAHYANc8ZG7+xbFe/D61MbULLu7YnICZR6j/hKu+oA8M71kwAAAGC1CmGZAAABAMARzBFAiEA5nytjK7dUCF3zlamSc4YurTDRo6hfa99hs8hIp2t34QCIEIOjtpxwcMjBWLf1F9ntjgVDTdFPUTYL6JGl7KrXlXTAHYAs3N3B+GEUPhjhtYFqdwRCUp5LbFnDAuH3PADDnk2pZoAAAGC1CmGzAAABAMARzBFAiEAw21OiZ3Aa+h5jygbmmMbwCDdrVFa//ow2rBgfflJgr8CIFZmi8Ypv4c4i9FAdyBjQwGHMGKBaB9//l/a4b0ymXWB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":false,"MaxPathLen":-1,"MaxPathLenZero":false,"SubjectKeyId":"VMkp+7RaaVTLJAtrEPpWDLsqog0=","AuthorityKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":["http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G1.crt"],"DNSNames":["*.fofa.info","fofa.info"],"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":null,"PolicyIdentifiers":[[2,23,140,1,2,1]]},{"Raw":"MIIEqjCCA5KgAwIBAgIQAnmsRYvBskWr+YBTzSybsTANBgkqhkiG9w0BAQsFADBhMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBDQTAeFw0xNzExMjcxMjQ2MTBaFw0yNzExMjcxMjQ2MTBaMG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALPeP6wkab41dyQh6mKcoHqt3jRIxW5MDvf9QyiOR7VfFwK656es0UFiIb74N9pRntzF1UgYzDGu3ppZVMdolbxhm6dWS9OK/lFehKNT0OYI9aqk6F+U7cA6jxSC+iDBPXwdF4rs3KRyp3aQn6pjpp1yr7IB6Y4zv72Ee/PlZ/6rK6InC6WpK0nPVOYR7n9iDuPe1E4IxUMBH/T33+3hyuH3dvfgiWUOUkjdpMbyxX+XNle5uEIiyBsi4IvbcTCh8ruifCIi5mDXkZrnMT8nwfYCV6v6kDdXkbgGRLKsR4pucbJtbKqIkUGxuZI2t7pfewKRc5nWecvDBZf3+p1MpA8CAwEAAaOCAU8wggFLMB0GA1UdDgQWBBRVdE+yck/1YLpQ0dfmUVyaAYca1zAfBgNVHSMEGDAWgBQD3lA1VtFMu2bwo+IbG8OXsj3RVTAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBIGA1UdEwEB/wQIMAYBAf8CAQAwNAYIKwYBBQUHAQEEKDAmMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2VydC5jb20wQgYDVR0fBDswOTA3oDWgM4YxaHR0cDovL2NybDMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsUm9vdENBLmNybDBMBgNVHSAERTBDMDcGCWCGSAGG/WwBAjAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMAgGBmeBDAECATANBgkqhkiG9w0BAQsFAAOCAQEAK3Gp6/aGq7aBZsxf/oQ+TD/BSwW3AU4ETK+GQf2kFzYZkby5SFrHdPomunx2HBzViUchGoofGgg7gHW0W3MlQAXWM0r5LUvStcr82QDWYNPaUy4taCQmyaJ+VB+6wxHstSigOlSNF2a6vg4rgexixeiV4YSB03Yqp2t3TeZHM9ESfkus74nQyW7pRGezj+TC44xCagCQQOzzNmzEAP2SnCrJsNE2DpRVMnL8J6xBRdjmOsC3N6cQuKuRXbzByVBjCqAA8t1L0I+9wXJerLPyErjyrMKWaBFLmfK/AHNF4ZihwPGOc7w6UHczBZXH5RFzJNnww+WnKuTPI0HfnVH8lg==","RawTBSCertificate":"MIIDkqADAgECAhACeaxFi8GyRav5gFPNLJuxMA0GCSqGSIb3DQEBCwUAMGExCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMB4XDTE3MTEyNzEyNDYxMFoXDTI3MTEyNzEyNDYxMFowbjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEtMCsGA1UEAxMkRW5jcnlwdGlvbiBFdmVyeXdoZXJlIERWIFRMUyBDQSAtIEcxMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs94/rCRpvjV3JCHqYpygeq3eNEjFbkwO9/1DKI5HtV8XArrnp6zRQWIhvvg32lGe3MXVSBjMMa7emllUx2iVvGGbp1ZL04r+UV6Eo1PQ5gj1qqToX5TtwDqPFIL6IME9fB0XiuzcpHKndpCfqmOmnXKvsgHpjjO/vYR78+Vn/qsroicLpakrSc9U5hHuf2IO497UTgjFQwEf9Pff7eHK4fd29+CJZQ5SSN2kxvLFf5c2V7m4QiLIGyLgi9txMKHyu6J8IiLmYNeRmucxPyfB9gJXq/qQN1eRuAZEsqxHim5xsm1sqoiRQbG5kja3ul97ApFzmdZ5y8MFl/f6nUykDwIDAQABo4IBTzCCAUswHQYDVR0OBBYEFFV0T7JyT/VgulDR1+ZRXJoBhxrXMB8GA1UdIwQYMBaAFAPeUDVW0Uy7ZvCj4hsbw5eyPdFVMA4GA1UdDwEB/wQEAwIBhjAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwEgYDVR0TAQH/BAgwBgEB/wIBADA0BggrBgEFBQcBAQQoMCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBCBgNVHR8EOzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRHbG9iYWxSb290Q0EuY3JsMEwGA1UdIARFMEMwNwYJYIZIAYb9bAECMCowKAYIKwYBBQUHAgEWHGh0dHBzOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwCAYGZ4EMAQIB","RawSubjectPublicKeyInfo":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs94/rCRpvjV3JCHqYpygeq3eNEjFbkwO9/1DKI5HtV8XArrnp6zRQWIhvvg32lGe3MXVSBjMMa7emllUx2iVvGGbp1ZL04r+UV6Eo1PQ5gj1qqToX5TtwDqPFIL6IME9fB0XiuzcpHKndpCfqmOmnXKvsgHpjjO/vYR78+Vn/qsroicLpakrSc9U5hHuf2IO497UTgjFQwEf9Pff7eHK4fd29+CJZQ5SSN2kxvLFf5c2V7m4QiLIGyLgi9txMKHyu6J8IiLmYNeRmucxPyfB9gJXq/qQN1eRuAZEsqxHim5xsm1sqoiRQbG5kja3ul97ApFzmdZ5y8MFl/f6nUykDwIDAQAB","RawSubject":"MG4xCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xLTArBgNVBAMTJEVuY3J5cHRpb24gRXZlcnl3aGVyZSBEViBUTFMgQ0EgLSBHMQ==","RawIssuer":"MGExCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENB","Signature":"K3Gp6/aGq7aBZsxf/oQ+TD/BSwW3AU4ETK+GQf2kFzYZkby5SFrHdPomunx2HBzViUchGoofGgg7gHW0W3MlQAXWM0r5LUvStcr82QDWYNPaUy4taCQmyaJ+VB+6wxHstSigOlSNF2a6vg4rgexixeiV4YSB03Yqp2t3TeZHM9ESfkus74nQyW7pRGezj+TC44xCagCQQOzzNmzEAP2SnCrJsNE2DpRVMnL8J6xBRdjmOsC3N6cQuKuRXbzByVBjCqAA8t1L0I+9wXJerLPyErjyrMKWaBFLmfK/AHNF4ZihwPGOc7w6UHczBZXH5RFzJNnww+WnKuTPI0HfnVH8lg==","SignatureAlgorithm":4,"PublicKeyAlgorithm":1,"Version":3,"SerialNumber":3290217995900168375215973871519570865,"Issuer":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"DigiCert Global Root CA","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"DigiCert Global Root CA"}],"ExtraNames":null},"Subject":{"Country":["US"],"Organization":["DigiCert Inc"],"OrganizationalUnit":["www.digicert.com"],"Locality":null,"Province":null,"StreetAddress":null,"PostalCode":null,"SerialNumber":"","CommonName":"Encryption Everywhere DV TLS CA - G1","Names":[{"Type":[2,5,4,6],"Value":"US"},{"Type":[2,5,4,10],"Value":"DigiCert Inc"},{"Type":[2,5,4,11],"Value":"www.digicert.com"},{"Type":[2,5,4,3],"Value":"Encryption Everywhere DV TLS CA - G1"}],"ExtraNames":null},"NotBefore":"2017-11-27T12:46:10Z","NotAfter":"2027-11-27T12:46:10Z","KeyUsage":97,"Extensions":[{"Id":[2,5,29,14],"Critical":false,"Value":"BBRVdE+yck/1YLpQ0dfmUVyaAYca1w=="},{"Id":[2,5,29,35],"Critical":false,"Value":"MBaAFAPeUDVW0Uy7ZvCj4hsbw5eyPdFV"},{"Id":[2,5,29,15],"Critical":true,"Value":"AwIBhg=="},{"Id":[2,5,29,37],"Critical":false,"Value":"MBQGCCsGAQUFBwMBBggrBgEFBQcDAg=="},{"Id":[2,5,29,19],"Critical":true,"Value":"MAYBAf8CAQA="},{"Id":[1,3,6,1,5,5,7,1,1],"Critical":false,"Value":"MCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbQ=="},{"Id":[2,5,29,31],"Critical":false,"Value":"MDkwN6A1oDOGMWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydEdsb2JhbFJvb3RDQS5jcmw="},{"Id":[2,5,29,32],"Critical":false,"Value":"MEMwNwYJYIZIAYb9bAECMCowKAYIKwYBBQUHAgEWHGh0dHBzOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMwCAYGZ4EMAQIB"}],"ExtraExtensions":null,"UnhandledCriticalExtensions":null,"ExtKeyUsage":[1,2],"UnknownExtKeyUsage":null,"BasicConstraintsValid":true,"IsCA":true,"MaxPathLen":0,"MaxPathLenZero":true,"SubjectKeyId":"VXRPsnJP9WC6UNHX5lFcmgGHGtc=","AuthorityKeyId":"A95QNVbRTLtm8KPiGxvDl7I90VU=","OCSPServer":["http://ocsp.digicert.com"],"IssuingCertificateURL":null,"DNSNames":null,"EmailAddresses":null,"IPAddresses":null,"URIs":null,"PermittedDNSDomainsCritical":false,"PermittedDNSDomains":null,"ExcludedDNSDomains":null,"PermittedIPRanges":null,"ExcludedIPRanges":null,"PermittedEmailAddresses":null,"ExcludedEmailAddresses":null,"PermittedURIDomains":null,"ExcludedURIDomains":null,"CRLDistributionPoints":["http://crl3.digicert.com/DigiCertGlobalRootCA.crl"],"PolicyIdentifiers":[[2,16,840,1,114412,1,2],[2,23,140,1,2,1]]}]`
	var allCert []*x509.Certificate
	err = json.Unmarshal([]byte(allCertStr), &allCert)
	assert.Nil(t, err)
	certObj := GetCertsObj(lenCert, sCert, &crt, allCert, "", false)
	assert.Equal(t, "v3", certObj.V)
	assert.Equal(t, "9125946881735400482959016744378213965", certObj.SN)
	assert.Equal(t, "SHA256-RSA", certObj.SigAlth)
	assert.Equal(t, "Encryption Everywhere DV TLS CA - G1", certObj.IssuerCn)
	assert.Equal(t, 2, certObj.CertNum)
	assert.Equal(t, "*.fofa.info", certObj.Domain)
}
