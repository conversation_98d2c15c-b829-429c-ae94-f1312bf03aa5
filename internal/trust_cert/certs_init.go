package trust_cert

import (
	"bufio"
	"crawler/internal/util"
	"io"
	"log"
	"os"
)

var trustedCerts []string
var certImportTime = "2021-03-09 14:00:00"

// 初始化信任证书
func InitTrustCerts(filename string) {
	exists, err := util.FileExists(filename)
	if !exists {
		log.Fatalln("[ERROR] trust cert file not exists", err)
		return
	}

	fi, err := os.Open(filename)
	if err != nil {
		log.Fatalf("[ERROR] open file failed: %v\n", err)
		return
	}
	defer fi.Close()

	fi.Seek(0, 0)

	br := bufio.NewReaderSize(fi, 10000)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}

		if len(a) > 0 {
			trustedCerts = append(trustedCerts, string(a))
		}
	}

	lenTrustedCert := len(trustedCerts)
	if lenTrustedCert <= 0 {
		log.Fatal("[ERROR] empty trusted cert")
	} else {
		log.Println("load trust time:", certImportTime, "count:", lenTrustedCert)
	}
}
