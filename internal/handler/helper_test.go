package handler

import (
	"bytes"
	"encoding/json"
	"errors"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"io/ioutil"
	"net/http"
	"testing"
)

type HelperSuit struct {
	suite.Suite
}

func TestHelperSuit(t *testing.T) {
	s := &HelperSuit{}
	suite.Run(t, s)
}

func (s *HelperSuit) TestGetAllProtocols() {
	Convey("TestGetAllProtocols", s.T(), func() {
		Convey("http.NewRequest error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, errors.New("")).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("http.Client.Do error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			defer ApplyMethodReturn(&http.Client{}, "Do", nil, errors.New("")).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("json.NewDecoder error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", errors.New("")).Reset()

			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("http.StatusOK no ok", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("json.Marshal error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyFunc(json.NewDecoder(do.Body).Decode, func(v interface{}) error {
				// TODO not working
				res := &Res{
					StatusCode: http.StatusOK,
				}

				v = &res
				return nil
			}).Reset()
			defer ApplyFuncReturn(json.Marshal, nil, errors.New("")).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("json.Unmarshal error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			defer ApplyFuncReturn(json.Marshal, nil, nil).Reset()
			defer ApplyFuncReturn(json.Unmarshal, errors.New("")).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			defer ApplyFuncReturn(json.Marshal, nil, nil).Reset()
			defer ApplyFuncReturn(json.Unmarshal, nil).Reset()
			_, err := GetAllProtocols("")
			So(err, ShouldBeNil)
		})
	})
}
