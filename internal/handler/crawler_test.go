package handler

import (
	"context"
	"crawler/internal/config"
	"crawler/internal/job"
	"errors"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"golang.org/x/sync/errgroup"
	"reflect"
	"sync"
	"testing"
)

type CrawlerSuit struct {
	suite.Suite
	crawler Crawler
}

func TestCrawlerSuit(t *testing.T) {
	s := &CrawlerSuit{}
	s.crawler = Crawler{
		srv:        micro.NewService(),
		conf:       &config.Config{},
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
	suite.Run(t, s)
}

func TestCrawler_Decrement(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "",
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Crawler{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
			}
			c.Decrement(tt.args.taskId)
		})
	}
}

func TestCrawler_FinishedNotify(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
	}
	type args struct {
		ctx context.Context
		req *rpcx.FinishedNotifyRequest
		rsp *rpcx.FinishedNotifyResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &rpcx.FinishedNotifyRequest{},
				rsp: &rpcx.FinishedNotifyResponse{},
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Crawler{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
			}
			if err := c.FinishedNotify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("FinishedNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func (s *CrawlerSuit) TestCrawler_HandleEvent() {
	Convey("TestNewRawGrab", s.T(), func() {
		Convey("param error", func() {
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "",
				},
			})
			So(r, ShouldResemble, errors.New("参数错误"))
		})

		Convey("SiteURL error", func() {
			j := job.NewJob(&rpcx.CrawlerEvent{}, s.crawler.conf.CrawlerConf)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "SiteURL", errors.New("")).Reset()
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId:             "task_id",
					IsCrawlerAllUrl:    true,
					CrawlerUrlBlackKey: "aaa|bb",
				},
				JobId: "job_id",
				Url:   "http://10.10.10.10",
			})
			So(r, ShouldBeError)
		})

		Convey("Normal error", func() {
			j := job.NewJob(&rpcx.CrawlerEvent{}, s.crawler.conf.CrawlerConf)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "SiteURL", nil).Reset()
			defer ApplyMethodReturn(j, "Normal", nil, errors.New("")).Reset()
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId:             "task_id",
					IsCrawlerAllUrl:    true,
					CrawlerUrlBlackKey: "aaa|bb",
				},
				JobId: "job_id",
				Url:   "http://10.10.10.10",
			})
			So(r, ShouldBeError)
		})

		Convey("Publish error", func() {
			j := job.NewJob(&rpcx.CrawlerEvent{}, s.crawler.conf.CrawlerConf)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "SiteURL", nil).Reset()
			defer ApplyMethodReturn(j, "Normal", nil, nil).Reset()
			defer ApplyMethodReturn(s.crawler.srv.Client(), "Publish", errors.New("")).Reset()
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId:             "task_id",
					IsCrawlerAllUrl:    true,
					CrawlerUrlBlackKey: "aaa|bb",
				},
				JobId: "job_id",
				Url:   "http://10.10.10.10",
			})
			So(r, ShouldBeError)
		})

		Convey("Wait error", func() {
			j := job.NewJob(&rpcx.CrawlerEvent{}, s.crawler.conf.CrawlerConf)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "SiteURL", nil).Reset()
			defer ApplyMethodReturn(j, "Normal", nil, nil).Reset()
			defer ApplyMethodReturn(s.crawler.srv.Client(), "Publish", nil).Reset()
			defer ApplyMethodReturn(&errgroup.Group{}, "Wait", errors.New("")).Reset()
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId:             "task_id",
					IsCrawlerAllUrl:    true,
					CrawlerUrlBlackKey: "aaa|bb",
				},
				JobId: "job_id",
				Url:   "http://10.10.10.10",
			})
			So(r, ShouldBeError)
		})

		Convey("IsCrawlerAllUrl pass", func() {
			j := job.NewJob(&rpcx.CrawlerEvent{}, s.crawler.conf.CrawlerConf)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "SiteURL", nil).Reset()
			defer ApplyMethodReturn(j, "Normal", nil, nil).Reset()
			defer ApplyMethodReturn(s.crawler.srv.Client(), "Publish", nil).Reset()
			defer ApplyMethodReturn(&errgroup.Group{}, "Wait", nil).Reset()
			r := s.crawler.HandleEvent(context.Background(), &rpcx.CrawlerEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId:             "task_id",
					IsCrawlerAllUrl:    true,
					CrawlerUrlBlackKey: "aaa|bb",
				},
				JobId: "job_id",
				Url:   "http://10.10.10.10",
			})
			So(r, ShouldBeNil)
		})
	})
}

func TestCrawler_Increment(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "",
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Crawler{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
			}
			c.Increment(tt.args.taskId)
		})
	}
}

func TestCrawler_StateQuery(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
	}
	type args struct {
		ctx context.Context
		req *rpcx.StateQueryRequest
		rsp *rpcx.StateQueryResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &rpcx.StateQueryRequest{},
				rsp: &rpcx.StateQueryResponse{},
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Crawler{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
			}
			if err := c.StateQuery(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("StateQuery() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewCrawler(t *testing.T) {
	type args struct {
		conf *config.Config
		srv  micro.Service
	}
	tests := []struct {
		name string
		args args
		want Crawler
	}{
		{
			args: args{
				srv:  micro.NewService(),
				conf: &config.Config{},
			},
			want: Crawler{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCrawler(tt.args.conf, tt.args.srv); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCrawler() = %v, want %v", got, tt.want)
			}
		})
	}
}
