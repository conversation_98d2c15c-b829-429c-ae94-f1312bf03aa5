package handler

import (
	"context"
	"errors"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/job"
	"git.gobies.org/shared-platform/quickstore/internal/model"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/elastic/go-elasticsearch/v6"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
)

type HandlerSuit struct {
	suite.Suite
	store QuickStore
}

func TestHelperSuit(t *testing.T) {
	s := &HandlerSuit{}

	var (
		c      *model.Config
		client *elasticsearch.Client
	)

	s.store = NewQuickStore(c, client)
	suite.Run(t, s)
}

func (s *HandlerSuit) TestNewQuickStore() {
	Convey("TestNewQuickStore", s.T(), func() {
		Convey("pass", func() {
			var (
				c      *model.Config
				client *elasticsearch.Client
			)
			store := NewQuickStore(c, client)
			So(store.conf, ShouldResemble, s.store.conf)
			So(store.elastic, ShouldResemble, s.store.elastic)
		})
	})
}

func (s *HandlerSuit) TestQuickStore_Decrement() {

}

func (s *HandlerSuit) TestQuickStore_FinishedNotify() {
	Convey("TestQuickStore_FinishedNotify", s.T(), func() {
		Convey("pass", func() {
			defer ApplyFuncReturn(s.store.lock.Lock).Reset()
			defer ApplyFuncReturn(s.store.lock.Unlock).Reset()
			err := s.store.FinishedNotify(context.Background(), &rpcx.FinishedNotifyRequest{}, &rpcx.FinishedNotifyResponse{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *HandlerSuit) TestQuickStore_HandleEvent() {
	Convey("TestQuickStore_HandleEvent", s.T(), func() {
		Convey("param error", func() {
			err := s.store.HandleEvent(context.Background(), &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{},
			})
			So(err, ShouldResemble, errors.New("参数错误"))
		})

		Convey("j.Normal error", func() {
			j := job.NewTask(s.store.conf, s.store.elastic, &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{},
				Data:     &rpcx.QuickStoreEvent_Normal{},
			})

			defer ApplyFuncReturn(s.store.Increment).Reset()
			defer ApplyFuncReturn(s.store.Decrement).Reset()
			defer ApplyMethodReturn(j, "Normal", errors.New("")).Reset()
			err := s.store.HandleEvent(context.Background(), &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "1",
				},
				JobId: "1",
				Data:  &rpcx.QuickStoreEvent_Normal{},
			})
			So(err, ShouldBeError)
		})

		Convey("j.FullSiteCrawler error", func() {

			j := job.NewTask(s.store.conf, s.store.elastic, &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "1",
				},
				JobId: "1",
				Data:  &rpcx.QuickStoreEvent_FullSiteCrawler{},
			})

			defer ApplyFuncReturn(s.store.Increment).Reset()
			defer ApplyFuncReturn(s.store.Decrement).Reset()
			defer ApplyMethodReturn(j, "FullSiteCrawler", errors.New("")).Reset()
			err := s.store.HandleEvent(context.Background(), &rpcx.QuickStoreEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "1",
				},
				JobId: "1",
				Data:  &rpcx.QuickStoreEvent_FullSiteCrawler{},
			})
			So(err, ShouldBeError)
		})
	})
}

func (s *HandlerSuit) TestQuickStore_StateQuery() {
	Convey("TestQuickStore_StateQuery", s.T(), func() {
		Convey("pass", func() {
			err := s.store.StateQuery(context.Background(), &rpcx.StateQueryRequest{}, &rpcx.StateQueryResponse{})
			So(err, ShouldBeNil)
		})
	})
}
