package handler

import (
	"context"
	"crawler/internal/config"
	"crawler/internal/job"
	"crawler/internal/req"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"golang.org/x/sync/errgroup"
	"strings"
	"sync"
)

type Crawler struct {
	conf       *config.Config
	jobs       map[string]int64
	remainJobs map[string]int64
	lock       sync.Mutex
	srv        micro.Service
}

func NewCrawler(conf *config.Config, srv micro.Service) Crawler {
	req.EnableInsecureTLS(true)

	return Crawler{
		conf:       conf,
		srv:        srv,
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
}

func (c *Crawler) Increment(taskId string) {
	c.lock.Lock()
	defer c.lock.Unlock()

	if count, ok := c.jobs[taskId]; ok {
		c.jobs[taskId] = count + 1
	} else {
		c.jobs[taskId] = int64(1)
	}

	if count, ok := c.remainJobs[taskId]; ok {
		c.remainJobs[taskId] = count + 1
	} else {
		c.remainJobs[taskId] = int64(1)
	}

	logger.Infof("the Crawler jobs total:%d remain:%d", c.jobs[taskId], c.remainJobs[taskId])
}

func (c *Crawler) Decrement(taskId string) {
	c.lock.Lock()
	defer c.lock.Unlock()

	if count, ok := c.remainJobs[taskId]; ok {
		remain := count - 1
		c.remainJobs[taskId] = remain
		logger.Info("the Crawler remain jobs:", remain)
	}
}
func (c *Crawler) StateQuery(ctx context.Context, req *rpcx.StateQueryRequest, rsp *rpcx.StateQueryResponse) error {
	logger.Infof("Received Crawler.StateQuery request: %v", req)

	if count, ok := c.jobs[req.TaskId]; ok {
		rsp.Total = count
	}

	if remain, ok := c.remainJobs[req.TaskId]; ok {
		rsp.Remain = remain
	}

	return nil
}

func (c *Crawler) FinishedNotify(ctx context.Context, req *rpcx.FinishedNotifyRequest, rsp *rpcx.FinishedNotifyResponse) error {
	logger.Infof("Received Crawler.FinishedNotify request: %v", req)

	c.lock.Lock()
	defer c.lock.Unlock()

	delete(c.jobs, req.TaskId)
	delete(c.remainJobs, req.TaskId)

	return nil
}

func (c *Crawler) HandleEvent(ctx context.Context, event *rpcx.CrawlerEvent) error {
	logger.Infof("Received Crawler.HandleEvent request: %v", event)

	if event.TaskInfo.TaskId == "" || event.JobId == "" {
		logger.Errorf("参数错误 event: %+v", event)
		return errors.New("参数错误")
	}

	c.Increment(event.TaskInfo.TaskId)

	defer func() {
		c.Decrement(event.TaskInfo.TaskId)
	}()

	if len(event.TaskInfo.CrawlerUrlBlackKey) > 0 {
		blacklist := strings.Split(event.TaskInfo.CrawlerUrlBlackKey, "|")

		for _, b := range blacklist {
			if strings.Contains(event.Url, b) {
				logger.Infof("the request is in blacklist url not crawler request: %v", event)
				return nil
			}
		}
	}

	j := job.NewJob(event, c.conf.CrawlerConf)

	var eg errgroup.Group

	if event.TaskInfo.IsCrawlerAllUrl {
		eg.Go(func() error {
			err := j.SiteURL(c.srv)

			if err != nil {
				logger.Errorf("Crawler SiteURL error: %v event: %v", err, event)
				return err
			}
			return nil
		})
	}

	eg.Go(func() error {
		dataAnalysisEvent, err := j.Normal()
		if err != nil {
			logger.Errorf("Crawler get Normal error: %v event: %v", err, event)
			return err
		}

		err = c.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceDataAnalysis, dataAnalysisEvent))

		if err != nil {
			logger.Errorf("Crawler send Normal error: %v event: %v", err, event)
			return err
		}

		logger.Infof("Crawler send to ServiceDataAnalysis event: %v", dataAnalysisEvent)
		return nil
	})

	err := eg.Wait()
	if err != nil {
		logger.Errorf("Crawler errgroup.Group error: %v event: %v", err, event)
		return err
	}
	return nil
}
