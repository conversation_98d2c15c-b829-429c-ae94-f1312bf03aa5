package handler

import (
	"bytes"
	"encoding/json"
	"errors"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"io/ioutil"
	"net/http"
	"testing"
)

type RuleSuit struct {
	suite.Suite
}

func TestRuleSuit(t *testing.T) {
	s := &RuleSuit{}
	suite.Run(t, s)
}

func (s *RuleSuit) TestGetRules() {
	<PERSON><PERSON>("TestGetRules", s.T(), func() {
		<PERSON>vey("http.NewRequest error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, errors.New("")).Reset()
			_, err := GetRules("")
			So(err, ShouldBeError)
		})

		Convey("client.Do error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			defer ApplyMethodReturn(&http.Client{}, "Do", nil, errors.New("")).Reset()
			_, err := GetRules("")
			So(err, ShouldBeError)
		})

		<PERSON>vey("json.NewDecoder error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", errors.New("")).Reset()

			_, err := GetRules("")
			So(err, ShouldBeError)
		})

		Convey("http.StatusOK no ok", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			_, err := GetRules("")
			So(err, ShouldBeError)
		})

		Convey("json.Unmarshal error", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			defer ApplyFuncReturn(json.Marshal, nil, nil).Reset()
			defer ApplyFuncReturn(json.Unmarshal, errors.New("")).Reset()
			_, err := GetRules("")
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(http.NewRequest, nil, nil).Reset()
			do := http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader([]byte("hello world"))),
			}
			defer ApplyMethodReturn(&http.Client{}, "Do", &do, nil).Reset()
			defer ApplyMethodReturn(json.NewDecoder(do.Body), "Decode", nil).Reset()
			defer ApplyFuncReturn(json.Marshal, nil, nil).Reset()
			defer ApplyFuncReturn(json.Unmarshal, nil).Reset()
			_, err := GetRules("")
			So(err, ShouldBeNil)
		})
	})
}
