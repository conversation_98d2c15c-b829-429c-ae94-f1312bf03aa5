package handler

import (
	"context"
	"errors"
	"git.gobies.org/fofa-backend/rawgrab/grab"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/rawgrab"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	m "git.gobies.org/shared-platform/foscan/pkg/model"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/logger"
	"rawgrab/internal/job"
	"rawgrab/internal/model"
	"strings"
	"sync"
)

type RawGrab struct {
	conf       *model.Config
	grab       rawgrab.GrabScanner
	srv        micro.Service
	jobs       map[string]int64
	remainJobs map[string]int64
	protocols  []m.Protocol
	lock       sync.Mutex
}

func NewRawGrab(conf *model.Config, srv micro.Service) RawGrab {
	grabOptions := make([]func(plugin *grab.GrabPlugin), 0)

	// 初始化服务器ip
	grabOptions = append(grabOptions, grab.WithInitRegServerIp(conf.ServerIp))

	// 初始化信任证书
	if conf.TrustCertFile != "" {
		grabOptions = append(grabOptions, grab.WithInitTrustCerts(conf.TrustCertFile))
	}

	optGraber := make([]func(scanner rawgrab.GrabScanner), 0, 3)

	if conf.TraverseAllProtocol {
		optGraber = append(optGraber, grab.WithAllRetry())
	}

	//加载特定产品的重试协议
	optGraber = append(optGraber, grab.WithProductType("fofa/foeye"))

	defaultPlugin := grab.NewGrabPlugin(grabOptions...)
	g := defaultPlugin.NewGrab(optGraber...)

	protocols, err := GetAllProtocols(conf.ApiHost)

	if err != nil {
		logger.Warnf("NewRawGrab GetAllProtocols Failed ApiHost:%s error:%v", conf.ApiHost, err)
	}

	return RawGrab{
		conf:       conf,
		srv:        srv,
		grab:       g,
		protocols:  protocols,
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
}

func (g *RawGrab) StateQuery(ctx context.Context, req *pb.StateQueryRequest, rsp *pb.StateQueryResponse) error {
	logger.Infof("Received RawGrab.StateQuery request: %v", req)

	if count, ok := g.jobs[req.TaskId]; ok {
		rsp.Total = count
	}

	if remain, ok := g.remainJobs[req.TaskId]; ok {
		rsp.Remain = remain
	}

	return nil
}

func (g *RawGrab) FinishedNotify(ctx context.Context, req *pb.FinishedNotifyRequest, rsp *pb.FinishedNotifyResponse) error {
	logger.Infof("Received RawGrab.FinishedNotify request: %v", req)
	g.lock.Lock()
	defer g.lock.Unlock()

	delete(g.jobs, req.TaskId)
	delete(g.remainJobs, req.TaskId)

	return nil
}

func (g *RawGrab) CustomProtocolNotify(ctx context.Context, req *pb.EmptyNotifyRequest, rsp *pb.EmptyNotifyResponse) error {
	protocols, err := GetAllProtocols(g.conf.ApiHost)
	if err != nil {
		logger.Warnf("NewRawGrab GetAllProtocols Failed ApiHost:%s", g.conf.ApiHost)
	}

	if len(protocols) != 0 {
		logger.Info("Received CustomProtocolNotify protocols:", protocols)
		g.protocols = protocols
	}

	return nil
}

func (g *RawGrab) Increment(taskId string) {
	g.lock.Lock()
	defer g.lock.Unlock()

	if count, ok := g.jobs[taskId]; ok {
		g.jobs[taskId] = count + 1
	} else {
		g.jobs[taskId] = int64(1)
	}

	if count, ok := g.remainJobs[taskId]; ok {
		g.remainJobs[taskId] = count + 1
	} else {
		g.remainJobs[taskId] = int64(1)
	}

	logger.Infof("the RawGrab jobs total:%d remain:%d", g.jobs[taskId], g.remainJobs[taskId])
}

func (g *RawGrab) Decrement(taskId string) {
	g.lock.Lock()
	defer g.lock.Unlock()

	if count, ok := g.remainJobs[taskId]; ok {
		remain := count - 1
		g.remainJobs[taskId] = remain
		logger.Info("the RawGrab remain jobs:", remain)
	}
}

func (g *RawGrab) HandleEvent(ctx context.Context, event *pb.RawGrabEvent) error {
	logger.Infof("Received RawGrab HandleEvent request: %v", event)

	if event.TaskInfo.TaskId == "" {
		logger.Errorf("the task_id is empty. event: %v", event)
		return errors.New("the task_id is empty")
	}

	g.Increment(event.TaskInfo.TaskId)

	defer func() {
		g.Decrement(event.TaskInfo.TaskId)
	}()

	j := job.NewJob(g.conf, g.grab, g.protocols)
	dataAnalysisEvent, err := j.Grab(event)

	if err != nil {
		logger.Errorf("Received HandleEvent error: %v event: %v", err, event)
		return err
	}

	logger.Infof("RawGrab job.Grab successful, dataAnalysisEvent: %v", dataAnalysisEvent)

	if strings.ToLower(dataAnalysisEvent.GetNormal().Protocol) == "http" ||
		strings.ToLower(dataAnalysisEvent.GetNormal().Protocol) == "https" {

		checkURLEvent := &pb.CheckURLEvent{
			TaskInfo:     event.TaskInfo,
			JobId:        event.JobId,
			Ip:           dataAnalysisEvent.GetNormal().Ip,
			Port:         dataAnalysisEvent.GetNormal().Port,
			Protocol:     dataAnalysisEvent.GetNormal().Protocol,
			BaseProtocol: dataAnalysisEvent.GetNormal().BaseProtocol,
		}

		logger.Infof("Publishing to checkURL: %v", checkURLEvent)
		err = g.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceCheckUrl, checkURLEvent))

		if err != nil {
			logger.Errorf("Publish Event to checkURL error: %v  event:%v", err, event)
		} else {
			logger.Infof("Successfully published to checkURL")
		}
	}

	logger.Infof("Publishing to DataAnalysis: %v", dataAnalysisEvent)
	err = g.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceDataAnalysis, dataAnalysisEvent))
	if err != nil {
		logger.Errorf("Publish Event to DataAnalysis error: %v  event:%v", err, event)
		return err
	}
	logger.Infof("Successfully published to DataAnalysis")
	return nil
}
