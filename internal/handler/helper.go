package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"net/http"
)

type Res struct {
	StatusCode int         `json:"status_code"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data"`
}

func GetAllProtocols(apiHost string) ([]model.Protocol, error) {
	uri := fmt.Sprintf("%s/api/v1/protocols", apiHost)
	req, err := http.NewRequest(http.MethodGet, uri, nil)

	if err != nil {
		return nil, err
	}

	client := http.Client{}
	do, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	defer do.Body.Close()

	var res Res
	err = json.NewDecoder(do.Body).Decode(&res)
	if err != nil {
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		return nil, errors.New(res.Message)
	}

	marshal, err := json.Marshal(res.Data)
	if err != nil {
		return nil, err
	}
	var protocols []model.Protocol

	err = json.Unmarshal(marshal, &protocols)
	if err != nil {
		return nil, err
	}

	return protocols, nil
}
