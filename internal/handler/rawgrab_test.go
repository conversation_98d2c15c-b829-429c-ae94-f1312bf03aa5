package handler

import (
	"context"
	"errors"
	"git.gobies.org/fofa-backend/rawgrab/grab"
	m "git.gobies.org/shared-platform/foscan/pkg/model"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"rawgrab/internal/job"
	"rawgrab/internal/model"
	"sync"
	"testing"
)

type RawGrabSuit struct {
	suite.Suite
	grab RawGrab
}

func TestRawGrabSuit(t *testing.T) {
	s := &RawGrabSuit{}
	defaultPlugin := grab.NewGrabPlugin()
	g := defaultPlugin.NewGrab()
	s.grab = RawGrab{
		srv:        micro.NewService(),
		grab:       g,
		conf:       &model.Config{},
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
	suite.Run(t, s)
}

func (s *RawGrabSuit) TestNewRawGrab() {
	Convey("TestNewRawGrab", s.T(), func() {
		conf := &model.Config{
			Timeout: "5s",
		}
		srv := micro.NewService()
		Convey("pass", func() {
			g := RawGrab{
				conf:       conf,
				srv:        srv,
				grab:       s.grab.grab,
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			}
			defer ApplyFuncReturn(GetAllProtocols, nil, errors.New("")).Reset()
			r := NewRawGrab(conf, srv)
			So(r, ShouldResemble, g)
		})
	})
}

func (s *RawGrabSuit) TestRawGrab_CustomProtocolNotify() {
	Convey("TestRawGrab_CustomProtocolNotify", s.T(), func() {
		Convey("pass protocols empty", func() {
			defer ApplyFuncReturn(GetAllProtocols, nil, errors.New("")).Reset()
			err := s.grab.CustomProtocolNotify(context.Background(), &pb.EmptyNotifyRequest{}, &pb.EmptyNotifyResponse{})
			So(err, ShouldBeNil)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(GetAllProtocols, []m.Protocol{{
				Name: "",
			}}, nil).Reset()
			err := s.grab.CustomProtocolNotify(context.Background(), &pb.EmptyNotifyRequest{}, &pb.EmptyNotifyResponse{})
			So(err, ShouldBeNil)
		})
	})
}

func TestRawGrab_Decrement(t *testing.T) {
	type fields struct {
		conf       *model.Config
		srv        micro.Service
		jobs       map[string]int64
		remainJobs map[string]int64
		protocols  []m.Protocol
		lock       sync.Mutex
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &RawGrab{
				conf:       tt.fields.conf,
				srv:        tt.fields.srv,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				protocols:  tt.fields.protocols,
				lock:       tt.fields.lock,
			}
			g.Decrement(tt.args.taskId)
		})
	}
}

func TestRawGrab_FinishedNotify(t *testing.T) {
	type fields struct {
		conf       *model.Config
		srv        micro.Service
		jobs       map[string]int64
		remainJobs map[string]int64
		protocols  []m.Protocol
		lock       sync.Mutex
	}
	type args struct {
		ctx context.Context
		req *pb.FinishedNotifyRequest
		rsp *pb.FinishedNotifyResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &pb.FinishedNotifyRequest{},
				rsp: &pb.FinishedNotifyResponse{},
			},
			fields: fields{
				conf:       &model.Config{},
				srv:        micro.NewService(),
				protocols:  []m.Protocol{},
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &RawGrab{
				conf:       tt.fields.conf,
				srv:        tt.fields.srv,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				protocols:  tt.fields.protocols,
				lock:       tt.fields.lock,
			}
			if err := g.FinishedNotify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("FinishedNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func (s *RawGrabSuit) TestRawGrab_HandleEvent() {
	Convey("TestRawGrab_HandleEvent", s.T(), func() {
		Convey("event.TaskInfo.TaskId empty", func() {
			err := s.grab.HandleEvent(context.Background(), &pb.RawGrabEvent{
				TaskInfo: &pb.TaskInfo{},
			})
			So(err, ShouldResemble, errors.New("the task_id is empty"))
		})

		Convey("j.Grab error", func() {
			j := job.NewJob(s.grab.conf, s.grab.grab, s.grab.protocols)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyFuncReturn(j.Grab, &pb.DataAnalysisEvent{}, errors.New("")).Reset()
			err := s.grab.HandleEvent(context.Background(), &pb.RawGrabEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId: "1",
				},
			})
			So(err, ShouldBeError)
		})

		Convey("handel http or https", func() {
			j := job.NewJob(s.grab.conf, s.grab.grab, s.grab.protocols)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "Grab", &pb.DataAnalysisEvent{
				Normal: &pb.Normal{
					Protocol: "http",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.grab.srv.Client(), "Publish", nil).Reset()
			err := s.grab.HandleEvent(context.Background(), &pb.RawGrabEvent{
				TaskInfo: &pb.TaskInfo{
					TaskId: "1",
				},
			})
			So(err, ShouldBeNil)
		})
	})
}

func TestRawGrab_Increment(t *testing.T) {
	type fields struct {
		conf       *model.Config
		srv        micro.Service
		jobs       map[string]int64
		remainJobs map[string]int64
		protocols  []m.Protocol
		lock       sync.Mutex
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
			fields: fields{
				conf:       &model.Config{},
				srv:        micro.NewService(),
				protocols:  []m.Protocol{},
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &RawGrab{
				conf:       tt.fields.conf,
				srv:        tt.fields.srv,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				protocols:  tt.fields.protocols,
				lock:       tt.fields.lock,
			}
			g.Increment(tt.args.taskId)
		})
	}
}

func TestRawGrab_StateQuery(t *testing.T) {
	type fields struct {
		conf       *model.Config
		srv        micro.Service
		jobs       map[string]int64
		remainJobs map[string]int64
		protocols  []m.Protocol
		lock       sync.Mutex
	}
	type args struct {
		ctx context.Context
		req *pb.StateQueryRequest
		rsp *pb.StateQueryResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &pb.StateQueryRequest{},
				rsp: &pb.StateQueryResponse{},
			},
			fields: fields{
				conf:       &model.Config{},
				srv:        micro.NewService(),
				protocols:  []m.Protocol{},
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &RawGrab{
				conf:       tt.fields.conf,
				srv:        tt.fields.srv,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				protocols:  tt.fields.protocols,
				lock:       tt.fields.lock,
			}
			if err := g.StateQuery(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("StateQuery() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
