package handler

import (
	"bytes"
	"context"
	"data_analysis/internal/config"
	"data_analysis/internal/job"
	"data_analysis/internal/sutra"
	"encoding/json"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/hashicorp/go-version"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"math"
	"sync"
	"testing"
)

type HandleSuit struct {
	suite.Suite
	dataAnalysis DataAnalysis
}

func TestHandleSuit(t *testing.T) {
	s := &HandleSuit{}
	newSutra, err := sutra.NewSutra()
	assert.NoError(t, err)
	s.dataAnalysis = DataAnalysis{
		srv:        micro.NewService(),
		conf:       &config.Config{},
		sutra:      newSutra,
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
	suite.Run(t, s)
}
func (s *HandleSuit) TestDataAnalysis_CustomRuleAdd() {
	Convey("TestDataAnalysis_CustomRuleAdd", s.T(), func() {
		Convey("json.NewEncoder(&buf).Encode(rule) error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleAdd(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("sutra.Add error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Add", nil, errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleAdd(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Add", nil, nil).Reset()
			err := s.dataAnalysis.CustomRuleAdd(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *HandleSuit) TestDataAnalysis_CustomRuleDelete() {
	Convey("TestDataAnalysis_CustomRuleDelete", s.T(), func() {
		Convey("json.NewEncoder(&buf).Encode(rule) error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleDelete(context.Background(), &rpcx.CustomRuleIDRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("sutra.Delete error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Delete", nil, errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleDelete(context.Background(), &rpcx.CustomRuleIDRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Delete", nil, nil).Reset()
			err := s.dataAnalysis.CustomRuleDelete(context.Background(), &rpcx.CustomRuleIDRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *HandleSuit) TestDataAnalysis_CustomRuleUpdate() {
	Convey("TestDataAnalysis_CustomRuleUpdate", s.T(), func() {
		Convey("json.NewEncoder(&buf).Encode(rule) error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleUpdate(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("sutra.Update error", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Update", errors.New("")).Reset()
			err := s.dataAnalysis.CustomRuleUpdate(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			var buf bytes.Buffer
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Update", nil).Reset()
			err := s.dataAnalysis.CustomRuleUpdate(context.Background(), &rpcx.CustomRuleRequest{}, &rpcx.RuleResponse{})
			So(err, ShouldBeNil)
		})
	})
}

func TestDataAnalysis_Decrement(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
		sutra      sutra.Sutra
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DataAnalysis{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
				sutra:      tt.fields.sutra,
			}
			d.Decrement(tt.args.taskId)
		})
	}
}

func TestDataAnalysis_FinishedNotify(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
		sutra      sutra.Sutra
	}
	type args struct {
		ctx context.Context
		req *rpcx.FinishedNotifyRequest
		rsp *rpcx.FinishedNotifyResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &rpcx.FinishedNotifyRequest{},
				rsp: &rpcx.FinishedNotifyResponse{},
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DataAnalysis{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
				sutra:      tt.fields.sutra,
			}
			if err := d.FinishedNotify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("FinishedNotify() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func (s *HandleSuit) TestDataAnalysis_HandleEvent() {
	Convey("TestDataAnalysis_HandleEvent", s.T(), func() {
		Convey("param error", func() {
			err := s.dataAnalysis.HandleEvent(context.Background(), &rpcx.DataAnalysisEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "",
				},
				JobId: "",
			})
			So(err, ShouldResemble, errors.New("参数错误"))
		})

		Convey("j.Run error", func() {
			j := job.NewJob(&rpcx.DataAnalysisEvent{}, s.dataAnalysis.sutra)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "Run", nil, errors.New("")).Reset()
			err := s.dataAnalysis.HandleEvent(context.Background(), &rpcx.DataAnalysisEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "1",
				},
				JobId: "1",
			})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			j := job.NewJob(&rpcx.DataAnalysisEvent{}, s.dataAnalysis.sutra)
			defer ApplyFuncReturn(job.NewJob, j).Reset()
			defer ApplyMethodReturn(j, "Run", &rpcx.DataAnalysisEvent{
				Normal: &rpcx.Normal{},
			}, nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.srv.Client(), "Publish", nil).Reset()
			err := s.dataAnalysis.HandleEvent(context.Background(), &rpcx.DataAnalysisEvent{
				TaskInfo: &rpcx.TaskInfo{
					TaskId: "1",
				},
				JobId: "1",
			})
			So(err, ShouldBeNil)
		})
	})
}

func TestDataAnalysis_Increment(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
		sutra      sutra.Sutra
	}
	type args struct {
		taskId string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{
				taskId: "1",
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DataAnalysis{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
				sutra:      tt.fields.sutra,
			}
			d.Increment(tt.args.taskId)
		})
	}
}

func TestDataAnalysis_StateQuery(t *testing.T) {
	type fields struct {
		conf       *config.Config
		jobs       map[string]int64
		remainJobs map[string]int64
		lock       sync.Mutex
		srv        micro.Service
		sutra      sutra.Sutra
	}
	type args struct {
		ctx context.Context
		req *rpcx.StateQueryRequest
		rsp *rpcx.StateQueryResponse
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				req: &rpcx.StateQueryRequest{},
				rsp: &rpcx.StateQueryResponse{},
			},
			fields: fields{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DataAnalysis{
				conf:       tt.fields.conf,
				jobs:       tt.fields.jobs,
				remainJobs: tt.fields.remainJobs,
				lock:       tt.fields.lock,
				srv:        tt.fields.srv,
				sutra:      tt.fields.sutra,
			}
			if err := d.StateQuery(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("StateQuery() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func (s *HandleSuit) TestNewDataAnalysis() {
	Convey("TestNewDataAnalysis", s.T(), func() {
		Convey("pass", func() {
			var buf bytes.Buffer

			defer ApplyFuncReturn(GetRules, []model.Rule{
				{
					Product: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(json.NewEncoder(&buf), "Encode", nil).Reset()
			defer ApplyMethodReturn(s.dataAnalysis.sutra, "Add", nil, nil).Reset()
			d := NewDataAnalysis(&config.Config{}, micro.NewService(), s.dataAnalysis.sutra)
			So(d, ShouldResemble, DataAnalysis{
				conf:       &config.Config{},
				srv:        micro.NewService(),
				jobs:       make(map[string]int64),
				remainJobs: make(map[string]int64),
				sutra:      s.dataAnalysis.sutra,
			})
		})
	})
}

func Test_version(t *testing.T) {
	versions := []string{
		"1.2.3",
		"1.1.3",
		"1.1.2",
		"1.1.02",
		"1.0",
		"1.3",
		"2",
		"0.4.2",
		"v0.4.2",
		"v0.4.2-alpha",
		"v0.4.2-beta",
		"0.6.1.9.8.9.101.8.9",
		"0.6.0.9.8.9.101.8.9",
	}
	for _, r := range versions {
		versionFromStringToNumber(r)
	}
}

func versionFromStringToNumber(s string) (float64, error) {
	v, err := version.NewVersion(s)
	if err != nil {
		return 0, err
	}

	p := v.Segments()

	if len(p) > 10 || len(p) == 0 {
		return 0, errors.New("")
	}

	var versionNum float64
	for i, n := range p {
		versionNum += float64(n) * math.Pow(10, float64(-i))
	}

	prerelease := v.Prerelease()
	if prerelease != "" {
		versionNum -= math.Pow(float64(prerelease[0]), -1)
	}

	fmt.Println(s, "|", v.String(), "=", versionNum)

	return versionNum, nil
}
