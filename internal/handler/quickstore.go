package handler

import (
	"context"
	"errors"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/job"
	"git.gobies.org/shared-platform/quickstore/internal/model"
	"github.com/elastic/go-elasticsearch/v6"
	"go-micro.dev/v4/logger"
	"sync"
)

type QuickStore struct {
	jobs       map[string]int64
	remainJobs map[string]int64
	lock       sync.Mutex
	conf       *model.Config
	elastic    *elasticsearch.Client
}

func (s *QuickStore) StateQuery(ctx context.Context, req *rpcx.StateQueryRequest, rsp *rpcx.StateQueryResponse) error {
	logger.Infof("Received QuickStore.StateQuery request: %v", req)

	if count, ok := s.jobs[req.TaskId]; ok {
		rsp.Total = count
	}

	if remain, ok := s.remainJobs[req.TaskId]; ok {
		rsp.Remain = remain
	}

	return nil
}

func (s *QuickStore) FinishedNotify(ctx context.Context, req *rpcx.FinishedNotifyRequest, rsp *rpcx.FinishedNotifyResponse) error {
	logger.Infof("Received QuickStore.FinishedNotify request: %v", req)

	s.lock.Lock()
	defer s.lock.Unlock()

	delete(s.jobs, req.TaskId)
	delete(s.remainJobs, req.TaskId)

	return nil
}

func (s *QuickStore) Increment(taskId string) {
	s.lock.Lock()
	defer s.lock.Unlock()

	if count, ok := s.jobs[taskId]; ok {
		s.jobs[taskId] = count + 1
	} else {
		s.jobs[taskId] = int64(1)
	}

	if count, ok := s.remainJobs[taskId]; ok {
		s.remainJobs[taskId] = count + 1
	} else {
		s.remainJobs[taskId] = int64(1)
	}

	logger.Infof("the QuickStore jobs total:%d remain:%d", s.jobs[taskId], s.remainJobs[taskId])
}

func (s *QuickStore) Decrement(taskId string) {
	s.lock.Lock()
	defer s.lock.Unlock()

	if count, ok := s.remainJobs[taskId]; ok {
		remain := count - 1
		s.remainJobs[taskId] = remain
		logger.Info("the QuickStore remain jobs:", remain)
	}
}

func (s *QuickStore) HandleEvent(ctx context.Context, event *rpcx.QuickStoreEvent) error {
	logger.Infof("Received QuickStore.HandleEvent event. %v", event)

	if event.TaskInfo.TaskId == "" ||
		event.JobId == "" ||
		event.Data == nil {
		logger.Errorf("参数错误 event:%+v", event)
		return errors.New("参数错误")
	}

	s.Increment(event.TaskInfo.TaskId)

	defer func() {
		s.Decrement(event.TaskInfo.TaskId)
	}()

	j := job.NewTask(s.conf, s.elastic, event)

	var err error

	switch event.Data.(type) {
	case *rpcx.QuickStoreEvent_Normal:
		err = j.Normal()
	case *rpcx.QuickStoreEvent_FullSiteCrawler:
		err = j.FullSiteCrawler()
	default:
		logger.Errorf("不支持的数据类型 event:%+v", event)
		return errors.New("不支持的数据类型")
	}

	if err != nil {
		logger.Errorf("the quick store is error:%v; event:%v", err, event)
	}

	return err
}

func NewQuickStore(conf *model.Config, client *elasticsearch.Client) QuickStore {
	return QuickStore{
		conf:       conf,
		elastic:    client,
		jobs:       make(map[string]int64),
		remainJobs: make(map[string]int64),
	}
}
