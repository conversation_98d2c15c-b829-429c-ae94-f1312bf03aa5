package service

import (
	"api/internal/httptransport/request"
	"errors"
	"github.com/samber/lo"
	"github.com/yl2chen/cidranger"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/registry"
	"go-micro.dev/v4/selector"
	"net"
	"os"
	"strings"
)

func IsFile(path string) bool {
	f, err := os.Stat(path)
	if err != nil {
		return false
	}

	if f.IsDir() {
		return false
	}
	return true
}

func IPTargetToIpList(ipTarget *request.IPTarget) ([]string, error) {
	if ipTarget == nil {
		return []string{}, nil
	}

	var ipList []string

	if len(ipTarget.List) > 0 {
		ipList = append(ipList, ipTarget.List...)
	}

	if len(ipTarget.File) > 0 {
		if !IsFile(ipTarget.File) {
			return ipList, errors.New("文件不存在")
		}
		content, err := os.ReadFile(ipTarget.File)
		if err != nil {
			return nil, err
		}
		items := strings.Split(string(content), "\n")
		items = lo.Filter(items, func(s string, i int) bool {
			return s != ""
		})
		ipList = append(ipList, items...)
	}

	ipList = lo.Uniq(ipList)

	return ipList, nil
}

func TransferSoftHard(level int) string {
	if level == 1 {
		return "1" // 硬件
	}

	return "2" // 软件
}

func GetServiceNodesByName(srv micro.Service, serviceName string) ([]*registry.Node, error) {
	services, err := GetServicesByName(srv, serviceName)
	if err != nil {
		return nil, err
	}

	nodes := make([]*registry.Node, 0, len(services))
	for _, service := range services {
		nodes = append(nodes, service.Nodes...)
	}
	return nodes, nil
}

func GetServicesByName(srv micro.Service, serviceName string) ([]*registry.Service, error) {
	return srv.Client().Options().Registry.GetService(serviceName)
}

func NodeSelectOption(nodeID string) selector.SelectOption {
	return selector.WithStrategy(SelectStrategyByNodeID(nodeID))
}

// SelectStrategyByNodeID 只筛选指定UUID的节点
func SelectStrategyByNodeID(nodeID string) selector.Strategy {
	return func(services []*registry.Service) selector.Next {
		nodes := make([]*registry.Node, 0, len(services))

		for _, service := range services {
			nodes = append(nodes, service.Nodes...)
		}

		return func() (*registry.Node, error) {
			if len(nodes) == 0 {
				return nil, selector.ErrNoneAvailable
			}

			for _, node := range nodes {
				if node.Id == nodeID {
					return node, nil
				}
			}
			return nil, selector.ErrNoneAvailable
		}
	}
}

func NewRangerStrategy(blacklist []string) (cidranger.Ranger, error) {
	ranger := cidranger.NewPCTrieRanger()
	for _, b := range blacklist {
		_, n, err := net.ParseCIDR(b)
		if err != nil {
			logger.Warnf("net.ParseCIDR error %s", b)
			continue
		}

		err = ranger.Insert(cidranger.NewBasicRangerEntry(*n))
		if err != nil {
			return nil, err
		}
	}

	return ranger, nil
}

func isValidIPv4(ip string) bool {
	parsedIP := net.ParseIP(ip)

	return parsedIP != nil && parsedIP.To4() != nil
}

func isValidIPv6(ip string) bool {
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() == nil
}

func isValidIPv4CIDR(cidr string) bool {
	_, ipNet, err := net.ParseCIDR(cidr)
	return err == nil && ipNet.IP.To4() != nil
}

func isValidIPv6CIDR(cidr string) bool {
	_, ipNet, err := net.ParseCIDR(cidr)
	return err == nil && ipNet.IP.To4() == nil
}
