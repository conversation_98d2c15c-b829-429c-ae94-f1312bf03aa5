package service

import (
	"api/internal/httptransport/response"
	"api/internal/license"
	"context"
	"encoding/json"
	"errors"
	go_crypto "git.gobies.org/foeye/key-license/go-crypto"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
)

type LicenseSuit struct {
	suite.Suite
	srv service
}

func TestLicenseSuit(t *testing.T) {
	s := &LicenseSuit{}
	s.srv = service{
		license: &license.License{
			Crypto: go_crypto.NewFilePath("", nil),
			Info:   go_crypto.ValidationRes{},
		},
	}
	suite.Run(t, s)
}

func (s *LicenseSuit) Test_service_ActivatedLicense() {
	Convey("TestGetServicesByName", s.T(), func() {
		Convey("os.WriteFile error", func() {
			defer ApplyFuncReturn(os.WriteFile, errors.New("")).Reset()
			err := s.srv.ActivatedLicense(context.Background(), "")
			So(err, ShouldBeError)
		})

		<PERSON>vey("s.license.Info.IsValid false", func() {
			defer ApplyFuncReturn(os.WriteFile, nil).Reset()
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = false
			}).Reset()
			err := s.srv.ActivatedLicense(context.Background(), "")
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(os.WriteFile, nil).Reset()
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = true
			}).Reset()
			err := s.srv.ActivatedLicense(context.Background(), "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *LicenseSuit) Test_service_GetIDKey() {
	Convey("Test_service_GetIDKey", s.T(), func() {
		Convey("os.ReadFile error", func() {
			defer ApplyFuncReturn(go_crypto.IsFileExist, true).Reset()
			defer ApplyFuncReturn(os.ReadFile, nil, errors.New("")).Reset()
			_, err := s.srv.GetIDKey(context.Background())
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(go_crypto.IsFileExist, true).Reset()
			defer ApplyFuncReturn(os.ReadFile, nil, nil).Reset()
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = true
			}).Reset()
			_, err := s.srv.GetIDKey(context.Background())
			So(err, ShouldBeNil)
		})
	})
}

func (s *LicenseSuit) Test_service_GetSystemInfo() {
	Convey("Test_service_GetSystemInfo", s.T(), func() {
		defaultModules := response.Modules{}

		Convey("LicenseState false", func() {
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = false
				s.srv.license.Info.FunctionModuleFile = ""
			}).Reset()

			res := s.srv.GetSystemInfo(context.Background())
			So(res.LicenseState, ShouldEqual, false)
		})

		Convey("defaultModules", func() {
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = true
				s.srv.license.Info.FunctionModuleFile = "1"
			}).Reset()
			defer ApplyFuncReturn(json.Unmarshal, errors.New("")).Reset()
			res := s.srv.GetSystemInfo(context.Background())
			So(res.Modules, ShouldResemble, defaultModules)
		})

		Convey("CustomPoc 1", func() {
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = true
				s.srv.license.Info.FunctionModuleFile = "{\"cp\":1}"
			}).Reset()
			res := s.srv.GetSystemInfo(context.Background())
			So(res.Modules.CustomPoc, ShouldResemble, 1)
		})

		Convey("LicenseState true", func() {
			defer ApplyMethodFunc(s.srv.license, "Verify", func() {
				s.srv.license.Info.IsValid = true
				s.srv.license.Info.FunctionModuleFile = ""
			}).Reset()
			res := s.srv.GetSystemInfo(context.Background())
			So(res.LicenseState, ShouldEqual, true)
		})
	})
}
