package service

import (
	"api/internal"
	"api/internal/httptransport/request"
	"api/internal/repository"
	"api/pkg/dns"
	"context"
	"errors"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/go-micro/plugins/v4/store/file"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"github.com/yl2chen/cidranger"
	"go-micro.dev/v4"
	"testing"
)

type TaskSuit struct {
	suite.Suite
	srv service
}

func TestTaskSuit(t *testing.T) {
	s := &TaskSuit{}
	s.srv = service{
		repo:       repository.NewRepository(file.NewStore(), file.NewStore()),
		micro:      micro.NewService(),
		dispatcher: rpcx.NewDispatcherTaskService("", micro.NewService().Client()),
	}
	suite.Run(t, s)
}

func (s *TaskSuit) Test_service_AddTask() {
	Convey("Test_service_AddTask", s.T(), func() {
		Convey("RequestParamMiss", func() {
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{})
			So(err.Error(), ShouldResemble, internal.RequestParamMiss)
		})

		Convey("NewRangerStrategy error", func() {
			defer ApplyFuncReturn(NewRangerStrategy, nil, errors.New("")).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
					},
				},
				HostInfos: []string{"", ""},
				Options:   request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})
		Convey("Resolution error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", nil, nil, nil, errors.New("")).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
					},
				},
				HostInfos: []string{"", ""},
				Options:   request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})
		Convey("IPTargetToIpList error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, errors.New("")).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})

		Convey("isValidIPv4 error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, nil).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
						IsIPV6:    false,
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{
						"1",
					},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})

		Convey("isValidIPv6 error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, nil).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
						IsIPV6:    true,
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{
						"1",
					},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})

		Convey("RPCRequest error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, errors.New("")).Reset()
			defer ApplyMethodReturn(&request.CreateRequest{}, "RPCRequest", &rpcx.DispatcherTaskStartRequest{}, errors.New("")).Reset()
			defer ApplyMethodReturn(s.srv.dispatcher, "Start", nil, errors.New("")).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
						IsIPV6:    false,
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{
						"***********",
					},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})

		Convey("dispatcher.Start error", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, errors.New("")).Reset()
			defer ApplyMethodReturn(&request.CreateRequest{}, "RPCRequest", &rpcx.DispatcherTaskStartRequest{}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dispatcher, "Start", nil, errors.New("")).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			r := dns.NewResolution(1, false, cidranger.NewPCTrieRanger())
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			defer ApplyFuncReturn(NewRangerStrategy, nil, nil).Reset()
			defer ApplyFuncReturn(dns.NewResolution, r).Reset()
			defer ApplyMethodReturn(r, "Resolution", []string{}, []string{}, nil, nil).Reset()
			defer ApplyFuncReturn(IPTargetToIpList, nil, errors.New("")).Reset()
			defer ApplyMethodReturn(&request.CreateRequest{}, "RPCRequest", &rpcx.DispatcherTaskStartRequest{}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dispatcher, "Start", nil, nil).Reset()
			_, err := s.srv.AddTask(context.Background(), &request.CreateRequest{
				PortScanner: &request.PortScanner{
					Options: request.PortScannerOptions{
						Blacklist: []string{"1"},
					},
				},
				HostInfos: []string{"", ""},
				IP: &request.IPTarget{
					List: []string{},
				},
				Options: request.RequestOptions{},
			})
			So(err, ShouldBeError)
		})
	})
}

func (s *TaskSuit) Test_service_DeleteTask() {
	Convey("Test_service_DeleteTask", s.T(), func() {
		Convey("dispatcher.Stop error", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Stop", nil, errors.New("")).Reset()
			err := s.srv.DeleteTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Stop", nil, nil).Reset()
			err := s.srv.DeleteTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *TaskSuit) Test_service_GetTask() {
	Convey("Test_service_GetTask", s.T(), func() {
		Convey("dispatcher.State error", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "State", nil, errors.New("")).Reset()
			_, err := s.srv.GetTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "State", &rpcx.DispatcherTaskStateResponse{}, nil).Reset()
			_, err := s.srv.GetTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *TaskSuit) Test_service_PauseTask() {
	Convey("Test_service_PauseTask", s.T(), func() {
		Convey("dispatcher.Pause error", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Pause", nil, errors.New("")).Reset()
			_, err := s.srv.PauseTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Pause", &rpcx.DispatcherTaskPauseResponse{}, nil).Reset()
			_, err := s.srv.PauseTask(context.Background(), &request.TaskIDRequest{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *TaskSuit) Test_service_ResumeTask() {
	Convey("Test_service_PauseTask", s.T(), func() {
		Convey("GetRate error", func() {
			defer ApplyMethodReturn(&request.TaskResumeRequest{Bandwidth: "300"}, "GetRate", nil, errors.New("")).Reset()
			_, err := s.srv.ResumeTask(context.Background(), &request.TaskResumeRequest{})
			So(err, ShouldBeError)
		})

		Convey("dispatcher.Pause error", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Resume", nil, errors.New("")).Reset()
			_, err := s.srv.ResumeTask(context.Background(), &request.TaskResumeRequest{})
			So(err, ShouldBeError)
		})

		Convey("pass without rate", func() {
			defer ApplyMethodReturn(s.srv.dispatcher, "Resume", &rpcx.DispatcherTaskResumeResponse{}, nil).Reset()
			_, err := s.srv.ResumeTask(context.Background(), &request.TaskResumeRequest{})
			So(err, ShouldBeNil)
		})

		Convey("pass with rate", func() {
			defer ApplyMethodReturn(&request.TaskResumeRequest{Bandwidth: "300"}, "GetRate", nil, errors.New("")).Reset()
			defer ApplyMethodReturn(s.srv.dispatcher, "Resume", &rpcx.DispatcherTaskResumeResponse{}, nil).Reset()
			_, err := s.srv.ResumeTask(context.Background(), &request.TaskResumeRequest{})
			So(err, ShouldBeNil)
		})
	})
}
