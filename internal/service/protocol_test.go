package service

import (
	"api/internal/httptransport/request"
	"api/internal/repository"
	"context"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/go-micro/plugins/v4/store/file"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/registry"
	"testing"
)

type ProtocolSuit struct {
	suite.Suite
	srv service
}

func TestProtocolSuit(t *testing.T) {
	s := &ProtocolSuit{}
	s.srv = service{
		repo:    repository.NewRepository(file.NewStore(), file.NewStore()),
		micro:   micro.NewService(),
		rawGrab: rpcx.NewRawGrabService("", micro.NewService().Client()),
	}
	suite.Run(t, s)
}
func (s *ProtocolSuit) Test_service_AddProtocols() {
	Convey("Test_service_AddProtocols", s.T(), func() {
		Convey("repo.AddProtocols error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddProtocols", errors.New("")).Reset()
			err := s.srv.AddProtocols(context.Background(), []model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddProtocols", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			err := s.srv.AddProtocols(context.Background(), []model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("CustomProtocolNotify error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddProtocols", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, errors.New("")).Reset()
			err := s.srv.AddProtocols(context.Background(), []model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddProtocols", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, nil).Reset()
			err := s.srv.AddProtocols(context.Background(), []model.Protocol{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *ProtocolSuit) Test_service_AllProtocols() {
	Convey("Test_service_AllProtocols", s.T(), func() {
		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "AllProtocols", nil, nil).Reset()
			_, err := s.srv.AllProtocols(context.Background())
			So(err, ShouldBeNil)
		})
	})
}

func (s *ProtocolSuit) Test_service_DeleteProtocol() {
	Convey("Test_service_DeleteProtocol", s.T(), func() {
		Convey("DeleteProtocol error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteProtocol", errors.New("")).Reset()
			err := s.srv.DeleteProtocol(context.Background(), request.DeleteProtocol{})
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			err := s.srv.DeleteProtocol(context.Background(), request.DeleteProtocol{})
			So(err, ShouldBeError)
		})

		Convey("CustomProtocolNotify error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, errors.New("")).Reset()
			err := s.srv.DeleteProtocol(context.Background(), request.DeleteProtocol{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, nil).Reset()
			err := s.srv.DeleteProtocol(context.Background(), request.DeleteProtocol{})
			So(err, ShouldBeNil)
		})
	})
}

func (s *ProtocolSuit) Test_service_UpdateProtocol() {
	Convey("Test_service_UpdateProtocol", s.T(), func() {
		Convey("UpdateProtocol error", func() {
			defer ApplyMethodReturn(s.srv.repo, "UpdateProtocol", errors.New("")).Reset()
			err := s.srv.UpdateProtocol(context.Background(), model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "UpdateProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			err := s.srv.UpdateProtocol(context.Background(), model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("CustomProtocolNotify error", func() {
			defer ApplyMethodReturn(s.srv.repo, "UpdateProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, errors.New("")).Reset()
			err := s.srv.UpdateProtocol(context.Background(), model.Protocol{})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "UpdateProtocol", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.rawGrab, "CustomProtocolNotify", nil, nil).Reset()
			err := s.srv.UpdateProtocol(context.Background(), model.Protocol{})
			So(err, ShouldBeNil)
		})
	})
}
