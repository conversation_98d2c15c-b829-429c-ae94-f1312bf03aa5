package service

import (
	"api/internal"
	"api/internal/httptransport/request"
	"api/internal/httptransport/response"
	"api/internal/jsonx"
	"api/pkg/dns"
	"context"
	"errors"
	"fmt"
	"time"

	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/samber/lo"
	"go-micro.dev/v4/logger"
)

func (s *service) AddTask(ctx context.Context, req *request.CreateRequest) (response.AddTaskResponse, error) {
	if req.Vulnerability == nil && req.PortScanner == nil {
		return response.AddTaskResponse{}, errors.New(internal.RequestParamMiss)
	}

	var (
		ips       []string
		faileds   []string
		relations map[string][]string
		err       error
	)

	req.HostInfos = lo.Uniq(req.HostInfos)
	domainLen := len(req.HostInfos)
	if len(req.Options.IPDomainRelations) == 0 && domainLen > 0 {
		strategy, err := NewRangerStrategy(req.PortScanner.Options.Blacklist)
		if err != nil {
			return response.AddTaskResponse{}, err
		}

		r := dns.NewResolution(domainLen, req.PortScanner.Options.IsIPV6, strategy)
		// DNS解析超时设置为60秒
		ips, faileds, relations, err = r.Resolution(req.HostInfos, 30*time.Second)
		if err != nil {
			return response.AddTaskResponse{}, err
		}
		logger.Infof("dns.NewResolution ips %v; relations %v", ips, relations)

		req.IP.List = append(req.IP.List, ips...)
		req.Options.IPDomainRelations = relations
	}

	items, err := IPTargetToIpList(req.IP)
	if err != nil {
		logger.Error("calculate ip target failed: %+v, err: %+v", req.IP, err)
		return response.AddTaskResponse{}, err
	}

	for _, ipOrCidr := range items {
		if !req.PortScanner.Options.IsIPV6 && (!isValidIPv4(ipOrCidr) && !isValidIPv4CIDR(ipOrCidr)) {
			return response.AddTaskResponse{}, errors.New(fmt.Sprintf("%s:%s", ipOrCidr, internal.InValidIPv4))
		}

		if req.PortScanner.Options.IsIPV6 && (!isValidIPv6(ipOrCidr) && !isValidIPv6CIDR(ipOrCidr)) {
			return response.AddTaskResponse{}, errors.New(fmt.Sprintf("%s:%s", ipOrCidr, internal.InValidIPv6))
		}
	}

	rpcReq, err := req.RPCRequest()
	if err != nil {
		return response.AddTaskResponse{}, err
	}

	rpcReq.IpLists = items

	if len(rpcReq.IpLists) == 0 && (rpcReq.Options.PortGroup == nil || len(rpcReq.Options.PortGroup) == 0) {
		return response.AddTaskResponse{}, errors.New(internal.NoScanTarget)
	}

	logger.Infof("send task add request to dispatcher: %s", jsonx.MustMarshal(rpcReq))

	if _, err = s.dispatcher.Start(ctx, rpcReq); err != nil {
		logger.Errorf("create failed. %+v", err)
		return response.AddTaskResponse{}, err
	}

	return response.AddTaskResponse{
		FailedDomains:    faileds,
		IpDomainRelation: relations,
	}, nil
}

func (s *service) DeleteTask(ctx context.Context, req *request.TaskIDRequest) error {
	logger.Infof("send stop to dispatcher: %s", jsonx.MustMarshal(req))

	_, err := s.dispatcher.Stop(ctx, &rpcx.DispatcherTaskStopRequest{TaskId: req.TaskID})
	if err != nil {
		logger.Errorf("stop failed. %+v", err)
		return err
	}

	return nil
}

func (s *service) PauseTask(ctx context.Context, req *request.TaskIDRequest) (response.PauseTaskResponse, error) {
	logger.Infof("send pause to dispatcher: %s", jsonx.MustMarshal(req))

	_, err := s.dispatcher.Pause(ctx, &rpcx.DispatcherTaskPauseRequest{TaskId: req.TaskID})
	if err != nil {
		logger.Errorf("pause failed. %+v", err)
		return response.PauseTaskResponse{}, err
	}

	return response.PauseTaskResponse{
		TaskID:   req.TaskID,
		Progress: 0,
	}, nil
}

func (s *service) ResumeTask(ctx context.Context, req *request.TaskResumeRequest) (response.ResumeTaskResponse, error) {
	logger.Infof("send resume to dispatcher: %s", jsonx.MustMarshal(req))

	var dispatcherTaskResumeRequest = &rpcx.DispatcherTaskResumeRequest{
		TaskId: req.TaskID,
	}

	if req.Bandwidth != "" {
		rate, err := req.GetRate()

		if err != nil {
			return response.ResumeTaskResponse{}, err
		}

		dispatcherTaskResumeRequest.Rate = rate
	}

	_, err := s.dispatcher.Resume(ctx, dispatcherTaskResumeRequest)

	if err != nil {
		logger.Errorf("resume failed. %+v", err)
		return response.ResumeTaskResponse{}, err
	}

	return response.ResumeTaskResponse{
		TaskID: req.TaskID,
	}, nil
}

func (s *service) GetTask(ctx context.Context, req *request.TaskIDRequest) (response.GetTaskResponse, error) {
	logger.Infof("send get task to dispatcher: %s", jsonx.MustMarshal(req))

	res, err := s.dispatcher.State(ctx, &rpcx.DispatcherTaskStateRequest{TaskId: req.TaskID})
	if err != nil {
		logger.Errorf("get state failed. %+v", err)
		return response.GetTaskResponse{}, err
	}

	return response.GetTaskResponse{
		TaskID:    req.TaskID,
		Status:    res.State,
		Progress:  res.Progress,
		WorkingOn: res.WorkingOn,
	}, nil
}
