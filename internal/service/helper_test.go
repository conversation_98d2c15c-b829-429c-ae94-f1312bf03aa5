package service

import (
	"api/internal/httptransport/request"
	"errors"
	. "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"github.com/yl2chen/cidranger"
	"go-micro.dev/v4"
	"go-micro.dev/v4/registry"
	"net"
	"os"
	"testing"
)

type HelperSuit struct {
	suite.Suite
}

func TestHelperSuit(t *testing.T) {
	s := &HelperSuit{}
	suite.Run(t, s)
}

func (s *HelperSuit) TestGetServiceNodesByName() {
	Convey("TestGetServiceNodesByName", s.T(), func() {
		Convey("GetServicesByName error", func() {
			defer ApplyFuncReturn(GetServicesByName, nil, errors.New("")).Reset()
			_, err := GetServiceNodesByName(micro.NewService(), "")
			So(err, ShouldBeError)
		})

		<PERSON>vey("pass", func() {
			defer ApplyFuncReturn(GetServicesByName, []*registry.Service{
				{
					Name: "",
				},
			}, nil).Reset()
			_, err := GetServiceNodesByName(micro.NewService(), "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *HelperSuit) TestGetServicesByName() {
	Convey("TestGetServicesByName", s.T(), func() {
		Convey("pass", func() {
			srv := micro.NewService()
			defer ApplyMethodReturn(srv.Client().Options().Registry, "GetService", nil, nil).Reset()
			_, err := GetServicesByName(micro.NewService(), "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *HelperSuit) TestIPTargetToIpList() {
	Convey("TestIPTargetToIpList", s.T(), func() {
		Convey("IsFile error", func() {
			defer ApplyFuncReturn(IsFile, false).Reset()
			_, err := IPTargetToIpList(&request.IPTarget{
				File: "file",
				List: []string{},
			})
			So(err, ShouldBeError)
		})

		Convey("os.ReadFile error", func() {
			defer ApplyFuncReturn(IsFile, true).Reset()
			defer ApplyFuncReturn(os.ReadFile, nil, errors.New("")).Reset()
			_, err := IPTargetToIpList(&request.IPTarget{
				File: "file",
				List: []string{},
			})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyFuncReturn(IsFile, true).Reset()
			defer ApplyFuncReturn(os.ReadFile, []byte("***********"), nil).Reset()
			_, err := IPTargetToIpList(&request.IPTarget{
				File: "file",
				List: []string{},
			})
			So(err, ShouldBeNil)
		})
	})
}

func (s *HelperSuit) TestIsFile() {
	Convey("TestIsFile", s.T(), func() {
		Convey("os.Stat error", func() {
			defer ApplyFuncReturn(os.Stat, nil, errors.New("")).Reset()
			ok := IsFile("")
			So(ok, ShouldEqual, false)
		})
	})
}

func (s *HelperSuit) TestNewRangerStrategy() {
	Convey("TestNewRangerStrategy", s.T(), func() {
		Convey("net.ParseCIDR error", func() {
			r := cidranger.NewPCTrieRanger()
			defer ApplyFuncReturn(net.ParseCIDR, nil, &net.IPNet{}, errors.New("")).Reset()
			defer ApplyFuncReturn(cidranger.NewPCTrieRanger, r).Reset()
			_, err := NewRangerStrategy([]string{"***********/32"})
			So(err, ShouldBeNil)
		})

		Convey("ranger.Insert error", func() {
			r := cidranger.NewPCTrieRanger()
			defer ApplyFuncReturn(net.ParseCIDR, nil, &net.IPNet{}, nil).Reset()
			defer ApplyFuncReturn(cidranger.NewPCTrieRanger, r).Reset()
			defer ApplyMethodReturn(r, "Insert", errors.New("")).Reset()
			_, err := NewRangerStrategy([]string{"***********/32"})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			r := cidranger.NewPCTrieRanger()
			defer ApplyFuncReturn(net.ParseCIDR, nil, &net.IPNet{}, nil).Reset()
			defer ApplyFuncReturn(cidranger.NewPCTrieRanger, r).Reset()
			defer ApplyMethodReturn(r, "Insert", nil).Reset()
			_, err := NewRangerStrategy([]string{"***********/32"})
			So(err, ShouldBeNil)
		})
	})
}

func TestNodeSelectOption(t *testing.T) {

}

func (s *HelperSuit) TestSelectStrategyByNodeID() {

}

func TestTransferSoftHard(t *testing.T) {
	type args struct {
		level int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				level: 1,
			},
			want: "1",
		},
		{
			args: args{
				level: 2,
			},
			want: "2",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TransferSoftHard(tt.args.level); got != tt.want {
				t.Errorf("TransferSoftHard() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidIPv4(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "ipv4",
			args: args{
				ip: "***********",
			},
			want: true,
		},
		{
			name: "invalid ipv4",
			args: args{
				ip: "10.10..10",
			},
			want: false,
		},
		{
			name: "invalid ipv4 cidr",
			args: args{
				ip: "10.10..0/29",
			},
			want: false,
		},
		{
			name: "ipv4 cidr",
			args: args{
				ip: "**********/24",
			},
			want: false,
		},
		{
			name: "ipv4",
			args: args{
				ip: "************",
			},
			want: true,
		},
		{
			name: "bug",
			args: args{
				ip: "***********",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidIPv4(tt.args.ip); got != tt.want {
				t.Errorf("isValidIPv4() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidIPv4CIDR(t *testing.T) {
	type args struct {
		cidr string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "ipv4 cidr",
			args: args{
				cidr: "**********/24",
			},
			want: true,
		},
		{
			name: "invalid ipv4 cidr",
			args: args{
				cidr: "10.10..0/29",
			},
			want: false,
		},
		{
			name: "ipv4",
			args: args{
				cidr: "***********",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidIPv4CIDR(tt.args.cidr); got != tt.want {
				t.Errorf("isValidIPv4CIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidIPv6(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "ipv4",
			args: args{
				ip: "***********",
			},
			want: false,
		},
		{
			name: "invalid ipv4",
			args: args{
				ip: "10.10..10",
			},
			want: false,
		},
		{
			name: "invalid ipv4 cidr",
			args: args{
				ip: "10.10..0/29",
			},
			want: false,
		},
		{
			name: "ipv4 cidr",
			args: args{
				ip: "**********/24",
			},
			want: false,
		},
		{
			name: "ipv6",
			args: args{
				ip: "2001:db8:3c4d:15::1a2f:1a2b",
			},
			want: true,
		},
		{
			name: "ipv6",
			args: args{
				ip: "2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
			},
			want: true,
		},
		{
			name: "ipv6 cidr",
			args: args{
				ip: "2001:db8::/32",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidIPv6(tt.args.ip); got != tt.want {
				t.Errorf("isValidIPv6() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidIPv6CIDR(t *testing.T) {
	type args struct {
		cidr string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "ipv6 cidr",
			args: args{
				cidr: "2001:db8::/32",
			},
			want: true,
		},
		{
			name: "ipv6 cidr",
			args: args{
				cidr: "fe80::/64",
			},
			want: true,
		},
		{
			name: "ipv6 cidr",
			args: args{
				cidr: "fe80::/64",
			},
			want: true,
		},
		{
			name: "ipv6",
			args: args{
				cidr: "2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidIPv6CIDR(tt.args.cidr); got != tt.want {
				t.Errorf("isValidIPv6CIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}
