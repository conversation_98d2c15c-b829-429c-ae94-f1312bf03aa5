package service

import (
	"api/internal"
	"api/internal/httptransport/request"
	"context"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/client"
	merrors "go-micro.dev/v4/errors"
	"go-micro.dev/v4/logger"
	"strconv"
)

func (s *service) AddRule(ctx context.Context, r request.AddRule) error {
	rule := model.Rule{
		RuleID:         r.RuleID,
		Product:        r.Product,
		Rule:           r.Rule,
		Level:          strconv.Itoa(r.Level),
		Softhard:       TransferSoftHard(r.Level),
		Company:        r.Company,
		Category:       r.Category,
		ParentCategory: r.ParentCategory,
		From:           r.From,
	}

	err := s.repo.AddRule(rule)

	if err != nil {
		return err
	}

	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceDataAnalysis)

	if err != nil {
		return err
	}

	for k := range nodes {
		_, err = s.dataAnalysis.CustomRuleAdd(ctx, &rpcx.CustomRuleRequest{
			RuleId:         r.RuleID,
			Product:        r.Product,
			Rule:           r.Rule,
			Level:          strconv.Itoa(r.Level),
			Softhard:       TransferSoftHard(r.Level),
			Company:        r.Company,
			Category:       r.Category,
			ParentCategory: r.ParentCategory,
			From:           r.From,
		}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))

		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("CustomRuleAdd notify node:%s failed rule:%v", nodes[k].Id, rule)
		}
	}

	return err
}

func (s *service) UpdateRule(ctx context.Context, ruleID string, r request.UpdateRule) error {
	_, err := s.repo.GetRule(ruleID)

	if err != nil {
		return errors.New(internal.DataNotExisted)
	}

	rule := model.Rule{
		RuleID:         ruleID,
		Product:        r.Product,
		Rule:           r.Rule,
		Level:          strconv.Itoa(r.Level),
		Softhard:       TransferSoftHard(r.Level),
		Company:        r.Company,
		Category:       r.Category,
		ParentCategory: r.ParentCategory,
		From:           r.From,
	}

	err = s.repo.UpdateRule(rule)

	if err != nil {
		return err
	}

	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceDataAnalysis)

	if err != nil {
		return err
	}

	for k := range nodes {
		_, err = s.dataAnalysis.CustomRuleUpdate(ctx, &rpcx.CustomRuleRequest{
			RuleId:         ruleID,
			Product:        r.Product,
			Rule:           r.Rule,
			Level:          strconv.Itoa(r.Level),
			Softhard:       TransferSoftHard(r.Level),
			Company:        r.Company,
			Category:       r.Category,
			ParentCategory: r.ParentCategory,
			From:           r.From,
		}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))

		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("CustomRuleUpdate notify node:%s failed rule:%v", nodes[k].Id, rule)
		}
	}

	return err
}

func (s *service) DeleteRule(ctx context.Context, ruleID string) error {
	err := s.repo.DeleteRule(ruleID)

	if err != nil {
		return err
	}

	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceDataAnalysis)

	if err != nil {
		return err
	}

	for k := range nodes {
		_, err = s.dataAnalysis.CustomRuleDelete(ctx, &rpcx.CustomRuleIDRequest{
			RuleId: ruleID,
		}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))
		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("CustomRuleDelete notify node:%s failed ruleID:%s", nodes[k].Id, ruleID)
		}
	}

	return err
}

func (s *service) AllRules(ctx context.Context) ([]model.Rule, error) {
	return s.repo.AllRules()
}
