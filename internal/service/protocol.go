package service

import (
	"api/internal/httptransport/request"
	"context"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/client"
	merrors "go-micro.dev/v4/errors"
	"go-micro.dev/v4/logger"
)

func (s *service) AddProtocols(ctx context.Context, protocols []model.Protocol) error {
	err := s.repo.AddProtocols(protocols)
	if err != nil {
		return err
	}

	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceRawGrab)

	if err != nil {
		return err
	}

	logger.Info("send protocol add request to raw_grab:", protocols)
	for k := range nodes {
		_, err = s.rawGrab.CustomProtocolNotify(ctx, &rpcx.EmptyNotifyRequest{}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))

		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("AddProtocols CustomProtocolNotify notify node:%s failed protocol:%v", nodes[k].Id, protocols)
		}
	}

	return err
}

func (s *service) UpdateProtocol(ctx context.Context, p model.Protocol) error {
	err := s.repo.UpdateProtocol(p)
	if err != nil {
		return err
	}
	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceRawGrab)

	if err != nil {
		return err
	}
	logger.Info("send protocol update request to raw_grab:", p)
	for k := range nodes {
		_, err = s.rawGrab.CustomProtocolNotify(ctx, &rpcx.EmptyNotifyRequest{}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))

		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("UpdateProtocol CustomProtocolNotify notify node:%s failed protocol:%v", nodes[k].Id, p)
		}
	}
	return err
}

func (s *service) DeleteProtocol(ctx context.Context, req request.DeleteProtocol) error {
	err := s.repo.DeleteProtocol(req.IDs, req.All)
	if err != nil {
		return err
	}
	nodes, err := GetServiceNodesByName(s.micro, constant.ServiceRawGrab)

	if err != nil {
		return err
	}

	for k := range nodes {
		_, err = s.rawGrab.CustomProtocolNotify(ctx, &rpcx.EmptyNotifyRequest{}, client.WithSelectOption(NodeSelectOption(nodes[k].Id)))

		if err != nil {
			e := merrors.Parse(err.Error())
			err = errors.New(e.Detail)
			logger.Warnf("DeleteProtocol CustomProtocolNotify notify node:%s failed protocol:%v", nodes[k].Id, req)
		}
	}
	return err
}

func (s *service) AllProtocols(ctx context.Context) ([]model.Protocol, error) {
	return s.repo.AllProtocols()
}
