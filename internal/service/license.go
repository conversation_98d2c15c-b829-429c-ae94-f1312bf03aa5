package service

import (
	"api/internal"
	"api/internal/httptransport/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	go_crypto "git.gobies.org/foeye/key-license/go-crypto"
	"go-micro.dev/v4/logger"
	"os"
)

func (s *service) ActivatedLicense(ctx context.Context, license string) error {
	content := []byte(license)
	filename := fmt.Sprintf("%s/license.txt", s.license.Crypto.Path)
	logger.Infof("license file: %s", filename)

	err := os.WriteFile(filename, content, 0o600|0o644)
	if err != nil {
		return err
	}

	s.license.Verify()

	if !s.license.Info.IsValid {
		return errors.New("激活失败，授权错误")
	}

	return nil
}

func (s *service) GetIDKey(ctx context.Context) (res response.GetIDKeyResponse, err error) {
	var content []byte

	if !go_crypto.IsFileExist(s.license.Crypto.Path + "/id.key") {
		s.license.Verify()
	}

	content, err = os.ReadFile(s.license.Crypto.Path + "/id.key")

	if err != nil {
		return
	}

	res.IDKey = string(content)

	return
}

func (s *service) GetSystemInfo(ctx context.Context) response.GetSystemInfoResponse {
	s.license.Verify()
	info := s.license.Info
	logger.Info("GetSystemInfo: ", info)

	functionModuleFile := info.FunctionModuleFile

	modules := response.Modules{}

	if functionModuleFile != "" {
		encryptModules := make(map[string]int)
		err := json.Unmarshal([]byte(functionModuleFile), &encryptModules)

		if err != nil {
			logger.Errorf("json Unmarshal functionModuleFile failed: %+v", err)
			functionModuleFile = ""
		} else {
			transferModules := make(map[string]int)
			for k, v := range internal.ModuleRelation {
				transferModules[v] = encryptModules[k]
			}
			data, _ := json.Marshal(transferModules)
			functionModuleFile = string(data)
		}
	}

	_ = json.Unmarshal([]byte(functionModuleFile), &modules)

	return response.GetSystemInfoResponse{
		Version:          internal.Version,
		AssetLimitNum:    info.AssetLimit,
		LicenseState:     info.IsValid,
		ProductLimitDate: info.ProductTime,
		UpgradeLimitDate: info.UpgradeTime,
		Modules:          modules,
	}
}
