package service

import (
	"api/internal/httptransport/request"
	"api/internal/httptransport/response"
	"api/internal/license"
	"api/internal/repository"
	"context"
	query "git.gobies.org/fofa-backend/fofacore/datastruct/request"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
)

type service struct {
	license      *license.License
	rawGrab      rpcx.RawGrabService
	dataAnalysis rpcx.DataAnalysisService
	dispatcher   rpcx.DispatcherTaskService
	repo         repository.Repository
	micro        micro.Service
}

type Service interface {
	AddTask(ctx context.Context, req *request.CreateRequest) (response.AddTaskResponse, error)
	DeleteTask(ctx context.Context, req *request.TaskIDRequest) error
	PauseTask(ctx context.Context, req *request.TaskIDRequest) (response.PauseTaskResponse, error)
	ResumeTask(ctx context.Context, req *request.TaskResumeRequest) (response.ResumeTaskResponse, error)
	GetTask(ctx context.Context, req *request.TaskIDRequest) (response.GetTaskResponse, error)

	ActivatedLicense(ctx context.Context, license string) error
	GetIDKey(ctx context.Context) (res response.GetIDKeyResponse, err error)
	GetSystemInfo(ctx context.Context) response.GetSystemInfoResponse

	AllProtocols(ctx context.Context) ([]model.Protocol, error)
	AddProtocols(ctx context.Context, protocols []model.Protocol) error
	UpdateProtocol(ctx context.Context, p model.Protocol) error
	DeleteProtocol(ctx context.Context, req request.DeleteProtocol) error

	AddRule(ctx context.Context, r request.AddRule) error
	AllRules(ctx context.Context) ([]model.Rule, error)
	UpdateRule(ctx context.Context, ruleID string, r request.UpdateRule) error
	DeleteRule(ctx context.Context, ruleID string) error

	QueryParse(req query.QueryInput) (*query.QueryOutput, error)
}

func NewService(
	dispatcher rpcx.DispatcherTaskService,
	license *license.License,
	repo repository.Repository,
	rawGrab rpcx.RawGrabService,
	dataAnalysis rpcx.DataAnalysisService,
	micro micro.Service,
) Service {
	return &service{
		license:      license,
		dispatcher:   dispatcher,
		repo:         repo,
		rawGrab:      rawGrab,
		dataAnalysis: dataAnalysis,
		micro:        micro,
	}
}
