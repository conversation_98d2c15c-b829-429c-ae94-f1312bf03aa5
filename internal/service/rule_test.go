package service

import (
	"api/internal/httptransport/request"
	"api/internal/repository"
	"context"
	"errors"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	. "github.com/agiledragon/gomonkey/v2"
	"github.com/go-micro/plugins/v4/store/file"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/registry"
	"testing"
)

type RuleSuit struct {
	suite.Suite
	srv service
}

func TestRuleSuit(t *testing.T) {
	s := &RuleSuit{}
	s.srv = service{
		repo:         repository.NewRepository(file.NewStore(), file.NewStore()),
		micro:        micro.NewService(),
		dataAnalysis: rpcx.NewDataAnalysisService("", micro.NewService().Client()),
	}
	suite.Run(t, s)
}

func (s *RuleSuit) Test_service_AddRule() {
	<PERSON>vey("Test_service_AddRule", s.T(), func() {
		<PERSON><PERSON>("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleAdd", nil, nil).Reset()
			err := s.srv.AddRule(context.Background(), request.AddRule{
				Level: 1,
			})
			So(err, ShouldBeNil)
		})

		Convey("dataAnalysis.CustomRuleAdd error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleAdd", nil, errors.New("")).Reset()
			err := s.srv.AddRule(context.Background(), request.AddRule{
				Level: 1,
			})
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, errors.New("")).Reset()
			err := s.srv.AddRule(context.Background(), request.AddRule{
				Level: 1,
			})
			So(err, ShouldBeError)
		})

		Convey("repo.AddRule error", func() {
			defer ApplyMethodReturn(s.srv.repo, "AddRule", errors.New("")).Reset()
			err := s.srv.AddRule(context.Background(), request.AddRule{
				Level: 1,
			})
			So(err, ShouldBeError)
		})
	})
}

func (s *RuleSuit) Test_service_AllRules() {
	Convey("Test_service_AllRules", s.T(), func() {
		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "AllRules", nil, nil).Reset()
			_, err := s.srv.AllRules(context.Background())
			So(err, ShouldBeNil)
		})
	})
}

func (s *RuleSuit) Test_service_DeleteRule() {
	Convey("Test_service_DeleteRule", s.T(), func() {
		Convey("repo.DeleteRule error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteRule", errors.New("")).Reset()
			err := s.srv.DeleteRule(context.Background(), "")
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			err := s.srv.DeleteRule(context.Background(), "")
			So(err, ShouldBeError)
		})

		Convey("dataAnalysis.CustomRuleDelete error", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleDelete", nil, errors.New("")).Reset()
			err := s.srv.DeleteRule(context.Background(), "")
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "DeleteRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleDelete", nil, nil).Reset()
			err := s.srv.DeleteRule(context.Background(), "")
			So(err, ShouldBeNil)
		})
	})
}

func (s *RuleSuit) Test_service_UpdateRule() {
	Convey("Test_service_UpdateRule", s.T(), func() {
		Convey("repo.GetRule error", func() {
			defer ApplyMethodReturn(s.srv.repo, "GetRule", nil, errors.New("")).Reset()
			err := s.srv.UpdateRule(context.Background(), "", request.UpdateRule{})
			So(err, ShouldBeError)
		})

		Convey("repo.UpdateRule error", func() {
			defer ApplyMethodReturn(s.srv.repo, "GetRule", nil, nil).Reset()
			defer ApplyMethodReturn(s.srv.repo, "UpdateRule", errors.New("")).Reset()
			err := s.srv.UpdateRule(context.Background(), "", request.UpdateRule{})
			So(err, ShouldBeError)
		})

		Convey("GetServiceNodesByName error", func() {
			defer ApplyMethodReturn(s.srv.repo, "GetRule", nil, nil).Reset()
			defer ApplyMethodReturn(s.srv.repo, "UpdateRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			err := s.srv.UpdateRule(context.Background(), "", request.UpdateRule{
				Level: 1,
			})
			So(err, ShouldBeError)
		})

		Convey("dataAnalysis.CustomRuleUpdate error", func() {
			defer ApplyMethodReturn(s.srv.repo, "GetRule", nil, nil).Reset()
			defer ApplyMethodReturn(s.srv.repo, "UpdateRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, nil, errors.New("")).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleUpdate", nil, errors.New("")).Reset()
			err := s.srv.UpdateRule(context.Background(), "", request.UpdateRule{
				Level: 1,
			})
			So(err, ShouldBeError)
		})

		Convey("pass", func() {
			defer ApplyMethodReturn(s.srv.repo, "GetRule", nil, nil).Reset()
			defer ApplyMethodReturn(s.srv.repo, "UpdateRule", nil).Reset()
			defer ApplyFuncReturn(GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()
			defer ApplyMethodReturn(s.srv.dataAnalysis, "CustomRuleUpdate", nil, nil).Reset()
			err := s.srv.UpdateRule(context.Background(), "", request.UpdateRule{
				Level: 1,
			})
			So(err, ShouldBeNil)
		})
	})
}
