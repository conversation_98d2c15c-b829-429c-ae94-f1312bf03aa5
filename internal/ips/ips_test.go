package ips

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBullshit(t *testing.T) {
	ip := "************"
	assert.False(t, <PERSON>hit(ip))
	ip = "0.0.0.0"
	assert.True(t, <PERSON>hit(ip))
	ip = "127.0.0.1"
	assert.True(t, <PERSON><PERSON>(ip))
	assert.True(t, <PERSON><PERSON>("0.0.0.0"))
	assert.True(t, <PERSON>hit("127.0.0.1"))
	assert.False(t, <PERSON>hit("*************"))

}
