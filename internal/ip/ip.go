package ip

import (
	"regexp"
	"strings"
)

var regIP = regexp.MustCompile(`\A\d\w*\.\d\w*\.\d\w*\.\d\w*\z`)
var regIPDecimal = regexp.MustCompile(`\A\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\z`)

// 判断是否为IP
func Valid(ip string) bool {
	if regIP.MatchString(ip) || strings.Contains(ip, ":") {
		return true
	}
	return false
}

// 判断是否为合法IP,即必须为十进制
func Decimal(ip string) bool {
	if regIPDecimal.MatchString(ip) || strings.Contains(ip, ":") {
		return true
	}
	return false
}
