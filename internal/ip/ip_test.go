package ip

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestDecimal(t *testing.T) {
	subTest := []struct {
		ip     string
		expRes bool
	}{
		{
			ip:     "**************",
			expRes: true,
		},
		{
			ip:     "2606:4700:3037::6815:1cd6",
			expRes: true,
		},
		{
			ip:     "",
			expRes: false,
		},
	}

	for _, subT := range subTest {
		assert.Equal(t, subT.expRes, Decimal(subT.ip))
	}
}

func TestValid(t *testing.T) {
	subTest := []struct {
		ip     string
		expRes bool
	}{
		{
			ip:     "**************",
			expRes: true,
		},
		{
			ip:     "2606:4700:3037::6815:1cd6",
			expRes: true,
		},
		{
			ip:     "",
			expRes: false,
		},
	}

	for _, subT := range subTest {
		assert.Equal(t, subT.expRes, Valid(subT.ip))
	}
}
