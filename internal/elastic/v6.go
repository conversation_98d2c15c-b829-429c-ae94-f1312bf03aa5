package elastic

import (
	"git.gobies.org/shared-platform/quickstore/internal/model"
	"github.com/elastic/go-elasticsearch/v6"
)

func NewV6(conf model.Elastic) (*elasticsearch.Client, error) {
	cfg := elasticsearch.Config{
		Addresses: []string{
			conf.Address,
		},
	}

	if conf.Username != "" {
		cfg.Username = conf.Username
	}

	if conf.Password != "" {
		cfg.Password = conf.Password
	}

	return elasticsearch.NewClient(cfg)
}
