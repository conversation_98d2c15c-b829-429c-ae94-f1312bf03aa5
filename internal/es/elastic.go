package es

import (
	"fmt"
	"github.com/pkg/errors"
	"time"
)

type ElasticBaseConf struct {
	version        int
	url            string
	indexName      string
	indexSubdomain string
	typeSubdomain  string
}

type ElasticStub interface {
	GetVersion() int
	GetElasticBaseConfig() *ElasticBaseConf
	GetClient() interface{}
	NeedUpdate(m map[string]interface{}) bool
	Index(id string, m map[string]interface{})
	UpdateCheckTime(id string, m map[string]interface{})
	Get(id string) (m map[string]interface{}, err error)
}

const (
	TIME_FORMAT     = "2006-01-02 15:04:05"
	LAST_CHECK_TIME = "lastchecktime"
)

var checkTimeInterval = time.Hour * 24 * 30 // 30天不更新

func NewElasitcBaseConf(version int, url string, index, idxSubdomain, typeSubdomain string) *ElasticBaseConf {
	return &ElasticBaseConf{
		version:        version,
		url:            url,
		indexName:      index,
		indexSubdomain: idxSubdomain,
		typeSubdomain:  typeSubdomain,
	}
}

func (ebc *ElasticBaseConf) GetIndexName(cat string) string {
	index := ""
	switch cat {
	case "subdomain":
		index = ebc.indexSubdomain
	}

	if len(index) == 0 {
		return ebc.indexName
	}

	return index
}

func (ebc *ElasticBaseConf) GetTypeName(cat string) string {
	types := ""
	switch cat {
	case "subdomain":
		types = ebc.typeSubdomain
	}

	return types
}

func (ebc *ElasticBaseConf) getTime(m map[string]interface{}, field string) (t time.Time, err error) {
	timeStr, ok := m[field].(string)
	if !ok {
		err = fmt.Errorf("%s is not string", field)
		return
	}

	t, err = time.ParseInLocation(TIME_FORMAT, timeStr, time.Local)
	if err != nil {
		err = errors.Wrapf(err, "date format err: %s", timeStr)
	}

	return
}

func NewElastic(ebc *ElasticBaseConf) ElasticStub {
	/*if ebc.version >= 5 {
		return NewElastic6(ebc)
	}*/
	return NewElastic6(ebc)
}
