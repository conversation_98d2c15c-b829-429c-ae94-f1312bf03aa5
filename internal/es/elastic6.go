package es

import (
	"context"
	"encoding/json"
	"fmt"
	elastic6 "github.com/olivere/elastic"
	"github.com/pkg/errors"
	"log"
	"time"
)

type Elastic6 struct {
	client *elastic6.Client
	*ElasticBaseConf
}

func NewElastic6(ebc *ElasticBaseConf) ElasticStub {
	client, err := elastic6.NewClient(elastic6.SetURL(ebc.url), elastic6.SetSniff(false))
	if err != nil {
		log.Fatal("FATAL new es6 client", err)
	}

	return &Elastic6{
		client:          client,
		ElasticBaseConf: ebc,
	}
}

func (es6 *Elastic6) GetVersion() int {
	return es6.version
}

func (es6 *Elastic6) GetClient() interface{} {
	return es6.client
}

func (es6 *Elastic6) GetElasticBaseConfig() *ElasticBaseConf {
	return es6.ElasticBaseConf
}

func (es6 *Elastic6) NeedUpdate(m map[string]interface{}) bool {
	t, err := es6.getTime(m, LAST_CHECK_TIME)
	if err != nil {
		log.Println("ERROR es6 need-update failed:", err)
		return true
	}

	if time.Since(t) > 0 && time.Since(t) < checkTimeInterval {
		return false
	}

	return true
}

func (es6 *Elastic6) UpdateCheckTime(id string, m map[string]interface{}) {
	if m == nil {
		log.Println("Error UpdateChkTime null input")
		return
	}

	m[LAST_CHECK_TIME] = time.Now().Format(TIME_FORMAT)
	es6.Index(id, m)
}

func (es6 *Elastic6) Index(id string, m map[string]interface{}) {
	_, err := es6.client.Index().
		Index(es6.indexSubdomain).
		Type(es6.typeSubdomain).
		Id(id).
		BodyJson(m).
		Do(context.Background())
	if err != nil {
		log.Println("ERROR es6 index failed:", err)
	}
}

func (es6 *Elastic6) Get(id string) (m map[string]interface{}, err error) {
	result, err1 := es6.get6(id)
	if err1 != nil {
		err = errors.WithStack(err1)
		return
	}

	if !result.Found || result.Source == nil {
		err = fmt.Errorf("es6 id:%s not found", id)
		return
	}

	m = make(map[string]interface{})
	err = json.Unmarshal(*result.Source, &m)
	if err != nil {
		err = errors.WithStack(err)
		return
	}

	return
}

func (es6 *Elastic6) get6(id string) (result *elastic6.GetResult, err error) {
	result, err = es6.client.Get().
		Index(es6.indexSubdomain).
		Type(es6.typeSubdomain).
		Id(id).
		Do(context.Background())
	if err != nil {
		err = errors.Wrapf(err, "es6 get id:%s failed", id)
	}

	return
}
