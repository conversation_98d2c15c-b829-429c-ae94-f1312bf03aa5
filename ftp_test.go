package probe

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestFTP(t *testing.T) {
	NewTcpMocker(5006).
		SetResponse([]byte{0x32, 0x32, 0x30, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
			0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x20, 0x57, 0x65,
			0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x20, 0x74, 0x6f,
			0x20, 0x50, 0x75, 0x72, 0x65, 0x2d, 0x46, 0x54,
			0x50, 0x64, 0x20, 0x5b, 0x70, 0x72, 0x69, 0x76,
			0x73, 0x65, 0x70, 0x5d, 0x20, 0x5b, 0x54, 0x4c,
			0x53, 0x5d, 0x20, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d,
			0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x0d, 0x0a, 0x32,
			0x32, 0x30, 0x2d, 0x59, 0x6f, 0x75, 0x20, 0x61,
			0x72, 0x65, 0x20, 0x75, 0x73, 0x65, 0x72, 0x20,
			0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x31,
			0x20, 0x6f, 0x66, 0x20, 0x35, 0x30, 0x20, 0x61,
			0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x2e, 0x0d,
			0x0a, 0x32, 0x32, 0x30, 0x2d, 0x4c, 0x6f, 0x63,
			0x61, 0x6c, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x20,
			0x69, 0x73, 0x20, 0x6e, 0x6f, 0x77, 0x20, 0x31,
			0x38, 0x3a, 0x35, 0x33, 0x2e, 0x20, 0x53, 0x65,
			0x72, 0x76, 0x65, 0x72, 0x20, 0x70, 0x6f, 0x72,
			0x74, 0x3a, 0x20, 0x32, 0x31, 0x2e, 0x0d, 0x0a,
			0x32, 0x32, 0x30, 0x2d, 0x54, 0x68, 0x69, 0x73,
			0x20, 0x69, 0x73, 0x20, 0x61, 0x20, 0x70, 0x72,
			0x69, 0x76, 0x61, 0x74, 0x65, 0x20, 0x73, 0x79,
			0x73, 0x74, 0x65, 0x6d, 0x20, 0x2d, 0x20, 0x4e,
			0x6f, 0x20, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
			0x6f, 0x75, 0x73, 0x20, 0x6c, 0x6f, 0x67, 0x69,
			0x6e, 0x0d, 0x0a, 0x32, 0x32, 0x30, 0x2d, 0x49,
			0x50, 0x76, 0x36, 0x20, 0x63, 0x6f, 0x6e, 0x6e,
			0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20,
			0x61, 0x72, 0x65, 0x20, 0x61, 0x6c, 0x73, 0x6f,
			0x20, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65,
			0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x69, 0x73,
			0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
			0x0d, 0x0a, 0x32, 0x32, 0x30, 0x20, 0x59, 0x6f,
			0x75, 0x20, 0x77, 0x69, 0x6c, 0x6c, 0x20, 0x62,
			0x65, 0x20, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e,
			0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x20, 0x61,
			0x66, 0x74, 0x65, 0x72, 0x20, 0x31, 0x35, 0x20,
			0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x20,
			0x6f, 0x66, 0x20, 0x69, 0x6e, 0x61, 0x63, 0x74,
			0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x0d, 0x0a}).
		SetProtocol("ftp").
		Run(FTP).Test(t)
}

func TestCheckFTP(t *testing.T) {
	data := []byte("200 hello and welcome to SchoolsNET SINA poppassd [299-9273]")
	assert.False(t, CheckFTP(data))
}
