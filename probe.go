package probe

import (
	"bytes"
	"crypto/tls"
	"encoding/binary"
	"errors"
	"fmt"
	"git.gobies.org/gobase/plugin/pkg/plugin/share/rawgrab"
	"io"
	"io/ioutil"
	"net"
	"regexp"
	"strconv"
	"time"
)

type ProbeFunc func(string, ...interface{}) (Info, error)

type Info struct {
	ConnectionState *tls.ConnectionState
	Protocol        string
	NotReTry        bool
	Banner          []byte
	StartTls        string
	Success         bool
	StructInfo      string //补充的结构化数据，json格式存储
	DbsInfo         *rawgrab.Dbs
	BannerLen       int
	IsHoneypot      bool
	HoneypotName    string
	Hostname        []string
}

var DefaultTimeout = 5 * time.Second

type Timeout time.Duration

// DailFunc 连接函数，用于替换默认的net.Dail, 比如代理设置
type DailFunc func(network, addr string) (net.Conn, error)

var globalDialer DailFunc

// SetDailFunc 设置连接函数，用于替换默认的net.Dail, 比如代理设置
func SetDailFunc(dial DailFunc) {
	globalDialer = dial
}

type probe struct {
	Timeout time.Duration
	addr    string
	network string
	conn    net.Conn
	tcp     *net.TCPConn
	udp     *net.UDPConn
	tls     *tls.Conn
}

func (o *probe) GetConnectionState() *tls.ConnectionState {
	if o.tls == nil {
		return nil
	}
	state := o.tls.ConnectionState()
	return &state
}

func (o *probe) GetTLS() (t *tls.Conn, err error) {
	if o.tls != nil {
		return o.tls, nil
	}
	return o.createTLS("tcp", &tls.Config{InsecureSkipVerify: true,
		MinVersion: tls.VersionTLS10})
}

func (o *probe) Close() {
	if o.conn != nil {
		o.conn.Close()
	}
	if o.tls != nil {
		o.tls.Close()
	}
}

func (o *probe) createTLS(network string, config *tls.Config) (t *tls.Conn, err error) {
	if globalDialer != nil {
		var conn net.Conn
		conn, err = globalDialer(network, o.addr)
		if err == nil {
			t = tls.Client(conn, config)
			o.conn = t
			o.tls = t
			return
		}
	}

	if o.Timeout > 0 {
		deadline := time.Now().Add(o.Timeout)
		t, err = tls.DialWithDialer(&net.Dialer{Timeout: o.Timeout}, network, o.addr, config)
		if err != nil {
			return
		}
		if time.Now().After(deadline) {
			err = fmt.Errorf("no time left for read and write")
			return
		}
		err = t.SetDeadline(deadline)
	} else {
		t, err = tls.Dial(network, o.addr, config)
	}
	if err == nil {
		o.conn = t
		o.tls = t
	}
	return
}

func (o *probe) InitConn(network string, isTls bool) error {
	if isTls {
		_, err := o.createTLS(network, &tls.Config{InsecureSkipVerify: true, MinVersion: tls.VersionTLS10})
		if err != nil {
			return err
		}
	} else {
		var err error
		if network == "udp" {
			err = o.connectUDP()
		} else {
			err = o.connectTCP()
		}
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *probe) MustWriteString(s string) {
	err := o.Write([]byte(s))
	if err != nil {
		panic(err)
	}
}
func (o *probe) MustWrite(b []byte) {
	err := o.Write(b)
	if err != nil {
		panic(err)
	}
}
func (o *probe) MustRead(n int) []byte {
	b, err := o.Read(n)
	if err != nil {
		panic(err)
	}
	return b
}

func (o *probe) Read(n int) ([]byte, error) {
	if o.conn == nil {
		return nil, errors.New("no connection yet")
	}
	conn := o.conn
	return readn(conn, n)
}

func (o *probe) Write(b []byte) error {
	if o.conn == nil {
		return errors.New("no connection yet")
	}
	conn := o.conn
	n, err := conn.Write(b)
	if err != nil {
		return err
	}
	if n != len(b) {
		return fmt.Errorf("write conn data not completely, want %d write %d", len(b), n)
	}
	return nil
}

func (o *probe) getConn(network string) (conn net.Conn, err error) {
	if globalDialer != nil {
		return globalDialer(network, o.addr)
	}

	if o.Timeout > 0 {
		deadline := time.Now().Add(o.Timeout)
		conn, err = net.DialTimeout(network, o.addr, o.Timeout)
		if err != nil {
			return
		}
		err = conn.SetDeadline(deadline)
	} else {
		conn, err = net.Dial(network, o.addr)
	}
	if err == nil {
		o.conn = conn
	}
	return
}

func (o *probe) connectUDP() error {
	conn, err := o.getConn(o.network)
	//conn.SetReadDeadline(time.Now().Add(time.Second*5))
	if err != nil {
		return err
	}
	udp, ok := conn.(*net.UDPConn)
	if !ok {
		err = fmt.Errorf("not udp? %+v", conn)
		return err
	}
	o.udp = udp
	return nil
}

func (o *probe) connectTCP() error {
	conn, err := o.getConn(o.network)
	if err != nil {
		return err
	}
	tcp, ok := conn.(*net.TCPConn)
	if !ok {
		err = fmt.Errorf("not tcp? %+v", conn)
		return err
	}
	o.tcp = tcp
	return nil
}

func (o *probe) GetTCP() (tcp *net.TCPConn, err error) {
	if o.tcp != nil {
		return o.tcp, nil
	}
	err = o.connectTCP()
	if err != nil {
		return nil, err
	}
	return o.tcp, nil
}

func (o *probe) GetUDP() (udp *net.UDPConn, err error) {
	if o.udp != nil {
		return o.udp, nil
	}
	err = o.connectUDP()
	if err != nil {
		return nil, err
	}
	return o.udp, nil
}

func (o *probe) MustReadUDP(n int) []byte {
	b, err := o.ReadUDP(n)
	if err != nil {
		panic(err)
	}
	return b
}

func (o *probe) ReTryReadUDP(ReqBytes []byte) (respBytes []byte, err error) {
	for i := 0; i < 3; i++ {
		o.connectUDP()
		err = o.WriteUDP(ReqBytes)
		if err != nil {
			break
		}
		respBytes, err = o.ReadUDP(1024)
		if err != nil {
			if i == 2 {
				return
			}
			//time.Sleep(time.Second*2)
			continue
		}
		break
	}
	return
}

func (o *probe) ReadUDP(n int) ([]byte, error) {
	udp, err := o.GetUDP()
	if err != nil {
		return nil, err
	}
	return readn(udp, n)
}

func (o *probe) WriteUDP(b []byte) error {
	udp, err := o.GetUDP()
	if err != nil {
		return err
	}
	n, err := udp.Write(b)
	if err != nil {
		return err
	}
	if n != len(b) {
		return fmt.Errorf("write udp data not completely, want %d write %d", len(b), n)
	}
	return nil
}

func (o *probe) MustWriteUDPString(s string) {
	o.MustWriteUDP([]byte(s))
}

func (o *probe) MustWriteUDP(b []byte) {
	err := o.WriteUDP(b)
	if err != nil {
		panic(err)
	}
}

func (o *probe) MustWriteTCPString(s string) {
	o.MustWriteTCP([]byte(s))
}

func (o *probe) MustWriteTCP(b []byte) {
	err := o.WriteTCP(b)
	if err != nil {
		panic(err)
	}
}

func (o *probe) WriteTCP(b []byte) error {
	tcp, err := o.GetTCP()
	if err != nil {
		return err
	}
	n, err := tcp.Write(b)
	if err != nil {
		return err
	}
	if n != len(b) {
		return fmt.Errorf("write tcp data not completely, want %d write %d", len(b), n)
	}
	return nil
}

func (o *probe) MustWriteTLSString(s string) {
	o.MustWriteTLS([]byte(s))
}

func (o *probe) MustWriteTLS(b []byte) {
	err := o.WriteTLS(b)
	if err != nil {
		panic(err)
	}
}

func (o *probe) WriteTLS(b []byte) error {
	t, err := o.GetTLS()
	if err != nil {
		return err
	}
	n, err := t.Write(b)
	if err != nil {
		return err
	}
	if n != len(b) {
		return fmt.Errorf("write tls data not completely, want %d write %d", len(b), n)
	}
	return nil
}

func readn(rd io.Reader, n int) (p []byte, err error) {
	if n < 0 {
		p, err = ioutil.ReadAll(rd)
	} else {
		p = make([]byte, n)
		var nn int
		nn, err = rd.Read(p)
		p = p[:nn]
	}
	if len(p) > 0 {
		err = nil
	}
	return
}

func (o *probe) MustReadTLS(n int) []byte {
	b, err := o.ReadTLS(n)
	if err != nil {
		panic(err)
	}
	return b
}
func (o *probe) ReadTLS(n int) ([]byte, error) {
	t, err := o.GetTLS()
	if err != nil {
		return nil, err
	}
	return readn(t, n)
}

func (o *probe) CheckRoundTripTCP(send []byte, reg *regexp.Regexp, read int) (valid bool, data []byte, err error) {
	err = o.WriteTCP(send)
	if err != nil {
		return
	}
	data, err = o.ReadTCP(read)
	if err != nil {
		return
	}
	if reg.Match(data) {
		valid = true
	}
	return
}

func (o *probe) MustReadTCP(n int) []byte {
	b, err := o.ReadTCP(n)
	if err != nil {
		panic(err)
	}
	return b
}

func (o *probe) ReadTCP(n int) ([]byte, error) {
	tcp, err := o.GetTCP()
	if err != nil {
		return nil, err
	}
	return readn(tcp, n)
}

type probeReader struct {
	o         *probe
	respBytes []byte
	reader    io.Reader
}

func (p *probeReader) Read(data []byte) (n int, err error) {
	var r []byte
	r, err = readn(p.reader, len(data))
	p.respBytes = append(p.respBytes, r...)
	return copy(data, r), err
}

// GetTCPReader 获取Reader接口，用于其他的获取函数，比如http.ReadResponse
func (o *probe) GetTCPReader() *probeReader {
	tcp, _ := o.GetTCP()
	return &probeReader{
		o:      o,
		reader: tcp,
	}
}

// GetTLSReader 获取Reader接口，用于其他的获取函数，比如http.ReadResponse
func (o *probe) GetTLSReader() *probeReader {
	tcp, _ := o.GetTLS()
	return &probeReader{
		o:      o,
		reader: tcp,
	}
}

func newProbe(addr string, opts ...interface{}) *probe {
	var ret probe
	ret.addr = addr
	for _, opt := range opts {
		if opt == nil {
			//log.Println("[ERROR]  new-probe invalid opts", addr, len(opts))
			continue
		}

		switch o := opt.(type) {
		case Timeout:
			ret.Timeout = time.Duration(o)
		case string:
			ret.network = string(o)
		}
	}
	if ret.Timeout == 0 {
		ret.Timeout = DefaultTimeout
	}

	return &ret
}

func recoverError(err *error) {
	r := recover()
	if r != nil {
		if _err, ok := r.(error); ok {
			*err = _err
		} else {
			*err = fmt.Errorf("%v", r)
		}
	}
}

func keepSafe(p *probe, err *error) {
	r := recover()
	if r != nil {
		if _err, ok := r.(error); ok {
			*err = _err
		} else {
			*err = fmt.Errorf("%v", r)
		}
	}
	p.Close()
}

type arrBanner struct {
	data [][2]string
}

func (b *arrBanner) Add(k, v string) {
	b.data = append(b.data, [2]string{k, v})
}

func (b *arrBanner) String() string {
	return string(b.Bytes())
}

func (b *arrBanner) Bytes() []byte {
	var buf bytes.Buffer
	for _, arr := range b.data {
		buf.WriteString(arr[0])
		buf.WriteString(": ")
		buf.WriteString(arr[1])
		buf.WriteString("\n")
	}
	return buf.Bytes()
}

func encodeEndian(endian binary.ByteOrder, v ...interface{}) []byte {
	var buf bytes.Buffer
	for _, i := range v {
		switch vv := i.(type) {
		case byte:
			buf.WriteByte(vv)
		case uint16:
			p := make([]byte, 2)
			endian.PutUint16(p, vv)
			buf.Write(p)
		case uint32:
			p := make([]byte, 4)
			endian.PutUint32(p, vv)
			buf.Write(p)
		case uint64:
			p := make([]byte, 8)
			endian.PutUint64(p, vv)
			buf.Write(p)
		case string:
			buf.WriteString(vv)
		}
	}
	return buf.Bytes()
}

func encodeLittleEndian(v ...interface{}) []byte {
	return encodeEndian(binary.LittleEndian, v...)
}

func encodeBigEndian(v ...interface{}) []byte {
	return encodeEndian(binary.BigEndian, v...)
}

func bytes2string(b []byte) []string {
	var s []string
	for _, bb := range b {
		s = append(s, strconv.Itoa(int(bb)))
	}
	return s
}

func trimNullSuffix(b []byte) string {
	nullIndex := len(b)
	for i, bb := range b {
		if bb == 0x00 {
			nullIndex = i
			break
		}
	}
	return string(b[:nullIndex])
}
