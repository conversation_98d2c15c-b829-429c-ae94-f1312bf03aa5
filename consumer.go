package main

import (
	"checkurl/internal/mqqueue"
	"checkurl/internal/mqqueue/kafkamq"
	"log"
)

func NewConsumer(conf mqqueue.SimpleConsumerConfig) mqqueue.SimpleConsumer {
	if conf.Kafka != nil {
		topic := conf.Kafka.Topic
		log.Println("connect to consumer topic:", topic, "addr:", conf.Kafka.Brokers)
		sc, err := kafkamq.NewCommonSimpleConsumer(topic, []string{topic}, conf.Kafka.Brokers, conf.Kafka.Partition, true, handleError)
		if err != nil {
			log.Fatal(err)
		}
		return sc
	}

	log.Fatal("please enable a consumer")
	return nil
}
