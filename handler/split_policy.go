package handler

type DefaultSplitPolicy struct{}

func (p *DefaultSplitPolicy) DoSplit(task Task) []Job {
	return nil
}

// AssetScanPortSplitPolicy 基于端口的拆分策略
type AssetScanPortSplitPolicy struct {
	Num int // 切分的份数
}

func NewAssetScanPortSplitPolicy(num int) *AssetScanPortSplitPolicy {
	return &AssetScanPortSplitPolicy{
		Num: num,
	}
}

func (p *AssetScanPortSplitPolicy) GetSplits(targets []string) [][]string {
	count := len(targets) / p.Num

	var result = make([][]string, p.Num)

	// 前面num-1个，都取count个数据
	for i := 0; i < p.Num-1; i++ {
		result[i] = targets[i*count : (i+1)*count]
	}

	// 最后一个，取最后剩下的
	result[p.Num-1] = targets[(p.Num-1)*count:]

	return result
}
