package handler

import (
	"context"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/registry"
	"strings"
	"sync"
)

type Job interface {
	GetID() string                                 // 获得任务ID
	Start(ctx context.Context) error               // 任务启动
	Pause(ctx context.Context) error               // 暂停启动
	Resume(ctx context.Context, rate *int32) error // 恢复启动
	Stop(ctx context.Context) error                // 停止启动
	UpdateState(notify *pb.StateNotify)            // 更新任务状态
	GetState() string                              // 获得任务状态
	GetProgress() float32                          // 获得任务的进度
	GetWorkingOn() string                          // 获得任务正在处理的
	SetNode(*registry.Node)                        // 设置处理节点
	SetMessage(string)                             // 设置job消息
	GetMessage() string                            // job消息
	GetNode() *registry.Node                       // 获得处理节点
	Parent() Task                                  // 获得父节点
}

type AssetScanJob struct {
	ID         string  // 任务ID
	WorkingOn  string  // 正在处理的
	Message    string  // job消息
	Progress   float32 // 当前进度
	State      string  // 当前状态
	data       *pb.TaskEvent
	node       *registry.Node
	parent     Task
	dispatcher *Dispatcher
	mutex      sync.Mutex
}

func NewAssetScanJob(id string, task Task, dispatcher *Dispatcher, data *pb.TaskEvent) *AssetScanJob {
	return &AssetScanJob{
		ID:         id,
		State:      constant.StateInitial,
		parent:     task,
		dispatcher: dispatcher,
		data:       data,
	}
}

func (j *AssetScanJob) GetID() string {
	return j.ID
}

func (j *AssetScanJob) UpdateState(notify *pb.StateNotify) {
	j.mutex.Lock()
	defer j.mutex.Unlock()
	if notify.State == constant.StateInitial || j.State == constant.StateFinished {
		// 2023-06-29 防止initial状态覆盖其他状态
		return
	}

	j.State = notify.State
	j.Message = notify.Message
	if len(notify.WorkingOn) > 0 {
		j.WorkingOn = notify.WorkingOn
		j.parent.SetWorkingOn(notify.WorkingOn)
	}
	if notify.Progress > j.Progress && notify.Progress <= 100 && notify.Progress > 0 {
		j.Progress = notify.Progress
	}

	// 2023-10-16 WUYB：防止因更新数据库导致锁时间太长，注释此处处理，调整到dispatcher.go中处理
	//if err := j.dispatcher.dbHelper.UpdateJobState(j.ID, j.State, j.Progress); err != nil {
	//	logger.Warnf("update job state failed. job_id=%s err=%s", j.ID, err)
	//}
}

func (j *AssetScanJob) GetState() string {
	return j.State
}

func (j *AssetScanJob) GetProgress() float32 {
	return j.Progress
}

func (j *AssetScanJob) GetWorkingOn() string {
	return j.WorkingOn
}

func (j *AssetScanJob) SetNode(node *registry.Node) {
	j.node = node
}

func (j *AssetScanJob) SetMessage(message string) {
	j.Message = message
}

func (j *AssetScanJob) GetMessage() string {
	return j.Message
}

func (j *AssetScanJob) GetNode() *registry.Node {
	return j.node
}

func (j *AssetScanJob) Parent() Task {
	return j.parent
}

func (j *AssetScanJob) Start(ctx context.Context) error {
	topic := strings.ReplaceAll(j.node.Id, "-", ".")
	j.data.Command = constant.CommandStart
	return j.dispatcher.srv.Client().Publish(ctx, client.NewMessage(topic, j.data))
}

func (j *AssetScanJob) Pause(ctx context.Context) error {
	topic := strings.ReplaceAll(j.node.Id, "-", ".")
	return j.dispatcher.srv.Client().Publish(ctx, client.NewMessage(topic, &pb.TaskEvent{
		TaskId:  j.parent.GetID(),
		JobId:   j.ID,
		Command: constant.CommandPause,
	}))
}

func (j *AssetScanJob) Resume(ctx context.Context, rate *int32) error {
	topic := strings.ReplaceAll(j.node.Id, "-", ".")
	return j.dispatcher.srv.Client().Publish(ctx, client.NewMessage(topic, &pb.TaskEvent{
		TaskId:  j.parent.GetID(),
		JobId:   j.ID,
		Command: constant.CommandResume,
		Options: &pb.TaskOption{
			Rate: rate,
		},
	}))
}

func (j *AssetScanJob) Stop(ctx context.Context) error {
	topic := strings.ReplaceAll(j.node.Id, "-", ".")
	return j.dispatcher.srv.Client().Publish(ctx, client.NewMessage(topic, &pb.TaskEvent{
		TaskId:  j.parent.GetID(),
		JobId:   j.ID,
		Command: constant.CommandStop,
	}))
}
