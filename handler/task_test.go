package handler

import (
	"context"
	"dispatcher/internal"
	"dispatcher/pkg/common"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/agiledragon/gomonkey/v2"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"github.com/google/uuid"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/registry"
	"testing"
	"time"
)

type TaskSuite struct {
	suite.Suite
	Task       *AssetScanTask
	Dispatcher *Dispatcher
	Context    context.Context
}

func Test_TaskSuite(t *testing.T) {
	s := &TaskSuite{}
	suite.Run(t, s)
}

func (s *TaskSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	dispatcher := NewDispatcher(srv)

	taskID, _ := uuid.NewUUID()
	s.Dispatcher = dispatcher
	var isIpv6 bool
	s.Task = NewAssetScanTask(taskID.String(), dispatcher, &pb.DispatcherTaskStartRequest{
		Options: &pb.DispatcherTaskStartOptions{},
		PortScanner: &pb.DispatcherPortScannerConfig{
			Options: &pb.DispatcherPortScannerOption{
				IsIpv6: &isIpv6,
			},
		},
	})
	s.Context = context.Background()
}

func (s *TaskSuite) GetTaskID() string {
	taskID, _ := uuid.NewUUID()
	return taskID.String()
}

func (s *TaskSuite) Test_UpdateState() {
	Convey("Test_UpdateState", s.T(), func() {
		Convey("normal dispatching state", func() {
			s.Task.SetState(constant.StateDispatching)
			jobs := make([]Job, 0)

			// 模拟第一个子任务处于Running状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateRunning,
				Progress:  50.0,
				WorkingOn: "***************",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Initial状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateInitial,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要有一个子任务状态为Running，则为Running
			So(s.Task.GetState(), ShouldEqual, constant.StateRunning)
			So(s.Task.GetProgress(), ShouldEqual, 20)
			So(s.Task.GetWorkingOn(), ShouldEqual, "***************")
		})

		Convey("not all stop state", func() {
			s.Task.SetState(constant.StateStopping)
			jobs := make([]Job, 0)
			// 模拟第一个子任务处于Stopped状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateStopped,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Stopping状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateStopping,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Stopped，才为Stopped
			So(s.Task.State, ShouldEqual, constant.StateStopping)
		})

		Convey("must all stop state", func() {
			s.Task.SetState(constant.StateStopping)
			jobs := make([]Job, 0)

			// 模拟第一个子任务处于Stopped状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateStopped,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Stopped状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateStopped,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Stopped，才为Stopped
			So(s.Task.State, ShouldEqual, constant.StateStopped)
		})

		Convey("not all paused state", func() {
			s.Task.SetState(constant.StatePausing)
			jobs := make([]Job, 0)
			// 模拟第一个子任务处于Paused状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StatePaused,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Pausing状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StatePausing,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Paused，才为Paused
			So(s.Task.State, ShouldEqual, constant.StatePausing)
		})

		Convey("must all paused state", func() {
			s.Task.SetState(constant.StatePausing)
			jobs := make([]Job, 0)

			// 模拟第一个子任务处于Paused状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StatePaused,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Paused状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StatePaused,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Paused，才为Paused
			So(s.Task.State, ShouldEqual, constant.StatePaused)
		})

		Convey("paused with finished state", func() {
			s.Task.SetState(constant.StatePausing)
			jobs := make([]Job, 0)

			// 模拟第一个子任务处于Paused状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StatePaused,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于StateFinished状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateFinished,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			So(s.Task.State, ShouldEqual, constant.StatePaused)
		})

		Convey("normal final state", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "IsProcessFinished", func(ctx context.Context, taskID string, processName string) bool { return true }).Reset()
			s.Task.SetState(constant.StateRunning)
			jobs := make([]Job, 0)

			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateFinished,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateFinished,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())
			So(s.Task.State, ShouldEqual, constant.StateRunning)
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceRawGrab)
		})

		Convey("StateResuming", func() {
			s.Task.SetState(constant.StateResuming)
			jobs := make([]Job, 0)
			// 模拟第一个子任务处于Finished状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateFinished,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Running状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateRunning,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Stopped，才为Stopped
			So(s.Task.State, ShouldEqual, constant.StateRunning)
		})

		Convey("StateResuming to Running", func() {
			s.Task.SetState(constant.StateResuming)
			jobs := make([]Job, 0)
			// 模拟第一个子任务处于Finished状态
			job1 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job1.UpdateState(&pb.StateNotify{
				State:     constant.StateRunning,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job1)

			// 模拟第二个子任务处于Running状态
			job2 := NewAssetScanJob(s.GetTaskID(), s.Task, s.Dispatcher, nil)
			job2.UpdateState(&pb.StateNotify{
				State:     constant.StateRunning,
				Progress:  0,
				WorkingOn: "",
			})
			jobs = append(jobs, job2)

			s.Task.jobs = jobs
			s.Task.UpdateState(context.Background())

			// 只要所有子任务状态为Stopped，才为Stopped
			So(s.Task.State, ShouldEqual, constant.StateRunning)
		})
	})
}

func (s *TaskSuite) Test_Start() {
	Convey("Test_Start", s.T(), func() {
		Convey("invalid state", func() {
			s.Task.SetState(constant.StateDispatching)
			err := s.Task.Start(context.Background())
			So(err, ShouldNotBeNil)
		})
		Convey("normal initial start", func() {
			// 模拟端口扫描正常返回
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			defer gomonkey.ApplyFuncReturn(common.SelectServiceNode, &registry.Node{Id: s.Task.ID}, nil).Reset()
			s.Task.SetState(constant.StateInitial)
			err := s.Task.Start(context.Background())
			So(err, ShouldBeNil)
		})

		Convey("normal paused start", func() {
			// 模拟端口扫描正常返回
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StatePaused)
			err := s.Task.Start(context.Background())
			So(err, ShouldBeNil)
		})

		Convey("invalid start", func() {
			// 模拟端口扫描失败
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", errors.New("start failed")).Reset()
			s.Task.SetState(constant.StateInitial)
			err := s.Task.Start(context.Background())
			So(err, ShouldNotBeNil)
		})

		Convey("TaskTypePing split job", func() {
			// 模拟端口扫描正常返回
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			defer gomonkey.ApplyFuncReturn(common.SelectServiceNode, &registry.Node{Id: s.Task.ID}, nil).Reset()
			s.Task.jobs = make([]Job, 0)
			s.Task.SetState(constant.StateInitial)
			s.Task.data.PortScanner.TaskType = constant.TaskTypePing
			err := s.Task.Start(context.Background())
			So(err, ShouldBeNil)
			So(len(s.Task.jobs), ShouldEqual, 2)
		})

		Convey("ping scan split job", func() {
			// 模拟端口扫描正常返回
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			defer gomonkey.ApplyFuncReturn(common.SelectServiceNode, &registry.Node{Id: s.Task.ID}, nil).Reset()
			pingScan := true
			s.Task.jobs = make([]Job, 0)
			s.Task.SetState(constant.StateInitial)
			s.Task.data.PortScanner.Options.PingScan = &pingScan
			err := s.Task.Start(context.Background())
			So(err, ShouldBeNil)
			So(len(s.Task.jobs), ShouldEqual, 2)
		})
	})
}

func (s *TaskSuite) Test_Stop() {
	Convey("Test_Stop", s.T(), func() {
		Convey("invalid state", func() {
			s.Task.SetState(constant.StateInitial)
			err := s.Task.Stop(context.Background())
			// 如果是StateInitial状态，也是正常返回
			So(err, ShouldBeNil)
		})

		Convey("running state 1", func() {
			s.Task.SetState(constant.StateRunning)
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			job := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				parent:     s.Task,
				dispatcher: s.Dispatcher,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}
			gomonkey.ApplyMethodReturn(job, "Stop", nil)
			s.Task.jobs = []Job{job}

			err := s.Task.Stop(context.Background())

			So(err, ShouldBeNil)
		})

		Convey("running state 2", func() {
			// 模拟正常发送到PortScanner处理
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StateRunning)

			job1 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			job2 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			s.Task.jobs = []Job{job1, job2}

			err := s.Task.Stop(context.Background())
			// 如果是StateRunning状态，但是PortScanner的Job状态为Running和Finished，可正常处理
			So(err, ShouldBeNil)
		})

		Convey("running state 3", func() {
			// 模拟正常发送到PortScanner处理
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StateRunning)

			job1 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			job2 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StatePausing,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			s.Task.jobs = []Job{job1, job2}

			err := s.Task.Stop(context.Background())
			// 如果是StateRunning状态，但是PortScanner的Job状态为Paused和Pausing，可正常处理
			So(err, ShouldBeNil)
			So(s.Task.State, ShouldEqual, constant.StateStopping)
		})
	})
}

func (s *TaskSuite) Test_Pause() {
	Convey("Test_Pause", s.T(), func() {
		Convey("invalid state initial", func() {
			s.Task.SetState(constant.StateInitial)
			err := s.Task.Pause(context.Background())
			// 如果是StateInitial状态，返回错误
			So(err, ShouldNotBeNil)
		})

		Convey("PauseTaskErrorCauseResuming", func() {
			s.Task.SetState(constant.StateResuming)
			err := s.Task.Pause(context.Background())
			// 如果是StateInitial状态，返回错误
			So(err, ShouldResemble, internal.PauseTaskErrorCauseResuming)
		})

		Convey("PauseTaskErrorCausePausing", func() {
			s.Task.SetState(constant.StatePausing)
			err := s.Task.Pause(context.Background())
			// 如果是StateInitial状态，返回错误
			So(err, ShouldResemble, internal.PauseTaskErrorCausePausing)
		})

		Convey("PauseTaskErrorCausePaused", func() {
			s.Task.SetState(constant.StatePaused)
			err := s.Task.Pause(context.Background())
			// 如果是StateInitial状态，返回错误
			So(err, ShouldResemble, internal.PauseTaskErrorCausePaused)
		})

		Convey("invalid state pausing", func() {
			s.Task.SetState(constant.StatePausing)
			err := s.Task.Pause(context.Background())
			// 如果是StatePausing状态，返回错误
			So(err, ShouldNotBeNil)
		})

		Convey("running state normal", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StateRunning)

			job1 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			job2 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			s.Task.jobs = []Job{job1, job2}

			err := s.Task.Pause(context.Background())
			// 如果是StateRunning状态，返回正常处理
			So(err, ShouldBeNil)
			So(s.Task.State, ShouldEqual, constant.StatePausing)
		})

		Convey("running state 2", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StateRunning)

			job1 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			job2 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			s.Task.jobs = []Job{job1, job2}

			err := s.Task.Pause(context.Background())

			So(err, ShouldBeNil)
		})
	})
}

func (s *TaskSuite) Test_Resume() {
	Convey("Test_Resume", s.T(), func() {
		Convey("invalid state finished", func() {
			s.Task.SetState(constant.StateFinished)
			err := s.Task.Resume(context.Background(), nil)
			// 如果是StateFinished状态，返回错误
			So(err, ShouldResemble, internal.NoNeedResumeTaskError)
		})

		Convey("PauseResumeErrorCausePausing", func() {
			s.Task.SetState(constant.StatePausing)
			err := s.Task.Resume(context.Background(), nil)
			// 如果是StateFinished状态，返回错误
			So(err, ShouldResemble, internal.PauseResumeErrorCausePausing)
		})

		Convey("paused state 1", func() {
			s.Task.SetState(constant.StatePaused)
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			job := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				parent:     s.Task,
				dispatcher: s.Dispatcher,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			gomonkey.ApplyMethodReturn(job, "Resume", nil)
			s.Task.jobs = []Job{job}

			err := s.Task.Resume(context.Background(), nil)

			So(err, ShouldBeNil)
		})

		Convey("paused state normal", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			s.Task.SetState(constant.StatePaused)

			job1 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			job2 := &AssetScanJob{
				ID:         Generator.MustNext(),
				State:      constant.StateFinished,
				dispatcher: s.Dispatcher,
				parent:     s.Task,
				node: &registry.Node{
					Id: Generator.MustNext(),
				},
			}

			s.Task.jobs = []Job{job1, job2}

			err := s.Task.Resume(context.Background(), nil)
			// 如果是StatePaused状态，正常处理
			So(err, ShouldBeNil)
			So(s.Task.State, ShouldEqual, constant.StateResuming)
		})
	})
}

func (s *TaskSuite) Test_MonitorTaskState() {
	Convey("Test_MonitorTaskState", s.T(), func() {
		Convey("normal", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) { return true, 0.5 }).Reset()
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			s.Task.MonitorTaskState(context.Background())
			So(s.Task.GetState(), ShouldEqual, constant.StateFinished)
		})

		Convey("bug #33", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceRawGrab {
					return false, 0.5
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 100.0
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(1 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceRawGrab)
			So(s.Task.Progress, ShouldEqual, float32(100))
		})

		Convey("rawgrab", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceRawGrab {
					return false, 0.5
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 50
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(1 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceRawGrab)
			So(s.Task.Progress, ShouldEqual, float32(82.0))
		})

		Convey("checkurl", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceCheckUrl {
					return false, 0.6
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 50
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(3 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceCheckUrl)
			So(s.Task.Progress, ShouldEqual, float32(86.4))
		})

		Convey("crawler", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceCrawler {
					return false, 0.7
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 50
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(1 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceCrawler)
			So(s.Task.Progress, ShouldEqual, float32(90.8))
		})

		Convey("data-analysis", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceDataAnalysis {
					return false, 0.8
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 50
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(1 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceDataAnalysis)
			So(s.Task.Progress, ShouldEqual, float32(95.2))
		})

		Convey("quick-store", func() {
			defer gomonkey.ApplyMethodFunc(s.Dispatcher, "QueryProcessResult", func(ctx context.Context, taskID string, processName string) (bool, float32) {
				if processName == constant.ServiceQuickStore {
					return false, 0.9
				}
				return true, 1.0
			}).Reset()
			s.Task.Progress = 50
			s.Task.SetState(constant.StateRunning)
			s.Task.CurrentProcess = constant.ServiceRawGrab
			ctx := context.Background()
			go s.Task.MonitorTaskState(ctx)
			time.Sleep(1 * time.Second)
			ctx.Done()
			So(s.Task.CurrentProcess, ShouldEqual, constant.ServiceQuickStore)
			So(s.Task.Progress, ShouldEqual, float32(99.6))
		})
	})
}
