package handler

import (
	"context"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"github.com/google/uuid"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"sync"
	"testing"
)

type JobSuite struct {
	suite.Suite
	Job        *AssetScanJob
	task       *AssetScanTask
	Dispatcher *Dispatcher
	Context    context.Context
}

func Test_JobSuite(t *testing.T) {
	s := &JobSuite{}
	suite.Run(t, s)
}

func (s *JobSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	dispatcher := NewDispatcher(srv)

	taskID, _ := uuid.NewUUID()
	jobID, _ := uuid.NewUUID()
	s.Dispatcher = dispatcher
	var isIpv6 bool
	s.task = NewAssetScanTask(taskID.String(), dispatcher, &pb.DispatcherTaskStartRequest{
		Options: &pb.DispatcherTaskStartOptions{},
		PortScanner: &pb.DispatcherPortScannerConfig{
			Options: &pb.DispatcherPortScannerOption{
				IsIpv6: &isIpv6,
			},
		},
	})
	s.Job = NewAssetScanJob(jobID.String(), s.task, dispatcher, &pb.TaskEvent{})
	s.Context = context.Background()
}

func (s *JobSuite) Test_UpdateState() {
	Convey("Test_UpdateState", s.T(), func() {
		Convey("normal", func() {
			s.Job.UpdateState(&pb.StateNotify{
				State:     constant.StateRunning,
				Progress:  50.0,
				WorkingOn: "***************",
			})

			So(s.Job.GetWorkingOn(), ShouldEqual, "***************")
			So(s.Job.GetProgress(), ShouldEqual, 50.0)
			So(s.Job.GetState(), ShouldEqual, constant.StateRunning)

			So(s.Job.Parent(), ShouldResemble, s.task)
			So(s.Job.GetNode(), ShouldBeNil)
		})
	})
}

func (s *JobSuite) Test_bug_132() {
	Convey("Test_bug_132", s.T(), func() {
		Convey("normal", func() {
			var wg sync.WaitGroup
			updateRunning := func() {
				s.Job.UpdateState(&pb.StateNotify{
					State:     constant.StateRunning,
					Progress:  50.0,
					WorkingOn: "***************",
				})
				wg.Done()
			}

			updateFinished := func() {
				s.Job.UpdateState(&pb.StateNotify{
					State:     constant.StateFinished,
					Progress:  100.0,
					WorkingOn: "***************",
				})
				wg.Done()
			}

			for i := 0; i < 100; i++ {
				s.Job.State = constant.StateRunning
				wg.Add(2)
				go updateFinished()
				go updateRunning()
				wg.Wait()
				So(s.Job.GetState(), ShouldEqual, constant.StateFinished)
			}
		})
	})
}
