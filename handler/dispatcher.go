package handler

import (
	"context"
	"dispatcher/model"
	"dispatcher/pkg/common"
	"encoding/json"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/client"
	"strings"
	"sync"
	"time"

	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
)

type Dispatcher struct {
	tasks      map[string]Task
	srv        micro.Service
	limit      Limitation // 限制
	listeners  []TaskStateListener
	processMap map[string]ProcessService
	mutex      sync.Mutex
	dbHelper   *model.DBHelper
}

type ProcessService interface {
	StateQuery(ctx context.Context, in *pb.StateQueryRequest, opts ...client.CallOption) (*pb.StateQueryResponse, error)
	FinishedNotify(ctx context.Context, in *pb.FinishedNotifyRequest, opts ...client.CallOption) (*pb.FinishedNotifyResponse, error)
}

func NewDispatcher(srv micro.Service) *Dispatcher {
	processMap := make(map[string]ProcessService)
	processMap[constant.ServiceRawGrab] = pb.NewRawGrabService(constant.ServiceRawGrab, srv.Client())
	processMap[constant.ServiceCheckUrl] = pb.NewCheckURLService(constant.ServiceCheckUrl, srv.Client())
	processMap[constant.ServiceCrawler] = pb.NewCrawlerService(constant.ServiceCrawler, srv.Client())
	processMap[constant.ServiceDataAnalysis] = pb.NewDataAnalysisService(constant.ServiceDataAnalysis, srv.Client())
	processMap[constant.ServiceQuickStore] = pb.NewQuickStoreService(constant.ServiceQuickStore, srv.Client())

	return &Dispatcher{
		srv:        srv,
		tasks:      make(map[string]Task),
		listeners:  make([]TaskStateListener, 0),
		processMap: processMap,
		dbHelper:   model.NewDBHelper("data.db"),
	}
}

func (d *Dispatcher) WithLimitation(limitation Limitation) {
	d.limit = limitation
}

func (d *Dispatcher) GetTasks() []Task {
	var tasks = make([]Task, 0)
	for _, task := range d.tasks {
		tasks = append(tasks, task)
	}
	return tasks
}

func (d *Dispatcher) GetTask(taskID string) Task {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	return d.tasks[taskID]
}

func (d *Dispatcher) State(ctx context.Context, request *pb.DispatcherTaskStateRequest, response *pb.DispatcherTaskStateResponse) error {
	logger.Infof("received task state request. data=%+v", request)
	response.TaskId = request.TaskId
	d.mutex.Lock()
	d.mutex.Unlock()

	if task, ok := d.tasks[request.TaskId]; ok {
		response.State = task.GetState()
		response.Progress = task.GetProgress()
		response.WorkingOn = task.GetWorkingOn()
		response.Jobs = make([]*pb.JobState, len(task.GetJobs()))
		for _, job := range task.GetJobs() {
			var s pb.JobState
			s.JobId = job.GetID()
			s.State = job.GetState()
			s.Progress = job.GetProgress()
			s.WorkingOn = job.GetWorkingOn()
			response.Jobs = append(response.Jobs, &s)
		}
		return nil
	}
	return errors.New(fmt.Sprintf("task doesn't exist. task=%s", request.TaskId))
}

// Start to dispatch task
func (d *Dispatcher) Start(ctx context.Context, request *pb.DispatcherTaskStartRequest, response *pb.DispatcherTaskResponse) error {
	logger.Infof("received task start request. data=%+v", request)
	response.TaskId = request.TaskId

	// 1、先进行参数验证, 由于API已经做过了参数验证，暂时不做参数验证
	// 初始化任务限制
	if d.limit != nil {
		err := d.limit.Initial(ctx, request.TaskId, request.Options.MaxAssetNum)
		if err != nil {
			logger.Warnf("initial limit failed. caused by %s", err)
		}
	}

	var task Task

	// 2、判断任务ID是否存在
	if t := d.GetTask(request.TaskId); t != nil {
		task = t
	} else {
		task = NewAssetScanTask(request.TaskId, d, request)
	}

	// 缓存任务
	err := d.dbHelper.CreateTask(request.TaskId, request)
	if err != nil {
		return err
	}

	// 3、进行任务调度
	if err := task.(Task).Start(ctx); err != nil {
		logger.Errorf("task start failed. [%s] caused by %s", request.TaskId, err)

		err1 := d.dbHelper.DeleteTask(request.TaskId)
		if err1 != nil {
			logger.Warn("clear task from db failed. task: ", request.TaskId)
		}
		return err
	}

	d.mutex.Lock()
	d.tasks[request.TaskId] = task
	d.mutex.Unlock()
	return nil
}

func (d *Dispatcher) Pause(ctx context.Context, request *pb.DispatcherTaskPauseRequest, response *pb.DispatcherTaskPauseResponse) error {
	logger.Infof("received task pause request.data=%+v", request)
	response.TaskId = request.TaskId

	task := d.GetTask(request.TaskId)
	// 1、先判断任务是否存在
	if task != nil {
		// 2、调用任务的暂停操作
		err := task.Pause(ctx)
		if err != nil {
			logger.Errorf("pause failed. [%s] caused by %s", request.TaskId, err)
		}
		return err
	}

	logger.Warnf("task doesn't exist. task=%s", request.TaskId)
	return errors.New(fmt.Sprintf("task doesn't exist. task=%s", request.TaskId))
}

func (d *Dispatcher) Resume(ctx context.Context, request *pb.DispatcherTaskResumeRequest, response *pb.DispatcherTaskResumeResponse) error {
	logger.Infof("received task resume request. data=%+v", request)
	response.TaskId = request.TaskId

	task := d.GetTask(request.TaskId)
	// 1、先判断任务是否存在
	if task != nil {
		// 2、调用任务的恢复操作
		err := task.Resume(ctx, request.Rate)
		if err != nil {
			logger.Errorf("resume failed. [%s] caused by %s", request.TaskId, err)
		}
		return err
	}

	logger.Warnf("task doesn't exist. task=%s", request.TaskId)
	return errors.New(fmt.Sprintf("task doesn't exist. task=%s", request.TaskId))
}

func (d *Dispatcher) Stop(ctx context.Context, request *pb.DispatcherTaskStopRequest, response *pb.DispatcherTaskStopResponse) error {
	logger.Infof("received task stop request. data=%+v", request)
	response.TaskId = request.TaskId

	task := d.GetTask(request.TaskId)
	// 1、先判断任务是否存在
	if task != nil {
		// 2、调用任务的停止操作
		err := task.Stop(ctx)
		if err != nil {
			logger.Errorf("stop failed.%+v caused by %s", request, err)
		}
		return err
	}

	logger.Warnf("task doesn't exist. task=%s", request.TaskId)
	return errors.New(fmt.Sprintf("task doesn't exist. task=%s", request.TaskId))
}

func (d *Dispatcher) NotifyStateChanged(ctx context.Context, taskID, state, message string) {
	for _, listener := range d.listeners {
		go listener.NotifyStateChanged(ctx, taskID, state, message)
	}
}

func (d *Dispatcher) NotifyProgress(ctx context.Context, taskID string, progress float32, workingOn, remainTime string) {
	for _, listener := range d.listeners {
		go listener.NotifyProgress(ctx, taskID, progress, workingOn, remainTime)
	}
}

func (d *Dispatcher) AppendStateChangedListener(listener TaskStateListener) {
	d.listeners = append(d.listeners, listener)
}

func (d *Dispatcher) TaskNotifyHandler(ctx context.Context, event *pb.TaskNotify) error {
	logger.Infof("received result event. %+v", event)
	task := d.GetTask(event.TaskId)
	if task != nil {
		result := event.GetResult()
		if result != nil {
			if d.limit != nil && d.limit.Exceed(ctx, event.TaskId, result.Ip, result.Port) {
				// 超过限制，停止扫描，并丢弃扫描的结果
				logger.Warnf("license asset limit is overflows")
				if task.GetState() == constant.StateRunning {
					task.SetIsOverflow(true)
					return task.Stop(ctx)
				}

				return nil
			}

			task.SetWorkingOn(result.Ip)

			var err error
			taskInfo := &pb.TaskInfo{
				TaskId:              event.TaskId,
				IsIpv6:              *task.GetRawData().PortScanner.Options.IsIpv6,
				TaskType:            int32(task.GetRawData().PortScanner.TaskType),
				UnknownProtocolInDb: task.GetRawData().Options.UnknownProtocolIndb,
				IsCrawlerAllUrl:     task.GetRawData().Options.CrawlerAllUrl,
				CrawlerUrlBlackKey:  task.GetRawData().Options.CrawlerUrlBlackKey,
				CrawlerSpecificUrl:  task.GetRawData().Options.CrawlerSpecificUrl,
				Extra:               task.GetRawData().Options.Extra,
			}

			if strings.EqualFold(result.BaseProtocol, "icmp") || task.GetRawData().PortScanner.TaskType == constant.TaskTypePing {
				// 如果是ICMP协议，直接入库
				err = d.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceQuickStore, &pb.QuickStoreEvent{
					TaskInfo: taskInfo,
					JobId:    event.JobId,
					Origin:   constant.ServicePortScanner,
					Data: &pb.QuickStoreEvent_Normal{
						Normal: &pb.Normal{
							Ip:           result.Ip,
							Port:         result.Port,
							BaseProtocol: result.BaseProtocol,
						},
					},
				}))
			} else {
				// 其他的发送给RawGrab处理
				err = d.srv.Client().Publish(ctx, client.NewMessage(constant.ServiceRawGrab, &pb.RawGrabEvent{
					TaskInfo:     taskInfo,
					JobId:        event.JobId,
					Origin:       constant.ServicePortScanner,
					Ip:           result.Ip,
					Port:         result.Port,
					BaseProtocol: result.BaseProtocol,
					BindProtocol: result.BindProtocol,
				}))
			}
			if err != nil {
				logger.Warn("publish failed. err:", err)
			}
			return err
		}

		state := event.GetState()
		if state != nil {
			if job := task.GetJob(event.JobId); job != nil {
				job.UpdateState(state)
				if err := d.dbHelper.UpdateJobState(job.GetID(), job.GetState(), job.GetProgress()); err != nil {
					logger.Warnf("update job state failed. job_id=%s err=%s", job.GetID(), err)
				}
			} else {
				logger.Infof("can't get job. job:%s", event.JobId)
			}

			// 设置任务正在处理
			task.SetWorkingOn(state.WorkingOn)

			//TODO 没有解决任务拆分成多个job的问题，后续考虑，具体逻辑应该在task.SetRemainTime内部实现
			if state.RemainTime == "" {
				state.RemainTime = "00:00:00"
			}

			task.SetRemainTime(state.RemainTime)
			task.UpdateState(ctx)
			return nil
		}
	}
	logger.Infof("can't get task. task:%s", event.TaskId)
	return nil
}

func (d *Dispatcher) ClearTaskCache(ctx context.Context, taskID string) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	if _, ok := d.tasks[taskID]; ok {
		logger.Infof("clear task cache. task:%s", taskID)
		delete(d.tasks, taskID)

		err := d.dbHelper.DeleteTask(taskID)
		if err != nil {
			logger.Warn("clear task from db failed. task: ", taskID)
		}
	}
}

func (d *Dispatcher) IsProcessFinished(ctx context.Context, taskID string, processName string) bool {
	if processService, ok := d.processMap[processName]; ok {
		nodes, err := common.GetServiceNodesByName(d.srv, processName)
		if err != nil {
			logger.Warnf("get service nodes failed. task:%s process=%s caused by %s", taskID, processName, err)
			return false
		}

		// 遍历每个处理器的多个处理节点
		for _, node := range nodes {
			out, err := processService.StateQuery(ctx, &pb.StateQueryRequest{TaskId: taskID}, client.WithSelectOption(common.NodeSelectOption(node.Id)))
			if err != nil {
				logger.Warnf("query state failed.task=%s process=%s. caused by %s", taskID, node.Id, err)
				return false
			}
			logger.Infof("query state result. task=%s process=%s result=%+v", taskID, node.Id, out)
			if out.Remain > 0 {
				return false
			}
		}
	}
	return true
}

func (d *Dispatcher) QueryProcessResult(ctx context.Context, taskID string, processName string) (bool, float32) {
	if processService, ok := d.processMap[processName]; ok {
		doQuery := func() (bool, float32) {
			nodes, err := common.GetServiceNodesByName(d.srv, processName)
			if err != nil {
				logger.Warnf("get service nodes failed. task:%s process=%s caused by %s", taskID, processName, err)
				return false, 0
			}

			if len(nodes) <= 0 {
				logger.Warnf("can not find nodes for %s", processName)
			}

			// 遍历每个处理器的多个处理节点
			for _, node := range nodes {
				out, err := processService.StateQuery(ctx, &pb.StateQueryRequest{TaskId: taskID}, client.WithSelectOption(common.NodeSelectOption(node.Id)))
				if err != nil {
					logger.Warnf("query state failed.task=%s process=%s. caused by %s", taskID, node.Id, err)
					return false, 0
				}
				logger.Infof("query state result. task=%s process=%s result=%+v", taskID, node.Id, out)
				if out.Remain > 0 {
					ratio := float32(0.0)
					if out.Total > 0 {
						ratio = float32((out.Total - out.Remain) / out.Total)
					}
					return false, ratio
				}
			}
			return true, 1.0
		}

		var tries = 0
		for {
			isFinished, ratio := doQuery()
			if !isFinished {
				return isFinished, ratio
			}

			if tries > 3 {
				return isFinished, ratio
			}

			tries++
			// http broker 处理多个消息间隔100ms
			time.Sleep(88 * time.Millisecond)
		}

	}
	return true, 1.0
}

func (d *Dispatcher) NotifyFinished(ctx context.Context, taskID string) {
	// 遍历所有的处理器
	for name, processService := range d.processMap {
		nodes, err := common.GetServiceNodesByName(d.srv, name)
		if err != nil {
			logger.Warnf("get service nodes failed. task:%s process=%s caused by %s", taskID, name, err)
			continue
		}

		// 遍历每个处理器的多个处理节点
		for _, node := range nodes {
			_, err = processService.FinishedNotify(ctx, &pb.FinishedNotifyRequest{TaskId: taskID}, client.WithSelectOption(common.NodeSelectOption(node.Id)))
			if err != nil {
				logger.Warnf("notify %s finish failed. task:%s. caused by %s", node.Id, taskID, err)
				continue
			}
			logger.Infof("notify %s finish successful. task:%s", node.Id, taskID)
		}
	}

	d.ClearTaskCache(ctx, taskID)
}

func (d *Dispatcher) LoadTasks() {
	logger.Info("start loading tasks from db")
	tasks, err := d.dbHelper.GetUnfinishedTasks()
	if err != nil {
		logger.Warnf("load unfinished task failed.")
		return
	}

	nodes, err := common.GetServiceNodesByName(d.srv, constant.ServicePortScanner)
	if err != nil {
		logger.Warnf("can't find port scanner nodes.")
		return
	}

	for _, task := range tasks {
		var request pb.DispatcherTaskStartRequest
		err = json.Unmarshal([]byte(task.Data), &request)
		if err != nil {
			logger.Warnf("unmarshal task data failed.")
			continue
		}
		t := NewAssetScanTask(task.ID, d, &request)
		t.State = task.State
		t.Progress = task.Progress
		t.CurrentProcess = task.CurrentProcess

		jobs, err := d.dbHelper.GetJobs(task.ID)
		if err != nil {
			logger.Warnf("get jobs for task failed. task_id=%s", task.ID)
		} else {
			// 处理缓存的Job
			for _, j := range jobs {
				var event pb.TaskEvent
				err = json.Unmarshal([]byte(j.Data), &event)
				if err != nil {
					fmt.Println("err", err)
					logger.Warnf("unmarshal job data failed.")
					continue
				}
				job := NewAssetScanJob(j.ID, t, d, &event)
				job.State = j.State
				job.Progress = j.Progress
				for _, node := range nodes {
					if node.Id == j.NodeID {
						job.node = node
						break
					}
				}
				t.jobs = append(t.jobs, job)
			}
		}

		d.mutex.Lock()
		d.tasks[task.ID] = t
		d.mutex.Unlock()
	}

}
