package handler

import (
	"context"
	"dispatcher/internal"
	"dispatcher/pkg/common"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4/logger"
	"golang.org/x/sync/errgroup"
	"strings"
	"sync"
	"time"
)

type Task interface {
	GetID() string                                 // 获得任务ID
	GetState() string                              // 获得任务状态
	GetProgress() float32                          // 获得任务的进度
	GetWorkingOn() string                          // 获得任务正在处理的
	Start(ctx context.Context) error               // 任务启动
	Pause(ctx context.Context) error               // 暂停启动
	Resume(ctx context.Context, rate *int32) error // 恢复启动
	Stop(ctx context.Context) error                // 停止启动
	UpdateState(ctx context.Context)               // 更新总任务状态和进度
	GetJob(string) Job                             // 子任务
	GetJobs() []Job                                // 获得所有
	SetWorkingOn(string)                           // 设置任务正在处理的
	SetRemainTime(string)                          // 设置端口扫描剩余时间
	SetMessage(string)                             // 设置任务消息
	SetIsOverflow(bool)                            // 设置资产是否溢出超出license限制
	SetState(string)                               // 设置任务状态
	GetRawData() *pb.DispatcherTaskStartRequest    // 获得任务的原始数据
}

type AssetScanTask struct {
	ID             string      // 任务ID
	WorkingOn      string      // 正在处理的
	RemainTime     string      // 端口扫描剩余时间
	Message        string      // 任务消息
	Progress       float32     // 当前进度
	State          string      // 当前状态
	IsOverflow     bool        // 资产是否溢出超出license限制
	CurrentProcess string      // 当前的处理进程
	jobs           []Job       // 拆分的子任务
	splitPolicy    SplitPolicy // 拆分策略
	dispatcher     *Dispatcher // 调度器
	data           *pb.DispatcherTaskStartRequest
	once           sync.Once
}

func NewAssetScanTask(taskID string, dispatcher *Dispatcher, data *pb.DispatcherTaskStartRequest) *AssetScanTask {
	return &AssetScanTask{
		ID:         taskID,
		State:      constant.StateInitial,
		dispatcher: dispatcher,
		data:       data,
		jobs:       make([]Job, 0),
		once:       sync.Once{},
	}
}

func (s *AssetScanTask) GetRawData() *pb.DispatcherTaskStartRequest {
	return s.data
}

// UpdateState 更新总任务状态和进度
func (s *AssetScanTask) UpdateState(ctx context.Context) {
	logger.Infof("current state:%s task_id:%s", s.State, s.ID)
	switch s.State {
	case constant.StateDispatching:
		// 如果是dispatching或者是resuming，判断子任务，如果有一个是Running的，则任务状态为Running
		for _, job := range s.jobs {
			if job.GetState() == constant.StateRunning {
				s.State = constant.StateRunning
				// 任务状态变化通知
				s.NotifyStateChanged(ctx, constant.StateRunning, "")
				break
			}
		}
	case constant.StateResuming:
		for _, job := range s.jobs {
			if job.GetState() != constant.StateRunning &&
				job.GetState() != constant.StateFinished &&
				job.GetState() != constant.StateInitial {
				return
			}

			s.State = constant.StateRunning
			// 任务状态变化通知
			s.NotifyStateChanged(ctx, constant.StateRunning, "")
		}

	case constant.StateStopping:
		// 如果是stopping，判断是否所有的子任务都变成了stopped
		for _, job := range s.jobs {
			if job.GetState() != constant.StateStopped && job.GetState() != constant.StateFinished {
				return
			}
		}

		if s.IsOverflow {
			// 任务状态变化通知
			s.State = constant.StateFinished
			s.NotifyStateChanged(ctx, constant.StateFinished, "")
			logger.Info("assets is overflow notify finished.")
		} else {
			s.State = constant.StateStopped
			s.NotifyStateChanged(ctx, constant.StateStopped, "")
		}

		// 清空缓存
		s.dispatcher.NotifyFinished(ctx, s.ID)
	case constant.StatePausing:
		// 如果是pausing，判断是否所有的子任务都变成了paused
		for _, job := range s.jobs {
			if job.GetState() == constant.StateError {
				s.State = constant.StateError
				// 任务状态变化通知
				s.NotifyStateChanged(ctx, constant.StateError, internal.PausedError)
				s.dispatcher.NotifyFinished(ctx, s.ID)
				return
			}

			if job.GetState() != constant.StatePaused && job.GetState() != constant.StateFinished {
				return
			}
		}
		s.State = constant.StatePaused
		// 任务状态变化通知
		s.NotifyStateChanged(ctx, constant.StatePaused, "")
	default:
		for _, job := range s.jobs {
			if job.GetState() == constant.StateError {
				s.State = constant.StateError
				// 任务状态变化通知
				s.NotifyStateChanged(ctx, constant.StateError, job.GetMessage())
				s.dispatcher.NotifyFinished(ctx, s.ID)
				return
			}
		}
		break
	}

	// 计算任务进度
	if s.State == constant.StateRunning {
		if s.AllJobFinished(ctx) {
			// PortScanner已经全部完成
			s.once.Do(func() {
				// 启动一个监听线程，判断任务是否完成
				s.CurrentProcess = constant.ServiceRawGrab

				if err := s.dispatcher.dbHelper.UpdateTaskCurrentProgress(s.ID, constant.ServiceRawGrab); err != nil {
					logger.Warnf("update task current progress failed. task_id=%s err=%+v", s.ID, err)
				}

				go s.MonitorTaskState(ctx)
			})
		}

		s.Progress = s.CalcProgress(ctx)
		s.dispatcher.NotifyProgress(ctx, s.ID, s.Progress, s.WorkingOn, s.RemainTime)
	}
}

func (s *AssetScanTask) MonitorTaskState(ctx context.Context) {
	logger.Infof("start monitor state to finished. task:%s", s.ID)
	for {
		select {
		case <-ctx.Done():
			return
		default:
			break
		}
		var currentProgress float32

		// 只有状态为Running的时候，才去监控任务状态，防止portscanner已完成，然后又暂停了
		if s.State == constant.StateRunning {
			finished, ratio := s.dispatcher.QueryProcessResult(ctx, s.ID, s.CurrentProcess)

			switch s.CurrentProcess {
			case constant.ServiceRawGrab:
				currentProgress = 80 + 4*ratio

				if finished {
					s.CurrentProcess = constant.ServiceCheckUrl

					if err := s.dispatcher.dbHelper.UpdateTaskCurrentProgress(s.ID, constant.ServiceCheckUrl); err != nil {
						logger.Warnf("update task current progress failed. task_id=%s err=%+v", s.ID, err)
					}
				}
			case constant.ServiceCheckUrl:
				currentProgress = 84 + 4*ratio

				if finished {
					s.CurrentProcess = constant.ServiceCrawler

					if err := s.dispatcher.dbHelper.UpdateTaskCurrentProgress(s.ID, constant.ServiceCrawler); err != nil {
						logger.Warnf("update task current progress failed. task_id=%s err=%+v", s.ID, err)
					}
				}
			case constant.ServiceCrawler:
				currentProgress = 88 + 4*ratio

				if finished {
					s.CurrentProcess = constant.ServiceDataAnalysis

					if err := s.dispatcher.dbHelper.UpdateTaskCurrentProgress(s.ID, constant.ServiceDataAnalysis); err != nil {
						logger.Warnf("update task current progress failed. task_id=%s err=%+v", s.ID, err)
					}
				}
			case constant.ServiceDataAnalysis:
				currentProgress = 92 + 4*ratio

				if finished {
					s.CurrentProcess = constant.ServiceQuickStore

					if err := s.dispatcher.dbHelper.UpdateTaskCurrentProgress(s.ID, constant.ServiceQuickStore); err != nil {
						logger.Warnf("update task current progress failed. task_id=%s err=%+v", s.ID, err)
					}
				}
			case constant.ServiceQuickStore:
				currentProgress = 96 + 4*ratio

				if finished {
					s.State = constant.StateFinished

					s.NotifyStateChanged(ctx, constant.StateFinished, "")
					// 通知所有的处理节点，已经完成
					s.dispatcher.NotifyFinished(ctx, s.ID)
					return
				}
			}

			if currentProgress > s.Progress {
				s.Progress = currentProgress
			}
			s.dispatcher.NotifyProgress(ctx, s.ID, s.Progress, s.WorkingOn, "00:00:00")

			// 如果某个阶段完成，则直接继续，防止等待5秒
			if finished {
				continue
			}
		}
		time.Sleep(time.Second * 2)
	}
}

func (s *AssetScanTask) CalcProgress(ctx context.Context) float32 {
	if s.jobs == nil || len(s.jobs) == 0 {
		return s.Progress
	}
	progress := float32(0.0)
	for _, job := range s.jobs {
		progress += job.GetProgress()
	}
	// 端口扫描完成，则为80%
	return progress * 0.8 / float32(len(s.jobs))
}

func (s *AssetScanTask) NotifyStateChanged(ctx context.Context, state, message string) {
	logger.Infof("state change to %s task:%s", state, s.ID)

	if err := s.dispatcher.dbHelper.UpdateTaskState(s.ID, state); err != nil {
		logger.Warnf("update task state failed. task_id=%s err=%+v", s.ID, err)
	}

	s.dispatcher.NotifyStateChanged(ctx, s.ID, state, message)
}

func (s *AssetScanTask) GetWorkingOn() string {
	return s.WorkingOn
}

func (s *AssetScanTask) SetWorkingOn(data string) {
	s.WorkingOn = data
}

func (s *AssetScanTask) SetRemainTime(data string) {
	s.RemainTime = data
}

func (s *AssetScanTask) SetMessage(data string) {
	s.Message = data
}

func (s *AssetScanTask) SetState(state string) {
	s.State = state
}

func (s *AssetScanTask) SetIsOverflow(b bool) {
	s.IsOverflow = b
}

func (s *AssetScanTask) GetJob(jobID string) Job {
	for _, job := range s.jobs {
		if job.GetID() == jobID {
			return job
		}
	}
	return nil
}

func (s *AssetScanTask) GetJobs() []Job {
	return s.jobs
}

func (s *AssetScanTask) GetID() string {
	return s.ID
}

func (s *AssetScanTask) GetProgress() float32 {
	return s.Progress
}

func (s *AssetScanTask) GetState() string {
	return s.State
}

type SplitPolicy interface {
	DoSplit(task Task) []Job // 执行拆分
}

func (s *AssetScanTask) Start(ctx context.Context) error {
	logger.Infof("asset scan task[%s] start.", s.ID)

	if s.State != constant.StateInitial && s.State != constant.StatePaused {
		logger.Warnf("invalid state=[%s] ID=[%s]", s.State, s.ID)
		return internal.TaskAddBadRequestError
	}

	// 如果是暂停状态，则直接恢复任务
	if s.State == constant.StatePaused {
		return s.Resume(ctx, nil)
	}

	// 设置任务状态为调度中
	s.State = constant.StateDispatching
	s.NotifyStateChanged(ctx, constant.StateDispatching, "")

	if s.splitPolicy != nil {
		// 执行拆分策略，将一个任务拆分为多个子任务
		s.jobs = s.splitPolicy.DoSplit(s)
	} else {
		// 如果有IP域名关系库，直接发送给checkURL存起来
		if len(s.data.Options.IpDomainRelations) > 0 {
			checkUrlService := s.dispatcher.processMap[constant.ServiceCheckUrl]
			resp, err := checkUrlService.(pb.CheckURLService).SaveIPDomainRelation(ctx, &pb.SaveIPDomainRelationRequest{TaskId: s.ID, IpDomainRelations: s.data.Options.IpDomainRelations})
			if err != nil {
				logger.Warnf("send ip domain relation to checkURL failed. task=%s caused by %s", s.ID, err)
			} else {
				logger.Infof("send ip domain relation to checkURL. task=%s success=%t", s.ID, resp.Success)
			}
		}

		// 端口IP拆分
		if len(s.data.Options.PortGroup) > 0 {
			for _, item := range s.data.Options.PortGroup {
				err := s.generateJobs(item.IpList, strings.Split(item.Ports, ","))
				if err != nil {
					logger.Error("port group split error: ", err)
					return internal.TaskAddError
				}
			}
		} else {
			// 不拆分的话，子任务就是自己了
			err := s.generateJobs(s.data.IpLists, s.data.Ports)
			if err != nil {
				logger.Error("split error: ", err)
				return internal.TaskAddError
			}
		}
	}

	var g errgroup.Group
	for key := range s.jobs {
		job := s.jobs[key]
		g.Go(func() error {
			return job.Start(ctx)
		})
	}
	err := g.Wait()
	if err != nil {
		logger.Errorf("asset scan task start failed. task:%s caused by %s", s.ID, err)
		return internal.TaskAddError
	}

	// 保存任务
	return nil
}

func (s *AssetScanTask) generateJobs(ipLists, ports []string) error {
	// 如果开启了PING扫描或者是存活探测，则拆分成两个子任务
	if s.data.PortScanner.TaskType == constant.TaskTypePing || s.data.PortScanner.GetOptions().GetPingScan() {
		// 生成一个ping扫描的任务
		job, err := s.generateJob(ipLists, ports, true)
		if err != nil {
			return err
		}
		s.jobs = append(s.jobs, job)
	}

	// 生成一个端口扫描任务
	job, err := s.generateJob(ipLists, ports, false)
	if err != nil {
		return err
	}
	s.jobs = append(s.jobs, job)
	return nil
}

func (s *AssetScanTask) generateJob(ipLists, ports []string, pingScan bool) (Job, error) {
	node, err := common.SelectServiceNode(s.dispatcher.srv, constant.ServicePortScanner)
	if err != nil {
		logger.Errorf("select port scan failed. task:%s caused by %s", s.ID, err)
		return nil, err
	}
	id := Generator.MustNext()
	data := &pb.TaskEvent{
		TaskId:    s.data.TaskId,
		JobId:     id,
		TaskType:  s.data.PortScanner.TaskType,
		Ports:     ports,
		HostInfos: ipLists,
		Options: &pb.TaskOption{
			Rate:       s.data.PortScanner.Options.Rate,
			Blacklist:  s.data.PortScanner.Options.Blacklist,
			GatewayMac: s.data.PortScanner.Options.GatewayMac,
			PingScan:   &pingScan,
			SendEth:    s.data.PortScanner.Options.SendEth,
			Retries:    s.data.PortScanner.Options.Retries,
			DeepGetOs:  s.data.PortScanner.Options.DeepGetOs,
			DeepGetMac: s.data.PortScanner.Options.DeepGetMac,
			IsIpv6:     s.data.PortScanner.Options.IsIpv6,
			DeviceName: s.data.PortScanner.Options.DeviceName,
			TreckScan:  s.data.PortScanner.Options.TreckScan,
		},
		Parameters: &pb.TransparentParameters{
			IsIpv6:              *s.data.PortScanner.Options.IsIpv6,
			GrabConcurrent:      s.data.Options.GrabConcurrent,
			ProtocolUpdateCycle: s.data.Options.ProtocolUpdateCycle,
			UnknownProtocolIndb: s.data.Options.UnknownProtocolIndb,
			MaxAssetNum:         s.data.Options.MaxAssetNum,
			CrawlerAllUrl:       s.data.Options.CrawlerAllUrl,
			CrawlerUrlBlackKey:  s.data.Options.CrawlerUrlBlackKey,
			CrawlerSpecificUrl:  s.data.Options.CrawlerSpecificUrl,
			FullProtocolDetect:  s.data.Options.FullProtocolDetect,
			ResolveHost:         s.data.Options.ResolveHost,
			NameServer:          s.data.Options.NameServer,
		},
	}
	job := NewAssetScanJob(id, s, s.dispatcher, data)
	job.SetNode(node)
	err = s.dispatcher.dbHelper.CreateJob(s.data.TaskId, job.ID, data, node.Id)
	return job, err
}

func (s *AssetScanTask) AllJobFinished(ctx context.Context) bool {
	stateFinished := true
	for key := range s.jobs {
		job := s.jobs[key]
		if job.GetState() != constant.StateFinished {
			stateFinished = false
			break
		}
	}
	return stateFinished
}

func (s *AssetScanTask) Pause(ctx context.Context) error {
	logger.Infof("asset scan task pause. ID=[%s]", s.ID)
	if s.State != constant.StateRunning {
		logger.Warnf("invalid state for pause. task:%s state:%s", s.ID, s.State)

		if s.State == constant.StateResuming {
			return internal.PauseTaskErrorCauseResuming
		}

		if s.State == constant.StatePausing {
			return internal.PauseTaskErrorCausePausing
		}

		if s.State == constant.StatePaused {
			return internal.PauseTaskErrorCausePaused
		}

		return internal.CantPauseTaskError
	}

	// 设置任务状态
	s.State = constant.StatePausing
	s.NotifyStateChanged(ctx, constant.StatePausing, "")

	var g errgroup.Group
	for key := range s.jobs {
		job := s.jobs[key]
		logger.Info("range task job for pause. job: ", job)
		g.Go(func() error {
			return job.Pause(ctx)
		})
	}
	err := g.Wait()
	if err != nil {
		logger.Errorf("asset scan task pause failed. task:%s caused by %s", s.ID, err)
		return internal.TaskPauseError
	}
	return nil
}

func (s *AssetScanTask) Resume(ctx context.Context, rate *int32) error {
	logger.Infof("asset scan task resume. ID=[%s]", s.ID)
	if s.State != constant.StatePaused {
		logger.Warnf("invalid state for resume. task:%s state:%s", s.ID, s.State)
		if s.State == constant.StatePausing {
			return internal.PauseResumeErrorCausePausing
		}
		return internal.NoNeedResumeTaskError
	}

	// 设置任务状态
	s.State = constant.StateResuming
	s.NotifyStateChanged(ctx, constant.StateResuming, "")

	var g errgroup.Group
	for key, _ := range s.jobs {
		job := s.jobs[key]
		g.Go(func() error {
			return job.Resume(ctx, rate)
		})
	}
	err := g.Wait()
	if err != nil {
		logger.Errorf("asset scan task resume failed. task:%s caused by %s", s.ID, err)
		return internal.TaskResumeError
	}
	return nil
}

func (s *AssetScanTask) Stop(ctx context.Context) error {
	logger.Infof("asset scan task stop. ID=[%s]", s.ID)

	if s.State == constant.StateInitial ||
		s.State == constant.StateFinished ||
		s.State == constant.StateStopped {
		logger.Warnf("invalid state for stop. task:%s state:%s", s.ID, s.State)
		// 防止foeye不正确处理错误，因为删除任务参数校验错误时，不做任务处理，所以对于foscan来说直接返回nil和error没有区别
		// 避免返回error foeye不正确处理error，删除不掉任务导致任务卡住
		return nil
	}

	s.State = constant.StateStopping
	s.NotifyStateChanged(ctx, constant.StateStopping, "")

	// 停止优先级最高，中间状态也可以调用停止
	var g errgroup.Group
	for key, _ := range s.jobs {
		job := s.jobs[key]
		g.Go(func() error {
			return job.Stop(ctx)
		})
	}
	err := g.Wait()
	if err != nil {
		logger.Errorf("asset scan task stop failed. task:%s caused by %s", s.ID, err)
		return internal.TaskStopError
	}
	return nil
}
