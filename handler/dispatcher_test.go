package handler

import (
	"context"
	"dispatcher/internal/foeye"
	"dispatcher/model"
	"dispatcher/pkg/common"
	"errors"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"github.com/agiledragon/gomonkey/v2"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"github.com/google/uuid"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/registry"
	"testing"
)

type DispatcherSuite struct {
	suite.Suite
	Dispatcher *Dispatcher
	Context    context.Context
}

func Test_DispatcherSuite(t *testing.T) {
	s := &DispatcherSuite{}
	suite.Run(t, s)
}

func (s *DispatcherSuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	s.Dispatcher = NewDispatcher(srv)
	s.Context = context.Background()
}

func (s *DispatcherSuite) Test_GetTask() {
	Convey("Test_GetTask", s.T(), func() {
		Convey("empty task", func() {
			tasks := s.Dispatcher.GetTasks()
			So(tasks, ShouldBeEmpty)
		})

		Convey("with task", func() {
			taskID, _ := uuid.NewUUID()
			s.Dispatcher.tasks[taskID.String()] = &AssetScanTask{}
			tasks := s.Dispatcher.GetTasks()
			So(tasks, ShouldNotBeEmpty)
		})
	})
}

func (s *DispatcherSuite) Test_Start() {
	Convey("Test_Start", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		Convey("normal", func() {
			// 模拟端口扫描正常返回
			defer gomonkey.ApplyMethodReturn(&AssetScanTask{}, "Start", nil).Reset()
			err := s.Dispatcher.Start(s.Context, &pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			}, &pb.DispatcherTaskResponse{})
			So(err, ShouldBeNil)

			// 正常情况下，会先缓存任务
			task := s.Dispatcher.GetTask(taskID.String())
			So(task, ShouldNotBeNil)

			// 正常情况下，状态预期为TaskStateDispatching
			So(task.GetState(), ShouldEqual, constant.StateInitial)
		})
		Convey("mock task start failed", func() {
			// 模拟端口扫描失败
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.dbHelper, "CreateTask", nil).Reset()
			defer gomonkey.ApplyMethodReturn(&AssetScanTask{}, "Start", errors.New("publish failed")).Reset()
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.dbHelper, "DeleteTask", nil).Reset()
			err := s.Dispatcher.Start(s.Context, &pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			}, &pb.DispatcherTaskResponse{})

			// 如果放入portscanner的队列失败，预期返回的不是nil
			So(err, ShouldNotBeNil)

			// 如果放入portscanner的队列失败，预期任务缓存会被删除
			task := s.Dispatcher.GetTask(taskID.String())
			So(task, ShouldBeNil)
		})

		Convey("duplicate starting task", func() {
			// 模拟端口扫描失败
			defer gomonkey.ApplyMethodReturn(&AssetScanTask{}, "Start", nil).Reset()
			err := s.Dispatcher.Start(s.Context, &pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			}, &pb.DispatcherTaskResponse{})
			So(err, ShouldBeNil)

			// 正常情况下，会先缓存任务
			task := s.Dispatcher.GetTask(taskID.String())
			So(task, ShouldNotBeNil)

			// 正常情况下，状态预期为TaskStateDispatching
			So(task.GetState(), ShouldEqual, constant.StateInitial)

			// 再发一遍，会走缓存逻辑
			err = s.Dispatcher.Start(s.Context, &pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			}, &pb.DispatcherTaskResponse{})
			So(err, ShouldNotBeNil)
		})

		Convey("test with limit", func() {
			// 模拟端口扫描正常返回
			limitation := &foeye.AssetsLimit{}
			defer gomonkey.ApplyMethodReturn(&AssetScanTask{}, "Start", nil).Reset()
			defer gomonkey.ApplyMethodReturn(limitation, "Initial", errors.New("initial asset failed")).Reset()
			s.Dispatcher.WithLimitation(limitation)
			err := s.Dispatcher.Start(s.Context, &pb.DispatcherTaskStartRequest{
				TaskId:  taskID.String(),
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			}, &pb.DispatcherTaskResponse{})
			So(err, ShouldBeNil)

			// 正常情况下，会先缓存任务
			task := s.Dispatcher.GetTask(taskID.String())
			So(task, ShouldNotBeNil)

			// 正常情况下，状态预期为TaskStateDispatching
			So(task.GetState(), ShouldEqual, constant.StateInitial)
		})

	})
}

func (s *DispatcherSuite) Test_Pause() {
	Convey("Test_Pause", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		Convey("no task", func() {
			// 模拟在没有缓存任务的情况下，直接返回成功
			err := s.Dispatcher.Pause(s.Context, &pb.DispatcherTaskPauseRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskPauseResponse{})
			So(err, ShouldBeError)
		})
		Convey("invalid state", func() {
			// 模拟有缓存任务的情况下，任务状态不对的情况
			s.Dispatcher.tasks[taskID.String()] = &AssetScanTask{
				ID:    taskID.String(),
				State: constant.StateFinished,
			}
			err := s.Dispatcher.Pause(s.Context, &pb.DispatcherTaskPauseRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskPauseResponse{})
			So(err, ShouldNotBeNil)
		})
		Convey("mock task pause success", func() {
			// 模拟有任务正在运行时，调用暂停接口成功s
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Pause(s.Context, &pb.DispatcherTaskPauseRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskPauseResponse{})
			So(err, ShouldBeNil)
			// 任务状态变为Pausing
			So(task.State, ShouldEqual, constant.StatePausing)
		})
		Convey("mock task pause failed", func() {
			// 模拟有任务正在运行时，调用停止接口失败
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Pause(s.Context, &pb.DispatcherTaskPauseRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskPauseResponse{})
			So(err, ShouldNotBeNil)
		})
		Convey("mock multiple jobs pause success", func() {
			// 模拟有任务正在运行时，且有多个子任务，调用暂停接口成功
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
			}
			jobID, _ := uuid.NewUUID()
			job1 := &AssetScanJob{
				ID:         jobID.String(),
				State:      constant.StateInitial,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			jobID, _ = uuid.NewUUID()
			job2 := &AssetScanJob{
				ID:         jobID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job1, job2}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Pause(s.Context, &pb.DispatcherTaskPauseRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskPauseResponse{})
			So(err, ShouldBeNil)
			// 任务状态变为Pausing
			So(task.State, ShouldEqual, constant.StatePausing)
		})
	})
}

func (s *DispatcherSuite) Test_Resume() {
	Convey("Test_Resume", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		Convey("no task", func() {
			// 模拟在没有缓存任务的情况下，直接返回成功
			err := s.Dispatcher.Resume(s.Context, &pb.DispatcherTaskResumeRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskResumeResponse{})
			So(err, ShouldBeError)
		})
		Convey("invalid state", func() {
			// 模拟有缓存任务的情况下，任务状态不对的情况
			s.Dispatcher.tasks[taskID.String()] = &AssetScanTask{
				ID:    taskID.String(),
				State: constant.StateFinished,
			}
			err := s.Dispatcher.Resume(s.Context, &pb.DispatcherTaskResumeRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskResumeResponse{})
			So(err, ShouldNotBeNil)
		})
		Convey("mock task resume success", func() {
			// 模拟有任务正在运行时，调用恢复接口成功
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Resume(s.Context, &pb.DispatcherTaskResumeRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskResumeResponse{})
			So(err, ShouldBeNil)
			// 任务状态变为Resuming
			So(task.State, ShouldEqual, constant.StateResuming)
		})
		Convey("mock task resume failed", func() {
			// 模拟有任务正在运行时，调用恢复接口失败
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Resume(s.Context, &pb.DispatcherTaskResumeRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskResumeResponse{})
			So(err, ShouldNotBeNil)
		})
		Convey("mock multiple jobs resume success", func() {
			// 模拟有任务正在运行时，且有多个子任务，调用恢复接口成功
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StatePaused,
				dispatcher: s.Dispatcher,
			}
			jobID, _ := uuid.NewUUID()
			job1 := &AssetScanJob{
				ID:         jobID.String(),
				State:      constant.StateInitial,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			jobID, _ = uuid.NewUUID()
			job2 := &AssetScanJob{
				ID:         jobID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job1, job2}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Resume(s.Context, &pb.DispatcherTaskResumeRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskResumeResponse{})
			So(err, ShouldBeNil)
			// 任务状态变为Resuming
			So(task.State, ShouldEqual, constant.StateResuming)
		})
	})
}

func (s *DispatcherSuite) Test_Stop() {
	Convey("Test_Stop", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		Convey("no task", func() {
			// 模拟在没有缓存任务的情况下，直接返回成功
			err := s.Dispatcher.Stop(s.Context, &pb.DispatcherTaskStopRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskStopResponse{})
			So(err, ShouldBeError)
		})
		Convey("with state", func() {
			// 模拟有缓存任务的情况下，其他状态的情况下，任何状态都为正常
			s.Dispatcher.tasks[taskID.String()] = &AssetScanTask{
				ID:    taskID.String(),
				State: constant.StateFinished,
			}
			err := s.Dispatcher.Stop(s.Context, &pb.DispatcherTaskStopRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskStopResponse{})
			So(err, ShouldBeNil)
		})
		Convey("mock task stop success", func() {
			// 模拟有任务正在运行时，调用停止接口成功
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Stop(s.Context, &pb.DispatcherTaskStopRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskStopResponse{})
			So(err, ShouldBeNil)
			// 任务状态变为Stopping
			So(task.State, ShouldEqual, constant.StateStopping)
		})
		Convey("mock task stop failed", func() {
			// 模拟有任务正在运行时，调用停止接口失败
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			task := &AssetScanTask{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
			}
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				parent:     task,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = []Job{job}
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.Stop(s.Context, &pb.DispatcherTaskStopRequest{
				TaskId: taskID.String(),
			}, &pb.DispatcherTaskStopResponse{})
			So(err, ShouldNotBeNil)
		})
	})
}

func (s *DispatcherSuite) Test_TaskNotifyHandler() {
	Convey("Test_TaskNotifyHandler", s.T(), func() {
		Convey("normal state without task", func() {
			taskID, _ := uuid.NewUUID()
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_State{
					State: &pb.StateNotify{},
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("normal state with task", func() {
			taskID, _ := uuid.NewUUID()
			task := NewAssetScanTask(taskID.String(), s.Dispatcher, &pb.DispatcherTaskStartRequest{
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			})
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_State{
					State: &pb.StateNotify{},
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("normal state with task & job", func() {
			taskID, _ := uuid.NewUUID()
			task := NewAssetScanTask(taskID.String(), s.Dispatcher, &pb.DispatcherTaskStartRequest{
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{},
				},
			})
			job := &AssetScanJob{
				ID:         taskID.String(),
				State:      constant.StateRunning,
				dispatcher: s.Dispatcher,
				node:       &registry.Node{Id: taskID.String()},
			}
			task.jobs = append(task.jobs, job)
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				Node:   &pb.ServiceNode{},
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_State{
					State: &pb.StateNotify{},
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("normal result with task", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", nil).Reset()
			taskID, _ := uuid.NewUUID()
			var isIpv6 = false
			task := NewAssetScanTask(taskID.String(), s.Dispatcher, &pb.DispatcherTaskStartRequest{
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{
						IsIpv6: &isIpv6,
					},
				},
			})
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_Result{
					Result: &pb.ResultNotify{
						BaseProtocol: "icmp",
					},
				},
			})
			So(err, ShouldBeNil)
		})

		Convey("normal result publish failed", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Publish", errors.New("publish failed")).Reset()
			taskID, _ := uuid.NewUUID()
			var isIpv6 = false
			task := NewAssetScanTask(taskID.String(), s.Dispatcher, &pb.DispatcherTaskStartRequest{
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{
						IsIpv6: &isIpv6,
					},
				},
			})
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_Result{
					Result: &pb.ResultNotify{
						BaseProtocol: "tcp",
					},
				},
			})
			So(err, ShouldNotBeNil)
		})

		Convey("normal result exceed limit", func() {
			limitation := &foeye.AssetsLimit{}
			s.Dispatcher.WithLimitation(limitation)
			defer gomonkey.ApplyMethodReturn(&AssetScanTask{}, "Stop", nil).Reset()
			defer gomonkey.ApplyMethodReturn(limitation, "Exceed", true).Reset()
			taskID, _ := uuid.NewUUID()
			var isIpv6 = false
			task := NewAssetScanTask(taskID.String(), s.Dispatcher, &pb.DispatcherTaskStartRequest{
				Options: &pb.DispatcherTaskStartOptions{},
				PortScanner: &pb.DispatcherPortScannerConfig{
					Options: &pb.DispatcherPortScannerOption{
						IsIpv6: &isIpv6,
					},
				},
			})
			task.State = constant.StateRunning
			s.Dispatcher.tasks[taskID.String()] = task
			err := s.Dispatcher.TaskNotifyHandler(context.Background(), &pb.TaskNotify{
				TaskId: taskID.String(),
				JobId:  taskID.String(),
				Data: &pb.TaskNotify_Result{
					Result: &pb.ResultNotify{
						BaseProtocol: "tcp",
					},
				},
			})
			So(err, ShouldBeNil)
		})
	})
}

func (s *DispatcherSuite) Test_NotifyFinished() {
	Convey("Test_NotifyFinished", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		s.Dispatcher.tasks[taskID.String()] = &AssetScanTask{}
		Convey("get service node failed", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, nil, errors.New("get service node failed")).Reset()
			s.Dispatcher.NotifyFinished(context.Background(), taskID.String())
			So(s.Dispatcher.tasks, ShouldBeEmpty)
		})

		Convey("get service node", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{{Id: "node-id"}}, nil).Reset()
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Call", nil).Reset()
			s.Dispatcher.NotifyFinished(context.Background(), taskID.String())
			So(s.Dispatcher.tasks, ShouldBeEmpty)
		})

		Convey("service notify failed", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{{Id: "node-id"}}, nil).Reset()
			defer gomonkey.ApplyFuncReturn(ProcessService.FinishedNotify, nil, errors.New("notify failed")).Reset()
			s.Dispatcher.NotifyFinished(context.Background(), taskID.String())
			So(s.Dispatcher.tasks, ShouldBeEmpty)
		})
	})
}

func (s *DispatcherSuite) Test_IsProcessFinished() {
	Convey("Test_IsProcessFinished", s.T(), func() {
		taskID, _ := uuid.NewUUID()
		Convey("get service node failed", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, nil, errors.New("get service node failed")).Reset()
			result := s.Dispatcher.IsProcessFinished(context.Background(), taskID.String(), constant.ServiceRawGrab)
			So(result, ShouldEqual, false)
		})

		Convey("get service node1", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{{Id: "node-id"}}, nil).Reset()
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.srv.Client(), "Call", errors.New("call failed")).Reset()
			result := s.Dispatcher.IsProcessFinished(context.Background(), taskID.String(), constant.ServiceRawGrab)
			So(result, ShouldEqual, false)
		})

		Convey("get service node2", func() {
			service := s.Dispatcher.processMap[constant.ServiceRawGrab]
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{{Id: "node-id"}}, nil).Reset()
			defer gomonkey.ApplyMethodReturn(service, "StateQuery", &pb.StateQueryResponse{Remain: 10}, nil).Reset()
			result := s.Dispatcher.IsProcessFinished(context.Background(), taskID.String(), constant.ServiceRawGrab)
			So(result, ShouldEqual, false)
		})

		Convey("get service node3", func() {
			service := s.Dispatcher.processMap[constant.ServiceRawGrab]
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{{Id: "node-id"}}, nil).Reset()
			defer gomonkey.ApplyMethodReturn(service, "StateQuery", &pb.StateQueryResponse{Remain: 0}, nil).Reset()
			result := s.Dispatcher.IsProcessFinished(context.Background(), taskID.String(), constant.ServiceRawGrab)
			So(result, ShouldEqual, true)
		})

	})
}

func (s *DispatcherSuite) Test_AppendStateChangedListener() {
	Convey("Test_AppendStateChangedListener", s.T(), func() {
		s.Dispatcher.AppendStateChangedListener(foeye.NewTaskStateCallback(nil))
		So(len(s.Dispatcher.listeners), ShouldEqual, 1)
	})
}

func (s *DispatcherSuite) Test_NotifyStateChanged() {
	Convey("Test_NotifyStateChanged", s.T(), func() {
		Convey("normal test", func() {
			callback := foeye.NewTaskStateCallback(&foeye.Config{ThirdSystem: foeye.ThirdSystem{
				Scheme:             "http",
				Host:               "127.0.0.1:65534",
				PathAssets:         "",
				PathNotifyProgress: "",
				PathNotifyStatus:   "",
			}})
			s.Dispatcher.AppendStateChangedListener(callback)
			taskID, _ := uuid.NewUUID()
			s.Dispatcher.NotifyStateChanged(context.Background(), taskID.String(), constant.StateRunning, "")
		})
	})
}

func (s *DispatcherSuite) Test_NotifyProgress() {
	Convey("Test_NotifyProgress", s.T(), func() {
		Convey("normal test", func() {
			callback := foeye.NewTaskStateCallback(&foeye.Config{ThirdSystem: foeye.ThirdSystem{
				Scheme:             "http",
				Host:               "127.0.0.1:65534",
				PathAssets:         "",
				PathNotifyProgress: "",
				PathNotifyStatus:   "",
			}})
			s.Dispatcher.AppendStateChangedListener(callback)
			taskID, _ := uuid.NewUUID()
			s.Dispatcher.NotifyProgress(context.Background(), taskID.String(), 50.0, "127.0.0.1", "0:0:0")
		})
	})
}

func (s *DispatcherSuite) TestDispatcher_LoadTasks() {
	Convey("TestDispatcher_LoadTasks", s.T(), func() {
		Convey("pass", func() {
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.dbHelper, "GetUnfinishedTasks", []model.Task{
				{
					ID:   "id",
					Data: "{\"task_id\":\"1\"}",
				},
			}, nil).Reset()
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{}, nil).Reset()
			defer gomonkey.ApplyFuncReturn(NewAssetScanTask, &AssetScanTask{}).Reset()
			defer gomonkey.ApplyMethodReturn(s.Dispatcher.dbHelper, "GetJobs", []model.Job{
				{
					ID:        "",
					WorkingOn: "",
					Data:      "{\"task_id\":\"1\"}",
				},
			}, nil).Reset()
			s.Dispatcher.LoadTasks()
		})
	})
}

func (s *DispatcherSuite) TestDispatcher_QueryProcessResult() {
	Convey("TestDispatcher_QueryProcessResult", s.T(), func() {
		Convey("GetServiceNodesByName error", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, errors.New("")).Reset()

			ok, ratio := s.Dispatcher.QueryProcessResult(context.Background(), "task_id", constant.ServiceRawGrab)
			So(ok, ShouldResemble, false)
			So(ratio, ShouldResemble, float32(0.0))
		})

		Convey("StateQuery error", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()

			service := pb.NewRawGrabService(constant.ServiceRawGrab, s.Dispatcher.srv.Client())
			defer gomonkey.ApplyMethodReturn(service, "StateQuery", &pb.StateQueryResponse{
				Remain: 1,
				Total:  1,
			}, errors.New("")).Reset()

			ok, ratio := s.Dispatcher.QueryProcessResult(context.Background(), "task_id", constant.ServiceRawGrab)
			So(ok, ShouldResemble, false)
			So(ratio, ShouldResemble, float32(0.0))
		})

		Convey("Remain", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()

			service := pb.NewRawGrabService(constant.ServiceRawGrab, s.Dispatcher.srv.Client())
			defer gomonkey.ApplyMethodReturn(service, "StateQuery", &pb.StateQueryResponse{
				Remain: 1,
				Total:  1,
			}, nil).Reset()

			ok, ratio := s.Dispatcher.QueryProcessResult(context.Background(), "task_id", constant.ServiceRawGrab)
			So(ok, ShouldResemble, false)
			So(ratio, ShouldResemble, float32(0.0))
		})

		Convey("pass", func() {
			defer gomonkey.ApplyFuncReturn(common.GetServiceNodesByName, []*registry.Node{
				{
					Id: "",
				},
			}, nil).Reset()

			service := pb.NewRawGrabService(constant.ServiceRawGrab, s.Dispatcher.srv.Client())
			defer gomonkey.ApplyMethodReturn(service, "StateQuery", &pb.StateQueryResponse{
				Remain: 0,
			}, nil).Reset()

			ok, ratio := s.Dispatcher.QueryProcessResult(context.Background(), "task_id", constant.ServiceRawGrab)
			So(ok, ShouldResemble, true)
			So(ratio, ShouldResemble, float32(1.0))
		})
	})
}
