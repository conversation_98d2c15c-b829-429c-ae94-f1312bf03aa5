package handler

import (
	"context"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"testing"
)

type AssetScanPortSplitPolicySuite struct {
	suite.Suite
	Policy     *AssetScanPortSplitPolicy
	Dispatcher *Dispatcher
	Context    context.Context
}

func Test_AssetScanPortSplitPolicySuite(t *testing.T) {
	s := &AssetScanPortSplitPolicySuite{}
	srv := micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	s.Dispatcher = NewDispatcher(srv)
	suite.Run(t, s)
}

func (s *AssetScanPortSplitPolicySuite) BeforeTest(suiteName, testName string) {
	logger.Info("BeforeTest")
	s.Context = context.Background()
}

func (s *AssetScanPortSplitPolicySuite) Test_GetSplits() {
	Convey("Test_GetSplits", s.T(), func() {
		Convey("normal1", func() {
			s.Policy = NewAssetScanPortSplitPolicy(2)
			result := s.Policy.GetSplits([]string{"80", "443", "22"})
			So(result, ShouldResemble, [][]string{{"80"}, {"443", "22"}})
		})
		Convey("normal2", func() {
			s.Policy = NewAssetScanPortSplitPolicy(2)
			result := s.Policy.GetSplits([]string{"80", "443", "22", "3306"})
			So(result, ShouldResemble, [][]string{{"80", "443"}, {"22", "3306"}})
		})
		Convey("normal3", func() {
			s.Policy = NewAssetScanPortSplitPolicy(3)
			result := s.Policy.GetSplits([]string{"80", "443", "22", "3306"})
			So(result, ShouldResemble, [][]string{{"80"}, {"443"}, {"22", "3306"}})
		})
	})
}
