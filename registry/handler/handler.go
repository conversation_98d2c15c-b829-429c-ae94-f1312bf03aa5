package handler

import (
	"context"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/foscan/pkg/registry/util"
	"go-micro.dev/v4/errors"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/registry"
	"time"
)

type Registry struct {
	// service id
	ID string
}

// GetService from the registry with the name requested
func (r *Registry) GetService(ctx context.Context, req *pb.GetRequest, rsp *pb.GetResponse) error {
	// get the services in the namespace
	services, err := registry.DefaultRegistry.GetService(req.Service)
	if err == registry.ErrNotFound || len(services) == 0 {
		return errors.NotFound("registry.Registry.GetService", registry.ErrNotFound.Error())
	} else if err != nil {
		return errors.InternalServerError("registry.Registry.GetService", err.Error())
	}

	logger.Infof("get service [%s]", req.Service)

	// serialize the response
	rsp.Services = make([]*pb.Service, len(services))
	for i, srv := range services {
		rsp.Services[i] = util.ToProto(srv)
	}

	return nil
}

// Register a service
func (r *Registry) Register(ctx context.Context, req *pb.Service, rsp *pb.EmptyResponse) error {
	var opts []registry.RegisterOption

	// parse the options
	if req.Options != nil && req.Options.Ttl > 0 {
		ttl := time.Duration(req.Options.Ttl) * time.Second
		opts = append(opts, registry.RegisterTTL(ttl))
	}

	// register the service
	if err := registry.DefaultRegistry.Register(util.ToService(req), opts...); err != nil {
		return errors.InternalServerError("registry.Registry.Register", err.Error())
	}

	logger.Infof("register service [%s:%s]", req.Name, req.Version)

	return nil
}

// Deregister a service
func (r *Registry) Deregister(ctx context.Context, req *pb.Service, rsp *pb.EmptyResponse) error {
	// deregister the service
	if err := registry.DefaultRegistry.Deregister(util.ToService(req)); err != nil {
		return errors.InternalServerError("registry.Registry.Deregister", err.Error())
	}

	logger.Infof("deregister service [%s:%s]", req.Name, req.Version)

	return nil
}

// ListServices returns all the services
func (r *Registry) ListServices(ctx context.Context, req *pb.ListRequest, rsp *pb.ListResponse) error {
	// list the services from the registry
	services, err := registry.DefaultRegistry.ListServices()
	if err != nil {
		return errors.InternalServerError("registry.Registry.ListServices", err.Error())
	}

	// serialize the response
	rsp.Services = make([]*pb.Service, len(services))
	for i, srv := range services {
		rsp.Services[i] = util.ToProto(srv)
	}

	return nil
}

// Watch a service for changes
func (r *Registry) Watch(ctx context.Context, req *pb.WatchRequest, rsp pb.Registry_WatchStream) error {
	// setup the watcher
	watcher, err := registry.DefaultRegistry.Watch(registry.WatchService(req.Service))
	if err != nil {
		return errors.InternalServerError("registry.Registry.Watch", err.Error())
	}

	for {
		next, err := watcher.Next()
		if err != nil {
			return errors.InternalServerError("registry.Registry.Watch", err.Error())
		}

		err = rsp.Send(&pb.Result{
			Action:  next.Action,
			Service: util.ToProto(next.Service),
		})
		if err != nil {
			return errors.InternalServerError("registry.Registry.Watch", err.Error())
		}
	}
}
