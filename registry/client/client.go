package client

import (
	"context"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/foscan/pkg/registry/util"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/errors"
	"go-micro.dev/v4/registry"
	"time"
)

var name = "registry"

type srv struct {
	opts registry.Options
	// client to call registry
	client pb.RegistryService
}

func (s *srv) callOpts() []client.CallOption {
	var opts []client.CallOption

	// set registry address
	if len(s.opts.Addrs) > 0 {
		opts = append(opts, client.WithAddress(s.opts.Addrs...))
	}

	// set timeout
	if s.opts.Timeout > time.Duration(0) {
		opts = append(opts, client.WithRequestTimeout(s.opts.Timeout))
	}

	return opts
}

func (s *srv) Init(opts ...registry.Option) error {
	for _, o := range opts {
		o(&s.opts)
	}
	return nil
}

func (s *srv) Options() registry.Options {
	return s.opts
}

func (s *srv) Register(srv *registry.Service, opts ...registry.RegisterOption) error {
	var options registry.RegisterOptions
	for _, o := range opts {
		o(&options)
	}

	// encode srv into protobuf and pack TTL and domain into it
	pbSrv := util.ToProto(srv)
	pbSrv.Options.Ttl = int64(options.TTL.Seconds())

	// register the service
	_, err := s.client.Register(context.Background(), pbSrv, s.callOpts()...)
	return err
}

func (s *srv) Deregister(srv *registry.Service, opts ...registry.DeregisterOption) error {
	var options registry.DeregisterOptions
	for _, o := range opts {
		o(&options)
	}

	// encode srv into protobuf and pack domain into it
	pbSrv := util.ToProto(srv)

	// deregister the service
	_, err := s.client.Deregister(context.Background(), pbSrv, s.callOpts()...)
	return err
}

func (s *srv) GetService(name string, opts ...registry.GetOption) ([]*registry.Service, error) {
	var options registry.GetOptions
	for _, o := range opts {
		o(&options)
	}

	rsp, err := s.client.GetService(context.Background(), &pb.GetRequest{
		Service: name, Options: &pb.Options{},
	}, s.callOpts()...)

	if verr := errors.FromError(err); verr != nil && verr.Code == 404 {
		return nil, registry.ErrNotFound
	} else if err != nil {
		return nil, err
	}

	services := make([]*registry.Service, 0, len(rsp.Services))
	for _, service := range rsp.Services {
		services = append(services, util.ToService(service))
	}
	return services, nil
}

func (s *srv) ListServices(opts ...registry.ListOption) ([]*registry.Service, error) {
	var options registry.ListOptions
	for _, o := range opts {
		o(&options)
	}

	req := &pb.ListRequest{Options: &pb.Options{}}
	rsp, err := s.client.ListServices(context.Background(), req, s.callOpts()...)
	if err != nil {
		return nil, err
	}

	services := make([]*registry.Service, 0, len(rsp.Services))
	for _, service := range rsp.Services {
		services = append(services, util.ToService(service))
	}

	return services, nil
}

func (s *srv) Watch(opts ...registry.WatchOption) (registry.Watcher, error) {
	var options registry.WatchOptions
	for _, o := range opts {
		o(&options)
	}

	stream, err := s.client.Watch(context.Background(), &pb.WatchRequest{
		Service: options.Service, Options: &pb.Options{},
	}, s.callOpts()...)

	if err != nil {
		return nil, err
	}

	return newWatcher(stream), nil
}

func (s *srv) String() string {
	return "local"
}

// NewRegistry returns a new registry service client
func NewRegistry(opts ...registry.Option) registry.Registry {
	var options registry.Options
	for _, o := range opts {
		o(&options)
	}

	return &srv{
		opts:   options,
		client: pb.NewRegistryService(name, grpcc.NewClient()),
	}
}
