package probe

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"net"
	"strconv"
	"testing"
	"time"
)

type Packet []byte

type Mocker struct {
	packets []interface{}
	ip      string
	port    int
	net     string
	info    Info
	err     error
	//是否需要moke http相关库
	isHttp bool
	//预期的协议
	protocol string
}

func NewTcpMocker(port int) *Mocker {
	return &Mocker{
		ip:   "localhost",
		net:  "tcp",
		port: port,
	}
}

func NewUdpMocker(port int) *Mocker {
	return &Mocker{
		ip:   "localhost",
		net:  "udp",
		port: port,
	}
}

func (m *Mocker) Run(handler ProbeFunc) *Mocker {
	if m.isHttp {
		GlobalPacketPatch.initHttpPatch()
	} else {
		GlobalPacketPatch.initPatch()
	}

	var datas []string
	for _, v := range m.packets {
		datas = append(datas, string(v.(Packet)))
	}

	GlobalPacketPatch.WithRead(&PacketResult{Data: datas})

	if handler != nil {
		m.info, m.err = handler(m.ip+":"+strconv.Itoa(m.port), m.net, time.Millisecond*1000)
		if m.err != nil {
			fmt.Println(m.err)
		}
	}
	GlobalPacketPatch.unPatch()
	return m
}

func (m *Mocker) Test(t *testing.T) *Mocker {
	if m.err != nil {
		fmt.Println(m.err)
	}
	assert.Nil(t, m.err)
	assert.Equal(t, m.protocol, m.info.Protocol)
	assert.True(t, m.info.Success)
	t.Log(string(m.info.Banner))
	return m
}

func (m *Mocker) SetHttp() *Mocker {
	m.isHttp = true
	return m
}

func (m *Mocker) SetProtocol(protocol string) *Mocker {
	m.protocol = protocol
	return m
}

func (m *Mocker) SetResponse(response []byte) *Mocker {
	var p = Packet(response)
	if m.packets == nil {
		m.packets = []interface{}{p}
	} else {
		m.packets = append(m.packets, p)
	}
	return m
}

func (m *Mocker) SetResponseHandler(response func([]byte) []byte) *Mocker {
	if m.packets == nil {
		m.packets = []interface{}{response}
	} else {
		m.packets = append(m.packets, response)
	}
	return m
}

func (m *Mocker) SetResponseArray(packets []Packet) *Mocker {
	if m.packets == nil {
		m.packets = []interface{}{packets}
	} else {
		m.packets = append(m.packets, packets)
	}
	return m
}

func handleTcpPacket(mocker *Mocker, conn *net.TCPConn) {
	for _, packet := range mocker.packets {
		p := make([]byte, 1024)
		conn.Read(p)
		switch v := packet.(type) {
		case Packet:
			conn.Write(v)
		case func([]byte) []byte:
			conn.Write(v(p))
		case []Packet:
			for _, p := range v {
				conn.Write(p)
			}
		default:
			fmt.Printf("tytpe,%T", packet)
		}

	}
}

func handleUdpPacket(mocker *Mocker, conn *net.UDPConn) {
	for _, packet := range mocker.packets {
		buf := make([]byte, 1024)
		//阻塞获取数据
		n, rAddr, err := conn.ReadFromUDP(buf)
		if err != nil {
			fmt.Println(err.Error())
		}
		if n > 0 {
			switch v := packet.(type) {
			case Packet:
				conn.WriteToUDP(v, rAddr)
			case func([]byte) []byte:
				conn.WriteToUDP(v(buf), rAddr)
			case []Packet:
				for _, p := range v {
					conn.WriteToUDP(p, rAddr)
				}
			default:
				fmt.Printf("tytpe,%T", packet)
			}
		}

	}
}
