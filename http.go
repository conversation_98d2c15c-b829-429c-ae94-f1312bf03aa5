package probe

import (
	"bufio"
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"git.gobies.org/longzhuan/go_common/honeypot"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"sync"
)

var regHttp = regexp.MustCompile(`^HTTP/1\.(0|1)`)
var RegUnknownHttp = regexp.MustCompile(`(?i)(^(UNKNOWN|UnknownMethod) 400 Bad Request\r\n|^UnknownMethod 403 Forbidden\r\n|^UnknownMethod 404 Not Found\r\n|^UNKNOWN 501 Not Implemented\r\n|^<HEAD><TITLE>501 Not Implemented</TITLE></HEAD>|^<HTML><BODY><CENTER>Authentication failed</CENTER></BODY></HTML>\r\n|^(UNKNOWN|INV) 501 Not Implemented\r\n|^\r\n<HTML>\n<HEAD><TITLE>Error Observed</TITLE></HEAD>|^<HEAD><TITLE>Invalid HTTP Request</TITLE></HEAD>|^\xff\xf0 400 Bad Request\r\n\r\n<HEAD><TITLE>|^<HTML><HEAD><TITLE>|^\(null\) (302|400|403)|^ 501 Not Implemented\r\n(?:[^\r\n]+\r\n)*?Server: |^ 400 Invalid request\r\n|^\r\n\r\n\0HTTP/1\.0 500 Internal Server Error\r\n|^<HTML><HEAD><TITLE>501 Not Implemented</TITLE></HEAD>|^ERROR: Malformed startup string$|^<html>\r<head><title>Docupoint Discovery</title>|^401 Access denied\r\nWWW-Authenticate: |^<html>\n<head>\n<\S*>|^<html>\n<title>(D[EG]S-\w+) *(?:Login)?</title>\n|^Language received from client: .*\nSetlocale: .*\n|^<HTML>\r\n<HEAD>\r\n<TITLE>|^<html>\n<title>DES-(\w+) +(?:Login)?</title>\n|^<html>\r\n<head>\r\n<meta http-equiv=|^<html>\n\n<head>\n<title>|^HTT/1\.0 401 Not Authorized\r\n|^<html><head>\n<title>|^<html>\r\n<head><title>|^<HTML><HEAD></HEAD>\r\n|^<html>\n   <head>\n      <meta|^\0\x18HTTP/1\.0 |^.*<address>Apache/([\d.]+) |^<Html><Body><H1> |^<pre>\r\nIP Address: [\d.]+\r\n|^Command Not Reconized\r\n$|^<head><title>400 Bad Request</title>|^<html>\s*<head>\s*<title>400 Bad Request</title>|^ 400 badrequest\r\n|^<HEAD><TITLE>Not Found</TITLE>|^INV 501 Not Implemented\r\n|^/ 404 Not Found\r\n|Apache.* Server at.*Port|Bad Request.*Your browser sent a request that this server could not understand\.)`)
var regNoHeaderHttp = regexp.MustCompile(`^(?i)<(head|!doctype|html)`)
var regServer = regexp.MustCompile("\r\nServer: .*\r\n")
var regHttpAuthenticateNTLM = regexp.MustCompile(`(?i)WWW-Authenticate: NTLM (\S+)`)

const userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

type httpFilterContext struct {
	context.Context
}
type httpFilterContextKey int

var (
	httpFilterContextKeyAddr     = httpFilterContextKey(1)
	httpFilterContextKeyInfo     = httpFilterContextKey(2)
	httpFilterContextKeyBody     = httpFilterContextKey(3)
	httpFilterContextKeyHeader   = httpFilterContextKey(4)
	httpFilterContextKeyRespByte = httpFilterContextKey(5)
)

func (ctx httpFilterContext) GetAddr() *string { return ctx.Value(httpFilterContextKeyAddr).(*string) }
func (ctx httpFilterContext) GetInfo() *Info   { return ctx.Value(httpFilterContextKeyInfo).(*Info) }
func (ctx httpFilterContext) GetBody() []byte  { return ctx.Value(httpFilterContextKeyBody).([]byte) }
func (ctx httpFilterContext) GetHeader() *string {
	return ctx.Value(httpFilterContextKeyHeader).(*string)
}
func (ctx httpFilterContext) GetRespByte() []byte {
	return ctx.Value(httpFilterContextKeyRespByte).([]byte)
}

func newHttpFilterContext(
	addr *string,
	info *Info,
	body []byte,
	Header *string,
	respByte []byte,
) *httpFilterContext {
	ctx := context.WithValue(context.Background(), httpFilterContextKeyAddr, addr)
	ctx = context.WithValue(ctx, httpFilterContextKeyInfo, info)
	ctx = context.WithValue(ctx, httpFilterContextKeyBody, body)
	ctx = context.WithValue(ctx, httpFilterContextKeyHeader, Header)
	ctx = context.WithValue(ctx, httpFilterContextKeyRespByte, respByte)
	return &httpFilterContext{ctx}
}

type includeHttpFilter struct{}

func (_ *includeHttpFilter) Http() bool { return true }

type includeHttpsFilter struct{}

func (_ *includeHttpsFilter) Https() bool { return true }

type HttpChainHandler interface {
	Check(ctx *httpFilterContext, opts ...interface{}) bool
	Serve(ctx *httpFilterContext, opts ...interface{}) (Info, error)
	Https() bool
	Http() bool
}

var httpFilters = make([]HttpChainHandler, 0, 10)
var httpFiltersLock sync.RWMutex

// AddHttpFilterChain all filter will process after http main processing end;
func AddHttpFilterChain(filter HttpChainHandler) {
	httpFiltersLock.Lock()
	httpFiltersLock.Unlock()
	httpFilters = append(httpFilters, filter)
}

func getHTTP(p *probe, addr string, uri string, myheader string, tls ...bool) (respBytes []byte, header []byte, body []byte, connectionState *tls.ConnectionState, err error) {
	// 请求
	reqStr := fmt.Sprintf("GET %s HTTP/1.1\r\nHost: %s\r\nAccept: */*\r\n%s\r\n", uri, addr, myheader)

	// 响应
	var r *probeReader
	if tls != nil && tls[0] {
		r = p.GetTLSReader()
		connectionState = p.GetConnectionState()
		p.MustWriteTLS([]byte(reqStr))
	} else {
		r = p.GetTCPReader()
		p.MustWriteTCP([]byte(reqStr))
	}
	var resp *http.Response
	resp, err = http.ReadResponse(bufio.NewReader(r), nil)
	if err != nil {
		return r.respBytes, nil, nil, connectionState, err
	}
	defer resp.Body.Close()
	body, err = io.ReadAll(resp.Body)
	//存在err不为空，但body有值的情况
	if err != nil {
		if len(body) <= 0 {
			return r.respBytes, nil, nil, connectionState, err
		} else {
			log.Printf("[ERROR] %s http io readall error:%v\n", addr, err)
			err = nil
		}
	}

	respBytes = r.respBytes

	//respBytes = p.MustReadTCP(-1)

	// 没有 header 的情况
	if regNoHeaderHttp.Match(respBytes) {
		header = []byte("")
		body = respBytes[:]
		err = check400Code(body)
		return
	}
	// 判断http
	switch {
	case regHttp.Match(respBytes):
	case RegUnknownHttp.Match(respBytes):
	default:
		err = errors.New("not http")
		return
	}

	i := bytes.Index(respBytes, []byte("\r\n\r\n"))
	y := bytes.Index(respBytes, []byte("\n\n"))
	x := bytes.LastIndex(respBytes, []byte("\r\n"))

	if i <= 0 && x <= 0 && y <= 0 {
		err = errors.New("not http")
		return
	}
	if i > 0 {
		header = respBytes[:i]
		body = respBytes[i:]
	} else if y > 0 {
		header = respBytes[:y]
		body = respBytes[y:]
	} else if x > 0 {
		header = respBytes[:x]
		body = respBytes[x:]
	}
	err = check400Code(body)

	return
}

func HTTP(addr string, opts ...interface{}) (info Info, err error) {
	info.Protocol = "http"
	p := newProbe(addr, opts...)
	defer keepSafe(p, &err)

	respBytes, header, body, _, err := getHTTP(p, addr, "/", "")
	info.Banner = respBytes
	if err != nil {
		return
	}

	// 有header的时候才重新赋值
	if len(header) > 0 {
		info.Banner = header
	}
	hs := string(header)
	bs := string(body)
	ctx := newHttpFilterContext(&addr, &info, body, &hs, respBytes)
	if strings.Contains(hs, "418 I'm a teapot") {
		info.Protocol = "htcpcp"
	} else if isHttpsProtocol(hs, bs) {
		return HTTPS(addr, opts)
		//} else if isBeacon(hs) {
		//	return Beacon(addr, hs, info.Protocol, nil)
		//} else if isDaraph(bs) {
		//	return Dgraph(addr, hs, opts...)
		//} else if strings.Contains(addr, "8688") && strings.Contains(hs, "400 Bad Request") && strings.Contains(hs, "Server: Apache") && strings.Contains(hs, "Connection: close") {
		//	return OpenremoteCtrl(addr, info, opts...)
		//} else if strings.Contains(hs, "X-ClickHouse-Summary") {
		//	return ClickHouseHttp(addr, info, opts...)
		//} else if strings.Contains(hs, "400 Bad Request") && strings.Contains(string(respBytes), "is for clickhouse-client program") {
		//	info.Banner = respBytes
		//	return ClickHouse(addr, info, opts...)
		//	} else if isKubernetes(bs, hs) {
		//		return Kubernetes(addr, info, opts...)
		//	} else if isPrometheus(bs, hs) {
		//		return Prometheus(addr, info, opts...)
		//	} else if isWinRm(addr, bs, hs) {
		//		return WinRm(addr, info, opts...)
		//	}
	} else {
		httpFiltersLock.RLock()
		defer httpFiltersLock.RUnlock()
		for _, filter := range httpFilters {
			if filter.Http() && filter.Check(ctx, opts...) {
				return filter.Serve(ctx, opts...)
			}
		}
	}

	info.IsHoneypot, info.HoneypotName = honeypot.IsHttpHoneypot("", hs, bs)
	info.Success = true

	return
}

func isHttpsProtocol(hs, bs string) bool {
	if (strings.Contains(hs, "400 Bad Request") ||
		strings.Contains(hs, "200 Document follows") ||
		strings.Contains(hs, "HTTP/1.1 400 ")) &&
		(strings.Contains(bs, "The plain HTTP request was sent to HTTPS port") ||
			strings.Contains(bs, "You're speaking plain HTTP to an SSL-enabled server port") ||
			strings.Contains(bs, "Client sent an HTTP request to an HTTPS server") ||
			strings.Contains(bs, "This server uses SSL for security. Please use HTTPS to connect.") ||
			strings.Contains(bs, "This combination of host and port requires TLS.") ||
			strings.Contains(bs, "This web server is running in SSL mode.")) {
		return true
	}

	return false
}

func check400Code(body []byte) (err error) {
	var regHTTPS = regexp.MustCompile(`(?i)(400 Bad Request)[\s\S]*(ssl|https)`)
	if regHTTPS.Match(body) {
		err = errors.New("not http")
	}
	return
}

func init() {
	Protocols.AddProtocol(BaseProtocol{
		Name:       "http",
		RefererURL: "https://www.w3.org/Protocols/rfc2616/rfc2616.html",
		DefaultPorts: []int{30, 31, 32, 36, 38, 51, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 98,
			121, 138, 211, 280, 449, 540, 620, 631, 777, 800, 801, 808, 880, 888, 898, 900, 901, 990, 994, 999,
			1000, 1010, 1024, 1025, 1042, 1177, 1212, 1214, 1234, 1290, 1311, 1314, 1400, 1443, 1471, 1503, 1588, 1610, 1741,
			1830, 1880, 1901, 1935, 1947, 1991,
			2010, 2030, 2051, 2052, 2055, 2064, 2077, 2080, 2082, 2086, 2095, 2160, 2252, 2306, 2375, 2376, 2396, 2406, 2443, 2480, 2715, 2869,
			3000, 3001, 3002, 3005, 3052, 3075, 3097, 3128, 3280, 3311, 3312, 3337, 3352, 3372, 3443, 3520, 3522, 3523, 3524, 3525,
			3528, 3531, 3541, 3542, 3689, 3749, 3780, 3790,
			4000, 4022, 4040, 4155, 4200, 4300, 4369, 4440, 4567, 4660, 4664, 4711, 4782, 4842, 4848,
			5000, 5001, 5002, 5003, 5004, 5005, 5007, 5008, 5009, 5010, 5051, 5061, 5222, 5280, 5357, 5427, 5550, 5555, 5560, 5598,
			5678, 5800, 5801, 5802, 5820, 5900, 5901, 5938, 5984, 5985, 5986,
			6000, 6001, 6002, 6003, 6004, 6005, 6006, 6007, 6008, 6009, 6010, 6060, 6080, 6103, 6346, 6363, 6544, 6560, 6565, 6581,
			6588, 6590, 6600, 6664, 6697, 6699, 6780, 6782, 6868, 6998,
			7000, 7001, 7002, 7003, 7004, 7005, 7007, 7010, 7014, 7070, 7071, 7080, 7100, 7144, 7145, 7170, 7171, 7180, 7187, 7199, 7272, 7288, 7401, 7402,
			7443, 7474, 7479, 7493, 7500, 7547, 7548, 7657, 7676, 7776, 7777, 7778, 7779, 7780, 7788,
			8000, 8001, 8002, 8003, 8004, 8005, 8006, 8007, 8008, 8009, 8010, 8020, 8030, 8058, 8060, 8069, 8080, 8081, 8082, 8083,
			8084, 8085, 8086, 8087, 8088, 8089, 8090, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8112, 8118, 8123, 8129, 8138, 8140,
			8159, 8161, 8181, 8182, 8194, 8200, 8222, 8332, 8334, 8351, 8377, 8378, 8388, 8444, 8480, 8529, 8546, 8686, 8765, 8800,
			8834, 8880, 8881, 8882, 8884, 8885, 8886, 8887, 8888, 8889, 8890, 8899, 8983, 8999,
			9000, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009, 9010, 9012, 9030, 9050, 9080, 9083, 9090, 9091, 9100, 9191, 9200, 9292,
			9295, 9300, 9306, 9334, 9418, 9444, 9595, 9668, 9801, 9864, 9869, 9870, 9876, 9943, 9944, 9981, 9997, 9999,
			10000, 10005, 10030, 10035, 10243, 10250, 10255, 10332, 10443, 11371, 11965, 12000, 12300, 12999, 13579, 13666, 13720, 13722,
			14443, 14534, 15000, 16000, 16010, 16922, 16923, 16992, 16993, 17988, 18080, 18086, 18264, 19150, 19888,
			20332, 22335, 23424, 25010, 25105, 26214, 26470, 27016, 28017, 28080,
			30005, 31337, 32400, 32770, 32771, 32773, 34567, 34599, 37215,
			40000, 40001,
			50050, 50060, 50070, 50075, 50090, 50111, 51106, 52869, 55442, 55553, 55555,
			60001, 60443, 8688,
		},
		Handle: HTTP,
		Weight: 80,
		PClass: "B",
	})
}
