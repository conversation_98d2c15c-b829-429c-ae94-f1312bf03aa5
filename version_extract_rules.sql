insert into version_extract_rules (id, created_at, updated_at, deleted_at, rule_id, content, index, is_regexp, is_enable, from)
values  (1, '2023-09-19 10:15:08.978864+08:00', '2023-09-19 10:15:08.978865+08:00', null, 134080, 'Tengine\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (2, '2023-09-19 10:15:08.978867+08:00', '2023-09-19 10:15:08.978867+08:00', null, 17027, 'Linux\/armv5tejl ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (3, '2023-09-19 10:15:08.978868+08:00', '2023-09-19 10:15:08.978868+08:00', null, 0, 'awselb\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (4, '2023-09-19 10:15:08.978869+08:00', '2023-09-19 10:15:08.978869+08:00', null, 0, 'GBase Version: ([\w\.\-\ ]+)', '1', 1, 1, 'FOEYE'),
        (5, '2023-09-19 10:15:08.978869+08:00', '2023-09-19 10:15:08.97887+08:00', null, 106065, 'HP V1910-24G Switch Software Version ([\w\.\ ]+)', '1', 1, 1, 'FOEYE'),
        (6, '2023-09-19 10:15:08.97887+08:00', '2023-09-19 10:15:08.978871+08:00', null, 0, 'Raven-Server-Build: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (7, '2023-09-19 10:15:08.978871+08:00', '2023-09-19 10:15:08.978872+08:00', null, 7409, 'WebSphere Application Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (8, '2023-09-19 10:15:08.978872+08:00', '2023-09-19 10:15:08.978872+08:00', null, 321903, 'KFWebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (9, '2023-09-19 10:15:08.978873+08:00', '2023-09-19 10:15:08.978873+08:00', null, 217319, 'Microsoft-IIS\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (10, '2023-09-19 10:15:08.978874+08:00', '2023-09-19 10:15:08.978874+08:00', null, 7650, 'NetCache appliance \(NetApp\/([\w\.]+)\)', '1', 1, 1, 'FOEYE'),
        (11, '2023-09-19 10:15:08.978874+08:00', '2023-09-19 10:15:08.978874+08:00', null, 694810, 'unbound ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (12, '2023-09-19 10:15:08.978875+08:00', '2023-09-19 10:15:08.978875+08:00', null, 693374, 'NSD ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (13, '2023-09-19 10:15:08.978876+08:00', '2023-09-19 10:15:08.978876+08:00', null, 117779, 'JSP3\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (14, '2023-09-19 10:15:08.978876+08:00', '2023-09-19 10:15:08.978876+08:00', null, 220, 'Netscape-Enterprise\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (15, '2023-09-19 10:15:08.978877+08:00', '2023-09-19 10:15:08.978877+08:00', null, 8748, 'Sun-Java-System-Web-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (16, '2023-09-19 10:15:08.978878+08:00', '2023-09-19 10:15:08.978878+08:00', null, 0, 'WebLogic Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (17, '2023-09-19 10:15:08.978879+08:00', '2023-09-19 10:15:08.978879+08:00', null, 8235, 'PowerDNS Authoritative Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (18, '2023-09-19 10:15:08.978879+08:00', '2023-09-19 10:15:08.978879+08:00', null, 0, 'Roxen\/([\w\.\-]+)', '1', 1, 1, 'FOEYE'),
        (19, '2023-09-19 10:15:08.97888+08:00', '2023-09-19 10:15:08.97888+08:00', null, 327197, 'sslvpn ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (20, '2023-09-19 10:15:08.978882+08:00', '2023-09-19 10:15:08.978882+08:00', null, 482, 'MiniServ\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (21, '2023-09-19 10:15:08.978883+08:00', '2023-09-19 10:15:08.978883+08:00', null, 302, 'IIS Export v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (22, '2023-09-19 10:15:08.978884+08:00', '2023-09-19 10:15:08.978884+08:00', null, 4013, 'UrlRewriter.NET ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (23, '2023-09-19 10:15:08.978884+08:00', '2023-09-19 10:15:08.978885+08:00', null, 4034, 'Suhosin-Patch [a-z_]*\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (24, '2023-09-19 10:15:08.978885+08:00', '2023-09-19 10:15:08.978885+08:00', null, 4787, 'Ben-SSL\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (25, '2023-09-19 10:15:08.978886+08:00', '2023-09-19 10:15:08.978886+08:00', null, 4906, 'BlackJumboDog Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (26, '2023-09-19 10:15:08.978886+08:00', '2023-09-19 10:15:08.978886+08:00', null, 5501, 'EmWeb\/(R[\d_]+)', '1', 1, 1, 'FOEYE'),
        (27, '2023-09-19 10:15:08.978887+08:00', '2023-09-19 10:15:08.978887+08:00', null, 5644, 'RemObjects SDK for .NET HTTP Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (28, '2023-09-19 10:15:08.978888+08:00', '2023-09-19 10:15:08.978888+08:00', null, 5674, 'Deluge: Web UI ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (29, '2023-09-19 10:15:08.978889+08:00', '2023-09-19 10:15:08.978889+08:00', null, 5900, 'mod_gridsite\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (30, '2023-09-19 10:15:08.978889+08:00', '2023-09-19 10:15:08.978889+08:00', null, 5916, 'Geobytes-GeoSelect\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (31, '2023-09-19 10:15:08.97889+08:00', '2023-09-19 10:15:08.97889+08:00', null, 7473, 'libwww-perl-daemon\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (32, '2023-09-19 10:15:08.97889+08:00', '2023-09-19 10:15:08.978891+08:00', null, 9169, 'W3 Total Cache\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (33, '2023-09-19 10:15:08.978891+08:00', '2023-09-19 10:15:08.978891+08:00', null, 393, 'Z-Blog ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (34, '2023-09-19 10:15:08.978892+08:00', '2023-09-19 10:15:08.978892+08:00', null, 394, 'Z-BlogPHP ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (35, '2023-09-19 10:15:08.978893+08:00', '2023-09-19 10:15:08.978893+08:00', null, 8843, 'Communityserver: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (36, '2023-09-19 10:15:08.978893+08:00', '2023-09-19 10:15:08.978894+08:00', null, 8813, 'Synchronet BBS for [a-zA-Z0-9]* Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (37, '2023-09-19 10:15:08.978894+08:00', '2023-09-19 10:15:08.978894+08:00', null, 7497, 'Vanilla ([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (38, '2023-09-19 10:15:08.978895+08:00', '2023-09-19 10:15:08.978895+08:00', null, 4802, 'bbPress ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (39, '2023-09-19 10:15:08.978896+08:00', '2023-09-19 10:15:08.978896+08:00', null, 6140, 'Hiki ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (40, '2023-09-19 10:15:08.978896+08:00', '2023-09-19 10:15:08.978897+08:00', null, 5891, 'KaiBB ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (41, '2023-09-19 10:15:08.978897+08:00', '2023-09-19 10:15:08.978897+08:00', null, 0, 'David-WebBox\/([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (42, '2023-09-19 10:15:08.978898+08:00', '2023-09-19 10:15:08.978898+08:00', null, 8735, 'smartcds\/V*([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (43, '2023-09-19 10:15:08.978898+08:00', '2023-09-19 10:15:08.978899+08:00', null, 17333, 'iServer ([\d\.A-Z]+)', '1', 1, 1, 'FOEYE'),
        (44, '2023-09-19 10:15:08.978899+08:00', '2023-09-19 10:15:08.978899+08:00', null, 17037, 'lwIP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (45, '2023-09-19 10:15:08.9789+08:00', '2023-09-19 10:15:08.9789+08:00', null, 413, 'Adobe GoLive ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (46, '2023-09-19 10:15:08.9789+08:00', '2023-09-19 10:15:08.9789+08:00', null, 8244, 'ProScan ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (47, '2023-09-19 10:15:08.978901+08:00', '2023-09-19 10:15:08.978901+08:00', null, 402791, 'FileMakerPro\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (48, '2023-09-19 10:15:08.978901+08:00', '2023-09-19 10:15:08.978902+08:00', null, 338445, 'Redis Exporter v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (49, '2023-09-19 10:15:08.978902+08:00', '2023-09-19 10:15:08.978902+08:00', null, 694718, 'kong\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (50, '2023-09-19 10:15:08.978902+08:00', '2023-09-19 10:15:08.978903+08:00', null, 5, '(?i)Version\D*([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (51, '2023-09-19 10:15:08.978903+08:00', '2023-09-19 10:15:08.978903+08:00', null, 0, 'number\D*([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (52, '2023-09-19 10:15:08.978904+08:00', '2023-09-19 10:15:08.978904+08:00', null, 2728, 'CouchDB\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (53, '2023-09-19 10:15:08.978904+08:00', '2023-09-19 10:15:08.978905+08:00', null, 6030, 'OrientDB Server ([v\d\.]+)', '1', 1, 1, 'FOEYE'),
        (54, '2023-09-19 10:15:08.978905+08:00', '2023-09-19 10:15:08.978905+08:00', null, 0, 'Couchbase Sync Gateway\/([E \d\.]+)', '1', 1, 1, 'FOEYE'),
        (55, '2023-09-19 10:15:08.978906+08:00', '2023-09-19 10:15:08.978906+08:00', null, 7204, 'Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (56, '2023-09-19 10:15:08.978906+08:00', '2023-09-19 10:15:08.978906+08:00', null, 7205, 'Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (57, '2023-09-19 10:15:08.978907+08:00', '2023-09-19 10:15:08.978907+08:00', null, 7208, 'redis_version:([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (58, '2023-09-19 10:15:08.978908+08:00', '2023-09-19 10:15:08.978908+08:00', null, 7211, 'Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (59, '2023-09-19 10:15:08.978908+08:00', '2023-09-19 10:15:08.978908+08:00', null, 7213, 'version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (60, '2023-09-19 10:15:08.978909+08:00', '2023-09-19 10:15:08.978909+08:00', null, 7215, 'CQL_VERSION\S*05([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (61, '2023-09-19 10:15:08.97891+08:00', '2023-09-19 10:15:08.97891+08:00', null, 7217, 'Anywhere\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (62, '2023-09-19 10:15:08.97891+08:00', '2023-09-19 10:15:08.97891+08:00', null, 7714, 'Oracle_WebDb_Listener\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (63, '2023-09-19 10:15:08.978911+08:00', '2023-09-19 10:15:08.978911+08:00', null, 0, 'X-Influxdb-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (64, '2023-09-19 10:15:08.978912+08:00', '2023-09-19 10:15:08.978912+08:00', null, 676034, 'Version([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (65, '2023-09-19 10:15:08.978913+08:00', '2023-09-19 10:15:08.978913+08:00', null, 740398, 'GBase Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (66, '2023-09-19 10:15:08.978913+08:00', '2023-09-19 10:15:08.978914+08:00', null, 352, 'horde([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (67, '2023-09-19 10:15:08.978914+08:00', '2023-09-19 10:15:08.978914+08:00', null, 463, 'atmail([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (68, '2023-09-19 10:15:08.978915+08:00', '2023-09-19 10:15:08.978915+08:00', null, 471, 'AnyMacro\((\S+)\)', '1', 1, 1, 'FOEYE'),
        (69, '2023-09-19 10:15:08.978915+08:00', '2023-09-19 10:15:08.978915+08:00', null, 481, 'Mirapoint\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (70, '2023-09-19 10:15:08.978916+08:00', '2023-09-19 10:15:08.978916+08:00', null, 634, 'ExtMail CGI Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (71, '2023-09-19 10:15:08.978917+08:00', '2023-09-19 10:15:08.978917+08:00', null, 0, 'IceWarp\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (72, '2023-09-19 10:15:08.978917+08:00', '2023-09-19 10:15:08.978917+08:00', null, 0, 'WinWebMail \[([\d\.]+)\]', '1', 1, 1, 'FOEYE'),
        (73, '2023-09-19 10:15:08.978918+08:00', '2023-09-19 10:15:08.978918+08:00', null, 2701, 'richmail system v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (74, '2023-09-19 10:15:08.978919+08:00', '2023-09-19 10:15:08.978919+08:00', null, 5722, 'Eserv\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (75, '2023-09-19 10:15:08.978919+08:00', '2023-09-19 10:15:08.978919+08:00', null, 5881, 'Gordano Messaging Services Web Server v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (76, '2023-09-19 10:15:08.97892+08:00', '2023-09-19 10:15:08.97892+08:00', null, 6170, 'Kerio Connect ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (77, '2023-09-19 10:15:08.97892+08:00', '2023-09-19 10:15:08.97892+08:00', null, 7550, 'Mailman.*version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (78, '2023-09-19 10:15:08.978921+08:00', '2023-09-19 10:15:08.978921+08:00', null, 18005, 'CMailServer (WebMail )*([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (79, '2023-09-19 10:15:08.978922+08:00', '2023-09-19 10:15:08.978922+08:00', null, 21043, 'Aerofox Mail Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (80, '2023-09-19 10:15:08.978922+08:00', '2023-09-19 10:15:08.978922+08:00', null, 21045, 'Bossmail \(([\d\.]+)\)', '1', 1, 1, 'FOEYE'),
        (81, '2023-09-19 10:15:08.978923+08:00', '2023-09-19 10:15:08.978923+08:00', null, 21057, 'EQManager V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (82, '2023-09-19 10:15:08.978924+08:00', '2023-09-19 10:15:08.978924+08:00', null, 79621, 'MailMax ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (83, '2023-09-19 10:15:08.978924+08:00', '2023-09-19 10:15:08.978925+08:00', null, 79647, 'IdeaSmtpServer ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (84, '2023-09-19 10:15:08.978925+08:00', '2023-09-19 10:15:08.978926+08:00', null, 0, 'Cyrus IMAP v*([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (85, '2023-09-19 10:15:08.978926+08:00', '2023-09-19 10:15:08.978926+08:00', null, 13, 'MDaemon ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (86, '2023-09-19 10:15:08.978931+08:00', '2023-09-19 10:15:08.978931+08:00', null, 193, 'Ipswitch-IMail\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (87, '2023-09-19 10:15:08.978932+08:00', '2023-09-19 10:15:08.978932+08:00', null, 4719, 'ArGoSoft Mail Server.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (88, '2023-09-19 10:15:08.978932+08:00', '2023-09-19 10:15:08.978932+08:00', null, 4809, 'AXIGEN Webmail - v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (89, '2023-09-19 10:15:08.978933+08:00', '2023-09-19 10:15:08.978933+08:00', null, 5503, 'CommuniGatePro\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (90, '2023-09-19 10:15:08.978933+08:00', '2023-09-19 10:15:08.978933+08:00', null, 5737, 'Kerio MailServer ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (91, '2023-09-19 10:15:08.978934+08:00', '2023-09-19 10:15:08.978934+08:00', null, 7549, 'MailEnable .*Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (92, '2023-09-19 10:15:08.978935+08:00', '2023-09-19 10:15:08.978935+08:00', null, 79613, 'Mailtraq( |\/| *\()*([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (93, '2023-09-19 10:15:08.978935+08:00', '2023-09-19 10:15:08.978935+08:00', null, 80597, 'Matrix Mail Server V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (94, '2023-09-19 10:15:08.978936+08:00', '2023-09-19 10:15:08.978936+08:00', null, 89717, 'Exim ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (95, '2023-09-19 10:15:08.978936+08:00', '2023-09-19 10:15:08.978937+08:00', null, 89719, 'XMail ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (96, '2023-09-19 10:15:08.978937+08:00', '2023-09-19 10:15:08.978938+08:00', null, 101804, 'Hmail \(([\d\.]+)\)', '1', 1, 1, 'FOEYE'),
        (97, '2023-09-19 10:15:08.978938+08:00', '2023-09-19 10:15:08.978938+08:00', null, 675812, 'ZMailer Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (98, '2023-09-19 10:15:08.978939+08:00', '2023-09-19 10:15:08.978939+08:00', null, 2402, '360网神防火墙.*\/V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (99, '2023-09-19 10:15:08.97894+08:00', '2023-09-19 10:15:08.97894+08:00', null, 2824, 'SANGFOR AF ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (100, '2023-09-19 10:15:08.97894+08:00', '2023-09-19 10:15:08.97894+08:00', null, 4534, 'Cisco Adaptive Security Appliance Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (101, '2023-09-19 10:15:08.978941+08:00', '2023-09-19 10:15:08.978941+08:00', null, 4781, 'BinarySEC\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (102, '2023-09-19 10:15:08.978941+08:00', '2023-09-19 10:15:08.978941+08:00', null, 4792, 'BarracudaHTTP ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (103, '2023-09-19 10:15:08.978942+08:00', '2023-09-19 10:15:08.978942+08:00', null, 7675, '(O2micro|Firewall)\/([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (104, '2023-09-19 10:15:08.978943+08:00', '2023-09-19 10:15:08.978943+08:00', null, 10093, '(SG-600-P1256)v5.5', '', 0, 1, 'FOEYE'),
        (105, '2023-09-19 10:15:08.978944+08:00', '2023-09-19 10:15:08.978944+08:00', null, 10101, 'LANCOM ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (106, '2023-09-19 10:15:08.978944+08:00', '2023-09-19 10:15:08.978945+08:00', null, 774656, 'SonicOS Enhanced ([\d\.\-a-z]+)', '1', 1, 1, 'FOEYE'),
        (107, '2023-09-19 10:15:08.978945+08:00', '2023-09-19 10:15:08.978945+08:00', null, 17839, 'ZyWALL (USG )*([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (108, '2023-09-19 10:15:08.978946+08:00', '2023-09-19 10:15:08.978946+08:00', null, 22571, '([\S]+)版', '1', 1, 1, 'FOEYE'),
        (109, '2023-09-19 10:15:08.978946+08:00', '2023-09-19 10:15:08.978947+08:00', null, 23623, 'Version ([\d\.A-Z]+)', '1', 1, 1, 'FOEYE'),
        (110, '2023-09-19 10:15:08.978947+08:00', '2023-09-19 10:15:08.978947+08:00', null, 23747, 'Version ([\d\.A-Z]+)', '1', 1, 1, 'FOEYE'),
        (111, '2023-09-19 10:15:08.978948+08:00', '2023-09-19 10:15:08.978948+08:00', null, 64049, 'NDF7300-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (112, '2023-09-19 10:15:08.978948+08:00', '2023-09-19 10:15:08.978949+08:00', null, 846244, '蓝盾防火墙.*V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (113, '2023-09-19 10:15:08.978949+08:00', '2023-09-19 10:15:08.978949+08:00', null, 81905, 'Huawei Eudemon([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (114, '2023-09-19 10:15:08.97895+08:00', '2023-09-19 10:15:08.97895+08:00', null, 8438, 'SecPath ([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (115, '2023-09-19 10:15:08.97895+08:00', '2023-09-19 10:15:08.97895+08:00', null, 21381, 'USG5120(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (116, '2023-09-19 10:15:08.978951+08:00', '2023-09-19 10:15:08.978951+08:00', null, 21383, 'USG5150(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (117, '2023-09-19 10:15:08.978951+08:00', '2023-09-19 10:15:08.978952+08:00', null, 21367, 'USG5300(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (118, '2023-09-19 10:15:08.978952+08:00', '2023-09-19 10:15:08.978952+08:00', null, 21353, 'USG5310(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (119, '2023-09-19 10:15:08.978953+08:00', '2023-09-19 10:15:08.978953+08:00', null, 21355, 'USG5320(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (120, '2023-09-19 10:15:08.978954+08:00', '2023-09-19 10:15:08.978954+08:00', null, 21387, 'USG5530(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (121, '2023-09-19 10:15:08.978954+08:00', '2023-09-19 10:15:08.978954+08:00', null, 20963, 'USG6620(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (122, '2023-09-19 10:15:08.978955+08:00', '2023-09-19 10:15:08.978955+08:00', null, 21389, 'USG5550(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (123, '2023-09-19 10:15:08.978955+08:00', '2023-09-19 10:15:08.978956+08:00', null, 21445, 'USG2250(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (124, '2023-09-19 10:15:08.978956+08:00', '2023-09-19 10:15:08.978956+08:00', null, 21437, 'USG2220(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (125, '2023-09-19 10:15:08.978957+08:00', '2023-09-19 10:15:08.978957+08:00', null, 21423, 'USG2220(-| V)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (126, '2023-09-19 10:15:08.978957+08:00', '2023-09-19 10:15:08.978958+08:00', null, 101598, 'Sidewinder ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (127, '2023-09-19 10:15:08.978958+08:00', '2023-09-19 10:15:08.978958+08:00', null, 26655, '(RG-WALL-v|WALL-V160E-v)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (128, '2023-09-19 10:15:08.978959+08:00', '2023-09-19 10:15:08.978959+08:00', null, 26657, '(RG-WALL-v|WALL-V160s-v)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (129, '2023-09-19 10:15:08.978959+08:00', '2023-09-19 10:15:08.97896+08:00', null, 26661, '(RG-WALL-v|WALL-V50-v)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (130, '2023-09-19 10:15:08.97896+08:00', '2023-09-19 10:15:08.97896+08:00', null, 693690, 'SonicOS Enhanced ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (131, '2023-09-19 10:15:08.978961+08:00', '2023-09-19 10:15:08.978961+08:00', null, 85023, 'SonicOS Enhanced ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (132, '2023-09-19 10:15:08.978961+08:00', '2023-09-19 10:15:08.978961+08:00', null, 85025, 'SonicOS Enhanced ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (133, '2023-09-19 10:15:08.978962+08:00', '2023-09-19 10:15:08.978962+08:00', null, 114201, 'RG-NPE-v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (134, '2023-09-19 10:15:08.978962+08:00', '2023-09-19 10:15:08.978962+08:00', null, 114221, 'VPN3000E-v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (135, '2023-09-19 10:15:08.978963+08:00', '2023-09-19 10:15:08.978963+08:00', null, 307, 'Managedfusion-Rewriter-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (136, '2023-09-19 10:15:08.978964+08:00', '2023-09-19 10:15:08.978964+08:00', null, 422, 'PHP-CGI\/([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (137, '2023-09-19 10:15:08.978965+08:00', '2023-09-19 10:15:08.978965+08:00', null, 516, 'Play! Framework;([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (138, '2023-09-19 10:15:08.978965+08:00', '2023-09-19 10:15:08.978965+08:00', null, 517, 'Restlet-Framework\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (139, '2023-09-19 10:15:08.978966+08:00', '2023-09-19 10:15:08.978966+08:00', null, 520, 'SIMIT framework\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (140, '2023-09-19 10:15:08.978967+08:00', '2023-09-19 10:15:08.978967+08:00', null, 4042, '(?i)AspNetMvc-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (141, '2023-09-19 10:15:08.978967+08:00', '2023-09-19 10:15:08.978967+08:00', null, 4651, '(?i)Cocoon-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (142, '2023-09-19 10:15:08.978968+08:00', '2023-09-19 10:15:08.978968+08:00', null, 4707, 'Asterisk\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (143, '2023-09-19 10:15:08.978968+08:00', '2023-09-19 10:15:08.978968+08:00', null, 4898, 'BlueDragon Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (144, '2023-09-19 10:15:08.978969+08:00', '2023-09-19 10:15:08.978969+08:00', null, 6138, 'Karrigell( |\/)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (145, '2023-09-19 10:15:08.978969+08:00', '2023-09-19 10:15:08.97897+08:00', null, 6151, 'JSF\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (146, '2023-09-19 10:15:08.97897+08:00', '2023-09-19 10:15:08.97897+08:00', null, 6154, 'Zend (Core\/|framework )([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (147, '2023-09-19 10:15:08.978971+08:00', '2023-09-19 10:15:08.978971+08:00', null, 7463, 'LabVIEW\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (148, '2023-09-19 10:15:08.978972+08:00', '2023-09-19 10:15:08.978972+08:00', null, 7737, 'Passenger( |\/)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (149, '2023-09-19 10:15:08.978973+08:00', '2023-09-19 10:15:08.978973+08:00', null, 7770, 'jquery.mobile.js\?ver=([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (150, '2023-09-19 10:15:08.978973+08:00', '2023-09-19 10:15:08.978974+08:00', null, 8239, 'Qcodo\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (151, '2023-09-19 10:15:08.978974+08:00', '2023-09-19 10:15:08.978974+08:00', null, 8240, 'QCubed ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (152, '2023-09-19 10:15:08.978975+08:00', '2023-09-19 10:15:08.978975+08:00', null, 8416, 'CherryPy\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (153, '2023-09-19 10:15:08.978975+08:00', '2023-09-19 10:15:08.978975+08:00', null, 8694, 'Seagull ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (154, '2023-09-19 10:15:08.978976+08:00', '2023-09-19 10:15:08.978976+08:00', null, 8699, 'Simbix Framework\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (155, '2023-09-19 10:15:08.978976+08:00', '2023-09-19 10:15:08.978977+08:00', null, 9210, 'uPortal_rel-([\d\-]+)', '1', 1, 1, 'FOEYE'),
        (156, '2023-09-19 10:15:08.978977+08:00', '2023-09-19 10:15:08.978977+08:00', null, 20321, 'Lotus Expeditor Web Container\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (157, '2023-09-19 10:15:08.978978+08:00', '2023-09-19 10:15:08.978978+08:00', null, 7357, 'echarts(.min)*.js\?v=([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (158, '2023-09-19 10:15:08.978978+08:00', '2023-09-19 10:15:08.978978+08:00', null, 139, 'bootstrap(.min)*.(js|css)\?(_v|v|ver)=([\d\.]+)|bootstrap\/([\d\.]+)', '4,5', 1, 1, 'FOEYE'),
        (159, '2023-09-19 10:15:08.978979+08:00', '2023-09-19 10:15:08.978979+08:00', null, 136335, 'okayNav.js\?ver=([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (160, '2023-09-19 10:15:08.978979+08:00', '2023-09-19 10:15:08.97898+08:00', null, 136337, 'clipboard(.js\/|@)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (161, '2023-09-19 10:15:08.97898+08:00', '2023-09-19 10:15:08.97898+08:00', null, 20771, 'layer([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (162, '2023-09-19 10:15:08.978981+08:00', '2023-09-19 10:15:08.978981+08:00', null, 136229, 'materialize\/([\d\.]+)|materialize.(min.js)*\?ver=([\d\.]+)', '1,3', 1, 1, 'FOEYE'),
        (163, '2023-09-19 10:15:08.978981+08:00', '2023-09-19 10:15:08.978981+08:00', null, 136299, 'ionicons@([\d\.]+)|ionic\/([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (164, '2023-09-19 10:15:08.978982+08:00', '2023-09-19 10:15:08.978982+08:00', null, 7364, 'kissy(\/|-)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (165, '2023-09-19 10:15:08.978982+08:00', '2023-09-19 10:15:08.978983+08:00', null, 5511, 'zepto.js\?ver=([\d\.]+)|zepto\/([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (166, '2023-09-19 10:15:08.978983+08:00', '2023-09-19 10:15:08.978983+08:00', null, 669217, 'Learun_ADMS_V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (167, '2023-09-19 10:15:08.978984+08:00', '2023-09-19 10:15:08.978984+08:00', null, 266, 'PHP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (168, '2023-09-19 10:15:08.978984+08:00', '2023-09-19 10:15:08.978984+08:00', null, 267, 'Aspnet-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (169, '2023-09-19 10:15:08.978985+08:00', '2023-09-19 10:15:08.978985+08:00', null, 268, 'Python\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (170, '2023-09-19 10:15:08.978986+08:00', '2023-09-19 10:15:08.978986+08:00', null, 269, 'Ruby\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (171, '2023-09-19 10:15:08.978986+08:00', '2023-09-19 10:15:08.978987+08:00', null, 270, 'JSP[ \/]*([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (172, '2023-09-19 10:15:08.978988+08:00', '2023-09-19 10:15:08.978988+08:00', null, 271, 'Perl\/v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (173, '2023-09-19 10:15:08.978989+08:00', '2023-09-19 10:15:08.978989+08:00', null, 4088, 'Aspnet-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (174, '2023-09-19 10:15:08.978989+08:00', '2023-09-19 10:15:08.978989+08:00', null, 4520, 'Aspnet-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (175, '2023-09-19 10:15:08.97899+08:00', '2023-09-19 10:15:08.97899+08:00', null, 6024, 'iHTML\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (176, '2023-09-19 10:15:08.97899+08:00', '2023-09-19 10:15:08.978991+08:00', null, 22569, 'java\/([\d\.\-]+)', '1', 1, 1, 'FOEYE'),
        (177, '2023-09-19 10:15:08.978991+08:00', '2023-09-19 10:15:08.978991+08:00', null, 81563, 'CPython\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (178, '2023-09-19 10:15:08.978992+08:00', '2023-09-19 10:15:08.978992+08:00', null, 252, 'F5-BIG-LTM-10000S v13.1', '', 0, 1, 'FOEYE'),
        (179, '2023-09-19 10:15:08.978993+08:00', '2023-09-19 10:15:08.978993+08:00', null, 6143, 'Juniper Networks Application Acceleration Platform - DX ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (180, '2023-09-19 10:15:08.978993+08:00', '2023-09-19 10:15:08.978993+08:00', null, 17137, 'AppDirector with Cookie Persistency v([\w\.]+)', '1', 1, 1, 'FOEYE'),
        (181, '2023-09-19 10:15:08.978994+08:00', '2023-09-19 10:15:08.978994+08:00', null, 19065, 'FWN1000B V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (182, '2023-09-19 10:15:08.978995+08:00', '2023-09-19 10:15:08.978995+08:00', null, 19067, 'FWN200B V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (183, '2023-09-19 10:15:08.978995+08:00', '2023-09-19 10:15:08.978995+08:00', null, 79489, 'TopApp-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (184, '2023-09-19 10:15:08.978996+08:00', '2023-09-19 10:15:08.978996+08:00', null, 85239, 'ServerDiff.*v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (185, '2023-09-19 10:15:08.978996+08:00', '2023-09-19 10:15:08.978997+08:00', null, 20271, 'Array APV 2600 v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (186, '2023-09-19 10:15:08.978997+08:00', '2023-09-19 10:15:08.978997+08:00', null, 0, 'Mac_OS_X-x\d{2}_\d{2}-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (187, '2023-09-19 10:15:08.978998+08:00', '2023-09-19 10:15:08.978998+08:00', null, 4521, 'Oracle Solaris ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (188, '2023-09-19 10:15:08.978998+08:00', '2023-09-19 10:15:08.978998+08:00', null, 4523, ' Linux Mint ([\d\.]+)|MINT-X\/([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (189, '2023-09-19 10:15:08.978999+08:00', '2023-09-19 10:15:08.978999+08:00', null, 4525, '(?i)openSUSE ([\d\.]+|Tumbleweed|Leap ([\d\.]*))|SUSE Linux \D*([\d\.]+)', '1,3', 1, 1, 'FOEYE'),
        (190, '2023-09-19 10:15:08.978999+08:00', '2023-09-19 10:15:08.979+08:00', null, 4527, 'Fedora-RPM-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (191, '2023-09-19 10:15:08.979+08:00', '2023-09-19 10:15:08.979+08:00', null, 4531, 'Debian-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (192, '2023-09-19 10:15:08.979001+08:00', '2023-09-19 10:15:08.979001+08:00', null, 4560, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (193, '2023-09-19 10:15:08.979001+08:00', '2023-09-19 10:15:08.979001+08:00', null, 0, 'HyNetOS\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (194, '2023-09-19 10:15:08.979002+08:00', '2023-09-19 10:15:08.979002+08:00', null, 5960, 'PCLinuxOS ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (195, '2023-09-19 10:15:08.979002+08:00', '2023-09-19 10:15:08.979003+08:00', null, 5961, 'Edition ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (196, '2023-09-19 10:15:08.979003+08:00', '2023-09-19 10:15:08.979003+08:00', null, 7394, 'Microsoft-WinCE\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (197, '2023-09-19 10:15:08.979004+08:00', '2023-09-19 10:15:08.979004+08:00', null, 7556, 'Linux\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (198, '2023-09-19 10:15:08.979004+08:00', '2023-09-19 10:15:08.979004+08:00', null, 8316, '7', '', 0, 1, 'FOEYE'),
        (199, '2023-09-19 10:15:08.979005+08:00', '2023-09-19 10:15:08.979005+08:00', null, 134540, '2000', '', 0, 1, 'FOEYE'),
        (200, '2023-09-19 10:15:08.979006+08:00', '2023-09-19 10:15:08.979006+08:00', null, 8319, '2003', '', 0, 1, 'FOEYE'),
        (201, '2023-09-19 10:15:08.979006+08:00', '2023-09-19 10:15:08.979006+08:00', null, 0, '2008', '', 0, 1, 'FOEYE'),
        (202, '2023-09-19 10:15:08.979007+08:00', '2023-09-19 10:15:08.979007+08:00', null, 19961, '10', '', 0, 1, 'FOEYE'),
        (203, '2023-09-19 10:15:08.979007+08:00', '2023-09-19 10:15:08.979007+08:00', null, 0, '2012', '', 0, 1, 'FOEYE'),
        (204, '2023-09-19 10:15:08.979008+08:00', '2023-09-19 10:15:08.979008+08:00', null, 0, '2016', '', 0, 1, 'FOEYE'),
        (205, '2023-09-19 10:15:08.979008+08:00', '2023-09-19 10:15:08.979009+08:00', null, 20097, 'xp', '', 0, 1, 'FOEYE'),
        (206, '2023-09-19 10:15:08.979009+08:00', '2023-09-19 10:15:08.979009+08:00', null, 21501, '8', '', 0, 1, 'FOEYE'),
        (207, '2023-09-19 10:15:08.97901+08:00', '2023-09-19 10:15:08.97901+08:00', null, 21497, '8.1', '', 0, 1, 'FOEYE'),
        (208, '2023-09-19 10:15:08.97901+08:00', '2023-09-19 10:15:08.979011+08:00', null, 21507, '6.1', '', 0, 1, 'FOEYE'),
        (209, '2023-09-19 10:15:08.979011+08:00', '2023-09-19 10:15:08.979011+08:00', null, 21503, 'vista', '', 0, 1, 'FOEYE'),
        (210, '2023-09-19 10:15:08.979012+08:00', '2023-09-19 10:15:08.979012+08:00', null, 17007, 'VxWorks\/WIND version ([\d\.]+)|VxWorks([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (211, '2023-09-19 10:15:08.979012+08:00', '2023-09-19 10:15:08.979013+08:00', null, 25179, 'NetBSD ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (212, '2023-09-19 10:15:08.979014+08:00', '2023-09-19 10:15:08.979014+08:00', null, 87551, 'Contiki\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (213, '2023-09-19 10:15:08.979014+08:00', '2023-09-19 10:15:08.979015+08:00', null, 330315, 'Fabos Version ([\d\.z-z]+)', '1', 1, 1, 'FOEYE'),
        (214, '2023-09-19 10:15:08.979015+08:00', '2023-09-19 10:15:08.979015+08:00', null, 0, 'Fabos Version ([\d\.z-z]+)', '1', 1, 1, 'FOEYE'),
        (215, '2023-09-19 10:15:08.979016+08:00', '2023-09-19 10:15:08.979016+08:00', null, 780894, 'DIR-\S+', '0', 1, 1, 'FOEYE'),
        (216, '2023-09-19 10:15:08.979016+08:00', '2023-09-19 10:15:08.979017+08:00', null, 4128, 'Router ([A-Z0-9]+)|HostName: ([A-Z0-9]+)', '1,2', 1, 1, 'FOEYE'),
        (217, '2023-09-19 10:15:08.979023+08:00', '2023-09-19 10:15:08.979023+08:00', null, 4544, 'Version ([a-zA-Z0-9\.\(\)]+)', '1', 1, 1, 'FOEYE'),
        (218, '2023-09-19 10:15:08.979024+08:00', '2023-09-19 10:15:08.979024+08:00', null, 4571, 'M(i|I)-([A-Z0-9\-]+)', '2', 1, 1, 'FOEYE'),
        (219, '2023-09-19 10:15:08.979025+08:00', '2023-09-19 10:15:08.979025+08:00', null, 5430, '0.6.0 ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (220, '2023-09-19 10:15:08.979026+08:00', '2023-09-19 10:15:08.979026+08:00', null, 5453, '0.6.0 ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (221, '2023-09-19 10:15:08.979026+08:00', '2023-09-19 10:15:08.979026+08:00', null, 5454, 'TD-W8968 ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (222, '2023-09-19 10:15:08.979027+08:00', '2023-09-19 10:15:08.979027+08:00', null, 5456, '(Router |TL-)([A-Z0-9]+)', '2', 1, 1, 'FOEYE'),
        (223, '2023-09-19 10:15:08.979027+08:00', '2023-09-19 10:15:08.979027+08:00', null, 5460, '(Router |TL-)([A-Z0-9]+)', '2', 1, 1, 'FOEYE'),
        (224, '2023-09-19 10:15:08.979028+08:00', '2023-09-19 10:15:08.979028+08:00', null, 7176, '(Router |TL-)([A-Z0-9]+)', '2', 1, 1, 'FOEYE'),
        (225, '2023-09-19 10:15:08.979028+08:00', '2023-09-19 10:15:08.979029+08:00', null, 7221, 'AirLive ([A-Z0-9\-]+)', '1', 1, 1, 'FOEYE'),
        (226, '2023-09-19 10:15:08.979029+08:00', '2023-09-19 10:15:08.979029+08:00', null, 7232, '(Router |TL-)([A-Z0-9]+)', '2', 1, 1, 'FOEYE'),
        (227, '2023-09-19 10:15:08.97903+08:00', '2023-09-19 10:15:08.97903+08:00', null, 7275, 'Ver ([A-Z0-9\.]+)', '1', 1, 1, 'FOEYE'),
        (228, '2023-09-19 10:15:08.97903+08:00', '2023-09-19 10:15:08.979031+08:00', null, 7378, 'version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (229, '2023-09-19 10:15:08.979031+08:00', '2023-09-19 10:15:08.979031+08:00', null, 7578, 'Vanguard ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (230, '2023-09-19 10:15:08.979032+08:00', '2023-09-19 10:15:08.979032+08:00', null, 7618, 'Revision (R[\d\.]+)', '1', 1, 1, 'FOEYE'),
        (231, '2023-09-19 10:15:08.979033+08:00', '2023-09-19 10:15:08.979033+08:00', null, 9164, '\(([A-Z0-9\.]+)\)', '1', 1, 1, 'FOEYE'),
        (232, '2023-09-19 10:15:08.979033+08:00', '2023-09-19 10:15:08.979034+08:00', null, 10277, 'H150N ([A-Z0-9\-]+)', '1', 1, 1, 'FOEYE'),
        (233, '2023-09-19 10:15:08.979034+08:00', '2023-09-19 10:15:08.979034+08:00', null, 11499, '(Router |TL-)([A-Z0-9]+)', '2', 1, 1, 'FOEYE'),
        (234, '2023-09-19 10:15:08.979034+08:00', '2023-09-19 10:15:08.979035+08:00', null, 13635, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (235, '2023-09-19 10:15:08.979035+08:00', '2023-09-19 10:15:08.979035+08:00', null, 13817, 'HW_REV: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (236, '2023-09-19 10:15:08.979036+08:00', '2023-09-19 10:15:08.979036+08:00', null, 13909, 'Ver. ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (237, '2023-09-19 10:15:08.979036+08:00', '2023-09-19 10:15:08.979036+08:00', null, 14109, '(Version |v)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (238, '2023-09-19 10:15:08.979037+08:00', '2023-09-19 10:15:08.979037+08:00', null, 14179, 'ISR.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (239, '2023-09-19 10:15:08.979038+08:00', '2023-09-19 10:15:08.979038+08:00', null, 14227, 'C3560.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (240, '2023-09-19 10:15:08.979038+08:00', '2023-09-19 10:15:08.979038+08:00', null, 0, 'Box.*v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (241, '2023-09-19 10:15:08.979039+08:00', '2023-09-19 10:15:08.979039+08:00', null, 14987, 'Router - Ethernet - ([\dA-Z]+)', '1', 1, 1, 'FOEYE'),
        (242, '2023-09-19 10:15:08.979039+08:00', '2023-09-19 10:15:08.97904+08:00', null, 15649, 'Card ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (243, '2023-09-19 10:15:08.97904+08:00', '2023-09-19 10:15:08.97904+08:00', null, 16197, 'DX-1821.*Version ([\d\.A-Z]+)', '1', 1, 1, 'FOEYE'),
        (244, '2023-09-19 10:15:08.979041+08:00', '2023-09-19 10:15:08.979041+08:00', null, 16859, 'Software Build Ver([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (245, '2023-09-19 10:15:08.979041+08:00', '2023-09-19 10:15:08.979041+08:00', null, 16961, 'SW_REV: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (246, '2023-09-19 10:15:08.979042+08:00', '2023-09-19 10:15:08.979042+08:00', null, 17409, 'DR6410-([\dA-Z]+)', '1', 1, 1, 'FOEYE'),
        (247, '2023-09-19 10:15:08.979042+08:00', '2023-09-19 10:15:08.979042+08:00', null, 17621, 'EdgeRouter-([\d]+)', '1', 1, 1, 'FOEYE'),
        (248, '2023-09-19 10:15:08.979043+08:00', '2023-09-19 10:15:08.979043+08:00', null, 230, 'FastWeb( |\/)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (249, '2023-09-19 10:15:08.979043+08:00', '2023-09-19 10:15:08.979044+08:00', null, 4112, 'bftpd ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (250, '2023-09-19 10:15:08.979044+08:00', '2023-09-19 10:15:08.979044+08:00', null, 303, 'PowerCDN\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (251, '2023-09-19 10:15:08.979044+08:00', '2023-09-19 10:15:08.979045+08:00', null, 86655, 'Webcache\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (252, '2023-09-19 10:15:08.979045+08:00', '2023-09-19 10:15:08.979045+08:00', null, 315, 'UplusFtp Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (253, '2023-09-19 10:15:08.979046+08:00', '2023-09-19 10:15:08.979046+08:00', null, 4096, 'VxWorks([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (254, '2023-09-19 10:15:08.979046+08:00', '2023-09-19 10:15:08.979046+08:00', null, 4103, 'Version ([a-z\d\.]+)', '1', 1, 1, 'FOEYE'),
        (255, '2023-09-19 10:15:08.979047+08:00', '2023-09-19 10:15:08.979047+08:00', null, 4101, 'spftp\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (256, '2023-09-19 10:15:08.979048+08:00', '2023-09-19 10:15:08.979048+08:00', null, 4105, 'version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (257, '2023-09-19 10:15:08.979048+08:00', '2023-09-19 10:15:08.979049+08:00', null, 4106, 'FTP Server v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (258, '2023-09-19 10:15:08.979049+08:00', '2023-09-19 10:15:08.979049+08:00', null, 4107, 'version ([\d\.]+)|FileZilla Server v?([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (259, '2023-09-19 10:15:08.979049+08:00', '2023-09-19 10:15:08.97905+08:00', null, 4108, 'ProFTPD ([a-z\d\.]+)', '1', 1, 1, 'FOEYE'),
        (260, '2023-09-19 10:15:08.97905+08:00', '2023-09-19 10:15:08.97905+08:00', null, 4111, '(VxWorks|server )([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (261, '2023-09-19 10:15:08.979051+08:00', '2023-09-19 10:15:08.979051+08:00', null, 4113, 'vsFTPd ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (262, '2023-09-19 10:15:08.979051+08:00', '2023-09-19 10:15:08.979051+08:00', null, 7408, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (263, '2023-09-19 10:15:08.979052+08:00', '2023-09-19 10:15:08.979052+08:00', null, 0, '(FTP|Server) ([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (264, '2023-09-19 10:15:08.979052+08:00', '2023-09-19 10:15:08.979052+08:00', null, 836710, 'Wing FTP Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (265, '2023-09-19 10:15:08.979053+08:00', '2023-09-19 10:15:08.979053+08:00', null, 0, '(Version|VxWorks) ([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (266, '2023-09-19 10:15:08.979054+08:00', '2023-09-19 10:15:08.979054+08:00', null, 27087, 'SwiFTP ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (267, '2023-09-19 10:15:08.979054+08:00', '2023-09-19 10:15:08.979054+08:00', null, 87569, 'CuFTPD ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (268, '2023-09-19 10:15:08.979055+08:00', '2023-09-19 10:15:08.979055+08:00', null, 103, 'Sun-ONE-Web-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (269, '2023-09-19 10:15:08.979056+08:00', '2023-09-19 10:15:08.979056+08:00', null, 156, 'ZendServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (270, '2023-09-19 10:15:08.979056+08:00', '2023-09-19 10:15:08.979057+08:00', null, 322921, 'Microsoft-IIS\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (271, '2023-09-19 10:15:08.979057+08:00', '2023-09-19 10:15:08.979057+08:00', null, 209, 'nginx\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (272, '2023-09-19 10:15:08.979058+08:00', '2023-09-19 10:15:08.979058+08:00', null, 210, 'Apache Tomcat\/([\d\.x]+)', '1', 1, 1, 'FOEYE'),
        (273, '2023-09-19 10:15:08.979058+08:00', '2023-09-19 10:15:08.979059+08:00', null, 0, 'Apache\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (274, '2023-09-19 10:15:08.979059+08:00', '2023-09-19 10:15:08.979059+08:00', null, 0, 'Tengine\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (275, '2023-09-19 10:15:08.97906+08:00', '2023-09-19 10:15:08.97906+08:00', null, 320989, 'Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (276, '2023-09-19 10:15:08.979061+08:00', '2023-09-19 10:15:08.979062+08:00', null, 216, 'Microsoft-HTTPAPI\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (277, '2023-09-19 10:15:08.979062+08:00', '2023-09-19 10:15:08.979062+08:00', null, 218, 'Zeus\/([\d\._]+)', '1', 1, 1, 'FOEYE'),
        (278, '2023-09-19 10:15:08.979063+08:00', '2023-09-19 10:15:08.979063+08:00', null, 0, 'Resin\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (279, '2023-09-19 10:15:08.979063+08:00', '2023-09-19 10:15:08.979063+08:00', null, 222, 'WEBrick\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (280, '2023-09-19 10:15:08.979064+08:00', '2023-09-19 10:15:08.979064+08:00', null, 0, 'Jetty\(([\d\.v]+)\)', '1', 1, 1, 'FOEYE'),
        (281, '2023-09-19 10:15:08.979064+08:00', '2023-09-19 10:15:08.979064+08:00', null, 0, 'aicache([\d]+)', '1', 1, 1, 'FOEYE'),
        (282, '2023-09-19 10:15:08.979065+08:00', '2023-09-19 10:15:08.979065+08:00', null, 2729, 'HFS ([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (283, '2023-09-19 10:15:08.979065+08:00', '2023-09-19 10:15:08.979066+08:00', null, 4436, 'PGAPIServ\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (284, '2023-09-19 10:15:08.979066+08:00', '2023-09-19 10:15:08.979066+08:00', null, 4558, 'Cisco-MWEB\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (285, '2023-09-19 10:15:08.979067+08:00', '2023-09-19 10:15:08.979067+08:00', null, 4706, '(ATS|ApacheTrafficServer)\/([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (286, '2023-09-19 10:15:08.979067+08:00', '2023-09-19 10:15:08.979067+08:00', null, 0, 'Blazix Java Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (287, '2023-09-19 10:15:08.979068+08:00', '2023-09-19 10:15:08.979068+08:00', null, 5052, 'Zope ([\d\.b]+)|Zope\/\(([\d\.b]+)|ZServer\/([\d\.b]+)', '1,2,3', 1, 1, 'FOEYE'),
        (288, '2023-09-19 10:15:08.979069+08:00', '2023-09-19 10:15:08.979069+08:00', null, 0, '(Server |SSL )v([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (289, '2023-09-19 10:15:08.97907+08:00', '2023-09-19 10:15:08.97907+08:00', null, 5732, 'Server\/([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (290, '2023-09-19 10:15:08.97907+08:00', '2023-09-19 10:15:08.97907+08:00', null, 5812, 'TenseContentServer:([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (291, '2023-09-19 10:15:08.979071+08:00', '2023-09-19 10:15:08.979071+08:00', null, 320987, 'IBM_CICS_Transaction_Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (292, '2023-09-19 10:15:08.979072+08:00', '2023-09-19 10:15:08.979072+08:00', null, 6033, 'IdeaWebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (293, '2023-09-19 10:15:08.979072+08:00', '2023-09-19 10:15:08.979072+08:00', null, 0, 'SAP J2EE Engine\/([\d\.]+)|SAP\D*([\d\.]{2,})', '1,2', 1, 1, 'FOEYE'),
        (294, '2023-09-19 10:15:08.979073+08:00', '2023-09-19 10:15:08.979073+08:00', null, 7470, 'Lasso\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (295, '2023-09-19 10:15:08.979074+08:00', '2023-09-19 10:15:08.979074+08:00', null, 7598, 'MJNioHttpDaemon\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (296, '2023-09-19 10:15:08.979075+08:00', '2023-09-19 10:15:08.979075+08:00', null, 7654, 'NetPort Software ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (297, '2023-09-19 10:15:08.979075+08:00', '2023-09-19 10:15:08.979075+08:00', null, 7658, 'Netscape-FastTrack\/([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (298, '2023-09-19 10:15:08.979076+08:00', '2023-09-19 10:15:08.979076+08:00', null, 7718, 'orenosv\/([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (299, '2023-09-19 10:15:08.979076+08:00', '2023-09-19 10:15:08.979077+08:00', null, 8273, 'Railo-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (300, '2023-09-19 10:15:08.979077+08:00', '2023-09-19 10:15:08.979077+08:00', null, 8296, 'sambar\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (301, '2023-09-19 10:15:08.979078+08:00', '2023-09-19 10:15:08.979078+08:00', null, 27111, 'Apusic Application Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (302, '2023-09-19 10:15:08.979078+08:00', '2023-09-19 10:15:08.979079+08:00', null, 0, '(calibre\/|calibre )([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (303, '2023-09-19 10:15:08.979079+08:00', '2023-09-19 10:15:08.979079+08:00', null, 89715, ' Messaging Server ([\d\.u\-]+)', '1', 1, 1, 'FOEYE'),
        (304, '2023-09-19 10:15:08.97908+08:00', '2023-09-19 10:15:08.97908+08:00', null, 282, 'Microsoftsharepointteamservices: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (305, '2023-09-19 10:15:08.97908+08:00', '2023-09-19 10:15:08.97908+08:00', null, 7487, 'Microsoftsharepointteamservices: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (306, '2023-09-19 10:15:08.979081+08:00', '2023-09-19 10:15:08.979081+08:00', null, 7504, 'Liferay\D*Edition ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (307, '2023-09-19 10:15:08.979081+08:00', '2023-09-19 10:15:08.979081+08:00', null, 8256, 'Q330 V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (308, '2023-09-19 10:15:08.979082+08:00', '2023-09-19 10:15:08.979082+08:00', null, 2504, 'Schneider-WEB\/V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (309, '2023-09-19 10:15:08.979083+08:00', '2023-09-19 10:15:08.979083+08:00', null, 2505, 'WindRiver-WebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (310, '2023-09-19 10:15:08.979083+08:00', '2023-09-19 10:15:08.979084+08:00', null, 0, 'Goahead\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (311, '2023-09-19 10:15:08.979084+08:00', '2023-09-19 10:15:08.979084+08:00', null, 6173, 'Keil-EWEB\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (312, '2023-09-19 10:15:08.979085+08:00', '2023-09-19 10:15:08.979085+08:00', null, 7407, 'WMI V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (313, '2023-09-19 10:15:08.979085+08:00', '2023-09-19 10:15:08.979086+08:00', null, 5665, 'DLILPC.*Version=([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (314, '2023-09-19 10:15:08.979086+08:00', '2023-09-19 10:15:08.979086+08:00', null, 6074, 'Internet Cluster Manager Ver. ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (315, '2023-09-19 10:15:08.979087+08:00', '2023-09-19 10:15:08.979087+08:00', null, 5965, 'HP System Management Homepage\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (316, '2023-09-19 10:15:08.979087+08:00', '2023-09-19 10:15:08.979087+08:00', null, 6147, 'Jigsaw\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (317, '2023-09-19 10:15:08.979088+08:00', '2023-09-19 10:15:08.979088+08:00', null, 9413, 'Wowza Streaming Engine ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (318, '2023-09-19 10:15:08.979088+08:00', '2023-09-19 10:15:08.979088+08:00', null, 174, 'squid\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (319, '2023-09-19 10:15:08.979089+08:00', '2023-09-19 10:15:08.979089+08:00', null, 261, ' kangle\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (320, '2023-09-19 10:15:08.979089+08:00', '2023-09-19 10:15:08.97909+08:00', null, 283, '(varnish-v|Varnish\/)([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (321, '2023-09-19 10:15:08.97909+08:00', '2023-09-19 10:15:08.97909+08:00', null, 64089, 'Automation ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (322, '2023-09-19 10:15:08.979091+08:00', '2023-09-19 10:15:08.979091+08:00', null, 126, 'Plesk ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (323, '2023-09-19 10:15:08.979091+08:00', '2023-09-19 10:15:08.979092+08:00', null, 4490, 'RSYNCD: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (324, '2023-09-19 10:15:08.979092+08:00', '2023-09-19 10:15:08.979092+08:00', null, 0, 'NetTalk-WebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (325, '2023-09-19 10:15:08.979093+08:00', '2023-09-19 10:15:08.979093+08:00', null, 89723, 'Sendmail (version )?([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (326, '2023-09-19 10:15:08.979093+08:00', '2023-09-19 10:15:08.979093+08:00', null, 264973, 'EXCEL服务器([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (327, '2023-09-19 10:15:08.979094+08:00', '2023-09-19 10:15:08.979094+08:00', null, 265125, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (328, '2023-09-19 10:15:08.979095+08:00', '2023-09-19 10:15:08.979095+08:00', null, 265699, 'dropbear_([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (329, '2023-09-19 10:15:08.979095+08:00', '2023-09-19 10:15:08.979095+08:00', null, 118749, 'BBVS\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (330, '2023-09-19 10:15:08.979096+08:00', '2023-09-19 10:15:08.979096+08:00', null, 99079, 'HWServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (331, '2023-09-19 10:15:08.979097+08:00', '2023-09-19 10:15:08.979097+08:00', null, 98726, 'IQinVision Embedded ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (332, '2023-09-19 10:15:08.979097+08:00', '2023-09-19 10:15:08.979097+08:00', null, 118159, 'Wowza Media Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (333, '2023-09-19 10:15:08.979098+08:00', '2023-09-19 10:15:08.979098+08:00', null, 98766, 'Cohu\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (334, '2023-09-19 10:15:08.979098+08:00', '2023-09-19 10:15:08.979099+08:00', null, 98751, 'TAS-Tech Streaming Server ([VR\d\.]+)', '1', 1, 1, 'FOEYE'),
        (335, '2023-09-19 10:15:08.979099+08:00', '2023-09-19 10:15:08.979099+08:00', null, 338493, 'Chronograf-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (336, '2023-09-19 10:15:08.9791+08:00', '2023-09-19 10:15:08.9791+08:00', null, 0, 'WebSphere Application Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (337, '2023-09-19 10:15:08.9791+08:00', '2023-09-19 10:15:08.9791+08:00', null, 675830, 'Lotus-Domino\/(Release-)*([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (338, '2023-09-19 10:15:08.979101+08:00', '2023-09-19 10:15:08.979101+08:00', null, 675816, 'IWSVA ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (339, '2023-09-19 10:15:08.979102+08:00', '2023-09-19 10:15:08.979102+08:00', null, 118101, 'Anti-Web V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (340, '2023-09-19 10:15:08.979103+08:00', '2023-09-19 10:15:08.979103+08:00', null, 133687, 'APN HTTPD V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (341, '2023-09-19 10:15:08.979104+08:00', '2023-09-19 10:15:08.979104+08:00', null, 133617, 'nostromo ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (342, '2023-09-19 10:15:08.979104+08:00', '2023-09-19 10:15:08.979104+08:00', null, 321027, 'IceWarp\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (343, '2023-09-19 10:15:08.979105+08:00', '2023-09-19 10:15:08.979105+08:00', null, 54539, 'Camera Web Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (344, '2023-09-19 10:15:08.979105+08:00', '2023-09-19 10:15:08.979106+08:00', null, 326493, 'Seeyon-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (345, '2023-09-19 10:15:08.979106+08:00', '2023-09-19 10:15:08.979106+08:00', null, 133474, 'WildFly\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (346, '2023-09-19 10:15:08.979106+08:00', '2023-09-19 10:15:08.979107+08:00', null, 710524, 'Version(:| )([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (347, '2023-09-19 10:15:08.979107+08:00', '2023-09-19 10:15:08.979107+08:00', null, 132945, 'mitmproxy ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (348, '2023-09-19 10:15:08.979108+08:00', '2023-09-19 10:15:08.979108+08:00', null, 727378, 'X-NetCat-Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (349, '2023-09-19 10:15:08.979108+08:00', '2023-09-19 10:15:08.979109+08:00', null, 727372, 'Version: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (350, '2023-09-19 10:15:08.979109+08:00', '2023-09-19 10:15:08.979109+08:00', null, 0, 'C2900XL.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (351, '2023-09-19 10:15:08.97911+08:00', '2023-09-19 10:15:08.97911+08:00', null, 118091, 'Catalyst.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (352, '2023-09-19 10:15:08.97911+08:00', '2023-09-19 10:15:08.97911+08:00', null, 7371, 'Catalyst.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (353, '2023-09-19 10:15:08.979111+08:00', '2023-09-19 10:15:08.979111+08:00', null, 9495, 'Cisco MDS ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (354, '2023-09-19 10:15:08.979112+08:00', '2023-09-19 10:15:08.979112+08:00', null, 14251, 'ME340x.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (355, '2023-09-19 10:15:08.979112+08:00', '2023-09-19 10:15:08.979112+08:00', null, 14197, 'ME360x.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (356, '2023-09-19 10:15:08.979113+08:00', '2023-09-19 10:15:08.979113+08:00', null, 15821, 'n5000.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (357, '2023-09-19 10:15:08.979113+08:00', '2023-09-19 10:15:08.979114+08:00', null, 15823, 'n6000.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (358, '2023-09-19 10:15:08.979114+08:00', '2023-09-19 10:15:08.979114+08:00', null, 15825, 'n7000.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (359, '2023-09-19 10:15:08.979114+08:00', '2023-09-19 10:15:08.979115+08:00', null, 15827, 'n7700.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (360, '2023-09-19 10:15:08.979115+08:00', '2023-09-19 10:15:08.979115+08:00', null, 15829, 'n9000.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (361, '2023-09-19 10:15:08.979116+08:00', '2023-09-19 10:15:08.979116+08:00', null, 27185, 'Cisco WS-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (362, '2023-09-19 10:15:08.979116+08:00', '2023-09-19 10:15:08.979116+08:00', null, 13667, 'Baseline Switch.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (363, '2023-09-19 10:15:08.979117+08:00', '2023-09-19 10:15:08.979117+08:00', null, 27971, '3Com Switch 4200G 24-Port Software Version 3Com OS ([\w\.]+)', '1', 1, 1, 'FOEYE'),
        (364, '2023-09-19 10:15:08.979117+08:00', '2023-09-19 10:15:08.979118+08:00', null, 13701, '3Com IntelliJack ([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (365, '2023-09-19 10:15:08.979118+08:00', '2023-09-19 10:15:08.979118+08:00', null, 123609, 'S1626V([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (366, '2023-09-19 10:15:08.979119+08:00', '2023-09-19 10:15:08.979119+08:00', null, 17041, 'S5120-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (367, '2023-09-19 10:15:08.979119+08:00', '2023-09-19 10:15:08.979119+08:00', null, 19645, 'S5130-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (368, '2023-09-19 10:15:08.97912+08:00', '2023-09-19 10:15:08.97912+08:00', null, 330219, 'S6800-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (369, '2023-09-19 10:15:08.97912+08:00', '2023-09-19 10:15:08.97912+08:00', null, 7404, 'H3C S([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (370, '2023-09-19 10:15:08.979121+08:00', '2023-09-19 10:15:08.979121+08:00', null, 696056, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (371, '2023-09-19 10:15:08.979121+08:00', '2023-09-19 10:15:08.979122+08:00', null, 384, 'Home Gateway ([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (372, '2023-09-19 10:15:08.979122+08:00', '2023-09-19 10:15:08.979122+08:00', null, 119609, 'HW9306-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (373, '2023-09-19 10:15:08.979122+08:00', '2023-09-19 10:15:08.979123+08:00', null, 13697, 'S12708-(REL|CLK)-([\d\.A-Z\-]+)|Version ([\d\.]+)', '2,3', 1, 1, 'FOEYE'),
        (374, '2023-09-19 10:15:08.979123+08:00', '2023-09-19 10:15:08.979123+08:00', null, 79851, 'S3700-([\d\.a-zA-Z\-]+)|Version ([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (375, '2023-09-19 10:15:08.979124+08:00', '2023-09-19 10:15:08.979124+08:00', null, 16977, 'S5328C-EI-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (376, '2023-09-19 10:15:08.979124+08:00', '2023-09-19 10:15:08.979124+08:00', null, 79869, 'S5720-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (377, '2023-09-19 10:15:08.979125+08:00', '2023-09-19 10:15:08.979125+08:00', null, 157075, 'S5720s-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (378, '2023-09-19 10:15:08.979125+08:00', '2023-09-19 10:15:08.979126+08:00', null, 136275, 'S5730-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (379, '2023-09-19 10:15:08.979126+08:00', '2023-09-19 10:15:08.979126+08:00', null, 156701, 'S6720-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (380, '2023-09-19 10:15:08.979127+08:00', '2023-09-19 10:15:08.979127+08:00', null, 136271, 'S7700-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (381, '2023-09-19 10:15:08.979127+08:00', '2023-09-19 10:15:08.979127+08:00', null, 156717, 'S9306-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (382, '2023-09-19 10:15:08.979128+08:00', '2023-09-19 10:15:08.979128+08:00', null, 87565, 'S2800 V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (383, '2023-09-19 10:15:08.979128+08:00', '2023-09-19 10:15:08.979128+08:00', null, 79511, 'Switch\(([\d\.A-Z\-\/]+)\)', '1', 1, 1, 'FOEYE'),
        (384, '2023-09-19 10:15:08.979129+08:00', '2023-09-19 10:15:08.979129+08:00', null, 101356, '3928A-([\d\.A-Z\-]+)', '1', 1, 1, 'FOEYE'),
        (385, '2023-09-19 10:15:08.97913+08:00', '2023-09-19 10:15:08.97913+08:00', null, 7333, 'Switch ([\dA-Z]+)', '1', 1, 1, 'FOEYE'),
        (386, '2023-09-19 10:15:08.97913+08:00', '2023-09-19 10:15:08.97913+08:00', null, 27973, 'DCRS-([\dA-Z]+)', '1', 1, 1, 'FOEYE'),
        (387, '2023-09-19 10:15:08.979131+08:00', '2023-09-19 10:15:08.979131+08:00', null, 237, 'Sangfor-SSL M([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (388, '2023-09-19 10:15:08.979131+08:00', '2023-09-19 10:15:08.979131+08:00', null, 5433, 'TP-LINK Gigabit Broadband VPN Router R([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (389, '2023-09-19 10:15:08.979132+08:00', '2023-09-19 10:15:08.979132+08:00', null, 7305, 'SSL-VPN\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (390, '2023-09-19 10:15:08.979132+08:00', '2023-09-19 10:15:08.979132+08:00', null, 737502, 'VPN ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (391, '2023-09-19 10:15:08.979133+08:00', '2023-09-19 10:15:08.979133+08:00', null, 15971, 'ConnectPort WAN VPN Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (392, '2023-09-19 10:15:08.979134+08:00', '2023-09-19 10:15:08.979134+08:00', null, 24715, 'MB8000 v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (393, '2023-09-19 10:15:08.979134+08:00', '2023-09-19 10:15:08.979134+08:00', null, 24923, 'MPSec-.*v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (394, '2023-09-19 10:15:08.979135+08:00', '2023-09-19 10:15:08.979135+08:00', null, 26159, 'PLANET.*VPN.*v([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (395, '2023-09-19 10:15:08.979135+08:00', '2023-09-19 10:15:08.979136+08:00', null, 26625, 'Red-Giant SecVPN.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (396, '2023-09-19 10:15:08.979136+08:00', '2023-09-19 10:15:08.979136+08:00', null, 79471, 'MPSec-.*v([\w\.]+)', '1', 1, 1, 'FOEYE'),
        (397, '2023-09-19 10:15:08.979137+08:00', '2023-09-19 10:15:08.979137+08:00', null, 86797, 'Argon ([\dx]+)', '1', 1, 1, 'FOEYE'),
        (398, '2023-09-19 10:15:08.979137+08:00', '2023-09-19 10:15:08.979137+08:00', null, 87627, 'LANCOM 1781EF ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (399, '2023-09-19 10:15:08.979139+08:00', '2023-09-19 10:15:08.979139+08:00', null, 87629, 'LANCOM 1781EW ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (400, '2023-09-19 10:15:08.979139+08:00', '2023-09-19 10:15:08.97914+08:00', null, 0, 'LANCOM 1711[\+]* VPN ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (401, '2023-09-19 10:15:08.97914+08:00', '2023-09-19 10:15:08.97914+08:00', null, 89847, 'FirePass ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (402, '2023-09-19 10:15:08.979141+08:00', '2023-09-19 10:15:08.979141+08:00', null, 676356, 'P5100.*Ver.*?([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (403, '2023-09-19 10:15:08.979141+08:00', '2023-09-19 10:15:08.979141+08:00', null, 0, 'P5100.*Ver.*?([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (404, '2023-09-19 10:15:08.979143+08:00', '2023-09-19 10:15:08.979143+08:00', null, 676346, 'M5000-AC-([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (405, '2023-09-19 10:15:08.979143+08:00', '2023-09-19 10:15:08.979143+08:00', null, 823142, 'sslvpn ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (406, '2023-09-19 10:15:08.979144+08:00', '2023-09-19 10:15:08.979144+08:00', null, 25151, 'IX3100.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (407, '2023-09-19 10:15:08.979145+08:00', '2023-09-19 10:15:08.979145+08:00', null, 25139, 'IX2005.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (408, '2023-09-19 10:15:08.979145+08:00', '2023-09-19 10:15:08.979145+08:00', null, 25141, 'IX2010.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (409, '2023-09-19 10:15:08.979146+08:00', '2023-09-19 10:15:08.979146+08:00', null, 114015, 'RV130W.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (410, '2023-09-19 10:15:08.979146+08:00', '2023-09-19 10:15:08.979146+08:00', null, 114009, 'RV130.*Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (411, '2023-09-19 10:15:08.979147+08:00', '2023-09-19 10:15:08.979147+08:00', null, 85083, 'VPN ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (412, '2023-09-19 10:15:08.979147+08:00', '2023-09-19 10:15:08.979148+08:00', null, 85084, 'VPN ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (413, '2023-09-19 10:15:08.979148+08:00', '2023-09-19 10:15:08.979148+08:00', null, 4072, 'Abyss\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (414, '2023-09-19 10:15:08.979149+08:00', '2023-09-19 10:15:08.979149+08:00', null, 4026, 'aidex\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (415, '2023-09-19 10:15:08.979149+08:00', '2023-09-19 10:15:08.979149+08:00', null, 4678, 'Alpha\D*\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (416, '2023-09-19 10:15:08.97915+08:00', '2023-09-19 10:15:08.97915+08:00', null, 340, 'AOLserver\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (417, '2023-09-19 10:15:08.97915+08:00', '2023-09-19 10:15:08.979151+08:00', null, 263899, 'APV 6600 v8.5', '', 0, 1, 'FOEYE'),
        (418, '2023-09-19 10:15:08.979151+08:00', '2023-09-19 10:15:08.979151+08:00', null, 9100, 'ATOP Web Server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (419, '2023-09-19 10:15:08.979152+08:00', '2023-09-19 10:15:08.979152+08:00', null, 0, 'axhttpd\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (420, '2023-09-19 10:15:08.979152+08:00', '2023-09-19 10:15:08.979153+08:00', null, 4796, 'BadBlue\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (421, '2023-09-19 10:15:08.979153+08:00', '2023-09-19 10:15:08.979153+08:00', null, 4908, 'Boa\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (422, '2023-09-19 10:15:08.979154+08:00', '2023-09-19 10:15:08.979154+08:00', null, 87639, 'calibre ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (423, '2023-09-19 10:15:08.979154+08:00', '2023-09-19 10:15:08.979154+08:00', null, 5033, 'Caudium\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (424, '2023-09-19 10:15:08.979155+08:00', '2023-09-19 10:15:08.979155+08:00', null, 5045, 'Cherokee\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (425, '2023-09-19 10:15:08.979155+08:00', '2023-09-19 10:15:08.979155+08:00', null, 129189, 'CerberusFTPServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (426, '2023-09-19 10:15:08.979156+08:00', '2023-09-19 10:15:08.979156+08:00', null, 5022, 'CL-HTTP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (427, '2023-09-19 10:15:08.979156+08:00', '2023-09-19 10:15:08.979157+08:00', null, 0, 'CovalentSNMP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (428, '2023-09-19 10:15:08.979157+08:00', '2023-09-19 10:15:08.979157+08:00', null, 5508, ' Comanche\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (429, '2023-09-19 10:15:08.979158+08:00', '2023-09-19 10:15:08.979158+08:00', null, 5005, 'confproxy\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (430, '2023-09-19 10:15:08.979158+08:00', '2023-09-19 10:15:08.979159+08:00', null, 8314, '(?i)iDRAC\S* ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (431, '2023-09-19 10:15:08.979159+08:00', '2023-09-19 10:15:08.979159+08:00', null, 5651, 'DeleGate\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (432, '2023-09-19 10:15:08.97916+08:00', '2023-09-19 10:15:08.97916+08:00', null, 87637, 'DI-IOS-HTTPD([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (433, '2023-09-19 10:15:08.97916+08:00', '2023-09-19 10:15:08.979161+08:00', null, 5641, 'Web Server ([\d\.a-z]+)', '1', 1, 1, 'FOEYE'),
        (434, '2023-09-19 10:15:08.979161+08:00', '2023-09-19 10:15:08.979161+08:00', null, 81087, 'Embedthis-Appweb\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (435, '2023-09-19 10:15:08.979162+08:00', '2023-09-19 10:15:08.979162+08:00', null, 0, 'Enhydra-MultiServer\/([\d\.]+)| Enhydra Application Server\/([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (436, '2023-09-19 10:15:08.979162+08:00', '2023-09-19 10:15:08.979162+08:00', null, 5909, 'fnord\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (437, '2023-09-19 10:15:08.979163+08:00', '2023-09-19 10:15:08.979163+08:00', null, 0, '4D\/([\d\.]+)|4D([\d\.-]+)', '1,2', 1, 1, 'FOEYE'),
        (438, '2023-09-19 10:15:08.979163+08:00', '2023-09-19 10:15:08.979163+08:00', null, 5796, 'Fujitsu-InfoProvider-Pro\/([VL\d\.]+)', '1', 1, 1, 'FOEYE'),
        (439, '2023-09-19 10:15:08.979164+08:00', '2023-09-19 10:15:08.979164+08:00', null, 4873, 'GoAhead-Webs\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (440, '2023-09-19 10:15:08.979164+08:00', '2023-09-19 10:15:08.979165+08:00', null, 364, '(?i)gunicorn\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (441, '2023-09-19 10:15:08.979165+08:00', '2023-09-19 10:15:08.979165+08:00', null, 5899, 'gSOAP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (442, '2023-09-19 10:15:08.979166+08:00', '2023-09-19 10:15:08.979166+08:00', null, 5962, 'Http explorer ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (443, '2023-09-19 10:15:08.979167+08:00', '2023-09-19 10:15:08.979167+08:00', null, 0, 'IBM-PROXY-WTE\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (444, '2023-09-19 10:15:08.979168+08:00', '2023-09-19 10:15:08.979168+08:00', null, 6014, 'WebSEAL\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (445, '2023-09-19 10:15:08.979168+08:00', '2023-09-19 10:15:08.979168+08:00', null, 10189, 'Indy\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (446, '2023-09-19 10:15:08.979169+08:00', '2023-09-19 10:15:08.979169+08:00', null, 20883, 'WebServer ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (447, '2023-09-19 10:15:08.979169+08:00', '2023-09-19 10:15:08.97917+08:00', null, 0, ' Internet Cluster Manager Ver. ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (448, '2023-09-19 10:15:08.97917+08:00', '2023-09-19 10:15:08.97917+08:00', null, 4682, ' Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (449, '2023-09-19 10:15:08.979171+08:00', '2023-09-19 10:15:08.979171+08:00', null, 796614, 'Keil-EWEB\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (450, '2023-09-19 10:15:08.979171+08:00', '2023-09-19 10:15:08.979171+08:00', null, 6168, 'KFWebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (451, '2023-09-19 10:15:08.979172+08:00', '2023-09-19 10:15:08.979172+08:00', null, 6171, '(WebSTAR|WebStar)\/([\d\.]+)', '2', 1, 1, 'FOEYE'),
        (452, '2023-09-19 10:15:08.979173+08:00', '2023-09-19 10:15:08.979173+08:00', null, 0, 'Koala Web Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (453, '2023-09-19 10:15:08.979173+08:00', '2023-09-19 10:15:08.979173+08:00', null, 2725, 'lighttpd\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (454, '2023-09-19 10:15:08.979174+08:00', '2023-09-19 10:15:08.979174+08:00', null, 7534, ' Mathopd\/([\d\.pb]+)', '1', 1, 1, 'FOEYE'),
        (455, '2023-09-19 10:15:08.979174+08:00', '2023-09-19 10:15:08.979174+08:00', null, 7502, 'MacHTTP\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (456, '2023-09-19 10:15:08.979175+08:00', '2023-09-19 10:15:08.979175+08:00', null, 0, 'Mbedthis-Appweb\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (457, '2023-09-19 10:15:08.979175+08:00', '2023-09-19 10:15:08.979176+08:00', null, 4023, 'mhttpd ([v\d\.]+)', '1', 1, 1, 'FOEYE'),
        (458, '2023-09-19 10:15:08.979176+08:00', '2023-09-19 10:15:08.979176+08:00', null, 0, 'mini_httpd\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (459, '2023-09-19 10:15:08.979177+08:00', '2023-09-19 10:15:08.979177+08:00', null, 0, 'Mini web server ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (460, '2023-09-19 10:15:08.979177+08:00', '2023-09-19 10:15:08.979177+08:00', null, 7420, 'microHttp\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (461, '2023-09-19 10:15:08.979178+08:00', '2023-09-19 10:15:08.979178+08:00', null, 7584, 'Revision: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (462, '2023-09-19 10:15:08.979179+08:00', '2023-09-19 10:15:08.979179+08:00', null, 7427, 'MiniServ\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (463, '2023-09-19 10:15:08.979179+08:00', '2023-09-19 10:15:08.979179+08:00', null, 7602, 'MochiWeb\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (464, '2023-09-19 10:15:08.97918+08:00', '2023-09-19 10:15:08.97918+08:00', null, 7516, 'MS-MFC-HttpSvr\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (465, '2023-09-19 10:15:08.979181+08:00', '2023-09-19 10:15:08.979181+08:00', null, 0, 'MS-SDK-HttpServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (466, '2023-09-19 10:15:08.979182+08:00', '2023-09-19 10:15:08.979182+08:00', null, 133273, 'OpenTV\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (467, '2023-09-19 10:15:08.979188+08:00', '2023-09-19 10:15:08.979188+08:00', null, 7617, 'NCSA( |\/)([vb\d\.]+)', '2', 1, 1, 'FOEYE'),
        (468, '2023-09-19 10:15:08.979189+08:00', '2023-09-19 10:15:08.97919+08:00', null, 0, 'NetPort Software ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (469, '2023-09-19 10:15:08.97919+08:00', '2023-09-19 10:15:08.97919+08:00', null, 0, 'Netscape-FastTrack\/([\d\.a-zA-Z]+)', '1', 1, 1, 'FOEYE'),
        (470, '2023-09-19 10:15:08.979191+08:00', '2023-09-19 10:15:08.979191+08:00', null, 7662, 'NetTalk-WebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (471, '2023-09-19 10:15:08.979191+08:00', '2023-09-19 10:15:08.979191+08:00', null, 196705, 'Niagara Web Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (472, '2023-09-19 10:15:08.979192+08:00', '2023-09-19 10:15:08.979192+08:00', null, 19993, 'ILOM-Web-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (473, '2023-09-19 10:15:08.979192+08:00', '2023-09-19 10:15:08.979193+08:00', null, 7710, 'Oracle-iPlanet-Web-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (474, '2023-09-19 10:15:08.979193+08:00', '2023-09-19 10:15:08.979193+08:00', null, 7708, 'Oracle9iAS \(([\d\.]+)\)|Oracle9iAS\/([\d\.]+)', '1,2', 1, 1, 'FOEYE'),
        (475, '2023-09-19 10:15:08.979194+08:00', '2023-09-19 10:15:08.979194+08:00', null, 7512, 'OpenSSH_([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (476, '2023-09-19 10:15:08.979194+08:00', '2023-09-19 10:15:08.979194+08:00', null, 0, 'openresty\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (477, '2023-09-19 10:15:08.979195+08:00', '2023-09-19 10:15:08.979195+08:00', null, 7565, 'PasteWSGIServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (478, '2023-09-19 10:15:08.979195+08:00', '2023-09-19 10:15:08.979196+08:00', null, 8070, 'phpLDAPadmin \(([\d\.]+)\)', '1', 1, 1, 'FOEYE'),
        (479, '2023-09-19 10:15:08.979196+08:00', '2023-09-19 10:15:08.979196+08:00', null, 8096, 'Pi3Web\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (480, '2023-09-19 10:15:08.979197+08:00', '2023-09-19 10:15:08.979197+08:00', null, 544, 'Privoxy ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (481, '2023-09-19 10:15:08.979197+08:00', '2023-09-19 10:15:08.979197+08:00', null, 0, 'Q330 V([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (482, '2023-09-19 10:15:08.979198+08:00', '2023-09-19 10:15:08.979198+08:00', null, 8269, 'RaidenHTTPD\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (483, '2023-09-19 10:15:08.979198+08:00', '2023-09-19 10:15:08.979199+08:00', null, 8301, 'RealVNC\/([E\d\.]+)', '1', 1, 1, 'FOEYE'),
        (484, '2023-09-19 10:15:08.979199+08:00', '2023-09-19 10:15:08.979199+08:00', null, 8754, 'Stronghold\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (485, '2023-09-19 10:15:08.9792+08:00', '2023-09-19 10:15:08.9792+08:00', null, 820618, 'RemotelyAnywhere\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (486, '2023-09-19 10:15:08.9792+08:00', '2023-09-19 10:15:08.979201+08:00', null, 0, 'Roxen\/([\d\.\-release]+)', '1', 1, 1, 'FOEYE'),
        (487, '2023-09-19 10:15:08.979201+08:00', '2023-09-19 10:15:08.979201+08:00', null, 0, 'Savant\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (488, '2023-09-19 10:15:08.979202+08:00', '2023-09-19 10:15:08.979202+08:00', null, 4019, 'Seminole\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (489, '2023-09-19 10:15:08.979202+08:00', '2023-09-19 10:15:08.979203+08:00', null, 4538, 'Servlet\/([\d\.]+)|Servlet ([\d\.]+)|Servlet Engine v([\d\.]+)', '1,2,3', 1, 1, 'FOEYE'),
        (490, '2023-09-19 10:15:08.979203+08:00', '2023-09-19 10:15:08.979203+08:00', null, 0, 'Seeyon-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (491, '2023-09-19 10:15:08.979204+08:00', '2023-09-19 10:15:08.979204+08:00', null, 0, '(SG-600-P1256)v5.5', '', 0, 1, 'FOEYE'),
        (492, '2023-09-19 10:15:08.979204+08:00', '2023-09-19 10:15:08.979205+08:00', null, 0, 'Spyglass_MicroServer\/([\d\.FC]+)', '1', 1, 1, 'FOEYE'),
        (493, '2023-09-19 10:15:08.979205+08:00', '2023-09-19 10:15:08.979205+08:00', null, 8761, 'Boa\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (494, '2023-09-19 10:15:08.979205+08:00', '2023-09-19 10:15:08.979206+08:00', null, 0, 'Version ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (495, '2023-09-19 10:15:08.979206+08:00', '2023-09-19 10:15:08.979206+08:00', null, 0, 'Sun-Java-System-Web-Server\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (496, '2023-09-19 10:15:08.979207+08:00', '2023-09-19 10:15:08.979207+08:00', null, 0, 'GlassFish Server Open Source Edition ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (497, '2023-09-19 10:15:08.979207+08:00', '2023-09-19 10:15:08.979207+08:00', null, 8821, 'Comanche\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (498, '2023-09-19 10:15:08.979208+08:00', '2023-09-19 10:15:08.979208+08:00', null, 676252, 'ProjectRevision: ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (499, '2023-09-19 10:15:08.979208+08:00', '2023-09-19 10:15:08.979208+08:00', null, 8835, 'thin ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (500, '2023-09-19 10:15:08.979209+08:00', '2023-09-19 10:15:08.979209+08:00', null, 0, 'thttpd\/([\d\.b]+)', '1', 1, 1, 'FOEYE'),
        (501, '2023-09-19 10:15:08.979209+08:00', '2023-09-19 10:15:08.97921+08:00', null, 9251, 'TivoWebPlus - ([v\d\.b]+)', '1', 1, 1, 'FOEYE'),
        (502, '2023-09-19 10:15:08.97921+08:00', '2023-09-19 10:15:08.97921+08:00', null, 5081, 'TornadoServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (503, '2023-09-19 10:15:08.979211+08:00', '2023-09-19 10:15:08.979211+08:00', null, 0, 'TwistedWeb\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (504, '2023-09-19 10:15:08.979211+08:00', '2023-09-19 10:15:08.979211+08:00', null, 81057, 'uc-httpd ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (505, '2023-09-19 10:15:08.979212+08:00', '2023-09-19 10:15:08.979212+08:00', null, 9197, 'Velazquez ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (506, '2023-09-19 10:15:08.979213+08:00', '2023-09-19 10:15:08.979213+08:00', null, 7263, 'RFB ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (507, '2023-09-19 10:15:08.979213+08:00', '2023-09-19 10:15:08.979213+08:00', null, 9082, 'VNC Server Enterprise Edition\/([E\d\.]+)', '1', 1, 1, 'FOEYE'),
        (508, '2023-09-19 10:15:08.979214+08:00', '2023-09-19 10:15:08.979214+08:00', null, 0, 'W3MFC\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (509, '2023-09-19 10:15:08.979214+08:00', '2023-09-19 10:15:08.979215+08:00', null, 9156, 'Web *Crossing\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (510, '2023-09-19 10:15:08.979215+08:00', '2023-09-19 10:15:08.979215+08:00', null, 9144, 'webfs\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (511, '2023-09-19 10:15:08.979216+08:00', '2023-09-19 10:15:08.979216+08:00', null, 9147, 'Webduino\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (512, '2023-09-19 10:15:08.979216+08:00', '2023-09-19 10:15:08.979216+08:00', null, 0, 'WMI ([V\d\.]+)', '1', 1, 1, 'FOEYE'),
        (513, '2023-09-19 10:15:08.979217+08:00', '2023-09-19 10:15:08.979217+08:00', null, 226935, 'WildFly\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (514, '2023-09-19 10:15:08.979217+08:00', '2023-09-19 10:15:08.979218+08:00', null, 758132, 'WindRiver-WebServer\/([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (515, '2023-09-19 10:15:08.979218+08:00', '2023-09-19 10:15:08.979218+08:00', null, 213660, 'Yaws ([\d\.]+)', '1', 1, 1, 'FOEYE'),
        (516, '2023-09-19 10:15:08.979219+08:00', '2023-09-19 10:15:08.979219+08:00', null, 286804, 'Version: .* (V[\d\.]+)', '1', 1, 1, 'FOEYE'),
        (517, '2023-09-19 10:15:08.979219+08:00', '2023-09-19 10:15:08.979219+08:00', null, 696308, 'v3.03', '', 0, 1, 'FOEYE');
