package probe

import (
	"fmt"
	"log"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

/*
BaseProtocol 协议结构体，用户定义协议基础信息和处理函数

协议分类定义：
A： 连接服务器后，即由服务器主动返回信息的；如ssh/telnet等。
B： 按要求发送简单请求包即返回的；如http/smtp等。
C： 交互很复杂或需要多次交互(可能有随机数或session要求的)或做了深度解析的；主要指一些复杂的数据库/工控协议，如rdp/dcerpc/mongodb/cassandra/s7/bacnet等。
*/
type BaseProtocol struct {
	Name         string    // 协议名称
	AliasName    string    // 协议别名
	BaseProtocol string    // 协议tcp or udp
	RefererURL   string    // 协议参考网址
	DefaultPorts []int     // 默认端口列表，至少有一个端口
	Handle       ProbeFunc // 处理函数
	Weight       int       // 权重，越大的值越早执行，默认是0，根据协议的名称来排序
	PClass       string    // 我们定的协议分类（A/B/C三类）
	PType        string    // 协议类别（工控、物联网、数据库、消息队列等）
}

// ProtocolArray 协议列表
type ProtocolArray struct {
	BaseProtocols []BaseProtocol
}

// AddProtocol 添加协议
func (p *ProtocolArray) AddProtocol(addproto BaseProtocol) bool {
	if len(addproto.BaseProtocol) == 0 {
		addproto.BaseProtocol = "tcp" //默认为tcp
	}
	for _, proto := range p.BaseProtocols {
		if proto.Name == addproto.Name {
			log.Println("[ERROR] already has protocol named", addproto, proto)
			return false
		}
	}
	p.BaseProtocols = append(p.BaseProtocols, addproto)
	return true
}

// Clear 清空协议
func (p *ProtocolArray) Clear() {
	p.BaseProtocols = nil
}

// ProtocolNames 返回去重后的所有的协议名称
func (p *ProtocolArray) ProtocolNames() (names []string) {
	for _, proto := range p.BaseProtocols {
		str := ""
		if proto.BaseProtocol == "udp" {
			str = "U,"
		} else {
			str = "T,"
		}
		str = str + proto.Name + ","
		var ports []string
		for _, port := range proto.DefaultPorts {
			portstr := strconv.Itoa(port)
			ports = append(ports, portstr)
		}
		join := strings.Join(ports, "|")

		str = str + join

		names = append(names, str)
	}
	return names
}

// ProtocolNames 返回去重后的所有的协议名称
func (p *ProtocolArray) ProtocolClass() (names string) {
	var classA, classB, classC string
	var cntAll, cntA, cntB, cntC int
	for _, proto := range p.BaseProtocols {
		switch proto.PClass {
		case "A":
			classA += proto.Name + ","

		case "B":
			classB += proto.Name + ","

		case "C":
			classC += proto.Name + ","
		}
	}

	// 统计数量
	cntA = strings.Count(classA, ",")
	cntB = strings.Count(classB, ",")
	cntC = strings.Count(classC, ",")
	cntAll = cntA + cntB + cntC

	// 移除最后的逗号
	classA = classA[0 : len(classA)-1]
	classB = classB[0 : len(classB)-1]
	classC = classC[0 : len(classC)-1]

	names = "A Class:  cnt: " + strconv.Itoa(cntA) + " / " + getProtoClassRate(cntA, cntAll) + "%  detail: " + classA + "\r\n\r\n"
	names += "B Class:  cnt: " + strconv.Itoa(cntB) + " / " + getProtoClassRate(cntB, cntAll) + "%  detail: " + classB + "\r\n\r\n"
	names += "C Class:  cnt: " + strconv.Itoa(cntC) + " / " + getProtoClassRate(cntC, cntAll) + "%  detail: " + classC + "\r\n"

	return names
}

func getProtoClassRate(cnt, cntAll int) string {
	rate := (float64(cnt) / float64(cntAll)) * 100
	rateS := strconv.FormatFloat(rate, 'f', 2, 64)

	return rateS
}

// check协议名称的判断
func (p *ProtocolArray) CheckProtocolNames(addr string) {
	var group sync.WaitGroup
	for _, proto := range p.BaseProtocols {
		group.Add(1)
		go func(proto BaseProtocol, gro *sync.WaitGroup) {
			info, _ := proto.Handle(addr, time.Millisecond*100)
			if proto.Name != info.Protocol {
				fmt.Println(proto.Name, info.Protocol)
			}
			gro.Done()
		}(proto, &group)
	}
	group.Wait()
}

func contains(ports []int, port int) bool {
	for _, p := range ports {
		if p == port {
			return true
		}
	}
	return false
}

// ProtocolPorts 返回去重后的所有的端口号
func (p *ProtocolArray) ProtocolPorts() (ports []int) {
	for _, proto := range p.BaseProtocols {
		for _, port := range proto.DefaultPorts {
			if !contains(ports, port) {
				ports = append(ports, proto.DefaultPorts...)
			}
		}
	}
	return ports
}

func (p *ProtocolArray) PortsString() string {
	ports := p.ProtocolPorts()
	sort.Ints(ports)
	return strings.Trim(strings.Join(strings.Fields(fmt.Sprint(ports)), ","), "[]")
}

// FindProtocol 根据协议名称查找协议
func (p *ProtocolArray) FindProtocol(name string) *BaseProtocol {
	for _, proto := range p.BaseProtocols {
		if proto.Name == name {
			return &proto
		}
	}
	return nil
}

// Protocols 协议链
var Protocols ProtocolArray
