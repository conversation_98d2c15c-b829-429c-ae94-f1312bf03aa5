package probe

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"log"
	"net"
	"strings"
	"testing"
)

func WriteTest(cli net.Conn, b []byte) error {
	_, err := cli.Write(b)
	return err
}

func ReadTest(cli net.Conn) ([]byte, error) {
	r1 := make([]byte, 1024)
	_, err := cli.Read(r1)
	return r1, err
}

func TestRedis_NOAUTH(t *testing.T) {

	client, server := net.Pipe()
	//defer client.Close()
	//defer server.Close()

	// server receive and response
	go func() {
		for {
			bys, err := ReadTest(server)
			assert.Nil(t, err)
			if bytes.Contains(bys, []byte("help\r\n")) {
				respBytes := []byte(`-ERR unknown command 'help'"\r\n`)
				t.Log(" help")
				WriteTest(server, respBytes)
			}
			if bytes.Contains(bys, []byte("info\r\n")) {
				respBytes := []byte("-NOAUTH Authentication required.\r\n")
				log.Println("wtite info")
				WriteTest(server, respBytes)
			}
		}
	}()

	// client => server
	WriteTest(client, []byte("help\r\n"))
	bys, err := ReadTest(client)
	t.Log("bys:", string(bys))
	assert.Nil(t, err)
	if strings.Contains(string(bys), "-DENIED Redis is running in protected mode because protected mode is enabled") {
		t.Log("is -DENIED Redis")
		return
	}

	err = WriteTest(client, []byte("info\r\n"))

	assert.Nil(t, err)
	bys1, err := ReadTest(client)
	t.Log("bys1:", string(bys1))
	assert.Nil(t, err)
	bys = append(bys, bys1...)
	split := bytes.Split(bys, []byte("\r\n"))
	if len(split) < 2 && !bytes.Contains(bys, []byte("ERR unknown command")) {
		t.Error("fail")
	}

}

func TestRedis_DENIED(t *testing.T) {

	client, server := net.Pipe()
	//defer client.Close()
	//defer server.Close()

	// server receive and response
	go func() {
		for {
			bys, err := ReadTest(server)
			assert.Nil(t, err)
			if bytes.Contains(bys, []byte("help\r\n")) {
				respBytes := []byte("-DENIED Redis is running in protected mode because protected mode is enabled")
				t.Log(" help")
				WriteTest(server, respBytes)
			}
			if bytes.Contains(bys, []byte("info\r\n")) {
				respBytes := []byte("$1916\r\n# Server\r\nredis_version:3.0.500\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:e18d443f4404273c\r\nredis_mode:standalone\r\nos:Windows  \r\n")
				log.Println("wtite info")
				WriteTest(server, respBytes)
			}
		}
	}()

	// client => server
	WriteTest(client, []byte("help\r\n"))
	bys, err := ReadTest(client)
	t.Log("bys:", string(bys))
	assert.Nil(t, err)
	if strings.Contains(string(bys), "-DENIED Redis is running in protected mode because protected mode is enabled") {
		t.Log("is -DENIED Redis")
		return
	}

	err = WriteTest(client, []byte("info\r\n"))

	assert.Nil(t, err)
	bys1, err := ReadTest(client)
	t.Log("bys1:", string(bys1))
	assert.Nil(t, err)
	bys = append(bys, bys1...)
	split := bytes.Split(bys, []byte("\r\n"))
	if len(split) < 2 && !bytes.Contains(bys, []byte("ERR unknown command")) {
		t.Error("fail")
	}

}

func TestRedis_Info(t *testing.T) {

	client, server := net.Pipe()
	//defer client.Close()
	//defer server.Close()

	// server receive and response
	go func() {
		for {
			bys, err := ReadTest(server)
			assert.Nil(t, err)
			if bytes.Contains(bys, []byte("help\r\n")) {
				respBytes := []byte(`-ERR unknown command 'help'"\r\n`)
				t.Log(" help")
				WriteTest(server, respBytes)
			}
			if bytes.Contains(bys, []byte("info\r\n")) {
				respBytes := []byte("$1916\r\n# Server\r\nredis_version:3.0.500\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:e18d443f4404273c\r\nredis_mode:standalone\r\nos:Windows  \r\n")
				log.Println("wtite info")
				WriteTest(server, respBytes)
			}
		}
	}()

	// client => server
	WriteTest(client, []byte("help\r\n"))
	bys, err := ReadTest(client)
	t.Log("bys:", string(bys))
	assert.Nil(t, err)
	if strings.Contains(string(bys), "-DENIED Redis is running in protected mode because protected mode is enabled") {
		t.Log("is -DENIED Redis")
		return
	}

	err = WriteTest(client, []byte("info\r\n"))

	assert.Nil(t, err)
	bys1, err := ReadTest(client)
	t.Log("bys1:", string(bys1))
	assert.Nil(t, err)
	bys = append(bys, bys1...)
	split := bytes.Split(bys, []byte("\r\n"))
	if len(split) < 2 && !bytes.Contains(bys, []byte("ERR unknown command")) {
		t.Error("fail")
	}

}
