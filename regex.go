package probe

import (
	"regexp"
	"strings"
)

var (
	RegSieve          = regexp.MustCompile(`^"IMPLEMENTATION"`)
	RegShoutcast      = regexp.MustCompile("^ICY")
	RegSerialNumberd  = regexp.MustCompile("^SNRESPS")
	RegPjl            = regexp.MustCompile("^@PJL\\s*INFO\\s*STATUS")
	RegPCANYWHEREDATA = regexp.MustCompile(`^\0X\x08\0\}\x08\r\n\0\.\x08.*\.\.\.\r\n`)
	RegNetbus         = regexp.MustCompile("^NetBus")
	RegFinger         = regexp.MustCompile("^\r\n\\s+Line\\s+User\\s+Host")
	RegDvr            = regexp.MustCompile("^head")
	RegAcpp           = regexp.MustCompile("^acpp")
	RegAgent          = regexp.MustCompile("^Agent")
	RegActiveMQ       = regexp.MustCompile("^ActiveMQ")
	RegAmqp           = regexp.MustCompile("AMQP")
	RegAmqp01         = regexp.MustCompile("\x01\x00\x00\x00\x00\x01")
	RegCcnet          = regexp.MustCompile(`^\x01\x01\x00\(\x00\x00\x00\x00([0-9a-f]{40})`)
	RegCeph           = regexp.MustCompile(`^ceph (v[\w._-]+)\x00\x00\x00\x00....\x00\x02......\x00{120}\x00\x00\x00\x00....\x00\x02......\x00{120}`)
	RegCitrixICA      = regexp.MustCompile(`^\x7f\x7fICA\0\x7f\x7fICA\0`)
	RegCslistener     = regexp.MustCompile(`^\x00\x00\x00\x08yesorno\x00\x08\x00....$`)
	RegEcomHAP        = regexp.MustCompile("^HAP")
	RegFireBird       = regexp.MustCompile(`^\x00\x00\x00\x03\x00\x00\x00\x0a\x00\x00\x00\x01`)
	RegFlashConnect   = regexp.MustCompile(`^FlashCONNECT ([\d.]+) invalid message\.\n$`)
	RegFSSO           = regexp.MustCompile(`.*FSAE server.*FSAE_SERVER_10001.*`)
	RegGardasoft      = regexp.MustCompile(`^Gard`)
	RegGearman        = regexp.MustCompile("^\x00RES")
	RegGeovisonMobile = regexp.MustCompile(`^D3"`)
	RegGoip           = regexp.MustCompile("^GIOP")
	RegH239           = regexp.MustCompile(`^BadRecord`)
	RegHadoopIpc      = regexp.MustCompile(`^\0\0\0\0\x03\0\0\0\x7c\xff\xff\xff\xff\0\0\0\)org\.apache\.hadoop\.ipc\.RPC\$VersionMismatch\0\0\0>Server IPC version (\d+) cannot communicate with client version 47`)
	RegHadoopIpc2     = regexp.MustCompile(`^\0\0\0\x7c{\x08\xff\xff\xff\xff\x0f\x10\x02\x18\t\"\)org\.apache\.hadoop\.ipc\.RPC\$VersionMismatch\*>Server IPC version (\d+) cannot communicate with client version \d+\x0e:\0@\x01`)
	RegHadoopIpc3     = regexp.MustCompile(`^HTTP/1\.1 404 Not Found\r\nContent-type: text/plain\r\n\r\nIt looks like you are making an HTTP request to a Hadoop IPC port\. This is not the correct port for the web interface on this daemon\.\r\n`)
	RegHartIP         = regexp.MustCompile("^\x01\x01\x00\x00")
	RegHazelcast      = regexp.MustCompile(`^3c00000000c00101`)
	RegHSQLDB         = regexp.MustCompile(`^HSQLDB JDBC Network Listener\.\n`)
	RegDB2das         = regexp.MustCompile("^DB2RETADDR")
	RegIcap           = regexp.MustCompile(`^ICAP/1.0`)
	RegJavarmi        = regexp.MustCompile("^N\x00")
	RegJdwp           = regexp.MustCompile("^JDWP")
	RegJxta           = regexp.MustCompile(`^JXTA`)
	RegMSSQLM         = regexp.MustCompile(`^\x05..ServerName;([\w\-]+);InstanceName;[\w\-]+;IsClustered;\w{2,3};Version;([\d\.]+);`)
	RegNMM            = regexp.MustCompile(`^\0\x40\x51\0\0\0\0`)
	RegNoMachineNX    = regexp.MustCompile(`^NXD-([\d.]+)`)
	RegPoppassd       = regexp.MustCompile(`(?i)^200 \S+ hello, who are you?|(?i)^200 poppassd( \S+)* hello, who are you?|(?i)^200 \S+ poppassd v|(?i)200 hello and welcome to( \S+)* poppassd`)
	RegQuic           = regexp.MustCompile(`(?is)^\s\x89*.*((?:Q[0-8]\d\d)+)`)
	// RegRifarDvr RegRedis00        = regexp.MustCompile(`^-ERR wrong number of arguments for 'get' command\r\n`)
	//RegRedis01        = regexp.MustCompile(`-ERR operation not permitted\r\n`)
	//RegRedis03        = regexp.MustCompile(`^-DENIED Redis is running in protected mode because protected mode is enabled`)
	//RegRedis02        = regexp.MustCompile(`^\$\d+\r\n(?:#[^\r\n]*\r\n)*redis_version:([.\d]+)\r\n`)
	RegRifarDvr          = regexp.MustCompile("^RIFA")
	RegRtsp              = regexp.MustCompile("^RTSP")
	RegStratum1          = regexp.MustCompile(`^{\"id\":null,\"method\":\"mining\.notify\",\"params\":\[`)
	RegVmwareAuth        = regexp.MustCompile("VMware Authentication Daemon")
	RegVrv               = regexp.MustCompile(`^ vrv`)
	RegVtun              = regexp.MustCompile("^VTUN")
	RegWinbox            = regexp.MustCompile(`(?s)(index.*roteros.dll|\\xff\\x02index\\x00\\x00\\x00\\x00\\x00\\x00\\x01.*\S\.dll)`)
	RegZK                = regexp.MustCompile(`^Zookeeper version`)
	Regsvn               = regexp.MustCompile(`^\( success \(.*\) \)\s*$`)
	RegVss               = regexp.MustCompile("^GeOv")
	RegLibp2pMultistream = regexp.MustCompile(`^.{1,4}/multistream/([\d.]+)\n`)
)

func CheckSCCM(b []byte) bool {
	if strings.Contains(string(b), "\x00\x48\x00\x41\x00\x4e\x00\x44\x00\x53\x00\x48\x00\x41\x00\x4b\x00\x45") && strings.Contains(string(b), "\x00\x53\x00\x54\x00\x41\x00\x52\x00\x54") {
		return true
	}
	return false
}

func CheckTransBase(data []byte) bool {
	if strings.Contains(string(data), "@TransBase Multiplexer error") {
		return true
	}
	return false
}
