package rulengine

import (
	"bufio"
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"git.gobies.org/sutra/gosutra/rulengine/rsa"
	"git.gobies.org/sutra/gosutra/structs"
	"log"
	"strings"
	"sync"
)

var (
	globalProductManager *ProductManager
	productInitOnce      sync.Once
)

func NewProductManager() *ProductManager {
	return &ProductManager{
		ruleManager: &ruleManager{},
	}
}

func getProductManager() *ProductManager {
	productInitOnce.Do(func() {
		globalProductManager = NewProductManager()
	})
	return globalProductManager
}

func getProductManagerForEncrypt() *ProductManager {
	productInitOnce.Do(func() {
		globalProductManager = NewProductManager()
		globalProductManager.ruleManager.isEncrypt = true
	})
	return globalProductManager
}

/*
ProductManager 产品的管理器。包含两部分：一个是产品信息管理，一个是规则测试
*/
type ProductManager struct {
	ruleManager *ruleManager
	products    []*structs.Product
	productSize int
	pm          sync.Map //确保是否存在重复的值
	loadMetux   sync.Mutex
}

// Size 产品个数
func (p *ProductManager) Size() int {
	return p.productSize
}

// RuleSize 规则个数
func (p *ProductManager) RuleSize() int {
	return p.ruleManager.Size()
}

// 清空产品和规则
func (p *ProductManager) Clear() {
	p.ruleManager.Clear()
	p.products = nil
	p.productSize = 0
	p.pm.Range(func(key, value interface{}) bool {
		p.pm.Delete(key)
		return true
	})
}

// 是否存在产品
//func (p *ProductManager) Exist(name string) bool {
//	if _, ok := p.pm.Load(name); ok {
//		return true
//	}
//	return false
//}

// 获取产品信息
func (p *ProductManager) Get(name string) *structs.Product {
	v, ok := p.pm.Load(name)
	if !ok {
		return nil
	}
	return v.(*structs.Product)
}

// AddProduct 添加一个产品
func (p *ProductManager) AddProduct(pro *structs.Product) bool {
	// 确认不存在
	if _, ok := p.pm.Load(pro.Name); ok {
		log.Println("[WARNING] product exists:", pro.Name)
		return false
	}

	// 有些只有产品信息，没有规则
	if len(pro.Rule) > 0 {
		if !p.ruleManager.Add(pro.Name, pro.Rule) {
			return false
		}
	}

	p.products = append(p.products, pro)
	p.productSize++
	p.pm.Store(pro.Name, pro)
	return true
}

// UpdateProduct 更新规则
func (p *ProductManager) UpdateProduct(rulesContent string) bool {
	pro, err := structs.NewProductFromLine(rulesContent)

	if err != nil {
		log.Println("[WARNING] update product structs.NewProductFromLine error:", rulesContent)
		return false
	}
	index := -1

	for k := range p.products {
		if p.products[k].RuleId == pro.RuleId {
			index = k
		}
	}

	if index < 0 {
		log.Println("[WARNING] UpdateProduct not exists:", pro.RuleId)
		return false
	}

	if len(p.products[index].Rule) > 0 {
		if !p.ruleManager.Update(p.products[index].Name, p.products[index].Rule, pro.Name, pro.Rule) {
			return false
		}
	}

	name := p.products[index].Name
	p.products[index] = pro
	p.pm.Delete(name)
	p.pm.Store(pro.Name, pro)
	return true
}

func (p *ProductManager) DelProduct(rulesContent string) bool {
	pro, err := structs.NewProductFromLine(rulesContent)

	if err != nil {
		log.Println("[WARNING] update product structs.NewProductFromLine error:", rulesContent)
		return false
	}

	index := -1

	for k := range p.products {
		if p.products[k].RuleId == pro.RuleId {
			index = k
		}
	}

	if index < 0 {
		log.Println("[WARNING] DelProduct not exists:", pro.RuleId)
		return false
	}

	// 有些只有产品信息，没有规则
	if len(p.products[index].Rule) > 0 {
		if !p.ruleManager.Del(p.products[index].Name, p.products[index].Rule) {
			return false
		}
	}

	name := p.products[index].Name
	p.products = append(p.products[:index], p.products[index+1:]...)
	p.productSize--
	p.pm.Delete(name)
	return true
}

// 添加一行json解析为产品
func (p *ProductManager) AddProductLine(line string) bool {
	var (
		pro *structs.Product
		err error
	)

	pro, err = structs.NewProductFromLine(line)

	if err != nil || p == nil {
		return false
	}
	return p.AddProduct(pro)
}

// 添加一行json解析为产品
func (p *ProductManager) AddProductEncryptLine(line string) bool {
	var (
		pro *structs.Product
		err error
	)

	pro, err = structs.NewProductFromEncryptLine(line)

	if err != nil || p == nil {
		return false
	}
	return p.AddProduct(pro)
}

// 添加一个map解析为产品
func (p *ProductManager) AddProductMap(m map[string]string) bool {
	pro := structs.NewProductFromMap(m)
	return p.AddProduct(pro)
}

// LoadProducts 加载规则
func (p *ProductManager) LoadProducts(ctx context.Context, rulesContent string, clearBeforeLoad bool) bool {
	p.loadMetux.Lock()
	defer p.loadMetux.Unlock()

	if clearBeforeLoad {
		p.Clear()
	}

	scanner := bufio.NewScanner(strings.NewReader(rulesContent))

	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024) //https://github.com/ma6174/blog/issues/10
	for scanner.Scan() {
		// 确认没有停止
		select {
		case <-ctx.Done():
			return false
		default:
		}

		line := scanner.Bytes()
		if len(line) == 0 {
			continue
		}

		if !p.AddProductLine(string(line)) {
			return false
		}
	}
	return true
}

// dropCR drops a terminal \r from the data.
func dropCR(data []byte) []byte {
	if len(data) > 0 && data[len(data)-1] == '\r' {
		return data[0 : len(data)-1]
	}
	return data
}

// ScanCRLF fix read line according '\r\n'
func ScanCRLF(data []byte, atEOF bool) (advance int, token []byte, err error) {
	if atEOF && len(data) == 0 {
		return 0, nil, nil
	}
	if i := bytes.Index(data, []byte{'\r', '\n'}); i >= 0 {
		// We have a full newline-terminated line.
		return i + 2, dropCR(data[0:i]), nil
	}
	// If we're at EOF, we have a final, non-terminated line. Return it.
	if atEOF {
		return len(data), dropCR(data), nil
	}
	// Request more data.
	return 0, nil, nil
}

func (p *ProductManager) LoadEncryptProducts(ctx context.Context, rulesContent string, clearBeforeLoad bool) bool {
	p.loadMetux.Lock()
	defer p.loadMetux.Unlock()

	if clearBeforeLoad {
		p.Clear()
	}

	items := strings.SplitN(rulesContent, ":", 2)
	if len(items) != 2 {
		return false
	}

	encoded, err := base64.StdEncoding.DecodeString(items[1])
	if err != nil {
		fmt.Println("base64.StdEncoding.DecodeString error", err)
		return false
	}

	DECRYPTContent, err := rsa.RSA.PubKeyDECRYPT(encoded)

	scanner := bufio.NewScanner(strings.NewReader(string(DECRYPTContent)))
	scanner.Split(ScanCRLF)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024) //https://github.com/ma6174/blog/issues/10

	for scanner.Scan() {
		// 确认没有停止
		select {
		case <-ctx.Done():
			return false
		default:
		}

		line := scanner.Bytes()
		if len(line) == 0 {
			continue
		}

		if !p.AddProductEncryptLine(string(line)) {
			fmt.Println("error line , skip current line:", string(line))
			//  return false  如果直接报错返回false的话，会导致后续的规则都不生效，所以这里只是打印日志，不返回false，跳过当前错误的，继续后边规则加载
			continue
		}
	}

	return true
}

// 执行规则匹配
func (p *ProductManager) ProductsOfJson(obj *structs.JsonObj) ([]*structs.Product, error) {
	products, err := p.ruleManager.ProductsOfJson(obj)
	if err != nil {
		return nil, err
	}

	// ehash检查
	for _, pro := range p.products {
		if pro.EHash == "" || pro.EHash == "-" {
			continue
		}
		if pro.EHash == obj.EHash() {
			products = append(products, pro.Name)
			break
		}
	}

	var ps []*structs.Product
	for _, product := range products {
		pro := p.Get(product)
		if pro == nil {
			log.Println("[WARNING] product not exists:", product)
			continue
		}
		ps = append(ps, pro.Copy())
	}
	return ps, nil
}

func (p *ProductManager) AllProducts() []*structs.Product {
	return p.products
}
